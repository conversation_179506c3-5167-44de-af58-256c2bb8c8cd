{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\admin\\\\CategoryManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { FolderTree, Plus, Edit, Trash2, Eye, EyeOff } from 'lucide-react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CategoryManagement = () => {\n  _s();\n  const {\n    user\n  } = useAdminAuth();\n  const permissions = usePermissions(user);\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    // Check if user has permission to manage categories\n    if (!permissions.canManageCategories) {\n      setError('You do not have permission to manage categories');\n      setLoading(false);\n      return;\n    }\n    loadCategories();\n  }, [permissions.canManageCategories]);\n  const loadCategories = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // TODO: Implement API call to fetch categories\n      // const response = await categoryService.getCategoriesWithSubcategories();\n      // setCategories(response.data.categories);\n\n      // Mock data for now\n      setCategories([{\n        category_id: 1,\n        name: 'Academic',\n        description: 'Academic announcements and events',\n        color_code: '#3b82f6',\n        is_active: true,\n        subcategories: [{\n          subcategory_id: 1,\n          name: 'Exams',\n          description: 'Examination schedules and updates',\n          color_code: '#ef4444',\n          display_order: 1,\n          is_active: true\n        }]\n      }, {\n        category_id: 2,\n        name: 'Events',\n        description: 'School events and activities',\n        color_code: '#10b981',\n        is_active: true,\n        subcategories: []\n      }]);\n    } catch (err) {\n      setError('Failed to load categories');\n      console.error('Error loading categories:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (!permissions.canManageCategories) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      },\n      children: [/*#__PURE__*/_jsxDEV(FolderTree, {\n        size: 64,\n        style: {\n          marginBottom: '1rem',\n          opacity: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          margin: '0 0 0.5rem',\n          fontSize: '1.5rem',\n          fontWeight: '600'\n        },\n        children: \"Access Denied\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: 0,\n          fontSize: '1rem'\n        },\n        children: \"You do not have permission to manage categories and subcategories.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        },\n        children: [\"Current Role: \", permissions.getPositionDisplayName()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '40px',\n          height: '40px',\n          border: '4px solid #f3f4f6',\n          borderTop: '4px solid #3b82f6',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '2rem',\n        textAlign: 'center',\n        color: '#ef4444'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Error\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: loadCategories,\n        style: {\n          padding: '0.5rem 1rem',\n          background: '#3b82f6',\n          color: 'white',\n          border: 'none',\n          borderRadius: '6px',\n          cursor: 'pointer'\n        },\n        children: \"Retry\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      maxWidth: '1200px',\n      margin: '0 auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            margin: '0 0 0.5rem',\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          },\n          children: \"Category Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          },\n          children: \"Manage categories and subcategories for announcements and events\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          padding: '0.75rem 1.5rem',\n          background: '#3b82f6',\n          color: 'white',\n          border: 'none',\n          borderRadius: '8px',\n          fontSize: '0.875rem',\n          fontWeight: '600',\n          cursor: 'pointer',\n          transition: 'background-color 0.2s'\n        },\n        onMouseEnter: e => e.currentTarget.style.background = '#2563eb',\n        onMouseLeave: e => e.currentTarget.style.background = '#3b82f6',\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), \"Add Category\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '12px',\n        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n        overflow: 'hidden'\n      },\n      children: categories.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '3rem',\n          textAlign: 'center',\n          color: '#6b7280'\n        },\n        children: [/*#__PURE__*/_jsxDEV(FolderTree, {\n          size: 48,\n          style: {\n            marginBottom: '1rem',\n            opacity: 0.5\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 0.5rem',\n            fontSize: '1.125rem',\n            fontWeight: '600'\n          },\n          children: \"No categories found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            fontSize: '0.875rem'\n          },\n          children: \"Create your first category to get started\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 11\n      }, this) : categories.map((category, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '1.5rem',\n          borderBottom: index < categories.length - 1 ? '1px solid #f3f4f6' : 'none'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '16px',\n                height: '16px',\n                background: category.color_code,\n                borderRadius: '4px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: '0 0 0.25rem',\n                  fontSize: '1.125rem',\n                  fontWeight: '600',\n                  color: '#1f2937'\n                },\n                children: category.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 21\n              }, this), category.description && /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                },\n                children: category.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                padding: '0.25rem 0.5rem',\n                background: category.is_active ? '#dcfce7' : '#fef2f2',\n                color: category.is_active ? '#166534' : '#dc2626',\n                borderRadius: '4px',\n                fontSize: '0.75rem',\n                fontWeight: '600'\n              },\n              children: category.is_active ? 'Active' : 'Inactive'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                padding: '0.5rem',\n                background: 'transparent',\n                border: '1px solid #d1d5db',\n                borderRadius: '6px',\n                cursor: 'pointer',\n                color: '#6b7280'\n              },\n              children: /*#__PURE__*/_jsxDEV(Edit, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                padding: '0.5rem',\n                background: 'transparent',\n                border: '1px solid #d1d5db',\n                borderRadius: '6px',\n                cursor: 'pointer',\n                color: '#6b7280'\n              },\n              children: category.is_active ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 43\n              }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 66\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                padding: '0.5rem',\n                background: 'transparent',\n                border: '1px solid #fca5a5',\n                borderRadius: '6px',\n                cursor: 'pointer',\n                color: '#dc2626'\n              },\n              children: /*#__PURE__*/_jsxDEV(Trash2, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 15\n        }, this), category.subcategories && category.subcategories.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginLeft: '2rem',\n            paddingLeft: '1rem',\n            borderLeft: '2px solid #f3f4f6'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              margin: '0 0 0.75rem',\n              fontSize: '0.875rem',\n              fontWeight: '600',\n              color: '#6b7280',\n              textTransform: 'uppercase',\n              letterSpacing: '0.5px'\n            },\n            children: [\"Subcategories (\", category.subcategories.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 19\n          }, this), category.subcategories.map(subcategory => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              padding: '0.75rem',\n              background: '#f9fafb',\n              borderRadius: '6px',\n              marginBottom: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '12px',\n                  height: '12px',\n                  background: subcategory.color_code,\n                  borderRadius: '3px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '0.875rem',\n                    fontWeight: '500',\n                    color: '#1f2937'\n                  },\n                  children: subcategory.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 27\n                }, this), subcategory.description && /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: '0.25rem 0 0',\n                    fontSize: '0.75rem',\n                    color: '#6b7280'\n                  },\n                  children: subcategory.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 29\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  padding: '0.125rem 0.375rem',\n                  background: subcategory.is_active ? '#dcfce7' : '#fef2f2',\n                  color: subcategory.is_active ? '#166534' : '#dc2626',\n                  borderRadius: '3px',\n                  fontSize: '0.625rem',\n                  fontWeight: '600'\n                },\n                children: subcategory.is_active ? 'Active' : 'Inactive'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 25\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 23\n            }, this)]\n          }, subcategory.subcategory_id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 21\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 17\n        }, this)]\n      }, category.category_id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 167,\n    columnNumber: 5\n  }, this);\n};\n_s(CategoryManagement, \"Q0CuxGhrv/3/f8FwjZ8fzRHqf7A=\", false, function () {\n  return [useAdminAuth, usePermissions];\n});\n_c = CategoryManagement;\nexport default CategoryManagement;\nvar _c;\n$RefreshReg$(_c, \"CategoryManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FolderTree", "Plus", "Edit", "Trash2", "Eye", "Eye<PERSON>ff", "useAdminAuth", "usePermissions", "jsxDEV", "_jsxDEV", "CategoryManagement", "_s", "user", "permissions", "categories", "setCategories", "loading", "setLoading", "error", "setError", "canManageCategories", "loadCategories", "category_id", "name", "description", "color_code", "is_active", "subcategories", "subcategory_id", "display_order", "err", "console", "style", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "textAlign", "color", "children", "size", "marginBottom", "opacity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "margin", "fontSize", "fontWeight", "marginTop", "padding", "background", "getPositionBadgeColor", "borderRadius", "getPositionDisplayName", "width", "height", "border", "borderTop", "animation", "onClick", "cursor", "max<PERSON><PERSON><PERSON>", "gap", "transition", "onMouseEnter", "e", "currentTarget", "onMouseLeave", "boxShadow", "overflow", "length", "map", "category", "index", "borderBottom", "marginLeft", "paddingLeft", "borderLeft", "textTransform", "letterSpacing", "subcategory", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/admin/CategoryManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { FolderTree, Plus, Edit, Trash2, Eye, EyeOff } from 'lucide-react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\n\ninterface Category {\n  category_id: number;\n  name: string;\n  description?: string;\n  color_code: string;\n  is_active: boolean;\n  subcategories?: Subcategory[];\n}\n\ninterface Subcategory {\n  subcategory_id: number;\n  name: string;\n  description?: string;\n  color_code: string;\n  display_order: number;\n  is_active: boolean;\n}\n\nconst CategoryManagement: React.FC = () => {\n  const { user } = useAdminAuth();\n  const permissions = usePermissions(user);\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    // Check if user has permission to manage categories\n    if (!permissions.canManageCategories) {\n      setError('You do not have permission to manage categories');\n      setLoading(false);\n      return;\n    }\n\n    loadCategories();\n  }, [permissions.canManageCategories]);\n\n  const loadCategories = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      // TODO: Implement API call to fetch categories\n      // const response = await categoryService.getCategoriesWithSubcategories();\n      // setCategories(response.data.categories);\n      \n      // Mock data for now\n      setCategories([\n        {\n          category_id: 1,\n          name: 'Academic',\n          description: 'Academic announcements and events',\n          color_code: '#3b82f6',\n          is_active: true,\n          subcategories: [\n            {\n              subcategory_id: 1,\n              name: 'Exams',\n              description: 'Examination schedules and updates',\n              color_code: '#ef4444',\n              display_order: 1,\n              is_active: true\n            }\n          ]\n        },\n        {\n          category_id: 2,\n          name: 'Events',\n          description: 'School events and activities',\n          color_code: '#10b981',\n          is_active: true,\n          subcategories: []\n        }\n      ]);\n    } catch (err) {\n      setError('Failed to load categories');\n      console.error('Error loading categories:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (!permissions.canManageCategories) {\n    return (\n      <div style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      }}>\n        <FolderTree size={64} style={{ marginBottom: '1rem', opacity: 0.5 }} />\n        <h2 style={{ margin: '0 0 0.5rem', fontSize: '1.5rem', fontWeight: '600' }}>\n          Access Denied\n        </h2>\n        <p style={{ margin: 0, fontSize: '1rem' }}>\n          You do not have permission to manage categories and subcategories.\n        </p>\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        }}>\n          Current Role: {permissions.getPositionDisplayName()}\n        </div>\n      </div>\n    );\n  }\n\n  if (loading) {\n    return (\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px'\n      }}>\n        <div style={{\n          width: '40px',\n          height: '40px',\n          border: '4px solid #f3f4f6',\n          borderTop: '4px solid #3b82f6',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }} />\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div style={{\n        padding: '2rem',\n        textAlign: 'center',\n        color: '#ef4444'\n      }}>\n        <h2>Error</h2>\n        <p>{error}</p>\n        <button\n          onClick={loadCategories}\n          style={{\n            padding: '0.5rem 1rem',\n            background: '#3b82f6',\n            color: 'white',\n            border: 'none',\n            borderRadius: '6px',\n            cursor: 'pointer'\n          }}\n        >\n          Retry\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ maxWidth: '1200px', margin: '0 auto' }}>\n      {/* Header */}\n      <div style={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      }}>\n        <div>\n          <h1 style={{\n            margin: '0 0 0.5rem',\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          }}>\n            Category Management\n          </h1>\n          <p style={{\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          }}>\n            Manage categories and subcategories for announcements and events\n          </p>\n        </div>\n        \n        <button\n          style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            padding: '0.75rem 1.5rem',\n            background: '#3b82f6',\n            color: 'white',\n            border: 'none',\n            borderRadius: '8px',\n            fontSize: '0.875rem',\n            fontWeight: '600',\n            cursor: 'pointer',\n            transition: 'background-color 0.2s'\n          }}\n          onMouseEnter={(e) => e.currentTarget.style.background = '#2563eb'}\n          onMouseLeave={(e) => e.currentTarget.style.background = '#3b82f6'}\n        >\n          <Plus size={16} />\n          Add Category\n        </button>\n      </div>\n\n      {/* Categories List */}\n      <div style={{\n        background: 'white',\n        borderRadius: '12px',\n        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n        overflow: 'hidden'\n      }}>\n        {categories.length === 0 ? (\n          <div style={{\n            padding: '3rem',\n            textAlign: 'center',\n            color: '#6b7280'\n          }}>\n            <FolderTree size={48} style={{ marginBottom: '1rem', opacity: 0.5 }} />\n            <h3 style={{ margin: '0 0 0.5rem', fontSize: '1.125rem', fontWeight: '600' }}>\n              No categories found\n            </h3>\n            <p style={{ margin: 0, fontSize: '0.875rem' }}>\n              Create your first category to get started\n            </p>\n          </div>\n        ) : (\n          categories.map((category, index) => (\n            <div\n              key={category.category_id}\n              style={{\n                padding: '1.5rem',\n                borderBottom: index < categories.length - 1 ? '1px solid #f3f4f6' : 'none'\n              }}\n            >\n              {/* Category Header */}\n              <div style={{\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                marginBottom: '1rem'\n              }}>\n                <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>\n                  <div\n                    style={{\n                      width: '16px',\n                      height: '16px',\n                      background: category.color_code,\n                      borderRadius: '4px'\n                    }}\n                  />\n                  <div>\n                    <h3 style={{\n                      margin: '0 0 0.25rem',\n                      fontSize: '1.125rem',\n                      fontWeight: '600',\n                      color: '#1f2937'\n                    }}>\n                      {category.name}\n                    </h3>\n                    {category.description && (\n                      <p style={{\n                        margin: 0,\n                        fontSize: '0.875rem',\n                        color: '#6b7280'\n                      }}>\n                        {category.description}\n                      </p>\n                    )}\n                  </div>\n                </div>\n                \n                <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                  <span style={{\n                    padding: '0.25rem 0.5rem',\n                    background: category.is_active ? '#dcfce7' : '#fef2f2',\n                    color: category.is_active ? '#166534' : '#dc2626',\n                    borderRadius: '4px',\n                    fontSize: '0.75rem',\n                    fontWeight: '600'\n                  }}>\n                    {category.is_active ? 'Active' : 'Inactive'}\n                  </span>\n                  \n                  <button\n                    style={{\n                      padding: '0.5rem',\n                      background: 'transparent',\n                      border: '1px solid #d1d5db',\n                      borderRadius: '6px',\n                      cursor: 'pointer',\n                      color: '#6b7280'\n                    }}\n                  >\n                    <Edit size={16} />\n                  </button>\n                  \n                  <button\n                    style={{\n                      padding: '0.5rem',\n                      background: 'transparent',\n                      border: '1px solid #d1d5db',\n                      borderRadius: '6px',\n                      cursor: 'pointer',\n                      color: '#6b7280'\n                    }}\n                  >\n                    {category.is_active ? <EyeOff size={16} /> : <Eye size={16} />}\n                  </button>\n                  \n                  <button\n                    style={{\n                      padding: '0.5rem',\n                      background: 'transparent',\n                      border: '1px solid #fca5a5',\n                      borderRadius: '6px',\n                      cursor: 'pointer',\n                      color: '#dc2626'\n                    }}\n                  >\n                    <Trash2 size={16} />\n                  </button>\n                </div>\n              </div>\n\n              {/* Subcategories */}\n              {category.subcategories && category.subcategories.length > 0 && (\n                <div style={{\n                  marginLeft: '2rem',\n                  paddingLeft: '1rem',\n                  borderLeft: '2px solid #f3f4f6'\n                }}>\n                  <h4 style={{\n                    margin: '0 0 0.75rem',\n                    fontSize: '0.875rem',\n                    fontWeight: '600',\n                    color: '#6b7280',\n                    textTransform: 'uppercase',\n                    letterSpacing: '0.5px'\n                  }}>\n                    Subcategories ({category.subcategories.length})\n                  </h4>\n                  \n                  {category.subcategories.map((subcategory) => (\n                    <div\n                      key={subcategory.subcategory_id}\n                      style={{\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        padding: '0.75rem',\n                        background: '#f9fafb',\n                        borderRadius: '6px',\n                        marginBottom: '0.5rem'\n                      }}\n                    >\n                      <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>\n                        <div\n                          style={{\n                            width: '12px',\n                            height: '12px',\n                            background: subcategory.color_code,\n                            borderRadius: '3px'\n                          }}\n                        />\n                        <div>\n                          <span style={{\n                            fontSize: '0.875rem',\n                            fontWeight: '500',\n                            color: '#1f2937'\n                          }}>\n                            {subcategory.name}\n                          </span>\n                          {subcategory.description && (\n                            <p style={{\n                              margin: '0.25rem 0 0',\n                              fontSize: '0.75rem',\n                              color: '#6b7280'\n                            }}>\n                              {subcategory.description}\n                            </p>\n                          )}\n                        </div>\n                      </div>\n                      \n                      <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\n                        <span style={{\n                          padding: '0.125rem 0.375rem',\n                          background: subcategory.is_active ? '#dcfce7' : '#fef2f2',\n                          color: subcategory.is_active ? '#166534' : '#dc2626',\n                          borderRadius: '3px',\n                          fontSize: '0.625rem',\n                          fontWeight: '600'\n                        }}>\n                          {subcategory.is_active ? 'Active' : 'Inactive'}\n                        </span>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n          ))\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default CategoryManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,UAAU,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,GAAG,EAAEC,MAAM,QAAQ,cAAc;AAC1E,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAoBzD,MAAMC,kBAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM;IAAEC;EAAK,CAAC,GAAGN,YAAY,CAAC,CAAC;EAC/B,MAAMO,WAAW,GAAGN,cAAc,CAACK,IAAI,CAAC;EACxC,MAAM,CAACE,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAa,EAAE,CAAC;EAC5D,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAgB,IAAI,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACc,WAAW,CAACO,mBAAmB,EAAE;MACpCD,QAAQ,CAAC,iDAAiD,CAAC;MAC3DF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEAI,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACR,WAAW,CAACO,mBAAmB,CAAC,CAAC;EAErC,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFJ,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA;MACA;;MAEA;MACAJ,aAAa,CAAC,CACZ;QACEO,WAAW,EAAE,CAAC;QACdC,IAAI,EAAE,UAAU;QAChBC,WAAW,EAAE,mCAAmC;QAChDC,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,IAAI;QACfC,aAAa,EAAE,CACb;UACEC,cAAc,EAAE,CAAC;UACjBL,IAAI,EAAE,OAAO;UACbC,WAAW,EAAE,mCAAmC;UAChDC,UAAU,EAAE,SAAS;UACrBI,aAAa,EAAE,CAAC;UAChBH,SAAS,EAAE;QACb,CAAC;MAEL,CAAC,EACD;QACEJ,WAAW,EAAE,CAAC;QACdC,IAAI,EAAE,QAAQ;QACdC,WAAW,EAAE,8BAA8B;QAC3CC,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,IAAI;QACfC,aAAa,EAAE;MACjB,CAAC,CACF,CAAC;IACJ,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZX,QAAQ,CAAC,2BAA2B,CAAC;MACrCY,OAAO,CAACb,KAAK,CAAC,2BAA2B,EAAEY,GAAG,CAAC;IACjD,CAAC,SAAS;MACRb,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAI,CAACJ,WAAW,CAACO,mBAAmB,EAAE;IACpC,oBACEX,OAAA;MAAKuB,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE,OAAO;QAClBC,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACA/B,OAAA,CAACT,UAAU;QAACyC,IAAI,EAAE,EAAG;QAACT,KAAK,EAAE;UAAEU,YAAY,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAI;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvEtC,OAAA;QAAIuB,KAAK,EAAE;UAAEgB,MAAM,EAAE,YAAY;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAM,CAAE;QAAAV,QAAA,EAAC;MAE5E;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLtC,OAAA;QAAGuB,KAAK,EAAE;UAAEgB,MAAM,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAT,QAAA,EAAC;MAE3C;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJtC,OAAA;QAAKuB,KAAK,EAAE;UACVmB,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAExC,WAAW,CAACyC,qBAAqB,CAAC,CAAC;UAC/CC,YAAY,EAAE,KAAK;UACnBhB,KAAK,EAAE,OAAO;UACdU,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE;QACd,CAAE;QAAAV,QAAA,GAAC,gBACa,EAAC3B,WAAW,CAAC2C,sBAAsB,CAAC,CAAC;MAAA;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI/B,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKuB,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE;MACb,CAAE;MAAAG,QAAA,eACA/B,OAAA;QAAKuB,KAAK,EAAE;UACVyB,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,mBAAmB;UAC3BC,SAAS,EAAE,mBAAmB;UAC9BL,YAAY,EAAE,KAAK;UACnBM,SAAS,EAAE;QACb;MAAE;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEV;EAEA,IAAI7B,KAAK,EAAE;IACT,oBACET,OAAA;MAAKuB,KAAK,EAAE;QACVoB,OAAO,EAAE,MAAM;QACfd,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACA/B,OAAA;QAAA+B,QAAA,EAAI;MAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdtC,OAAA;QAAA+B,QAAA,EAAItB;MAAK;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdtC,OAAA;QACEqD,OAAO,EAAEzC,cAAe;QACxBW,KAAK,EAAE;UACLoB,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAE,SAAS;UACrBd,KAAK,EAAE,OAAO;UACdoB,MAAM,EAAE,MAAM;UACdJ,YAAY,EAAE,KAAK;UACnBQ,MAAM,EAAE;QACV,CAAE;QAAAvB,QAAA,EACH;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACEtC,OAAA;IAAKuB,KAAK,EAAE;MAAEgC,QAAQ,EAAE,QAAQ;MAAEhB,MAAM,EAAE;IAAS,CAAE;IAAAR,QAAA,gBAEnD/B,OAAA;MAAKuB,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfG,cAAc,EAAE,eAAe;QAC/BD,UAAU,EAAE,QAAQ;QACpBO,YAAY,EAAE;MAChB,CAAE;MAAAF,QAAA,gBACA/B,OAAA;QAAA+B,QAAA,gBACE/B,OAAA;UAAIuB,KAAK,EAAE;YACTgB,MAAM,EAAE,YAAY;YACpBC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjBX,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,EAAC;QAEH;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLtC,OAAA;UAAGuB,KAAK,EAAE;YACRgB,MAAM,EAAE,CAAC;YACTT,KAAK,EAAE,SAAS;YAChBU,QAAQ,EAAE;UACZ,CAAE;UAAAT,QAAA,EAAC;QAEH;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENtC,OAAA;QACEuB,KAAK,EAAE;UACLC,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpB8B,GAAG,EAAE,QAAQ;UACbb,OAAO,EAAE,gBAAgB;UACzBC,UAAU,EAAE,SAAS;UACrBd,KAAK,EAAE,OAAO;UACdoB,MAAM,EAAE,MAAM;UACdJ,YAAY,EAAE,KAAK;UACnBN,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE,KAAK;UACjBa,MAAM,EAAE,SAAS;UACjBG,UAAU,EAAE;QACd,CAAE;QACFC,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACrC,KAAK,CAACqB,UAAU,GAAG,SAAU;QAClEiB,YAAY,EAAGF,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACrC,KAAK,CAACqB,UAAU,GAAG,SAAU;QAAAb,QAAA,gBAElE/B,OAAA,CAACR,IAAI;UAACwC,IAAI,EAAE;QAAG;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEpB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNtC,OAAA;MAAKuB,KAAK,EAAE;QACVqB,UAAU,EAAE,OAAO;QACnBE,YAAY,EAAE,MAAM;QACpBgB,SAAS,EAAE,8BAA8B;QACzCC,QAAQ,EAAE;MACZ,CAAE;MAAAhC,QAAA,EACC1B,UAAU,CAAC2D,MAAM,KAAK,CAAC,gBACtBhE,OAAA;QAAKuB,KAAK,EAAE;UACVoB,OAAO,EAAE,MAAM;UACfd,SAAS,EAAE,QAAQ;UACnBC,KAAK,EAAE;QACT,CAAE;QAAAC,QAAA,gBACA/B,OAAA,CAACT,UAAU;UAACyC,IAAI,EAAE,EAAG;UAACT,KAAK,EAAE;YAAEU,YAAY,EAAE,MAAM;YAAEC,OAAO,EAAE;UAAI;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvEtC,OAAA;UAAIuB,KAAK,EAAE;YAAEgB,MAAM,EAAE,YAAY;YAAEC,QAAQ,EAAE,UAAU;YAAEC,UAAU,EAAE;UAAM,CAAE;UAAAV,QAAA,EAAC;QAE9E;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLtC,OAAA;UAAGuB,KAAK,EAAE;YAAEgB,MAAM,EAAE,CAAC;YAAEC,QAAQ,EAAE;UAAW,CAAE;UAAAT,QAAA,EAAC;QAE/C;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,GAENjC,UAAU,CAAC4D,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,kBAC7BnE,OAAA;QAEEuB,KAAK,EAAE;UACLoB,OAAO,EAAE,QAAQ;UACjByB,YAAY,EAAED,KAAK,GAAG9D,UAAU,CAAC2D,MAAM,GAAG,CAAC,GAAG,mBAAmB,GAAG;QACtE,CAAE;QAAAjC,QAAA,gBAGF/B,OAAA;UAAKuB,KAAK,EAAE;YACVC,OAAO,EAAE,MAAM;YACfG,cAAc,EAAE,eAAe;YAC/BD,UAAU,EAAE,QAAQ;YACpBO,YAAY,EAAE;UAChB,CAAE;UAAAF,QAAA,gBACA/B,OAAA;YAAKuB,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAE8B,GAAG,EAAE;YAAO,CAAE;YAAAzB,QAAA,gBACjE/B,OAAA;cACEuB,KAAK,EAAE;gBACLyB,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdL,UAAU,EAAEsB,QAAQ,CAAClD,UAAU;gBAC/B8B,YAAY,EAAE;cAChB;YAAE;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFtC,OAAA;cAAA+B,QAAA,gBACE/B,OAAA;gBAAIuB,KAAK,EAAE;kBACTgB,MAAM,EAAE,aAAa;kBACrBC,QAAQ,EAAE,UAAU;kBACpBC,UAAU,EAAE,KAAK;kBACjBX,KAAK,EAAE;gBACT,CAAE;gBAAAC,QAAA,EACCmC,QAAQ,CAACpD;cAAI;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,EACJ4B,QAAQ,CAACnD,WAAW,iBACnBf,OAAA;gBAAGuB,KAAK,EAAE;kBACRgB,MAAM,EAAE,CAAC;kBACTC,QAAQ,EAAE,UAAU;kBACpBV,KAAK,EAAE;gBACT,CAAE;gBAAAC,QAAA,EACCmC,QAAQ,CAACnD;cAAW;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtC,OAAA;YAAKuB,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAE8B,GAAG,EAAE;YAAS,CAAE;YAAAzB,QAAA,gBACnE/B,OAAA;cAAMuB,KAAK,EAAE;gBACXoB,OAAO,EAAE,gBAAgB;gBACzBC,UAAU,EAAEsB,QAAQ,CAACjD,SAAS,GAAG,SAAS,GAAG,SAAS;gBACtDa,KAAK,EAAEoC,QAAQ,CAACjD,SAAS,GAAG,SAAS,GAAG,SAAS;gBACjD6B,YAAY,EAAE,KAAK;gBACnBN,QAAQ,EAAE,SAAS;gBACnBC,UAAU,EAAE;cACd,CAAE;cAAAV,QAAA,EACCmC,QAAQ,CAACjD,SAAS,GAAG,QAAQ,GAAG;YAAU;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eAEPtC,OAAA;cACEuB,KAAK,EAAE;gBACLoB,OAAO,EAAE,QAAQ;gBACjBC,UAAU,EAAE,aAAa;gBACzBM,MAAM,EAAE,mBAAmB;gBAC3BJ,YAAY,EAAE,KAAK;gBACnBQ,MAAM,EAAE,SAAS;gBACjBxB,KAAK,EAAE;cACT,CAAE;cAAAC,QAAA,eAEF/B,OAAA,CAACP,IAAI;gBAACuC,IAAI,EAAE;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eAETtC,OAAA;cACEuB,KAAK,EAAE;gBACLoB,OAAO,EAAE,QAAQ;gBACjBC,UAAU,EAAE,aAAa;gBACzBM,MAAM,EAAE,mBAAmB;gBAC3BJ,YAAY,EAAE,KAAK;gBACnBQ,MAAM,EAAE,SAAS;gBACjBxB,KAAK,EAAE;cACT,CAAE;cAAAC,QAAA,EAEDmC,QAAQ,CAACjD,SAAS,gBAAGjB,OAAA,CAACJ,MAAM;gBAACoC,IAAI,EAAE;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGtC,OAAA,CAACL,GAAG;gBAACqC,IAAI,EAAE;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eAETtC,OAAA;cACEuB,KAAK,EAAE;gBACLoB,OAAO,EAAE,QAAQ;gBACjBC,UAAU,EAAE,aAAa;gBACzBM,MAAM,EAAE,mBAAmB;gBAC3BJ,YAAY,EAAE,KAAK;gBACnBQ,MAAM,EAAE,SAAS;gBACjBxB,KAAK,EAAE;cACT,CAAE;cAAAC,QAAA,eAEF/B,OAAA,CAACN,MAAM;gBAACsC,IAAI,EAAE;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL4B,QAAQ,CAAChD,aAAa,IAAIgD,QAAQ,CAAChD,aAAa,CAAC8C,MAAM,GAAG,CAAC,iBAC1DhE,OAAA;UAAKuB,KAAK,EAAE;YACV8C,UAAU,EAAE,MAAM;YAClBC,WAAW,EAAE,MAAM;YACnBC,UAAU,EAAE;UACd,CAAE;UAAAxC,QAAA,gBACA/B,OAAA;YAAIuB,KAAK,EAAE;cACTgB,MAAM,EAAE,aAAa;cACrBC,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBX,KAAK,EAAE,SAAS;cAChB0C,aAAa,EAAE,WAAW;cAC1BC,aAAa,EAAE;YACjB,CAAE;YAAA1C,QAAA,GAAC,iBACc,EAACmC,QAAQ,CAAChD,aAAa,CAAC8C,MAAM,EAAC,GAChD;UAAA;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAEJ4B,QAAQ,CAAChD,aAAa,CAAC+C,GAAG,CAAES,WAAW,iBACtC1E,OAAA;YAEEuB,KAAK,EAAE;cACLC,OAAO,EAAE,MAAM;cACfG,cAAc,EAAE,eAAe;cAC/BD,UAAU,EAAE,QAAQ;cACpBiB,OAAO,EAAE,SAAS;cAClBC,UAAU,EAAE,SAAS;cACrBE,YAAY,EAAE,KAAK;cACnBb,YAAY,EAAE;YAChB,CAAE;YAAAF,QAAA,gBAEF/B,OAAA;cAAKuB,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAE8B,GAAG,EAAE;cAAU,CAAE;cAAAzB,QAAA,gBACpE/B,OAAA;gBACEuB,KAAK,EAAE;kBACLyB,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdL,UAAU,EAAE8B,WAAW,CAAC1D,UAAU;kBAClC8B,YAAY,EAAE;gBAChB;cAAE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFtC,OAAA;gBAAA+B,QAAA,gBACE/B,OAAA;kBAAMuB,KAAK,EAAE;oBACXiB,QAAQ,EAAE,UAAU;oBACpBC,UAAU,EAAE,KAAK;oBACjBX,KAAK,EAAE;kBACT,CAAE;kBAAAC,QAAA,EACC2C,WAAW,CAAC5D;gBAAI;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,EACNoC,WAAW,CAAC3D,WAAW,iBACtBf,OAAA;kBAAGuB,KAAK,EAAE;oBACRgB,MAAM,EAAE,aAAa;oBACrBC,QAAQ,EAAE,SAAS;oBACnBV,KAAK,EAAE;kBACT,CAAE;kBAAAC,QAAA,EACC2C,WAAW,CAAC3D;gBAAW;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CACJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENtC,OAAA;cAAKuB,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAE8B,GAAG,EAAE;cAAU,CAAE;cAAAzB,QAAA,eACpE/B,OAAA;gBAAMuB,KAAK,EAAE;kBACXoB,OAAO,EAAE,mBAAmB;kBAC5BC,UAAU,EAAE8B,WAAW,CAACzD,SAAS,GAAG,SAAS,GAAG,SAAS;kBACzDa,KAAK,EAAE4C,WAAW,CAACzD,SAAS,GAAG,SAAS,GAAG,SAAS;kBACpD6B,YAAY,EAAE,KAAK;kBACnBN,QAAQ,EAAE,UAAU;kBACpBC,UAAU,EAAE;gBACd,CAAE;gBAAAV,QAAA,EACC2C,WAAW,CAACzD,SAAS,GAAG,QAAQ,GAAG;cAAU;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,GAnDDoC,WAAW,CAACvD,cAAc;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoD5B,CACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA,GA3KI4B,QAAQ,CAACrD,WAAW;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA4KtB,CACN;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpC,EAAA,CA1YID,kBAA4B;EAAA,QACfJ,YAAY,EACTC,cAAc;AAAA;AAAA6E,EAAA,GAF9B1E,kBAA4B;AA4YlC,eAAeA,kBAAkB;AAAC,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}