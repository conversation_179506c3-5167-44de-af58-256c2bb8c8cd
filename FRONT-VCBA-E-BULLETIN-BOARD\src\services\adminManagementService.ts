import { apiClient } from './api';

interface AdminAccount {
  admin_id?: number;
  email: string;
  is_active: boolean;
  last_login?: string | null;
  created_at?: string;
  profile: {
    first_name: string;
    middle_name?: string;
    last_name: string;
    suffix?: string;
    full_name: string;
    phone_number?: string;
    department?: string;
    position: 'super_admin' | 'professor';
    grade_level?: number;
    bio?: string;
    profile_picture?: string;
  };
}

interface CreateAdminData extends AdminAccount {
  password: string;
}

interface GetAdminsParams {
  page?: number;
  limit?: number;
  search?: string;
  position?: string;
  status?: string;
}

interface GetAdminsResponse {
  success: boolean;
  data: {
    admins: AdminAccount[];
    pagination: {
      currentPage: number;
      totalPages: number;
      totalItems: number;
      itemsPerPage: number;
    };
  };
  message?: string;
}

interface AdminResponse {
  success: boolean;
  data?: {
    admin: AdminAccount;
  };
  message: string;
}

class AdminManagementService {
  private baseUrl = '/api/admin/admins';

  /**
   * Get all admin accounts with optional filtering and pagination
   */
  async getAdmins(params: GetAdminsParams = {}): Promise<GetAdminsResponse> {
    try {
      const queryParams = new URLSearchParams();
      
      if (params.page) queryParams.append('page', params.page.toString());
      if (params.limit) queryParams.append('limit', params.limit.toString());
      if (params.search) queryParams.append('search', params.search);
      if (params.position) queryParams.append('position', params.position);
      if (params.status) queryParams.append('status', params.status);

      const response = await apiClient.get(`${this.baseUrl}?${queryParams.toString()}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch admin accounts');
    }
  }

  /**
   * Get a specific admin account by ID
   */
  async getAdmin(adminId: number): Promise<AdminResponse> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/${adminId}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch admin account');
    }
  }

  /**
   * Create a new admin account
   */
  async createAdmin(adminData: CreateAdminData): Promise<AdminResponse> {
    try {
      const response = await apiClient.post(this.baseUrl, adminData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to create admin account');
    }
  }

  /**
   * Update an existing admin account
   */
  async updateAdmin(adminId: number, adminData: Partial<AdminAccount>): Promise<AdminResponse> {
    try {
      const response = await apiClient.put(`${this.baseUrl}/${adminId}`, adminData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update admin account');
    }
  }

  /**
   * Delete (deactivate) an admin account
   */
  async deleteAdmin(adminId: number): Promise<AdminResponse> {
    try {
      const response = await apiClient.delete(`${this.baseUrl}/${adminId}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete admin account');
    }
  }

  /**
   * Toggle admin account status (active/inactive)
   */
  async toggleAdminStatus(adminId: number, isActive: boolean): Promise<AdminResponse> {
    try {
      const response = await apiClient.patch(`${this.baseUrl}/${adminId}/status`, {
        is_active: isActive
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update admin status');
    }
  }

  /**
   * Change admin password
   */
  async changePassword(adminId: number, currentPassword: string, newPassword: string): Promise<AdminResponse> {
    try {
      const response = await apiClient.patch(`${this.baseUrl}/${adminId}/password`, {
        current_password: currentPassword,
        new_password: newPassword
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to change password');
    }
  }

  /**
   * Reset admin password (super admin only)
   */
  async resetPassword(adminId: number, newPassword: string): Promise<AdminResponse> {
    try {
      const response = await apiClient.patch(`${this.baseUrl}/${adminId}/reset-password`, {
        new_password: newPassword
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to reset password');
    }
  }

  /**
   * Upload admin profile picture
   */
  async uploadProfilePicture(adminId: number, file: File): Promise<AdminResponse> {
    try {
      const formData = new FormData();
      formData.append('profile_picture', file);

      const response = await apiClient.post(`${this.baseUrl}/${adminId}/profile-picture`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to upload profile picture');
    }
  }

  /**
   * Get admin activity logs
   */
  async getAdminLogs(adminId: number, params: { page?: number; limit?: number } = {}): Promise<any> {
    try {
      const queryParams = new URLSearchParams();
      if (params.page) queryParams.append('page', params.page.toString());
      if (params.limit) queryParams.append('limit', params.limit.toString());

      const response = await apiClient.get(`${this.baseUrl}/${adminId}/logs?${queryParams.toString()}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch admin logs');
    }
  }

  /**
   * Bulk operations
   */
  async bulkUpdateStatus(adminIds: number[], isActive: boolean): Promise<AdminResponse> {
    try {
      const response = await apiClient.patch(`${this.baseUrl}/bulk/status`, {
        admin_ids: adminIds,
        is_active: isActive
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to bulk update admin status');
    }
  }

  async bulkDelete(adminIds: number[]): Promise<AdminResponse> {
    try {
      const response = await apiClient.delete(`${this.baseUrl}/bulk`, {
        data: { admin_ids: adminIds }
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to bulk delete admins');
    }
  }

  /**
   * Export admin data
   */
  async exportAdmins(format: 'csv' | 'xlsx' | 'json' = 'csv'): Promise<Blob> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/export`, {
        params: { format },
        responseType: 'blob'
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to export admin data');
    }
  }

  /**
   * Import admin data
   */
  async importAdmins(file: File): Promise<AdminResponse> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await apiClient.post(`${this.baseUrl}/import`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to import admin data');
    }
  }
}

export const adminManagementService = new AdminManagementService();
export default adminManagementService;
