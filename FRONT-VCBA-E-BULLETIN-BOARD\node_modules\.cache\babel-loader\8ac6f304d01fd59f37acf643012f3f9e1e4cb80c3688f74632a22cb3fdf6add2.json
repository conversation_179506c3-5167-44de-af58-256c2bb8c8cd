{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\admin\\\\SMSSettings.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { MessageSquare, Send, Settings, AlertTriangle, CheckCircle, Phone, Globe, Key } from 'lucide-react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SMSSettings = () => {\n  _s();\n  const {\n    user\n  } = useAdminAuth();\n  const permissions = usePermissions(user);\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n  const [activeTab, setActiveTab] = useState('config');\n\n  // SMS Configuration State\n  const [smsConfig, setSmsConfig] = useState({\n    apiKey: '',\n    apiUrl: '',\n    isEnabled: false,\n    defaultSender: 'VCBA',\n    maxMessageLength: 160,\n    rateLimitPerMinute: 10\n  });\n\n  // SMS Templates State\n  const [templates, setTemplates] = useState([{\n    id: '1',\n    name: 'Class Cancellation',\n    content: 'NOTICE: Your {subject} class scheduled for {time} on {date} has been cancelled. Please check the bulletin board for updates.',\n    category: 'announcement'\n  }, {\n    id: '2',\n    name: 'Emergency Alert',\n    content: 'EMERGENCY: {message}. Please follow safety protocols and await further instructions.',\n    category: 'emergency'\n  }, {\n    id: '3',\n    name: 'Event Reminder',\n    content: 'REMINDER: {event_name} is scheduled for {date} at {time}. Location: {venue}. Don\\'t miss it!',\n    category: 'reminder'\n  }]);\n\n  // Test SMS State\n  const [testPhone, setTestPhone] = useState('');\n  const [testMessage, setTestMessage] = useState('This is a test message from VCBA E-Bulletin Board system.');\n  const [testSending, setTestSending] = useState(false);\n  useEffect(() => {\n    // Check if user has permission to manage SMS settings\n    if (!permissions.canManageSMSSettings) {\n      setError('You do not have permission to manage SMS settings');\n      setLoading(false);\n      return;\n    }\n    loadSMSSettings();\n  }, [permissions.canManageSMSSettings]);\n  const loadSMSSettings = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // TODO: Implement API call to fetch SMS settings\n      // const response = await smsService.getSettings();\n      // setSmsConfig(response.data.config);\n\n      // Mock data for now - in production, load from backend\n      setTimeout(() => {\n        setSmsConfig({\n          apiKey: '••••••••••••••••',\n          apiUrl: 'https://api.semaphore.co',\n          isEnabled: true,\n          defaultSender: 'VCBA',\n          maxMessageLength: 160,\n          rateLimitPerMinute: 10\n        });\n        setLoading(false);\n      }, 1000);\n    } catch (err) {\n      setError('Failed to load SMS settings');\n      console.error('Error loading SMS settings:', err);\n      setLoading(false);\n    }\n  };\n  const handleSaveConfig = async () => {\n    try {\n      setSaving(true);\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement API call to save SMS settings\n      // await smsService.updateSettings(smsConfig);\n\n      // Mock save for now\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      setSuccess('SMS settings saved successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError('Failed to save SMS settings');\n      console.error('Error saving SMS settings:', err);\n    } finally {\n      setSaving(false);\n    }\n  };\n  const handleTestSMS = async () => {\n    if (!testPhone.trim() || !testMessage.trim()) {\n      setError('Please enter both phone number and message');\n      return;\n    }\n    try {\n      setTestSending(true);\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement API call to send test SMS\n      // await smsService.sendTestMessage(testPhone, testMessage);\n\n      // Mock send for now\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      setSuccess(`Test SMS sent successfully to ${testPhone}!`);\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError('Failed to send test SMS');\n      console.error('Error sending test SMS:', err);\n    } finally {\n      setTestSending(false);\n    }\n  };\n\n  // Permission check\n  if (!permissions.canManageSMSSettings) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      },\n      children: [/*#__PURE__*/_jsxDEV(MessageSquare, {\n        size: 64,\n        style: {\n          marginBottom: '1rem',\n          opacity: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          margin: '0 0 0.5rem',\n          fontSize: '1.5rem',\n          fontWeight: '600'\n        },\n        children: \"Access Denied\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: 0,\n          fontSize: '1rem'\n        },\n        children: \"You do not have permission to manage SMS settings.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        },\n        children: [\"Current Role: \", permissions.getPositionDisplayName()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '40px',\n          height: '40px',\n          border: '4px solid #f3f4f6',\n          borderTop: '4px solid #3b82f6',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this);\n  }\n  const tabs = [{\n    key: 'config',\n    label: 'Configuration',\n    icon: Settings\n  }, {\n    key: 'templates',\n    label: 'Templates',\n    icon: MessageSquare\n  }, {\n    key: 'test',\n    label: 'Test SMS',\n    icon: Send\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      maxWidth: '1200px',\n      margin: '0 auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            margin: '0 0 0.5rem',\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          },\n          children: \"SMS Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          },\n          children: \"Configure SMS notifications and messaging settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          padding: '0.75rem 1rem',\n          background: smsConfig.isEnabled ? '#dcfce7' : '#fef2f2',\n          color: smsConfig.isEnabled ? '#166534' : '#dc2626',\n          borderRadius: '8px',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Phone, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), \"SMS \", smsConfig.isEnabled ? 'Enabled' : 'Disabled']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '1rem',\n        background: '#fef2f2',\n        border: '1px solid #fecaca',\n        borderRadius: '8px',\n        color: '#dc2626',\n        marginBottom: '1rem',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n        size: 16\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 11\n      }, this), error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 9\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '1rem',\n        background: '#f0fdf4',\n        border: '1px solid #bbf7d0',\n        borderRadius: '8px',\n        color: '#166534',\n        marginBottom: '1rem',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n        size: 16\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 11\n      }, this), success]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 281,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        borderBottom: '1px solid #e5e7eb',\n        marginBottom: '2rem'\n      },\n      children: tabs.map(tab => {\n        const Icon = tab.icon;\n        return /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab(tab.key),\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            padding: '1rem 1.5rem',\n            border: 'none',\n            background: 'transparent',\n            color: activeTab === tab.key ? '#3b82f6' : '#6b7280',\n            borderBottom: activeTab === tab.key ? '2px solid #3b82f6' : '2px solid transparent',\n            cursor: 'pointer',\n            fontSize: '0.875rem',\n            fontWeight: '600',\n            transition: 'all 0.2s'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 15\n          }, this), tab.label]\n        }, tab.key, true, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 7\n    }, this), activeTab === 'config' && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '12px',\n        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n        padding: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          margin: '0 0 1.5rem',\n          fontSize: '1.25rem',\n          fontWeight: '600',\n          color: '#1f2937'\n        },\n        children: \"SMS Configuration\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n          gap: '1.5rem',\n          marginBottom: '2rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '600',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Key, {\n              size: 16,\n              style: {\n                display: 'inline',\n                marginRight: '0.5rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 17\n            }, this), \"API Key\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            value: smsConfig.apiKey,\n            onChange: e => setSmsConfig({\n              ...smsConfig,\n              apiKey: e.target.value\n            }),\n            placeholder: \"Enter your SMS API key\",\n            style: {\n              width: '100%',\n              padding: '0.75rem',\n              border: '1px solid #d1d5db',\n              borderRadius: '6px',\n              fontSize: '0.875rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '600',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Globe, {\n              size: 16,\n              style: {\n                display: 'inline',\n                marginRight: '0.5rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 17\n            }, this), \"API URL\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"url\",\n            value: smsConfig.apiUrl,\n            onChange: e => setSmsConfig({\n              ...smsConfig,\n              apiUrl: e.target.value\n            }),\n            placeholder: \"https://api.sms-provider.com\",\n            style: {\n              width: '100%',\n              padding: '0.75rem',\n              border: '1px solid #d1d5db',\n              borderRadius: '6px',\n              fontSize: '0.875rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '600',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            },\n            children: \"Default Sender ID\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: smsConfig.defaultSender,\n            onChange: e => setSmsConfig({\n              ...smsConfig,\n              defaultSender: e.target.value\n            }),\n            placeholder: \"VCBA\",\n            maxLength: 11,\n            style: {\n              width: '100%',\n              padding: '0.75rem',\n              border: '1px solid #d1d5db',\n              borderRadius: '6px',\n              fontSize: '0.875rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              fontSize: '0.875rem',\n              fontWeight: '600',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: smsConfig.isEnabled,\n              onChange: e => setSmsConfig({\n                ...smsConfig,\n                isEnabled: e.target.checked\n              }),\n              style: {\n                marginRight: '0.5rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 17\n            }, this), \"Enable SMS Notifications\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              fontSize: '0.75rem',\n              color: '#6b7280',\n              margin: 0\n            },\n            children: \"Allow the system to send SMS notifications to students and staff\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 432,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'flex-end',\n          gap: '1rem',\n          paddingTop: '1rem',\n          borderTop: '1px solid #e5e7eb'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleSaveConfig,\n          disabled: saving,\n          style: {\n            padding: '0.75rem 1.5rem',\n            background: saving ? '#9ca3af' : '#3b82f6',\n            color: 'white',\n            border: 'none',\n            borderRadius: '6px',\n            fontSize: '0.875rem',\n            fontWeight: '600',\n            cursor: saving ? 'not-allowed' : 'pointer',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Settings, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 15\n          }, this), saving ? 'Saving...' : 'Save Configuration']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 460,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 333,\n      columnNumber: 9\n    }, this), activeTab === 'test' && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '12px',\n        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n        padding: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          margin: '0 0 1.5rem',\n          fontSize: '1.25rem',\n          fontWeight: '600',\n          color: '#1f2937'\n        },\n        children: \"Test SMS Functionality\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 498,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n          gap: '1.5rem',\n          marginBottom: '2rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '600',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            },\n            children: \"Phone Number\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"tel\",\n            value: testPhone,\n            onChange: e => setTestPhone(e.target.value),\n            placeholder: \"+639123456789\",\n            style: {\n              width: '100%',\n              padding: '0.75rem',\n              border: '1px solid #d1d5db',\n              borderRadius: '6px',\n              fontSize: '0.875rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 513,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            gridColumn: '1 / -1'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '600',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            },\n            children: \"Test Message\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            value: testMessage,\n            onChange: e => setTestMessage(e.target.value),\n            placeholder: \"Enter your test message here...\",\n            rows: 4,\n            maxLength: 160,\n            style: {\n              width: '100%',\n              padding: '0.75rem',\n              border: '1px solid #d1d5db',\n              borderRadius: '6px',\n              fontSize: '0.875rem',\n              resize: 'vertical'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 548,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.75rem',\n              color: '#6b7280',\n              textAlign: 'right',\n              marginTop: '0.25rem'\n            },\n            children: [testMessage.length, \"/160 characters\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 563,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 507,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'flex-end',\n          gap: '1rem',\n          paddingTop: '1rem',\n          borderTop: '1px solid #e5e7eb'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleTestSMS,\n          disabled: testSending || !testPhone.trim() || !testMessage.trim(),\n          style: {\n            padding: '0.75rem 1.5rem',\n            background: testSending ? '#9ca3af' : '#10b981',\n            color: 'white',\n            border: 'none',\n            borderRadius: '6px',\n            fontSize: '0.875rem',\n            fontWeight: '600',\n            cursor: testSending ? 'not-allowed' : 'pointer',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Send, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 598,\n            columnNumber: 15\n          }, this), testSending ? 'Sending...' : 'Send Test SMS']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 581,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 574,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 492,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 220,\n    columnNumber: 5\n  }, this);\n};\n_s(SMSSettings, \"fmuli35a8AMmPigqwvBdrtpRQnM=\", false, function () {\n  return [useAdminAuth, usePermissions];\n});\n_c = SMSSettings;\nexport default SMSSettings;\nvar _c;\n$RefreshReg$(_c, \"SMSSettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "MessageSquare", "Send", "Settings", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CheckCircle", "Phone", "Globe", "Key", "useAdminAuth", "usePermissions", "jsxDEV", "_jsxDEV", "SMSSettings", "_s", "user", "permissions", "loading", "setLoading", "saving", "setSaving", "error", "setError", "success", "setSuccess", "activeTab", "setActiveTab", "smsConfig", "setSmsConfig", "<PERSON><PERSON><PERSON><PERSON>", "apiUrl", "isEnabled", "defaultSender", "maxMessage<PERSON><PERSON><PERSON>", "rateLimitPerMinute", "templates", "setTemplates", "id", "name", "content", "category", "testPhone", "setTestPhone", "testMessage", "setTestMessage", "testSending", "setTestSending", "canManageSMSSettings", "loadSMSSettings", "setTimeout", "err", "console", "handleSaveConfig", "Promise", "resolve", "handleTestSMS", "trim", "style", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "textAlign", "color", "children", "size", "marginBottom", "opacity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "margin", "fontSize", "fontWeight", "marginTop", "padding", "background", "getPositionBadgeColor", "borderRadius", "getPositionDisplayName", "width", "height", "border", "borderTop", "animation", "tabs", "key", "label", "icon", "max<PERSON><PERSON><PERSON>", "gap", "borderBottom", "map", "tab", "Icon", "onClick", "cursor", "transition", "boxShadow", "gridTemplateColumns", "marginRight", "type", "value", "onChange", "e", "target", "placeholder", "max<PERSON><PERSON><PERSON>", "checked", "paddingTop", "disabled", "gridColumn", "rows", "resize", "length", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/admin/SMSSettings.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { MessageSquare, Send, Settings, AlertTriangle, CheckCircle, Phone, Globe, Key } from 'lucide-react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\n\ninterface SMSConfig {\n  apiKey: string;\n  apiUrl: string;\n  isEnabled: boolean;\n  defaultSender: string;\n  maxMessageLength: number;\n  rateLimitPerMinute: number;\n}\n\ninterface SMSTemplate {\n  id: string;\n  name: string;\n  content: string;\n  category: 'announcement' | 'emergency' | 'reminder' | 'general';\n}\n\nconst SMSSettings: React.FC = () => {\n  const { user } = useAdminAuth();\n  const permissions = usePermissions(user);\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const [activeTab, setActiveTab] = useState<'config' | 'templates' | 'test'>('config');\n\n  // SMS Configuration State\n  const [smsConfig, setSmsConfig] = useState<SMSConfig>({\n    apiKey: '',\n    apiUrl: '',\n    isEnabled: false,\n    defaultSender: 'VCBA',\n    maxMessageLength: 160,\n    rateLimitPerMinute: 10\n  });\n\n  // SMS Templates State\n  const [templates, setTemplates] = useState<SMSTemplate[]>([\n    {\n      id: '1',\n      name: 'Class Cancellation',\n      content: 'NOTICE: Your {subject} class scheduled for {time} on {date} has been cancelled. Please check the bulletin board for updates.',\n      category: 'announcement'\n    },\n    {\n      id: '2',\n      name: 'Emergency Alert',\n      content: 'EMERGENCY: {message}. Please follow safety protocols and await further instructions.',\n      category: 'emergency'\n    },\n    {\n      id: '3',\n      name: 'Event Reminder',\n      content: 'REMINDER: {event_name} is scheduled for {date} at {time}. Location: {venue}. Don\\'t miss it!',\n      category: 'reminder'\n    }\n  ]);\n\n  // Test SMS State\n  const [testPhone, setTestPhone] = useState('');\n  const [testMessage, setTestMessage] = useState('This is a test message from VCBA E-Bulletin Board system.');\n  const [testSending, setTestSending] = useState(false);\n\n  useEffect(() => {\n    // Check if user has permission to manage SMS settings\n    if (!permissions.canManageSMSSettings) {\n      setError('You do not have permission to manage SMS settings');\n      setLoading(false);\n      return;\n    }\n\n    loadSMSSettings();\n  }, [permissions.canManageSMSSettings]);\n\n  const loadSMSSettings = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      // TODO: Implement API call to fetch SMS settings\n      // const response = await smsService.getSettings();\n      // setSmsConfig(response.data.config);\n      \n      // Mock data for now - in production, load from backend\n      setTimeout(() => {\n        setSmsConfig({\n          apiKey: '••••••••••••••••',\n          apiUrl: 'https://api.semaphore.co',\n          isEnabled: true,\n          defaultSender: 'VCBA',\n          maxMessageLength: 160,\n          rateLimitPerMinute: 10\n        });\n        setLoading(false);\n      }, 1000);\n      \n    } catch (err) {\n      setError('Failed to load SMS settings');\n      console.error('Error loading SMS settings:', err);\n      setLoading(false);\n    }\n  };\n\n  const handleSaveConfig = async () => {\n    try {\n      setSaving(true);\n      setError(null);\n      setSuccess(null);\n      \n      // TODO: Implement API call to save SMS settings\n      // await smsService.updateSettings(smsConfig);\n      \n      // Mock save for now\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      setSuccess('SMS settings saved successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n      \n    } catch (err) {\n      setError('Failed to save SMS settings');\n      console.error('Error saving SMS settings:', err);\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const handleTestSMS = async () => {\n    if (!testPhone.trim() || !testMessage.trim()) {\n      setError('Please enter both phone number and message');\n      return;\n    }\n\n    try {\n      setTestSending(true);\n      setError(null);\n      setSuccess(null);\n      \n      // TODO: Implement API call to send test SMS\n      // await smsService.sendTestMessage(testPhone, testMessage);\n      \n      // Mock send for now\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      setSuccess(`Test SMS sent successfully to ${testPhone}!`);\n      setTimeout(() => setSuccess(null), 3000);\n      \n    } catch (err) {\n      setError('Failed to send test SMS');\n      console.error('Error sending test SMS:', err);\n    } finally {\n      setTestSending(false);\n    }\n  };\n\n  // Permission check\n  if (!permissions.canManageSMSSettings) {\n    return (\n      <div style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      }}>\n        <MessageSquare size={64} style={{ marginBottom: '1rem', opacity: 0.5 }} />\n        <h2 style={{ margin: '0 0 0.5rem', fontSize: '1.5rem', fontWeight: '600' }}>\n          Access Denied\n        </h2>\n        <p style={{ margin: 0, fontSize: '1rem' }}>\n          You do not have permission to manage SMS settings.\n        </p>\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        }}>\n          Current Role: {permissions.getPositionDisplayName()}\n        </div>\n      </div>\n    );\n  }\n\n  if (loading) {\n    return (\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px'\n      }}>\n        <div style={{\n          width: '40px',\n          height: '40px',\n          border: '4px solid #f3f4f6',\n          borderTop: '4px solid #3b82f6',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }} />\n      </div>\n    );\n  }\n\n  const tabs = [\n    { key: 'config', label: 'Configuration', icon: Settings },\n    { key: 'templates', label: 'Templates', icon: MessageSquare },\n    { key: 'test', label: 'Test SMS', icon: Send }\n  ];\n\n  return (\n    <div style={{ maxWidth: '1200px', margin: '0 auto' }}>\n      {/* Header */}\n      <div style={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      }}>\n        <div>\n          <h1 style={{\n            margin: '0 0 0.5rem',\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          }}>\n            SMS Settings\n          </h1>\n          <p style={{\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          }}>\n            Configure SMS notifications and messaging settings\n          </p>\n        </div>\n        \n        <div style={{\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          padding: '0.75rem 1rem',\n          background: smsConfig.isEnabled ? '#dcfce7' : '#fef2f2',\n          color: smsConfig.isEnabled ? '#166534' : '#dc2626',\n          borderRadius: '8px',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        }}>\n          <Phone size={16} />\n          SMS {smsConfig.isEnabled ? 'Enabled' : 'Disabled'}\n        </div>\n      </div>\n\n      {/* Error/Success Messages */}\n      {error && (\n        <div style={{\n          padding: '1rem',\n          background: '#fef2f2',\n          border: '1px solid #fecaca',\n          borderRadius: '8px',\n          color: '#dc2626',\n          marginBottom: '1rem',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        }}>\n          <AlertTriangle size={16} />\n          {error}\n        </div>\n      )}\n\n      {success && (\n        <div style={{\n          padding: '1rem',\n          background: '#f0fdf4',\n          border: '1px solid #bbf7d0',\n          borderRadius: '8px',\n          color: '#166534',\n          marginBottom: '1rem',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        }}>\n          <CheckCircle size={16} />\n          {success}\n        </div>\n      )}\n\n      {/* Tabs */}\n      <div style={{\n        display: 'flex',\n        borderBottom: '1px solid #e5e7eb',\n        marginBottom: '2rem'\n      }}>\n        {tabs.map((tab) => {\n          const Icon = tab.icon;\n          return (\n            <button\n              key={tab.key}\n              onClick={() => setActiveTab(tab.key as any)}\n              style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                padding: '1rem 1.5rem',\n                border: 'none',\n                background: 'transparent',\n                color: activeTab === tab.key ? '#3b82f6' : '#6b7280',\n                borderBottom: activeTab === tab.key ? '2px solid #3b82f6' : '2px solid transparent',\n                cursor: 'pointer',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                transition: 'all 0.2s'\n              }}\n            >\n              <Icon size={16} />\n              {tab.label}\n            </button>\n          );\n        })}\n      </div>\n\n      {/* Tab Content */}\n      {activeTab === 'config' && (\n        <div style={{\n          background: 'white',\n          borderRadius: '12px',\n          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n          padding: '2rem'\n        }}>\n          <h3 style={{\n            margin: '0 0 1.5rem',\n            fontSize: '1.25rem',\n            fontWeight: '600',\n            color: '#1f2937'\n          }}>\n            SMS Configuration\n          </h3>\n\n          <div style={{\n            display: 'grid',\n            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n            gap: '1.5rem',\n            marginBottom: '2rem'\n          }}>\n            <div>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                <Key size={16} style={{ display: 'inline', marginRight: '0.5rem' }} />\n                API Key\n              </label>\n              <input\n                type=\"password\"\n                value={smsConfig.apiKey}\n                onChange={(e) => setSmsConfig({ ...smsConfig, apiKey: e.target.value })}\n                placeholder=\"Enter your SMS API key\"\n                style={{\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '6px',\n                  fontSize: '0.875rem'\n                }}\n              />\n            </div>\n\n            <div>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                <Globe size={16} style={{ display: 'inline', marginRight: '0.5rem' }} />\n                API URL\n              </label>\n              <input\n                type=\"url\"\n                value={smsConfig.apiUrl}\n                onChange={(e) => setSmsConfig({ ...smsConfig, apiUrl: e.target.value })}\n                placeholder=\"https://api.sms-provider.com\"\n                style={{\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '6px',\n                  fontSize: '0.875rem'\n                }}\n              />\n            </div>\n\n            <div>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                Default Sender ID\n              </label>\n              <input\n                type=\"text\"\n                value={smsConfig.defaultSender}\n                onChange={(e) => setSmsConfig({ ...smsConfig, defaultSender: e.target.value })}\n                placeholder=\"VCBA\"\n                maxLength={11}\n                style={{\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '6px',\n                  fontSize: '0.875rem'\n                }}\n              />\n            </div>\n\n            <div>\n              <label style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                <input\n                  type=\"checkbox\"\n                  checked={smsConfig.isEnabled}\n                  onChange={(e) => setSmsConfig({ ...smsConfig, isEnabled: e.target.checked })}\n                  style={{ marginRight: '0.5rem' }}\n                />\n                Enable SMS Notifications\n              </label>\n              <p style={{\n                fontSize: '0.75rem',\n                color: '#6b7280',\n                margin: 0\n              }}>\n                Allow the system to send SMS notifications to students and staff\n              </p>\n            </div>\n          </div>\n\n          <div style={{\n            display: 'flex',\n            justifyContent: 'flex-end',\n            gap: '1rem',\n            paddingTop: '1rem',\n            borderTop: '1px solid #e5e7eb'\n          }}>\n            <button\n              onClick={handleSaveConfig}\n              disabled={saving}\n              style={{\n                padding: '0.75rem 1.5rem',\n                background: saving ? '#9ca3af' : '#3b82f6',\n                color: 'white',\n                border: 'none',\n                borderRadius: '6px',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                cursor: saving ? 'not-allowed' : 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              }}\n            >\n              <Settings size={16} />\n              {saving ? 'Saving...' : 'Save Configuration'}\n            </button>\n          </div>\n        </div>\n      )}\n\n      {activeTab === 'test' && (\n        <div style={{\n          background: 'white',\n          borderRadius: '12px',\n          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n          padding: '2rem'\n        }}>\n          <h3 style={{\n            margin: '0 0 1.5rem',\n            fontSize: '1.25rem',\n            fontWeight: '600',\n            color: '#1f2937'\n          }}>\n            Test SMS Functionality\n          </h3>\n\n          <div style={{\n            display: 'grid',\n            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n            gap: '1.5rem',\n            marginBottom: '2rem'\n          }}>\n            <div>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                Phone Number\n              </label>\n              <input\n                type=\"tel\"\n                value={testPhone}\n                onChange={(e) => setTestPhone(e.target.value)}\n                placeholder=\"+639123456789\"\n                style={{\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '6px',\n                  fontSize: '0.875rem'\n                }}\n              />\n            </div>\n\n            <div style={{ gridColumn: '1 / -1' }}>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                Test Message\n              </label>\n              <textarea\n                value={testMessage}\n                onChange={(e) => setTestMessage(e.target.value)}\n                placeholder=\"Enter your test message here...\"\n                rows={4}\n                maxLength={160}\n                style={{\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '6px',\n                  fontSize: '0.875rem',\n                  resize: 'vertical'\n                }}\n              />\n              <div style={{\n                fontSize: '0.75rem',\n                color: '#6b7280',\n                textAlign: 'right',\n                marginTop: '0.25rem'\n              }}>\n                {testMessage.length}/160 characters\n              </div>\n            </div>\n          </div>\n\n          <div style={{\n            display: 'flex',\n            justifyContent: 'flex-end',\n            gap: '1rem',\n            paddingTop: '1rem',\n            borderTop: '1px solid #e5e7eb'\n          }}>\n            <button\n              onClick={handleTestSMS}\n              disabled={testSending || !testPhone.trim() || !testMessage.trim()}\n              style={{\n                padding: '0.75rem 1.5rem',\n                background: testSending ? '#9ca3af' : '#10b981',\n                color: 'white',\n                border: 'none',\n                borderRadius: '6px',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                cursor: testSending ? 'not-allowed' : 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              }}\n            >\n              <Send size={16} />\n              {testSending ? 'Sending...' : 'Send Test SMS'}\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default SMSSettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,aAAa,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,WAAW,EAAEC,KAAK,EAAEC,KAAK,EAAEC,GAAG,QAAQ,cAAc;AAC3G,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAkBzD,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM;IAAEC;EAAK,CAAC,GAAGN,YAAY,CAAC,CAAC;EAC/B,MAAMO,WAAW,GAAGN,cAAc,CAACK,IAAI,CAAC;EACxC,MAAM,CAACE,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,MAAM,EAAEC,SAAS,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAgB,IAAI,CAAC;EAC3D,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAkC,QAAQ,CAAC;;EAErF;EACA,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAY;IACpD8B,MAAM,EAAE,EAAE;IACVC,MAAM,EAAE,EAAE;IACVC,SAAS,EAAE,KAAK;IAChBC,aAAa,EAAE,MAAM;IACrBC,gBAAgB,EAAE,GAAG;IACrBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAgB,CACxD;IACEsC,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,oBAAoB;IAC1BC,OAAO,EAAE,8HAA8H;IACvIC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,iBAAiB;IACvBC,OAAO,EAAE,sFAAsF;IAC/FC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,gBAAgB;IACtBC,OAAO,EAAE,8FAA8F;IACvGC,QAAQ,EAAE;EACZ,CAAC,CACF,CAAC;;EAEF;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC4C,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAC,2DAA2D,CAAC;EAC3G,MAAM,CAAC8C,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAErDC,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACgB,WAAW,CAAC+B,oBAAoB,EAAE;MACrCzB,QAAQ,CAAC,mDAAmD,CAAC;MAC7DJ,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEA8B,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAAChC,WAAW,CAAC+B,oBAAoB,CAAC,CAAC;EAEtC,MAAMC,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF9B,UAAU,CAAC,IAAI,CAAC;MAChBI,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA;MACA;;MAEA;MACA2B,UAAU,CAAC,MAAM;QACfrB,YAAY,CAAC;UACXC,MAAM,EAAE,kBAAkB;UAC1BC,MAAM,EAAE,0BAA0B;UAClCC,SAAS,EAAE,IAAI;UACfC,aAAa,EAAE,MAAM;UACrBC,gBAAgB,EAAE,GAAG;UACrBC,kBAAkB,EAAE;QACtB,CAAC,CAAC;QACFhB,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAOgC,GAAG,EAAE;MACZ5B,QAAQ,CAAC,6BAA6B,CAAC;MACvC6B,OAAO,CAAC9B,KAAK,CAAC,6BAA6B,EAAE6B,GAAG,CAAC;MACjDhC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFhC,SAAS,CAAC,IAAI,CAAC;MACfE,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA;;MAEA;MACA,MAAM,IAAI6B,OAAO,CAACC,OAAO,IAAIL,UAAU,CAACK,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvD9B,UAAU,CAAC,kCAAkC,CAAC;MAC9CyB,UAAU,CAAC,MAAMzB,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IAE1C,CAAC,CAAC,OAAO0B,GAAG,EAAE;MACZ5B,QAAQ,CAAC,6BAA6B,CAAC;MACvC6B,OAAO,CAAC9B,KAAK,CAAC,4BAA4B,EAAE6B,GAAG,CAAC;IAClD,CAAC,SAAS;MACR9B,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAMmC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACd,SAAS,CAACe,IAAI,CAAC,CAAC,IAAI,CAACb,WAAW,CAACa,IAAI,CAAC,CAAC,EAAE;MAC5ClC,QAAQ,CAAC,4CAA4C,CAAC;MACtD;IACF;IAEA,IAAI;MACFwB,cAAc,CAAC,IAAI,CAAC;MACpBxB,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA;;MAEA;MACA,MAAM,IAAI6B,OAAO,CAACC,OAAO,IAAIL,UAAU,CAACK,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvD9B,UAAU,CAAC,iCAAiCiB,SAAS,GAAG,CAAC;MACzDQ,UAAU,CAAC,MAAMzB,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IAE1C,CAAC,CAAC,OAAO0B,GAAG,EAAE;MACZ5B,QAAQ,CAAC,yBAAyB,CAAC;MACnC6B,OAAO,CAAC9B,KAAK,CAAC,yBAAyB,EAAE6B,GAAG,CAAC;IAC/C,CAAC,SAAS;MACRJ,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,IAAI,CAAC9B,WAAW,CAAC+B,oBAAoB,EAAE;IACrC,oBACEnC,OAAA;MAAK6C,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE,OAAO;QAClBC,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACArD,OAAA,CAACX,aAAa;QAACiE,IAAI,EAAE,EAAG;QAACT,KAAK,EAAE;UAAEU,YAAY,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAI;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1E5D,OAAA;QAAI6C,KAAK,EAAE;UAAEgB,MAAM,EAAE,YAAY;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAM,CAAE;QAAAV,QAAA,EAAC;MAE5E;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL5D,OAAA;QAAG6C,KAAK,EAAE;UAAEgB,MAAM,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAT,QAAA,EAAC;MAE3C;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJ5D,OAAA;QAAK6C,KAAK,EAAE;UACVmB,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAE9D,WAAW,CAAC+D,qBAAqB,CAAC,CAAC;UAC/CC,YAAY,EAAE,KAAK;UACnBhB,KAAK,EAAE,OAAO;UACdU,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE;QACd,CAAE;QAAAV,QAAA,GAAC,gBACa,EAACjD,WAAW,CAACiE,sBAAsB,CAAC,CAAC;MAAA;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIvD,OAAO,EAAE;IACX,oBACEL,OAAA;MAAK6C,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE;MACb,CAAE;MAAAG,QAAA,eACArD,OAAA;QAAK6C,KAAK,EAAE;UACVyB,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,mBAAmB;UAC3BC,SAAS,EAAE,mBAAmB;UAC9BL,YAAY,EAAE,KAAK;UACnBM,SAAS,EAAE;QACb;MAAE;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEV;EAEA,MAAMe,IAAI,GAAG,CACX;IAAEC,GAAG,EAAE,QAAQ;IAAEC,KAAK,EAAE,eAAe;IAAEC,IAAI,EAAEvF;EAAS,CAAC,EACzD;IAAEqF,GAAG,EAAE,WAAW;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAEzF;EAAc,CAAC,EAC7D;IAAEuF,GAAG,EAAE,MAAM;IAAEC,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAExF;EAAK,CAAC,CAC/C;EAED,oBACEU,OAAA;IAAK6C,KAAK,EAAE;MAAEkC,QAAQ,EAAE,QAAQ;MAAElB,MAAM,EAAE;IAAS,CAAE;IAAAR,QAAA,gBAEnDrD,OAAA;MAAK6C,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfG,cAAc,EAAE,eAAe;QAC/BD,UAAU,EAAE,QAAQ;QACpBO,YAAY,EAAE;MAChB,CAAE;MAAAF,QAAA,gBACArD,OAAA;QAAAqD,QAAA,gBACErD,OAAA;UAAI6C,KAAK,EAAE;YACTgB,MAAM,EAAE,YAAY;YACpBC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjBX,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,EAAC;QAEH;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL5D,OAAA;UAAG6C,KAAK,EAAE;YACRgB,MAAM,EAAE,CAAC;YACTT,KAAK,EAAE,SAAS;YAChBU,QAAQ,EAAE;UACZ,CAAE;UAAAT,QAAA,EAAC;QAEH;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN5D,OAAA;QAAK6C,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBgC,GAAG,EAAE,QAAQ;UACbf,OAAO,EAAE,cAAc;UACvBC,UAAU,EAAEnD,SAAS,CAACI,SAAS,GAAG,SAAS,GAAG,SAAS;UACvDiC,KAAK,EAAErC,SAAS,CAACI,SAAS,GAAG,SAAS,GAAG,SAAS;UAClDiD,YAAY,EAAE,KAAK;UACnBN,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE;QACd,CAAE;QAAAV,QAAA,gBACArD,OAAA,CAACN,KAAK;UAAC4D,IAAI,EAAE;QAAG;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,QACf,EAAC7C,SAAS,CAACI,SAAS,GAAG,SAAS,GAAG,UAAU;MAAA;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLnD,KAAK,iBACJT,OAAA;MAAK6C,KAAK,EAAE;QACVoB,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,SAAS;QACrBM,MAAM,EAAE,mBAAmB;QAC3BJ,YAAY,EAAE,KAAK;QACnBhB,KAAK,EAAE,SAAS;QAChBG,YAAY,EAAE,MAAM;QACpBT,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBgC,GAAG,EAAE;MACP,CAAE;MAAA3B,QAAA,gBACArD,OAAA,CAACR,aAAa;QAAC8D,IAAI,EAAE;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAC1BnD,KAAK;IAAA;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEAjD,OAAO,iBACNX,OAAA;MAAK6C,KAAK,EAAE;QACVoB,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,SAAS;QACrBM,MAAM,EAAE,mBAAmB;QAC3BJ,YAAY,EAAE,KAAK;QACnBhB,KAAK,EAAE,SAAS;QAChBG,YAAY,EAAE,MAAM;QACpBT,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBgC,GAAG,EAAE;MACP,CAAE;MAAA3B,QAAA,gBACArD,OAAA,CAACP,WAAW;QAAC6D,IAAI,EAAE;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACxBjD,OAAO;IAAA;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,eAGD5D,OAAA;MAAK6C,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfmC,YAAY,EAAE,mBAAmB;QACjC1B,YAAY,EAAE;MAChB,CAAE;MAAAF,QAAA,EACCsB,IAAI,CAACO,GAAG,CAAEC,GAAG,IAAK;QACjB,MAAMC,IAAI,GAAGD,GAAG,CAACL,IAAI;QACrB,oBACE9E,OAAA;UAEEqF,OAAO,EAAEA,CAAA,KAAMvE,YAAY,CAACqE,GAAG,CAACP,GAAU,CAAE;UAC5C/B,KAAK,EAAE;YACLC,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBgC,GAAG,EAAE,QAAQ;YACbf,OAAO,EAAE,aAAa;YACtBO,MAAM,EAAE,MAAM;YACdN,UAAU,EAAE,aAAa;YACzBd,KAAK,EAAEvC,SAAS,KAAKsE,GAAG,CAACP,GAAG,GAAG,SAAS,GAAG,SAAS;YACpDK,YAAY,EAAEpE,SAAS,KAAKsE,GAAG,CAACP,GAAG,GAAG,mBAAmB,GAAG,uBAAuB;YACnFU,MAAM,EAAE,SAAS;YACjBxB,QAAQ,EAAE,UAAU;YACpBC,UAAU,EAAE,KAAK;YACjBwB,UAAU,EAAE;UACd,CAAE;UAAAlC,QAAA,gBAEFrD,OAAA,CAACoF,IAAI;YAAC9B,IAAI,EAAE;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACjBuB,GAAG,CAACN,KAAK;QAAA,GAlBLM,GAAG,CAACP,GAAG;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBN,CAAC;MAEb,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGL/C,SAAS,KAAK,QAAQ,iBACrBb,OAAA;MAAK6C,KAAK,EAAE;QACVqB,UAAU,EAAE,OAAO;QACnBE,YAAY,EAAE,MAAM;QACpBoB,SAAS,EAAE,8BAA8B;QACzCvB,OAAO,EAAE;MACX,CAAE;MAAAZ,QAAA,gBACArD,OAAA;QAAI6C,KAAK,EAAE;UACTgB,MAAM,EAAE,YAAY;UACpBC,QAAQ,EAAE,SAAS;UACnBC,UAAU,EAAE,KAAK;UACjBX,KAAK,EAAE;QACT,CAAE;QAAAC,QAAA,EAAC;MAEH;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEL5D,OAAA;QAAK6C,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACf2C,mBAAmB,EAAE,sCAAsC;UAC3DT,GAAG,EAAE,QAAQ;UACbzB,YAAY,EAAE;QAChB,CAAE;QAAAF,QAAA,gBACArD,OAAA;UAAAqD,QAAA,gBACErD,OAAA;YAAO6C,KAAK,EAAE;cACZC,OAAO,EAAE,OAAO;cAChBgB,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBX,KAAK,EAAE,SAAS;cAChBG,YAAY,EAAE;YAChB,CAAE;YAAAF,QAAA,gBACArD,OAAA,CAACJ,GAAG;cAAC0D,IAAI,EAAE,EAAG;cAACT,KAAK,EAAE;gBAAEC,OAAO,EAAE,QAAQ;gBAAE4C,WAAW,EAAE;cAAS;YAAE;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,WAExE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR5D,OAAA;YACE2F,IAAI,EAAC,UAAU;YACfC,KAAK,EAAE7E,SAAS,CAACE,MAAO;YACxB4E,QAAQ,EAAGC,CAAC,IAAK9E,YAAY,CAAC;cAAE,GAAGD,SAAS;cAAEE,MAAM,EAAE6E,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YACxEI,WAAW,EAAC,wBAAwB;YACpCnD,KAAK,EAAE;cACLyB,KAAK,EAAE,MAAM;cACbL,OAAO,EAAE,SAAS;cAClBO,MAAM,EAAE,mBAAmB;cAC3BJ,YAAY,EAAE,KAAK;cACnBN,QAAQ,EAAE;YACZ;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN5D,OAAA;UAAAqD,QAAA,gBACErD,OAAA;YAAO6C,KAAK,EAAE;cACZC,OAAO,EAAE,OAAO;cAChBgB,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBX,KAAK,EAAE,SAAS;cAChBG,YAAY,EAAE;YAChB,CAAE;YAAAF,QAAA,gBACArD,OAAA,CAACL,KAAK;cAAC2D,IAAI,EAAE,EAAG;cAACT,KAAK,EAAE;gBAAEC,OAAO,EAAE,QAAQ;gBAAE4C,WAAW,EAAE;cAAS;YAAE;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,WAE1E;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR5D,OAAA;YACE2F,IAAI,EAAC,KAAK;YACVC,KAAK,EAAE7E,SAAS,CAACG,MAAO;YACxB2E,QAAQ,EAAGC,CAAC,IAAK9E,YAAY,CAAC;cAAE,GAAGD,SAAS;cAAEG,MAAM,EAAE4E,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YACxEI,WAAW,EAAC,8BAA8B;YAC1CnD,KAAK,EAAE;cACLyB,KAAK,EAAE,MAAM;cACbL,OAAO,EAAE,SAAS;cAClBO,MAAM,EAAE,mBAAmB;cAC3BJ,YAAY,EAAE,KAAK;cACnBN,QAAQ,EAAE;YACZ;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN5D,OAAA;UAAAqD,QAAA,gBACErD,OAAA;YAAO6C,KAAK,EAAE;cACZC,OAAO,EAAE,OAAO;cAChBgB,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBX,KAAK,EAAE,SAAS;cAChBG,YAAY,EAAE;YAChB,CAAE;YAAAF,QAAA,EAAC;UAEH;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR5D,OAAA;YACE2F,IAAI,EAAC,MAAM;YACXC,KAAK,EAAE7E,SAAS,CAACK,aAAc;YAC/ByE,QAAQ,EAAGC,CAAC,IAAK9E,YAAY,CAAC;cAAE,GAAGD,SAAS;cAAEK,aAAa,EAAE0E,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAC/EI,WAAW,EAAC,MAAM;YAClBC,SAAS,EAAE,EAAG;YACdpD,KAAK,EAAE;cACLyB,KAAK,EAAE,MAAM;cACbL,OAAO,EAAE,SAAS;cAClBO,MAAM,EAAE,mBAAmB;cAC3BJ,YAAY,EAAE,KAAK;cACnBN,QAAQ,EAAE;YACZ;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN5D,OAAA;UAAAqD,QAAA,gBACErD,OAAA;YAAO6C,KAAK,EAAE;cACZC,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBgC,GAAG,EAAE,QAAQ;cACblB,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBX,KAAK,EAAE,SAAS;cAChBG,YAAY,EAAE;YAChB,CAAE;YAAAF,QAAA,gBACArD,OAAA;cACE2F,IAAI,EAAC,UAAU;cACfO,OAAO,EAAEnF,SAAS,CAACI,SAAU;cAC7B0E,QAAQ,EAAGC,CAAC,IAAK9E,YAAY,CAAC;gBAAE,GAAGD,SAAS;gBAAEI,SAAS,EAAE2E,CAAC,CAACC,MAAM,CAACG;cAAQ,CAAC,CAAE;cAC7ErD,KAAK,EAAE;gBAAE6C,WAAW,EAAE;cAAS;YAAE;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,4BAEJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR5D,OAAA;YAAG6C,KAAK,EAAE;cACRiB,QAAQ,EAAE,SAAS;cACnBV,KAAK,EAAE,SAAS;cAChBS,MAAM,EAAE;YACV,CAAE;YAAAR,QAAA,EAAC;UAEH;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN5D,OAAA;QAAK6C,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACfG,cAAc,EAAE,UAAU;UAC1B+B,GAAG,EAAE,MAAM;UACXmB,UAAU,EAAE,MAAM;UAClB1B,SAAS,EAAE;QACb,CAAE;QAAApB,QAAA,eACArD,OAAA;UACEqF,OAAO,EAAE7C,gBAAiB;UAC1B4D,QAAQ,EAAE7F,MAAO;UACjBsC,KAAK,EAAE;YACLoB,OAAO,EAAE,gBAAgB;YACzBC,UAAU,EAAE3D,MAAM,GAAG,SAAS,GAAG,SAAS;YAC1C6C,KAAK,EAAE,OAAO;YACdoB,MAAM,EAAE,MAAM;YACdJ,YAAY,EAAE,KAAK;YACnBN,QAAQ,EAAE,UAAU;YACpBC,UAAU,EAAE,KAAK;YACjBuB,MAAM,EAAE/E,MAAM,GAAG,aAAa,GAAG,SAAS;YAC1CuC,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBgC,GAAG,EAAE;UACP,CAAE;UAAA3B,QAAA,gBAEFrD,OAAA,CAACT,QAAQ;YAAC+D,IAAI,EAAE;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACrBrD,MAAM,GAAG,WAAW,GAAG,oBAAoB;QAAA;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEA/C,SAAS,KAAK,MAAM,iBACnBb,OAAA;MAAK6C,KAAK,EAAE;QACVqB,UAAU,EAAE,OAAO;QACnBE,YAAY,EAAE,MAAM;QACpBoB,SAAS,EAAE,8BAA8B;QACzCvB,OAAO,EAAE;MACX,CAAE;MAAAZ,QAAA,gBACArD,OAAA;QAAI6C,KAAK,EAAE;UACTgB,MAAM,EAAE,YAAY;UACpBC,QAAQ,EAAE,SAAS;UACnBC,UAAU,EAAE,KAAK;UACjBX,KAAK,EAAE;QACT,CAAE;QAAAC,QAAA,EAAC;MAEH;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEL5D,OAAA;QAAK6C,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACf2C,mBAAmB,EAAE,sCAAsC;UAC3DT,GAAG,EAAE,QAAQ;UACbzB,YAAY,EAAE;QAChB,CAAE;QAAAF,QAAA,gBACArD,OAAA;UAAAqD,QAAA,gBACErD,OAAA;YAAO6C,KAAK,EAAE;cACZC,OAAO,EAAE,OAAO;cAChBgB,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBX,KAAK,EAAE,SAAS;cAChBG,YAAY,EAAE;YAChB,CAAE;YAAAF,QAAA,EAAC;UAEH;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR5D,OAAA;YACE2F,IAAI,EAAC,KAAK;YACVC,KAAK,EAAE/D,SAAU;YACjBgE,QAAQ,EAAGC,CAAC,IAAKhE,YAAY,CAACgE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC9CI,WAAW,EAAC,eAAe;YAC3BnD,KAAK,EAAE;cACLyB,KAAK,EAAE,MAAM;cACbL,OAAO,EAAE,SAAS;cAClBO,MAAM,EAAE,mBAAmB;cAC3BJ,YAAY,EAAE,KAAK;cACnBN,QAAQ,EAAE;YACZ;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN5D,OAAA;UAAK6C,KAAK,EAAE;YAAEwD,UAAU,EAAE;UAAS,CAAE;UAAAhD,QAAA,gBACnCrD,OAAA;YAAO6C,KAAK,EAAE;cACZC,OAAO,EAAE,OAAO;cAChBgB,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBX,KAAK,EAAE,SAAS;cAChBG,YAAY,EAAE;YAChB,CAAE;YAAAF,QAAA,EAAC;UAEH;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR5D,OAAA;YACE4F,KAAK,EAAE7D,WAAY;YACnB8D,QAAQ,EAAGC,CAAC,IAAK9D,cAAc,CAAC8D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAChDI,WAAW,EAAC,iCAAiC;YAC7CM,IAAI,EAAE,CAAE;YACRL,SAAS,EAAE,GAAI;YACfpD,KAAK,EAAE;cACLyB,KAAK,EAAE,MAAM;cACbL,OAAO,EAAE,SAAS;cAClBO,MAAM,EAAE,mBAAmB;cAC3BJ,YAAY,EAAE,KAAK;cACnBN,QAAQ,EAAE,UAAU;cACpByC,MAAM,EAAE;YACV;UAAE;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACF5D,OAAA;YAAK6C,KAAK,EAAE;cACViB,QAAQ,EAAE,SAAS;cACnBV,KAAK,EAAE,SAAS;cAChBD,SAAS,EAAE,OAAO;cAClBa,SAAS,EAAE;YACb,CAAE;YAAAX,QAAA,GACCtB,WAAW,CAACyE,MAAM,EAAC,iBACtB;UAAA;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN5D,OAAA;QAAK6C,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACfG,cAAc,EAAE,UAAU;UAC1B+B,GAAG,EAAE,MAAM;UACXmB,UAAU,EAAE,MAAM;UAClB1B,SAAS,EAAE;QACb,CAAE;QAAApB,QAAA,eACArD,OAAA;UACEqF,OAAO,EAAE1C,aAAc;UACvByD,QAAQ,EAAEnE,WAAW,IAAI,CAACJ,SAAS,CAACe,IAAI,CAAC,CAAC,IAAI,CAACb,WAAW,CAACa,IAAI,CAAC,CAAE;UAClEC,KAAK,EAAE;YACLoB,OAAO,EAAE,gBAAgB;YACzBC,UAAU,EAAEjC,WAAW,GAAG,SAAS,GAAG,SAAS;YAC/CmB,KAAK,EAAE,OAAO;YACdoB,MAAM,EAAE,MAAM;YACdJ,YAAY,EAAE,KAAK;YACnBN,QAAQ,EAAE,UAAU;YACpBC,UAAU,EAAE,KAAK;YACjBuB,MAAM,EAAErD,WAAW,GAAG,aAAa,GAAG,SAAS;YAC/Ca,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBgC,GAAG,EAAE;UACP,CAAE;UAAA3B,QAAA,gBAEFrD,OAAA,CAACV,IAAI;YAACgE,IAAI,EAAE;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACjB3B,WAAW,GAAG,YAAY,GAAG,eAAe;QAAA;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC1D,EAAA,CAxkBID,WAAqB;EAAA,QACRJ,YAAY,EACTC,cAAc;AAAA;AAAA2G,EAAA,GAF9BxG,WAAqB;AA0kB3B,eAAeA,WAAW;AAAC,IAAAwG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}