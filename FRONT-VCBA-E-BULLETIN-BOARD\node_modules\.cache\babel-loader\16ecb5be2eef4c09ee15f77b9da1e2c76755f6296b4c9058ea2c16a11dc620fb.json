{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';\nimport { AdminAuthProvider } from './contexts/AdminAuthContext';\nimport { StudentAuthProvider } from './contexts/StudentAuthContext';\nimport { ToastProvider } from './contexts/ToastContext';\nimport { ProtectedRoute, PublicRoute } from './components/common';\nimport { ErrorBoundary } from './components/ErrorBoundary';\nimport './styles/commentDepth.css';\nimport { AdminLogin, StudentLogin, AdminRegister } from './pages';\nimport WelcomePage from './pages/WelcomePage';\nimport AdminLayout from './components/admin/layout/AdminLayout';\nimport AdminDashboard from './pages/admin/AdminDashboard';\nimport NewsFeed from './components/common/NewsFeed';\nimport Calendar from './pages/admin/Calendar';\nimport PostManagement from './pages/admin/PostManagement';\nimport StudentManagement from './pages/admin/StudentManagement';\nimport Archive from './pages/admin/Archive';\nimport Settings from './pages/admin/Settings';\nimport ApiTest from './pages/debug/ApiTest';\nimport CategoryManagement from './pages/admin/CategoryManagement';\nimport AdminManagement from './pages/admin/AdminManagement';\n// Removed unused student pages - using unified NewsFeed and Profile Settings modal\n// import StudentDashboard from './pages/student/StudentDashboard'; // REMOVED\n// import StudentNewsfeed from './pages/student/StudentNewsfeed'; // Now using unified NewsFeed\n// import StudentSettings from './pages/student/StudentSettings'; // REMOVED\nimport TVDisplay from './pages/tv/TVDisplay';\nimport TVControlPanel from './components/admin/tv-control/TVControlPanel';\nimport './App.css';\n\n// Smart redirect component that determines the appropriate login page based on the current path\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SmartRedirect = () => {\n  _s();\n  const location = useLocation();\n\n  // If the path starts with /student, redirect to student login\n  if (location.pathname.startsWith('/student')) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/student/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Default to admin login for all other paths\n  return /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/admin/login\",\n    replace: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 10\n  }, this);\n};\n\n// Admin Routes Component with isolated auth context\n_s(SmartRedirect, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c = SmartRedirect;\nconst AdminRoutes = () => /*#__PURE__*/_jsxDEV(AdminAuthProvider, {\n  children: /*#__PURE__*/_jsxDEV(Routes, {\n    children: [/*#__PURE__*/_jsxDEV(Route, {\n      path: \"/login\",\n      element: /*#__PURE__*/_jsxDEV(PublicRoute, {\n        restricted: true,\n        children: /*#__PURE__*/_jsxDEV(AdminLogin, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/register\",\n      element: /*#__PURE__*/_jsxDEV(PublicRoute, {\n        restricted: true,\n        children: /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n          children: /*#__PURE__*/_jsxDEV(AdminRegister, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/dashboard\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/_jsxDEV(AdminLayout, {\n          children: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/newsfeed\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/_jsxDEV(NewsFeed, {\n          userRole: \"admin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/calendar\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/_jsxDEV(AdminLayout, {\n          children: /*#__PURE__*/_jsxDEV(Calendar, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/posts\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/_jsxDEV(AdminLayout, {\n          children: /*#__PURE__*/_jsxDEV(PostManagement, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/student-management\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/_jsxDEV(AdminLayout, {\n          children: /*#__PURE__*/_jsxDEV(StudentManagement, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/categories\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/_jsxDEV(AdminLayout, {\n          children: /*#__PURE__*/_jsxDEV(CategoryManagement, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/admin-management\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/_jsxDEV(AdminLayout, {\n          children: /*#__PURE__*/_jsxDEV(AdminManagement, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/archive\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/_jsxDEV(AdminLayout, {\n          children: /*#__PURE__*/_jsxDEV(Archive, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/tv-control\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/_jsxDEV(AdminLayout, {\n          children: /*#__PURE__*/_jsxDEV(TVControlPanel, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/settings\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/_jsxDEV(AdminLayout, {\n          children: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/debug\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/_jsxDEV(ApiTest, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/admin/dashboard\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 47,\n  columnNumber: 3\n}, this);\n\n// Student Routes Component with isolated auth context\n_c2 = AdminRoutes;\nconst StudentRoutes = () => /*#__PURE__*/_jsxDEV(StudentAuthProvider, {\n  children: /*#__PURE__*/_jsxDEV(Routes, {\n    children: [/*#__PURE__*/_jsxDEV(Route, {\n      path: \"/login\",\n      element: /*#__PURE__*/_jsxDEV(PublicRoute, {\n        restricted: true,\n        children: /*#__PURE__*/_jsxDEV(StudentLogin, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/newsfeed\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"student\",\n        children: /*#__PURE__*/_jsxDEV(NewsFeed, {\n          userRole: \"student\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"student\",\n        children: /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/student/newsfeed\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 192,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 191,\n  columnNumber: 3\n}, this);\n_c3 = StudentRoutes;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ToastProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"App\",\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(WelcomePage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/tv-display\",\n            element: /*#__PURE__*/_jsxDEV(TVDisplay, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 48\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/*\",\n            element: /*#__PURE__*/_jsxDEV(AdminRoutes, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 45\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/student/*\",\n            element: /*#__PURE__*/_jsxDEV(StudentRoutes, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 47\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"*\",\n            element: /*#__PURE__*/_jsxDEV(SmartRedirect, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 226,\n    columnNumber: 5\n  }, this);\n}\n_c4 = App;\nexport default App;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"SmartRedirect\");\n$RefreshReg$(_c2, \"AdminRoutes\");\n$RefreshReg$(_c3, \"StudentRoutes\");\n$RefreshReg$(_c4, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "useLocation", "AdminAuth<PERSON><PERSON><PERSON>", "StudentAuthProvider", "ToastProvider", "ProtectedRoute", "PublicRoute", "Error<PERSON>ou<PERSON><PERSON>", "AdminLogin", "StudentLogin", "AdminRegister", "WelcomePage", "AdminLayout", "AdminDashboard", "NewsFeed", "Calendar", "PostManagement", "StudentManagement", "Archive", "Settings", "ApiTest", "CategoryManagement", "AdminManagement", "TVDisplay", "TVControlPanel", "jsxDEV", "_jsxDEV", "SmartRedirect", "_s", "location", "pathname", "startsWith", "to", "replace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "AdminRoutes", "children", "path", "element", "restricted", "requiredRole", "userRole", "_c2", "StudentRoutes", "_c3", "App", "className", "_c4", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';\nimport { AuthProvider } from './contexts';\nimport { AdminAuthProvider } from './contexts/AdminAuthContext';\nimport { StudentAuthProvider } from './contexts/StudentAuthContext';\nimport { ToastProvider } from './contexts/ToastContext';\nimport { ProtectedRoute, PublicRoute } from './components/common';\nimport { ErrorBoundary } from './components/ErrorBoundary';\nimport './styles/commentDepth.css';\nimport { AdminLogin, StudentLogin, AdminRegister } from './pages';\nimport WelcomePage from './pages/WelcomePage';\nimport AdminLayout from './components/admin/layout/AdminLayout';\nimport AdminDashboard from './pages/admin/AdminDashboard';\nimport NewsFeed from './components/common/NewsFeed';\nimport Calendar from './pages/admin/Calendar';\nimport PostManagement from './pages/admin/PostManagement';\nimport StudentManagement from './pages/admin/StudentManagement';\nimport Archive from './pages/admin/Archive';\nimport Settings from './pages/admin/Settings';\nimport ApiTest from './pages/debug/ApiTest';\nimport CategoryManagement from './pages/admin/CategoryManagement';\nimport AdminManagement from './pages/admin/AdminManagement';\nimport StudentLayout from './components/student/layout/StudentLayout';\n// Removed unused student pages - using unified NewsFeed and Profile Settings modal\n// import StudentDashboard from './pages/student/StudentDashboard'; // REMOVED\n// import StudentNewsfeed from './pages/student/StudentNewsfeed'; // Now using unified NewsFeed\n// import StudentSettings from './pages/student/StudentSettings'; // REMOVED\nimport TVDisplay from './pages/tv/TVDisplay';\nimport TVControlPanel from './components/admin/tv-control/TVControlPanel';\nimport './App.css';\n\n// Smart redirect component that determines the appropriate login page based on the current path\nconst SmartRedirect: React.FC = () => {\n  const location = useLocation();\n\n  // If the path starts with /student, redirect to student login\n  if (location.pathname.startsWith('/student')) {\n    return <Navigate to=\"/student/login\" replace />;\n  }\n\n  // Default to admin login for all other paths\n  return <Navigate to=\"/admin/login\" replace />;\n};\n\n// Admin Routes Component with isolated auth context\nconst AdminRoutes: React.FC = () => (\n  <AdminAuthProvider>\n    <Routes>\n      {/* Admin public routes */}\n      <Route\n        path=\"/login\"\n        element={\n          <PublicRoute restricted>\n            <AdminLogin />\n          </PublicRoute>\n        }\n      />\n\n      <Route\n        path=\"/register\"\n        element={\n          <PublicRoute restricted>\n            <ErrorBoundary>\n              <AdminRegister />\n            </ErrorBoundary>\n          </PublicRoute>\n        }\n      />\n\n      {/* Admin protected routes with layout */}\n      <Route\n        path=\"/dashboard\"\n        element={\n          <ProtectedRoute requiredRole=\"admin\">\n            <AdminLayout>\n              <AdminDashboard />\n            </AdminLayout>\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/newsfeed\"\n        element={\n          <ProtectedRoute requiredRole=\"admin\">\n            <NewsFeed userRole=\"admin\" />\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/calendar\"\n        element={\n          <ProtectedRoute requiredRole=\"admin\">\n            <AdminLayout>\n              <Calendar />\n            </AdminLayout>\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/posts\"\n        element={\n          <ProtectedRoute requiredRole=\"admin\">\n            <AdminLayout>\n              <PostManagement />\n            </AdminLayout>\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/student-management\"\n        element={\n          <ProtectedRoute requiredRole=\"admin\">\n            <AdminLayout>\n              <StudentManagement />\n            </AdminLayout>\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/categories\"\n        element={\n          <ProtectedRoute requiredRole=\"admin\">\n            <AdminLayout>\n              <CategoryManagement />\n            </AdminLayout>\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/admin-management\"\n        element={\n          <ProtectedRoute requiredRole=\"admin\">\n            <AdminLayout>\n              <AdminManagement />\n            </AdminLayout>\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/archive\"\n        element={\n          <ProtectedRoute requiredRole=\"admin\">\n            <AdminLayout>\n              <Archive />\n            </AdminLayout>\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/tv-control\"\n        element={\n          <ProtectedRoute requiredRole=\"admin\">\n            <AdminLayout>\n              <TVControlPanel />\n            </AdminLayout>\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/settings\"\n        element={\n          <ProtectedRoute requiredRole=\"admin\">\n            <AdminLayout>\n              <Settings />\n            </AdminLayout>\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/debug\"\n        element={\n          <ProtectedRoute requiredRole=\"admin\">\n            <ApiTest />\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/\"\n        element={\n          <ProtectedRoute requiredRole=\"admin\">\n            <Navigate to=\"/admin/dashboard\" replace />\n          </ProtectedRoute>\n        }\n      />\n    </Routes>\n  </AdminAuthProvider>\n);\n\n// Student Routes Component with isolated auth context\nconst StudentRoutes: React.FC = () => (\n  <StudentAuthProvider>\n    <Routes>\n      {/* Student public routes */}\n      <Route\n        path=\"/login\"\n        element={\n          <PublicRoute restricted>\n            <StudentLogin />\n          </PublicRoute>\n        }\n      />\n\n      {/* Student protected routes - using unified NewsFeed only */}\n      <Route\n        path=\"/newsfeed\"\n        element={\n          <ProtectedRoute requiredRole=\"student\">\n            <NewsFeed userRole=\"student\" />\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/\"\n        element={\n          <ProtectedRoute requiredRole=\"student\">\n            <Navigate to=\"/student/newsfeed\" replace />\n          </ProtectedRoute>\n        }\n      />\n    </Routes>\n  </StudentAuthProvider>\n);\n\nfunction App() {\n  return (\n    <ToastProvider>\n      <Router>\n        <div className=\"App\">\n          <Routes>\n            {/* Welcome page as default route */}\n            <Route path=\"/\" element={<WelcomePage />} />\n\n            {/* TV Display route - no authentication required */}\n            <Route path=\"/tv-display\" element={<TVDisplay />} />\n\n            {/* Admin routes with isolated auth context */}\n            <Route path=\"/admin/*\" element={<AdminRoutes />} />\n\n            {/* Student routes with isolated auth context */}\n            <Route path=\"/student/*\" element={<StudentRoutes />} />\n\n            {/* Catch all route - smart redirect based on path */}\n            <Route path=\"*\" element={<SmartRedirect />} />\n          </Routes>\n        </div>\n      </Router>\n    </ToastProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AAEhG,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,mBAAmB,QAAQ,+BAA+B;AACnE,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,cAAc,EAAEC,WAAW,QAAQ,qBAAqB;AACjE,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,OAAO,2BAA2B;AAClC,SAASC,UAAU,EAAEC,YAAY,EAAEC,aAAa,QAAQ,SAAS;AACjE,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,WAAW,MAAM,uCAAuC;AAC/D,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,QAAQ,MAAM,8BAA8B;AACnD,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,kBAAkB,MAAM,kCAAkC;AACjE,OAAOC,eAAe,MAAM,+BAA+B;AAE3D;AACA;AACA;AACA;AACA,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,cAAc,MAAM,8CAA8C;AACzE,OAAO,WAAW;;AAElB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAMC,QAAQ,GAAG5B,WAAW,CAAC,CAAC;;EAE9B;EACA,IAAI4B,QAAQ,CAACC,QAAQ,CAACC,UAAU,CAAC,UAAU,CAAC,EAAE;IAC5C,oBAAOL,OAAA,CAAC1B,QAAQ;MAACgC,EAAE,EAAC,gBAAgB;MAACC,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACjD;;EAEA;EACA,oBAAOX,OAAA,CAAC1B,QAAQ;IAACgC,EAAE,EAAC,cAAc;IAACC,OAAO;EAAA;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC/C,CAAC;;AAED;AAAAT,EAAA,CAZMD,aAAuB;EAAA,QACV1B,WAAW;AAAA;AAAAqC,EAAA,GADxBX,aAAuB;AAa7B,MAAMY,WAAqB,GAAGA,CAAA,kBAC5Bb,OAAA,CAACxB,iBAAiB;EAAAsC,QAAA,eAChBd,OAAA,CAAC5B,MAAM;IAAA0C,QAAA,gBAELd,OAAA,CAAC3B,KAAK;MACJ0C,IAAI,EAAC,QAAQ;MACbC,OAAO,eACLhB,OAAA,CAACpB,WAAW;QAACqC,UAAU;QAAAH,QAAA,eACrBd,OAAA,CAAClB,UAAU;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACd;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEFX,OAAA,CAAC3B,KAAK;MACJ0C,IAAI,EAAC,WAAW;MAChBC,OAAO,eACLhB,OAAA,CAACpB,WAAW;QAACqC,UAAU;QAAAH,QAAA,eACrBd,OAAA,CAACnB,aAAa;UAAAiC,QAAA,eACZd,OAAA,CAAChB,aAAa;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IACd;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGFX,OAAA,CAAC3B,KAAK;MACJ0C,IAAI,EAAC,YAAY;MACjBC,OAAO,eACLhB,OAAA,CAACrB,cAAc;QAACuC,YAAY,EAAC,OAAO;QAAAJ,QAAA,eAClCd,OAAA,CAACd,WAAW;UAAA4B,QAAA,eACVd,OAAA,CAACb,cAAc;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAAC3B,KAAK;MACJ0C,IAAI,EAAC,WAAW;MAChBC,OAAO,eACLhB,OAAA,CAACrB,cAAc;QAACuC,YAAY,EAAC,OAAO;QAAAJ,QAAA,eAClCd,OAAA,CAACZ,QAAQ;UAAC+B,QAAQ,EAAC;QAAO;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAAC3B,KAAK;MACJ0C,IAAI,EAAC,WAAW;MAChBC,OAAO,eACLhB,OAAA,CAACrB,cAAc;QAACuC,YAAY,EAAC,OAAO;QAAAJ,QAAA,eAClCd,OAAA,CAACd,WAAW;UAAA4B,QAAA,eACVd,OAAA,CAACX,QAAQ;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAAC3B,KAAK;MACJ0C,IAAI,EAAC,QAAQ;MACbC,OAAO,eACLhB,OAAA,CAACrB,cAAc;QAACuC,YAAY,EAAC,OAAO;QAAAJ,QAAA,eAClCd,OAAA,CAACd,WAAW;UAAA4B,QAAA,eACVd,OAAA,CAACV,cAAc;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAAC3B,KAAK;MACJ0C,IAAI,EAAC,qBAAqB;MAC1BC,OAAO,eACLhB,OAAA,CAACrB,cAAc;QAACuC,YAAY,EAAC,OAAO;QAAAJ,QAAA,eAClCd,OAAA,CAACd,WAAW;UAAA4B,QAAA,eACVd,OAAA,CAACT,iBAAiB;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAAC3B,KAAK;MACJ0C,IAAI,EAAC,aAAa;MAClBC,OAAO,eACLhB,OAAA,CAACrB,cAAc;QAACuC,YAAY,EAAC,OAAO;QAAAJ,QAAA,eAClCd,OAAA,CAACd,WAAW;UAAA4B,QAAA,eACVd,OAAA,CAACL,kBAAkB;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAAC3B,KAAK;MACJ0C,IAAI,EAAC,mBAAmB;MACxBC,OAAO,eACLhB,OAAA,CAACrB,cAAc;QAACuC,YAAY,EAAC,OAAO;QAAAJ,QAAA,eAClCd,OAAA,CAACd,WAAW;UAAA4B,QAAA,eACVd,OAAA,CAACJ,eAAe;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAAC3B,KAAK;MACJ0C,IAAI,EAAC,UAAU;MACfC,OAAO,eACLhB,OAAA,CAACrB,cAAc;QAACuC,YAAY,EAAC,OAAO;QAAAJ,QAAA,eAClCd,OAAA,CAACd,WAAW;UAAA4B,QAAA,eACVd,OAAA,CAACR,OAAO;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAAC3B,KAAK;MACJ0C,IAAI,EAAC,aAAa;MAClBC,OAAO,eACLhB,OAAA,CAACrB,cAAc;QAACuC,YAAY,EAAC,OAAO;QAAAJ,QAAA,eAClCd,OAAA,CAACd,WAAW;UAAA4B,QAAA,eACVd,OAAA,CAACF,cAAc;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAAC3B,KAAK;MACJ0C,IAAI,EAAC,WAAW;MAChBC,OAAO,eACLhB,OAAA,CAACrB,cAAc;QAACuC,YAAY,EAAC,OAAO;QAAAJ,QAAA,eAClCd,OAAA,CAACd,WAAW;UAAA4B,QAAA,eACVd,OAAA,CAACP,QAAQ;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAAC3B,KAAK;MACJ0C,IAAI,EAAC,QAAQ;MACbC,OAAO,eACLhB,OAAA,CAACrB,cAAc;QAACuC,YAAY,EAAC,OAAO;QAAAJ,QAAA,eAClCd,OAAA,CAACN,OAAO;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAAC3B,KAAK;MACJ0C,IAAI,EAAC,GAAG;MACRC,OAAO,eACLhB,OAAA,CAACrB,cAAc;QAACuC,YAAY,EAAC,OAAO;QAAAJ,QAAA,eAClCd,OAAA,CAAC1B,QAAQ;UAACgC,EAAE,EAAC,kBAAkB;UAACC,OAAO;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACQ,CACpB;;AAED;AAAAS,GAAA,GA/IMP,WAAqB;AAgJ3B,MAAMQ,aAAuB,GAAGA,CAAA,kBAC9BrB,OAAA,CAACvB,mBAAmB;EAAAqC,QAAA,eAClBd,OAAA,CAAC5B,MAAM;IAAA0C,QAAA,gBAELd,OAAA,CAAC3B,KAAK;MACJ0C,IAAI,EAAC,QAAQ;MACbC,OAAO,eACLhB,OAAA,CAACpB,WAAW;QAACqC,UAAU;QAAAH,QAAA,eACrBd,OAAA,CAACjB,YAAY;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IACd;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGFX,OAAA,CAAC3B,KAAK;MACJ0C,IAAI,EAAC,WAAW;MAChBC,OAAO,eACLhB,OAAA,CAACrB,cAAc;QAACuC,YAAY,EAAC,SAAS;QAAAJ,QAAA,eACpCd,OAAA,CAACZ,QAAQ;UAAC+B,QAAQ,EAAC;QAAS;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAAC3B,KAAK;MACJ0C,IAAI,EAAC,GAAG;MACRC,OAAO,eACLhB,OAAA,CAACrB,cAAc;QAACuC,YAAY,EAAC,SAAS;QAAAJ,QAAA,eACpCd,OAAA,CAAC1B,QAAQ;UAACgC,EAAE,EAAC,mBAAmB;UAACC,OAAO;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACU,CACtB;AAACW,GAAA,GAhCID,aAAuB;AAkC7B,SAASE,GAAGA,CAAA,EAAG;EACb,oBACEvB,OAAA,CAACtB,aAAa;IAAAoC,QAAA,eACZd,OAAA,CAAC7B,MAAM;MAAA2C,QAAA,eACLd,OAAA;QAAKwB,SAAS,EAAC,KAAK;QAAAV,QAAA,eAClBd,OAAA,CAAC5B,MAAM;UAAA0C,QAAA,gBAELd,OAAA,CAAC3B,KAAK;YAAC0C,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEhB,OAAA,CAACf,WAAW;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAG5CX,OAAA,CAAC3B,KAAK;YAAC0C,IAAI,EAAC,aAAa;YAACC,OAAO,eAAEhB,OAAA,CAACH,SAAS;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGpDX,OAAA,CAAC3B,KAAK;YAAC0C,IAAI,EAAC,UAAU;YAACC,OAAO,eAAEhB,OAAA,CAACa,WAAW;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGnDX,OAAA,CAAC3B,KAAK;YAAC0C,IAAI,EAAC,YAAY;YAACC,OAAO,eAAEhB,OAAA,CAACqB,aAAa;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGvDX,OAAA,CAAC3B,KAAK;YAAC0C,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEhB,OAAA,CAACC,aAAa;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEpB;AAACc,GAAA,GAzBQF,GAAG;AA2BZ,eAAeA,GAAG;AAAC,IAAAX,EAAA,EAAAQ,GAAA,EAAAE,GAAA,EAAAG,GAAA;AAAAC,YAAA,CAAAd,EAAA;AAAAc,YAAA,CAAAN,GAAA;AAAAM,YAAA,CAAAJ,GAAA;AAAAI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}