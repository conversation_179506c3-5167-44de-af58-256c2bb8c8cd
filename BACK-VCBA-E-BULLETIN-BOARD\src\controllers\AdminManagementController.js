const AdminModel = require('../models/AdminModel');
const { asyncHandler } = require('../middleware/errorHandler');
const { PermissionChecker } = require('../utils/permissions');
const logger = require('../utils/logger');

class AdminManagementController {
  // Get all admins with filtering and pagination
  getAdmins = asyncHandler(async (req, res) => {
    const { page = 1, limit = 10, position, is_active, search } = req.query;

    const filters = {};
    if (position) filters.position = position;
    if (is_active !== undefined) filters.is_active = is_active;
    if (search) filters.search = search;

    const result = await AdminModel.getAdminsWithPagination({
      page,
      limit,
      filters,
    });

    res.status(200).json({
      success: true,
      message: 'Admins retrieved successfully',
      data: result,
    });
  });

  // Get single admin by ID
  getAdmin = asyncHandler(async (req, res) => {
    const { adminId } = req.params;

    const admin = await AdminModel.getAdminWithProfile(adminId);
    if (!admin) {
      return res.status(404).json({
        success: false,
        message: 'Admin not found',
      });
    }

    res.status(200).json({
      success: true,
      message: 'Admin retrieved successfully',
      data: { admin },
    });
  });

  // Create new admin
  createAdmin = asyncHandler(async (req, res) => {
    const {
      email,
      password,
      first_name,
      last_name,
      middle_name,
      suffix,
      phone_number,
      department,
      position,
      grade_level,
      is_active = true,
    } = req.body;

    // Validate position
    if (!PermissionChecker.isValidPosition(position)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid position specified',
      });
    }

    const adminData = {
      email,
      password,
      is_active,
    };

    const profileData = {
      first_name,
      last_name,
      middle_name,
      suffix,
      phone_number,
      department,
      position,
      grade_level,
    };

    const admin = await AdminModel.createAdminWithProfile(adminData, profileData);

    logger.info('Admin created', {
      adminId: admin.admin_id,
      email: admin.email,
      position: admin.position,
      createdBy: req.user.id,
      userEmail: req.user.email,
    });

    res.status(201).json({
      success: true,
      message: 'Admin created successfully',
      data: { admin },
    });
  });

  // Update admin
  updateAdmin = asyncHandler(async (req, res) => {
    const { adminId } = req.params;
    const {
      email,
      first_name,
      last_name,
      middle_name,
      suffix,
      phone_number,
      department,
      position,
      grade_level,
      is_active,
    } = req.body;

    // Check if admin exists
    const existingAdmin = await AdminModel.getAdminWithProfile(adminId);
    if (!existingAdmin) {
      return res.status(404).json({
        success: false,
        message: 'Admin not found',
      });
    }

    // Validate position change if provided
    if (position && position !== existingAdmin.position) {
      await AdminModel.validatePositionUpdate(adminId, position, req.user.id);
    }

    const accountData = {};
    if (email !== undefined) accountData.email = email;
    if (is_active !== undefined) accountData.is_active = is_active;

    const profileData = {};
    if (first_name !== undefined) profileData.first_name = first_name;
    if (last_name !== undefined) profileData.last_name = last_name;
    if (middle_name !== undefined) profileData.middle_name = middle_name;
    if (suffix !== undefined) profileData.suffix = suffix;
    if (phone_number !== undefined) profileData.phone_number = phone_number;
    if (department !== undefined) profileData.department = department;
    if (position !== undefined) profileData.position = position;
    if (grade_level !== undefined) profileData.grade_level = grade_level;

    const admin = await AdminModel.updateAdminWithProfile(adminId, accountData, profileData);

    logger.info('Admin updated', {
      adminId,
      updatedBy: req.user.id,
      userEmail: req.user.email,
      changes: { ...accountData, ...profileData },
    });

    res.status(200).json({
      success: true,
      message: 'Admin updated successfully',
      data: { admin },
    });
  });

  // Delete admin (soft delete)
  deleteAdmin = asyncHandler(async (req, res) => {
    const { adminId } = req.params;

    // Check if admin exists
    const existingAdmin = await AdminModel.getAdminWithProfile(adminId);
    if (!existingAdmin) {
      return res.status(404).json({
        success: false,
        message: 'Admin not found',
      });
    }

    // Prevent deletion of the last super admin
    if (existingAdmin.position === 'super_admin') {
      const superAdmins = await AdminModel.getAdminsByPosition('super_admin');
      if (superAdmins.length <= 1) {
        return res.status(400).json({
          success: false,
          message: 'Cannot delete the last super admin',
        });
      }
    }

    // Prevent self-deletion
    if (parseInt(adminId) === req.user.id) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete your own account',
      });
    }

    await AdminModel.deleteAdmin(adminId);

    logger.info('Admin deleted', {
      adminId,
      deletedAdmin: existingAdmin.email,
      deletedBy: req.user.id,
      userEmail: req.user.email,
    });

    res.status(200).json({
      success: true,
      message: 'Admin deleted successfully',
    });
  });

  // Reset admin password
  resetAdminPassword = asyncHandler(async (req, res) => {
    const { adminId } = req.params;
    const { new_password } = req.body;

    // Check if admin exists
    const existingAdmin = await AdminModel.getAdminWithProfile(adminId);
    if (!existingAdmin) {
      return res.status(404).json({
        success: false,
        message: 'Admin not found',
      });
    }

    await AdminModel.updatePassword(adminId, new_password);

    logger.info('Admin password reset', {
      adminId,
      targetAdmin: existingAdmin.email,
      resetBy: req.user.id,
      userEmail: req.user.email,
    });

    res.status(200).json({
      success: true,
      message: 'Admin password reset successfully',
    });
  });

  // Update admin position
  updateAdminPosition = asyncHandler(async (req, res) => {
    const { adminId } = req.params;
    const { position } = req.body;

    // Validate position update
    await AdminModel.validatePositionUpdate(adminId, position, req.user.id);

    const admin = await AdminModel.updateAdminProfile(adminId, { position });

    logger.info('Admin position updated', {
      adminId,
      newPosition: position,
      updatedBy: req.user.id,
      userEmail: req.user.email,
    });

    res.status(200).json({
      success: true,
      message: 'Admin position updated successfully',
      data: { admin },
    });
  });

  // Get admins by position
  getAdminsByPosition = asyncHandler(async (req, res) => {
    const { position } = req.params;

    if (!PermissionChecker.isValidPosition(position)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid position specified',
      });
    }

    const admins = await AdminModel.getAdminsByPosition(position);

    res.status(200).json({
      success: true,
      message: `${position} admins retrieved successfully`,
      data: { admins },
    });
  });

  // Activate admin
  activateAdmin = asyncHandler(async (req, res) => {
    const { adminId } = req.params;

    await AdminModel.updateAdminAccount(adminId, { is_active: true });

    logger.info('Admin activated', {
      adminId,
      activatedBy: req.user.id,
      userEmail: req.user.email,
    });

    res.status(200).json({
      success: true,
      message: 'Admin activated successfully',
    });
  });

  // Deactivate admin
  deactivateAdmin = asyncHandler(async (req, res) => {
    const { adminId } = req.params;

    // Prevent self-deactivation
    if (parseInt(adminId) === req.user.id) {
      return res.status(400).json({
        success: false,
        message: 'Cannot deactivate your own account',
      });
    }

    // Check if admin exists and get their position
    const existingAdmin = await AdminModel.getAdminWithProfile(adminId);
    if (!existingAdmin) {
      return res.status(404).json({
        success: false,
        message: 'Admin not found',
      });
    }

    // Prevent deactivation of the last super admin
    if (existingAdmin.position === 'super_admin') {
      const superAdmins = await AdminModel.getAdminsByPosition('super_admin');
      const activeSuperAdmins = superAdmins.filter(admin => admin.is_active);
      if (activeSuperAdmins.length <= 1) {
        return res.status(400).json({
          success: false,
          message: 'Cannot deactivate the last active super admin',
        });
      }
    }

    await AdminModel.updateAdminAccount(adminId, { is_active: false });

    logger.info('Admin deactivated', {
      adminId,
      deactivatedAdmin: existingAdmin.email,
      deactivatedBy: req.user.id,
      userEmail: req.user.email,
    });

    res.status(200).json({
      success: true,
      message: 'Admin deactivated successfully',
    });
  });
}

module.exports = new AdminManagementController();
