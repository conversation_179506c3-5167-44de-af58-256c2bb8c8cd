import React, { useState, useEffect } from 'react';
import { UserPlus, Search, Filter, Users, AlertTriangle, CheckCircle } from 'lucide-react';
import { useAdminAuth } from '../../contexts/AdminAuthContext';
import { usePermissions } from '../../utils/permissions';
import { adminManagementService } from '../../services/adminManagementService';
import AdminAccountList from '../../components/admin/AdminAccountList';
import AdminAccountModal from '../../components/admin/AdminAccountModal';

interface AdminAccount {
  admin_id?: number;
  email: string;
  is_active: boolean;
  last_login?: string | null;
  created_at?: string;
  profile: {
    first_name: string;
    middle_name?: string;
    last_name: string;
    suffix?: string;
    full_name: string;
    phone_number?: string;
    department?: string;
    position: 'super_admin' | 'professor';
    grade_level?: number;
    bio?: string;
    profile_picture?: string;
  };
}

const AdminManagement: React.FC = () => {
  const { user } = useAdminAuth();
  const permissions = usePermissions(user);

  // State management
  const [admins, setAdmins] = useState<AdminAccount[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Modal state
  const [showModal, setShowModal] = useState(false);
  const [editingAdmin, setEditingAdmin] = useState<AdminAccount | null>(null);
  const [modalLoading, setModalLoading] = useState(false);

  // Search and filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [positionFilter, setPositionFilter] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('');

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);

  useEffect(() => {
    // Check if user has permission to manage admins
    if (!permissions.canManageAdmins) {
      setError('You do not have permission to manage admin accounts');
      setLoading(false);
      return;
    }

    loadAdmins();
  }, [permissions.canManageAdmins, currentPage, itemsPerPage, searchTerm, positionFilter, statusFilter]);

  // Reset to page 1 when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, positionFilter, statusFilter]);

  const loadAdmins = async () => {
    try {
      setLoading(true);
      setError(null);

      // TODO: Implement actual API call
      // const response = await adminManagementService.getAdmins({
      //   page: currentPage,
      //   limit: itemsPerPage,
      //   search: searchTerm,
      //   position: positionFilter,
      //   status: statusFilter
      // });

      // Mock data with proper structure
      const mockAdmins: AdminAccount[] = [
        {
          admin_id: 1,
          email: '<EMAIL>',
          is_active: true,
          last_login: '2025-08-05T10:30:00Z',
          created_at: '2025-01-01T00:00:00Z',
          profile: {
            first_name: 'Josh',
            middle_name: 'Christian',
            last_name: 'Mojica',
            full_name: 'Josh Christian Mojica',
            phone_number: '+************',
            department: 'Administration',
            position: 'super_admin',
            grade_level: 12,
            bio: 'System Administrator with expertise in educational technology.'
          }
        },
        {
          admin_id: 2,
          email: '<EMAIL>',
          is_active: true,
          last_login: '2025-08-05T09:15:00Z',
          created_at: '2025-01-15T00:00:00Z',
          profile: {
            first_name: 'Zaira',
            last_name: 'Plarisan',
            full_name: 'Zaira Plarisan',
            phone_number: '+************',
            department: 'Mathematics',
            position: 'professor',
            grade_level: 11,
            bio: 'Mathematics professor specializing in Grade 11 curriculum.'
          }
        }
      ];

      // Apply filters
      let filteredAdmins = [...mockAdmins];

      if (searchTerm.trim()) {
        const searchLower = searchTerm.toLowerCase().trim();
        filteredAdmins = filteredAdmins.filter(admin =>
          admin.profile.full_name.toLowerCase().includes(searchLower) ||
          admin.email.toLowerCase().includes(searchLower) ||
          admin.profile.first_name.toLowerCase().includes(searchLower) ||
          admin.profile.last_name.toLowerCase().includes(searchLower) ||
          (admin.profile.department && admin.profile.department.toLowerCase().includes(searchLower))
        );
      }

      if (positionFilter) {
        filteredAdmins = filteredAdmins.filter(admin => admin.profile.position === positionFilter);
      }

      if (statusFilter) {
        const isActive = statusFilter === 'active';
        filteredAdmins = filteredAdmins.filter(admin => admin.is_active === isActive);
      }

      setTotalItems(filteredAdmins.length);

      // Apply pagination
      const startIndex = (currentPage - 1) * itemsPerPage;
      const paginatedAdmins = filteredAdmins.slice(startIndex, startIndex + itemsPerPage);

      setAdmins(paginatedAdmins);

    } catch (err: any) {
      setError(err.message || 'Failed to load admin accounts');
    } finally {
      setLoading(false);
    }
  };

  // Handler functions
  const handleAddAdmin = () => {
    setEditingAdmin(null);
    setShowModal(true);
  };

  const handleEditAdmin = (admin: AdminAccount) => {
    setEditingAdmin(admin);
    setShowModal(true);
  };

  const handleDeleteAdmin = async (admin: AdminAccount) => {
    try {
      setError(null);
      setSuccess(null);

      // TODO: Implement actual API call
      // await adminManagementService.deleteAdmin(admin.admin_id);

      // Mock success
      setSuccess(`Admin account for ${admin.profile.full_name} has been deactivated`);
      loadAdmins();

    } catch (err: any) {
      setError(err.message || 'Failed to delete admin account');
    }
  };

  const handleToggleStatus = async (admin: AdminAccount) => {
    try {
      setError(null);
      setSuccess(null);

      // TODO: Implement actual API call
      // await adminManagementService.toggleAdminStatus(admin.admin_id, !admin.is_active);

      // Mock success
      const action = admin.is_active ? 'deactivated' : 'activated';
      setSuccess(`Admin account for ${admin.profile.full_name} has been ${action}`);
      loadAdmins();

    } catch (err: any) {
      setError(err.message || 'Failed to update admin status');
    }
  };

  const handleSaveAdmin = async (adminData: AdminAccount) => {
    try {
      setModalLoading(true);
      setError(null);
      setSuccess(null);

      if (editingAdmin) {
        // TODO: Implement actual API call for update
        // await adminManagementService.updateAdmin(editingAdmin.admin_id, adminData);
        setSuccess(`Admin account for ${adminData.profile.first_name} ${adminData.profile.last_name} has been updated`);
      } else {
        // TODO: Implement actual API call for create
        // await adminManagementService.createAdmin(adminData);
        setSuccess(`Admin account for ${adminData.profile.first_name} ${adminData.profile.last_name} has been created`);
      }

      loadAdmins();

    } catch (err: any) {
      setError(err.message || 'Failed to save admin account');
      throw err; // Re-throw to prevent modal from closing
    } finally {
      setModalLoading(false);
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (itemsPerPage: number) => {
    setItemsPerPage(itemsPerPage);
    setCurrentPage(1); // Reset to first page
  };

  const clearMessages = () => {
    setError(null);
    setSuccess(null);
  };

  if (!permissions.canManageAdmins) {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '400px',
        textAlign: 'center',
        color: '#6b7280'
      }}>
        <Users size={64} style={{ marginBottom: '1rem', opacity: 0.5 }} />
        <h2 style={{ margin: '0 0 0.5rem', fontSize: '1.5rem', fontWeight: '600' }}>
          Access Denied
        </h2>
        <p style={{ margin: 0, fontSize: '1rem' }}>
          You do not have permission to manage admin accounts.
        </p>
        <div style={{
          marginTop: '1rem',
          padding: '0.5rem 1rem',
          background: permissions.getPositionBadgeColor(),
          borderRadius: '6px',
          color: 'white',
          fontSize: '0.875rem',
          fontWeight: '600'
        }}>
          Current Role: {permissions.getPositionDisplayName()}
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '400px'
      }}>
        <div style={{
          width: '40px',
          height: '40px',
          border: '4px solid #f3f4f6',
          borderTop: '4px solid #3b82f6',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }} />
      </div>
    );
  }

  if (error) {
    return (
      <div style={{
        padding: '2rem',
        textAlign: 'center',
        color: '#ef4444'
      }}>
        <h2>Error</h2>
        <p>{error}</p>
        <button
          onClick={loadAdmins}
          style={{
            padding: '0.5rem 1rem',
            background: '#3b82f6',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: 'pointer'
          }}
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div style={{ maxWidth: '1400px', margin: '0 auto' }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '2rem'
      }}>
        <div>
          <h1 style={{
            margin: '0 0 0.5rem',
            fontSize: '2rem',
            fontWeight: '700',
            color: '#1f2937'
          }}>
            Admin Account Management
          </h1>
          <p style={{
            margin: 0,
            color: '#6b7280',
            fontSize: '1rem'
          }}>
            Manage administrator accounts and permissions
          </p>
        </div>

        <button
          onClick={handleAddAdmin}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            padding: '0.75rem 1.5rem',
            background: '#3b82f6',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            fontSize: '0.875rem',
            fontWeight: '600',
            cursor: 'pointer',
            transition: 'background-color 0.2s'
          }}
          onMouseEnter={(e) => e.currentTarget.style.background = '#2563eb'}
          onMouseLeave={(e) => e.currentTarget.style.background = '#3b82f6'}
        >
          <UserPlus size={16} />
          Add Admin
        </button>
      </div>

      {/* Search and Filter Bar */}
      <div style={{
        display: 'flex',
        gap: '1rem',
        marginBottom: '2rem',
        flexWrap: 'wrap'
      }}>
        <div style={{ flex: 1, minWidth: '300px' }}>
          <div style={{ position: 'relative' }}>
            <Search size={20} style={{
              position: 'absolute',
              left: '0.75rem',
              top: '50%',
              transform: 'translateY(-50%)',
              color: '#6b7280'
            }} />
            <input
              type="text"
              placeholder="Search admins by name or email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{
                width: '100%',
                padding: '0.75rem 0.75rem 0.75rem 2.5rem',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                fontSize: '0.875rem'
              }}
            />
          </div>
        </div>

        <select
          value={positionFilter}
          onChange={(e) => setPositionFilter(e.target.value)}
          style={{
            padding: '0.75rem',
            border: '1px solid #d1d5db',
            borderRadius: '8px',
            fontSize: '0.875rem',
            minWidth: '150px'
          }}
        >
          <option value="">All Positions</option>
          <option value="super_admin">Super Admin</option>
          <option value="professor">Professor</option>
        </select>

        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          style={{
            padding: '0.75rem',
            border: '1px solid #d1d5db',
            borderRadius: '8px',
            fontSize: '0.875rem',
            minWidth: '120px'
          }}
        >
          <option value="">All Status</option>
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
        </select>
      </div>

      {/* Success/Error Messages */}
      {(error || success) && (
        <div style={{ marginBottom: '1.5rem' }}>
          {error && (
            <div style={{
              padding: '1rem',
              background: '#fef2f2',
              border: '1px solid #fecaca',
              borderRadius: '8px',
              color: '#dc2626',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <AlertTriangle size={16} />
                {error}
              </div>
              <button
                onClick={clearMessages}
                style={{
                  background: 'transparent',
                  border: 'none',
                  color: '#dc2626',
                  cursor: 'pointer',
                  padding: '0.25rem'
                }}
              >
                ×
              </button>
            </div>
          )}

          {success && (
            <div style={{
              padding: '1rem',
              background: '#f0fdf4',
              border: '1px solid #bbf7d0',
              borderRadius: '8px',
              color: '#166534',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <CheckCircle size={16} />
                {success}
              </div>
              <button
                onClick={clearMessages}
                style={{
                  background: 'transparent',
                  border: 'none',
                  color: '#166534',
                  cursor: 'pointer',
                  padding: '0.25rem'
                }}
              >
                ×
              </button>
            </div>
          )}
        </div>
      )}

      {/* Admin List */}
      <AdminAccountList
        admins={admins}
        loading={loading}
        onEdit={handleEditAdmin}
        onDelete={handleDeleteAdmin}
        onToggleStatus={handleToggleStatus}
        currentPage={currentPage}
        totalPages={Math.ceil(totalItems / itemsPerPage)}
        itemsPerPage={itemsPerPage}
        totalItems={totalItems}
        onPageChange={handlePageChange}
        onItemsPerPageChange={handleItemsPerPageChange}
      />

      {/* Admin Modal */}
      <AdminAccountModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onSave={handleSaveAdmin}
        admin={editingAdmin}
        loading={modalLoading}
      />
    </div>
  );
};

export default AdminManagement;
