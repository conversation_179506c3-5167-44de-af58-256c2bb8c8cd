{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\admin\\\\BulkOperations.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { Upload, Download, Users, FileText, Trash2, CheckCircle, AlertTriangle, RefreshCw } from 'lucide-react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BulkOperations = () => {\n  _s();\n  const {\n    user\n  } = useAdminAuth();\n  const permissions = usePermissions(user);\n  const fileInputRef = useRef(null);\n  const [activeTab, setActiveTab] = useState('import');\n  const [operations, setOperations] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n\n  // Import/Export states\n  const [importType, setImportType] = useState('students');\n  const [exportType, setExportType] = useState('students');\n  const [exportFormat, setExportFormat] = useState('csv');\n  const [selectedFile, setSelectedFile] = useState(null);\n\n  // Bulk action states\n  const [bulkActionType, setBulkActionType] = useState('delete_inactive_students');\n\n  // Permission check\n  if (!permissions.isSuperAdmin) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Upload, {\n        size: 64,\n        style: {\n          marginBottom: '1rem',\n          opacity: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          margin: '0 0 0.5rem',\n          fontSize: '1.5rem',\n          fontWeight: '600'\n        },\n        children: \"Access Denied\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: 0,\n          fontSize: '1rem'\n        },\n        children: \"Bulk operations are restricted to Super Administrators only.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        },\n        children: [\"Current Role: \", permissions.getPositionDisplayName()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this);\n  }\n  const handleFileSelect = event => {\n    var _event$target$files;\n    const file = (_event$target$files = event.target.files) === null || _event$target$files === void 0 ? void 0 : _event$target$files[0];\n    if (file) {\n      setSelectedFile(file);\n      setError(null);\n    }\n  };\n  const handleImport = async () => {\n    if (!selectedFile) {\n      setError('Please select a file to import');\n      return;\n    }\n    setLoading(true);\n    setError(null);\n    setSuccess(null);\n    try {\n      // TODO: Implement actual import API call\n      // const formData = new FormData();\n      // formData.append('file', selectedFile);\n      // formData.append('type', importType);\n      // const response = await bulkOperationsService.importData(formData);\n\n      // Mock import process\n      const newOperation = {\n        id: Date.now().toString(),\n        type: 'student_import',\n        status: 'processing',\n        progress: 0,\n        total: 100,\n        processed: 0,\n        errors: [],\n        createdAt: new Date()\n      };\n      setOperations(prev => [newOperation, ...prev]);\n\n      // Simulate progress\n      let progress = 0;\n      const interval = setInterval(() => {\n        progress += 10;\n        setOperations(prev => prev.map(op => op.id === newOperation.id ? {\n          ...op,\n          progress,\n          processed: progress\n        } : op));\n        if (progress >= 100) {\n          clearInterval(interval);\n          setOperations(prev => prev.map(op => op.id === newOperation.id ? {\n            ...op,\n            status: 'completed',\n            completedAt: new Date()\n          } : op));\n          setSuccess(`Successfully imported ${importType} data`);\n          setSelectedFile(null);\n          if (fileInputRef.current) {\n            fileInputRef.current.value = '';\n          }\n        }\n      }, 500);\n    } catch (err) {\n      setError(err.message || 'Import failed');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleExport = async () => {\n    setLoading(true);\n    setError(null);\n    setSuccess(null);\n    try {\n      // TODO: Implement actual export API call\n      // const response = await bulkOperationsService.exportData({\n      //   type: exportType,\n      //   format: exportFormat\n      // });\n\n      // Mock export process\n      const newOperation = {\n        id: Date.now().toString(),\n        type: 'student_export',\n        status: 'processing',\n        progress: 0,\n        total: 100,\n        processed: 0,\n        errors: [],\n        createdAt: new Date()\n      };\n      setOperations(prev => [newOperation, ...prev]);\n\n      // Simulate progress\n      let progress = 0;\n      const interval = setInterval(() => {\n        progress += 20;\n        setOperations(prev => prev.map(op => op.id === newOperation.id ? {\n          ...op,\n          progress,\n          processed: progress\n        } : op));\n        if (progress >= 100) {\n          clearInterval(interval);\n          setOperations(prev => prev.map(op => op.id === newOperation.id ? {\n            ...op,\n            status: 'completed',\n            completedAt: new Date()\n          } : op));\n          setSuccess(`Successfully exported ${exportType} data as ${exportFormat.toUpperCase()}`);\n\n          // Mock file download\n          const blob = new Blob(['Mock exported data'], {\n            type: 'text/plain'\n          });\n          const url = URL.createObjectURL(blob);\n          const a = document.createElement('a');\n          a.href = url;\n          a.download = `${exportType}_export.${exportFormat}`;\n          document.body.appendChild(a);\n          a.click();\n          document.body.removeChild(a);\n          URL.revokeObjectURL(url);\n        }\n      }, 300);\n    } catch (err) {\n      setError(err.message || 'Export failed');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleBulkAction = async () => {\n    setLoading(true);\n    setError(null);\n    setSuccess(null);\n    try {\n      // TODO: Implement actual bulk action API call\n      // const response = await bulkOperationsService.performBulkAction(bulkActionType);\n\n      // Mock bulk action\n      const actionNames = {\n        delete_inactive_students: 'Delete Inactive Students',\n        archive_old_announcements: 'Archive Old Announcements',\n        cleanup_old_events: 'Cleanup Old Events'\n      };\n      const newOperation = {\n        id: Date.now().toString(),\n        type: 'announcement_bulk_delete',\n        status: 'processing',\n        progress: 0,\n        total: 50,\n        processed: 0,\n        errors: [],\n        createdAt: new Date()\n      };\n      setOperations(prev => [newOperation, ...prev]);\n\n      // Simulate progress\n      let progress = 0;\n      const interval = setInterval(() => {\n        progress += 10;\n        setOperations(prev => prev.map(op => op.id === newOperation.id ? {\n          ...op,\n          progress,\n          processed: Math.floor(progress * 0.5)\n        } : op));\n        if (progress >= 100) {\n          clearInterval(interval);\n          setOperations(prev => prev.map(op => op.id === newOperation.id ? {\n            ...op,\n            status: 'completed',\n            completedAt: new Date()\n          } : op));\n          setSuccess(`Successfully completed: ${actionNames[bulkActionType]}`);\n        }\n      }, 400);\n    } catch (err) {\n      setError(err.message || 'Bulk action failed');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'completed':\n        return /*#__PURE__*/_jsxDEV(CheckCircle, {\n          size: 16,\n          color: \"#10b981\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 16\n        }, this);\n      case 'failed':\n        return /*#__PURE__*/_jsxDEV(AlertTriangle, {\n          size: 16,\n          color: \"#ef4444\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 16\n        }, this);\n      case 'processing':\n        return /*#__PURE__*/_jsxDEV(RefreshCw, {\n          size: 16,\n          color: \"#3b82f6\",\n          className: \"animate-spin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(RefreshCw, {\n          size: 16,\n          color: \"#6b7280\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const tabs = [{\n    key: 'import',\n    label: 'Import Data',\n    icon: Upload\n  }, {\n    key: 'export',\n    label: 'Export Data',\n    icon: Download\n  }, {\n    key: 'bulk_actions',\n    label: 'Bulk Actions',\n    icon: Trash2\n  }, {\n    key: 'history',\n    label: 'Operation History',\n    icon: FileText\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      maxWidth: '1200px',\n      margin: '0 auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            margin: '0 0 0.5rem',\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          },\n          children: \"Bulk Operations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          },\n          children: \"Import, export, and perform bulk actions on system data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          padding: '0.75rem 1rem',\n          background: '#dc2626',\n          color: 'white',\n          borderRadius: '8px',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Users, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this), \"Super Admin Only\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '1rem',\n        background: '#fef2f2',\n        border: '1px solid #fecaca',\n        borderRadius: '8px',\n        color: '#dc2626',\n        marginBottom: '1rem',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n        size: 16\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 11\n      }, this), error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 9\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '1rem',\n        background: '#f0fdf4',\n        border: '1px solid #bbf7d0',\n        borderRadius: '8px',\n        color: '#166534',\n        marginBottom: '1rem',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n        size: 16\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 11\n      }, this), success]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 347,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        borderBottom: '1px solid #e5e7eb',\n        marginBottom: '2rem'\n      },\n      children: tabs.map(tab => {\n        const Icon = tab.icon;\n        return /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab(tab.key),\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            padding: '1rem 1.5rem',\n            border: 'none',\n            background: 'transparent',\n            color: activeTab === tab.key ? '#3b82f6' : '#6b7280',\n            borderBottom: activeTab === tab.key ? '2px solid #3b82f6' : '2px solid transparent',\n            cursor: 'pointer',\n            fontSize: '0.875rem',\n            fontWeight: '600',\n            transition: 'all 0.2s'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 15\n          }, this), tab.label]\n        }, tab.key, true, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 364,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '12px',\n        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n        padding: '2rem'\n      },\n      children: [activeTab === 'import' && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 1.5rem',\n            fontSize: '1.25rem',\n            fontWeight: '600'\n          },\n          children: \"Import Data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n            gap: '1.5rem',\n            marginBottom: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              },\n              children: \"Data Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: importType,\n              onChange: e => setImportType(e.target.value),\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #d1d5db',\n                borderRadius: '6px',\n                fontSize: '0.875rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"students\",\n                children: \"Students\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"announcements\",\n                children: \"Announcements\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"events\",\n                children: \"Calendar Events\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              },\n              children: \"Select File\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              ref: fileInputRef,\n              type: \"file\",\n              accept: \".csv,.xlsx,.json\",\n              onChange: handleFileSelect,\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #d1d5db',\n                borderRadius: '6px',\n                fontSize: '0.875rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleImport,\n          disabled: loading || !selectedFile,\n          style: {\n            padding: '0.75rem 1.5rem',\n            background: loading || !selectedFile ? '#9ca3af' : '#3b82f6',\n            color: 'white',\n            border: 'none',\n            borderRadius: '6px',\n            fontSize: '0.875rem',\n            fontWeight: '600',\n            cursor: loading || !selectedFile ? 'not-allowed' : 'pointer',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Upload, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 15\n          }, this), loading ? 'Importing...' : 'Import Data']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 11\n      }, this), activeTab === 'export' && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 1.5rem',\n            fontSize: '1.25rem',\n            fontWeight: '600'\n          },\n          children: \"Export Data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n            gap: '1.5rem',\n            marginBottom: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              },\n              children: \"Data Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: exportType,\n              onChange: e => setExportType(e.target.value),\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #d1d5db',\n                borderRadius: '6px',\n                fontSize: '0.875rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"students\",\n                children: \"Students\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"announcements\",\n                children: \"Announcements\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"events\",\n                children: \"Calendar Events\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              },\n              children: \"Export Format\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: exportFormat,\n              onChange: e => setExportFormat(e.target.value),\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #d1d5db',\n                borderRadius: '6px',\n                fontSize: '0.875rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"csv\",\n                children: \"CSV\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"xlsx\",\n                children: \"Excel (XLSX)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"json\",\n                children: \"JSON\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleExport,\n          disabled: loading,\n          style: {\n            padding: '0.75rem 1.5rem',\n            background: loading ? '#9ca3af' : '#10b981',\n            color: 'white',\n            border: 'none',\n            borderRadius: '6px',\n            fontSize: '0.875rem',\n            fontWeight: '600',\n            cursor: loading ? 'not-allowed' : 'pointer',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Download, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 15\n          }, this), loading ? 'Exporting...' : 'Export Data']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 501,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 464,\n        columnNumber: 11\n      }, this), activeTab === 'bulk_actions' && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 1.5rem',\n            fontSize: '1.25rem',\n            fontWeight: '600'\n          },\n          children: \"Bulk Actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '600',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            },\n            children: \"Select Action\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: bulkActionType,\n            onChange: e => setBulkActionType(e.target.value),\n            style: {\n              width: '100%',\n              maxWidth: '400px',\n              padding: '0.75rem',\n              border: '1px solid #d1d5db',\n              borderRadius: '6px',\n              fontSize: '0.875rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"delete_inactive_students\",\n              children: \"Delete Inactive Students (30+ days)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"archive_old_announcements\",\n              children: \"Archive Old Announcements (90+ days)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"cleanup_old_events\",\n              children: \"Cleanup Old Events (180+ days)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleBulkAction,\n          disabled: loading,\n          style: {\n            padding: '0.75rem 1.5rem',\n            background: loading ? '#9ca3af' : '#ef4444',\n            color: 'white',\n            border: 'none',\n            borderRadius: '6px',\n            fontSize: '0.875rem',\n            fontWeight: '600',\n            cursor: loading ? 'not-allowed' : 'pointer',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Trash2, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 15\n          }, this), loading ? 'Processing...' : 'Execute Bulk Action']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 545,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 525,\n        columnNumber: 11\n      }, this), activeTab === 'history' && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 1.5rem',\n            fontSize: '1.25rem',\n            fontWeight: '600'\n          },\n          children: \"Operation History\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 570,\n          columnNumber: 13\n        }, this), operations.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            padding: '3rem',\n            color: '#6b7280'\n          },\n          children: [/*#__PURE__*/_jsxDEV(FileText, {\n            size: 48,\n            style: {\n              marginBottom: '1rem',\n              opacity: 0.5\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No operations performed yet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 577,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '1rem'\n          },\n          children: operations.map(operation => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '1rem',\n              border: '1px solid #e5e7eb',\n              borderRadius: '8px',\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1rem'\n              },\n              children: [getStatusIcon(operation.status), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontWeight: '600',\n                    color: '#1f2937'\n                  },\n                  children: operation.type.replace(/_/g, ' ').toUpperCase()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 596,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '0.875rem',\n                    color: '#6b7280'\n                  },\n                  children: operation.createdAt.toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 599,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 595,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 593,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'right'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  color: '#1f2937'\n                },\n                children: [operation.processed, \"/\", operation.total]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '0.75rem',\n                  color: '#6b7280'\n                },\n                children: operation.status === 'processing' ? `${operation.progress}%` : operation.status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 609,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 605,\n              columnNumber: 21\n            }, this)]\n          }, operation.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 580,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 569,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 398,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 286,\n    columnNumber: 5\n  }, this);\n};\n_s(BulkOperations, \"ReyzTvILednnAK82pAaq9o3wBVc=\", false, function () {\n  return [useAdminAuth, usePermissions];\n});\n_c = BulkOperations;\nexport default BulkOperations;\nvar _c;\n$RefreshReg$(_c, \"BulkOperations\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "Upload", "Download", "Users", "FileText", "Trash2", "CheckCircle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RefreshCw", "useAdminAuth", "usePermissions", "jsxDEV", "_jsxDEV", "BulkOperations", "_s", "user", "permissions", "fileInputRef", "activeTab", "setActiveTab", "operations", "setOperations", "loading", "setLoading", "error", "setError", "success", "setSuccess", "importType", "setImportType", "exportType", "setExportType", "exportFormat", "setExportFormat", "selectedFile", "setSelectedFile", "bulkActionType", "setBulkActionType", "isSuperAdmin", "style", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "textAlign", "color", "children", "size", "marginBottom", "opacity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "margin", "fontSize", "fontWeight", "marginTop", "padding", "background", "getPositionBadgeColor", "borderRadius", "getPositionDisplayName", "handleFileSelect", "event", "_event$target$files", "file", "target", "files", "handleImport", "newOperation", "id", "Date", "now", "toString", "type", "status", "progress", "total", "processed", "errors", "createdAt", "prev", "interval", "setInterval", "map", "op", "clearInterval", "completedAt", "current", "value", "err", "message", "handleExport", "toUpperCase", "blob", "Blob", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleBulkAction", "actionNames", "delete_inactive_students", "archive_old_announcements", "cleanup_old_events", "Math", "floor", "getStatusIcon", "className", "tabs", "key", "label", "icon", "max<PERSON><PERSON><PERSON>", "gap", "border", "borderBottom", "tab", "Icon", "onClick", "cursor", "transition", "boxShadow", "gridTemplateColumns", "onChange", "e", "width", "ref", "accept", "disabled", "length", "operation", "replace", "toLocaleString", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/admin/BulkOperations.tsx"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport { Upload, Download, Users, FileText, Calendar, Trash2, CheckCircle, AlertTriangle, RefreshCw } from 'lucide-react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\n\ninterface BulkOperation {\n  id: string;\n  type: 'student_import' | 'student_export' | 'announcement_bulk_delete' | 'event_bulk_delete';\n  status: 'pending' | 'processing' | 'completed' | 'failed';\n  progress: number;\n  total: number;\n  processed: number;\n  errors: string[];\n  createdAt: Date;\n  completedAt?: Date;\n}\n\nconst BulkOperations: React.FC = () => {\n  const { user } = useAdminAuth();\n  const permissions = usePermissions(user);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  \n  const [activeTab, setActiveTab] = useState<'import' | 'export' | 'bulk_actions' | 'history'>('import');\n  const [operations, setOperations] = useState<BulkOperation[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  // Import/Export states\n  const [importType, setImportType] = useState<'students' | 'announcements' | 'events'>('students');\n  const [exportType, setExportType] = useState<'students' | 'announcements' | 'events'>('students');\n  const [exportFormat, setExportFormat] = useState<'csv' | 'xlsx' | 'json'>('csv');\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n\n  // Bulk action states\n  const [bulkActionType, setBulkActionType] = useState<'delete_inactive_students' | 'archive_old_announcements' | 'cleanup_old_events'>('delete_inactive_students');\n\n  // Permission check\n  if (!permissions.isSuperAdmin) {\n    return (\n      <div style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      }}>\n        <Upload size={64} style={{ marginBottom: '1rem', opacity: 0.5 }} />\n        <h2 style={{ margin: '0 0 0.5rem', fontSize: '1.5rem', fontWeight: '600' }}>\n          Access Denied\n        </h2>\n        <p style={{ margin: 0, fontSize: '1rem' }}>\n          Bulk operations are restricted to Super Administrators only.\n        </p>\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        }}>\n          Current Role: {permissions.getPositionDisplayName()}\n        </div>\n      </div>\n    );\n  }\n\n  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (file) {\n      setSelectedFile(file);\n      setError(null);\n    }\n  };\n\n  const handleImport = async () => {\n    if (!selectedFile) {\n      setError('Please select a file to import');\n      return;\n    }\n\n    setLoading(true);\n    setError(null);\n    setSuccess(null);\n\n    try {\n      // TODO: Implement actual import API call\n      // const formData = new FormData();\n      // formData.append('file', selectedFile);\n      // formData.append('type', importType);\n      // const response = await bulkOperationsService.importData(formData);\n\n      // Mock import process\n      const newOperation: BulkOperation = {\n        id: Date.now().toString(),\n        type: 'student_import',\n        status: 'processing',\n        progress: 0,\n        total: 100,\n        processed: 0,\n        errors: [],\n        createdAt: new Date()\n      };\n\n      setOperations(prev => [newOperation, ...prev]);\n\n      // Simulate progress\n      let progress = 0;\n      const interval = setInterval(() => {\n        progress += 10;\n        setOperations(prev => prev.map(op => \n          op.id === newOperation.id \n            ? { ...op, progress, processed: progress }\n            : op\n        ));\n\n        if (progress >= 100) {\n          clearInterval(interval);\n          setOperations(prev => prev.map(op => \n            op.id === newOperation.id \n              ? { ...op, status: 'completed', completedAt: new Date() }\n              : op\n          ));\n          setSuccess(`Successfully imported ${importType} data`);\n          setSelectedFile(null);\n          if (fileInputRef.current) {\n            fileInputRef.current.value = '';\n          }\n        }\n      }, 500);\n\n    } catch (err: any) {\n      setError(err.message || 'Import failed');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleExport = async () => {\n    setLoading(true);\n    setError(null);\n    setSuccess(null);\n\n    try {\n      // TODO: Implement actual export API call\n      // const response = await bulkOperationsService.exportData({\n      //   type: exportType,\n      //   format: exportFormat\n      // });\n\n      // Mock export process\n      const newOperation: BulkOperation = {\n        id: Date.now().toString(),\n        type: 'student_export',\n        status: 'processing',\n        progress: 0,\n        total: 100,\n        processed: 0,\n        errors: [],\n        createdAt: new Date()\n      };\n\n      setOperations(prev => [newOperation, ...prev]);\n\n      // Simulate progress\n      let progress = 0;\n      const interval = setInterval(() => {\n        progress += 20;\n        setOperations(prev => prev.map(op => \n          op.id === newOperation.id \n            ? { ...op, progress, processed: progress }\n            : op\n        ));\n\n        if (progress >= 100) {\n          clearInterval(interval);\n          setOperations(prev => prev.map(op => \n            op.id === newOperation.id \n              ? { ...op, status: 'completed', completedAt: new Date() }\n              : op\n          ));\n          setSuccess(`Successfully exported ${exportType} data as ${exportFormat.toUpperCase()}`);\n          \n          // Mock file download\n          const blob = new Blob(['Mock exported data'], { type: 'text/plain' });\n          const url = URL.createObjectURL(blob);\n          const a = document.createElement('a');\n          a.href = url;\n          a.download = `${exportType}_export.${exportFormat}`;\n          document.body.appendChild(a);\n          a.click();\n          document.body.removeChild(a);\n          URL.revokeObjectURL(url);\n        }\n      }, 300);\n\n    } catch (err: any) {\n      setError(err.message || 'Export failed');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleBulkAction = async () => {\n    setLoading(true);\n    setError(null);\n    setSuccess(null);\n\n    try {\n      // TODO: Implement actual bulk action API call\n      // const response = await bulkOperationsService.performBulkAction(bulkActionType);\n\n      // Mock bulk action\n      const actionNames = {\n        delete_inactive_students: 'Delete Inactive Students',\n        archive_old_announcements: 'Archive Old Announcements',\n        cleanup_old_events: 'Cleanup Old Events'\n      };\n\n      const newOperation: BulkOperation = {\n        id: Date.now().toString(),\n        type: 'announcement_bulk_delete',\n        status: 'processing',\n        progress: 0,\n        total: 50,\n        processed: 0,\n        errors: [],\n        createdAt: new Date()\n      };\n\n      setOperations(prev => [newOperation, ...prev]);\n\n      // Simulate progress\n      let progress = 0;\n      const interval = setInterval(() => {\n        progress += 10;\n        setOperations(prev => prev.map(op => \n          op.id === newOperation.id \n            ? { ...op, progress, processed: Math.floor(progress * 0.5) }\n            : op\n        ));\n\n        if (progress >= 100) {\n          clearInterval(interval);\n          setOperations(prev => prev.map(op => \n            op.id === newOperation.id \n              ? { ...op, status: 'completed', completedAt: new Date() }\n              : op\n          ));\n          setSuccess(`Successfully completed: ${actionNames[bulkActionType]}`);\n        }\n      }, 400);\n\n    } catch (err: any) {\n      setError(err.message || 'Bulk action failed');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getStatusIcon = (status: BulkOperation['status']) => {\n    switch (status) {\n      case 'completed':\n        return <CheckCircle size={16} color=\"#10b981\" />;\n      case 'failed':\n        return <AlertTriangle size={16} color=\"#ef4444\" />;\n      case 'processing':\n        return <RefreshCw size={16} color=\"#3b82f6\" className=\"animate-spin\" />;\n      default:\n        return <RefreshCw size={16} color=\"#6b7280\" />;\n    }\n  };\n\n  const tabs = [\n    { key: 'import', label: 'Import Data', icon: Upload },\n    { key: 'export', label: 'Export Data', icon: Download },\n    { key: 'bulk_actions', label: 'Bulk Actions', icon: Trash2 },\n    { key: 'history', label: 'Operation History', icon: FileText }\n  ];\n\n  return (\n    <div style={{ maxWidth: '1200px', margin: '0 auto' }}>\n      {/* Header */}\n      <div style={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      }}>\n        <div>\n          <h1 style={{\n            margin: '0 0 0.5rem',\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          }}>\n            Bulk Operations\n          </h1>\n          <p style={{\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          }}>\n            Import, export, and perform bulk actions on system data\n          </p>\n        </div>\n        \n        <div style={{\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          padding: '0.75rem 1rem',\n          background: '#dc2626',\n          color: 'white',\n          borderRadius: '8px',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        }}>\n          <Users size={16} />\n          Super Admin Only\n        </div>\n      </div>\n\n      {/* Error/Success Messages */}\n      {error && (\n        <div style={{\n          padding: '1rem',\n          background: '#fef2f2',\n          border: '1px solid #fecaca',\n          borderRadius: '8px',\n          color: '#dc2626',\n          marginBottom: '1rem',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        }}>\n          <AlertTriangle size={16} />\n          {error}\n        </div>\n      )}\n\n      {success && (\n        <div style={{\n          padding: '1rem',\n          background: '#f0fdf4',\n          border: '1px solid #bbf7d0',\n          borderRadius: '8px',\n          color: '#166534',\n          marginBottom: '1rem',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        }}>\n          <CheckCircle size={16} />\n          {success}\n        </div>\n      )}\n\n      {/* Tabs */}\n      <div style={{\n        display: 'flex',\n        borderBottom: '1px solid #e5e7eb',\n        marginBottom: '2rem'\n      }}>\n        {tabs.map((tab) => {\n          const Icon = tab.icon;\n          return (\n            <button\n              key={tab.key}\n              onClick={() => setActiveTab(tab.key as any)}\n              style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                padding: '1rem 1.5rem',\n                border: 'none',\n                background: 'transparent',\n                color: activeTab === tab.key ? '#3b82f6' : '#6b7280',\n                borderBottom: activeTab === tab.key ? '2px solid #3b82f6' : '2px solid transparent',\n                cursor: 'pointer',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                transition: 'all 0.2s'\n              }}\n            >\n              <Icon size={16} />\n              {tab.label}\n            </button>\n          );\n        })}\n      </div>\n\n      {/* Tab Content */}\n      <div style={{\n        background: 'white',\n        borderRadius: '12px',\n        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n        padding: '2rem'\n      }}>\n        {activeTab === 'import' && (\n          <div>\n            <h3 style={{ margin: '0 0 1.5rem', fontSize: '1.25rem', fontWeight: '600' }}>\n              Import Data\n            </h3>\n            \n            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '1.5rem', marginBottom: '2rem' }}>\n              <div>\n                <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>\n                  Data Type\n                </label>\n                <select\n                  value={importType}\n                  onChange={(e) => setImportType(e.target.value as any)}\n                  style={{ width: '100%', padding: '0.75rem', border: '1px solid #d1d5db', borderRadius: '6px', fontSize: '0.875rem' }}\n                >\n                  <option value=\"students\">Students</option>\n                  <option value=\"announcements\">Announcements</option>\n                  <option value=\"events\">Calendar Events</option>\n                </select>\n              </div>\n\n              <div>\n                <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>\n                  Select File\n                </label>\n                <input\n                  ref={fileInputRef}\n                  type=\"file\"\n                  accept=\".csv,.xlsx,.json\"\n                  onChange={handleFileSelect}\n                  style={{ width: '100%', padding: '0.75rem', border: '1px solid #d1d5db', borderRadius: '6px', fontSize: '0.875rem' }}\n                />\n              </div>\n            </div>\n\n            <button\n              onClick={handleImport}\n              disabled={loading || !selectedFile}\n              style={{\n                padding: '0.75rem 1.5rem',\n                background: loading || !selectedFile ? '#9ca3af' : '#3b82f6',\n                color: 'white',\n                border: 'none',\n                borderRadius: '6px',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                cursor: loading || !selectedFile ? 'not-allowed' : 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              }}\n            >\n              <Upload size={16} />\n              {loading ? 'Importing...' : 'Import Data'}\n            </button>\n          </div>\n        )}\n\n        {activeTab === 'export' && (\n          <div>\n            <h3 style={{ margin: '0 0 1.5rem', fontSize: '1.25rem', fontWeight: '600' }}>\n              Export Data\n            </h3>\n            \n            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '1.5rem', marginBottom: '2rem' }}>\n              <div>\n                <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>\n                  Data Type\n                </label>\n                <select\n                  value={exportType}\n                  onChange={(e) => setExportType(e.target.value as any)}\n                  style={{ width: '100%', padding: '0.75rem', border: '1px solid #d1d5db', borderRadius: '6px', fontSize: '0.875rem' }}\n                >\n                  <option value=\"students\">Students</option>\n                  <option value=\"announcements\">Announcements</option>\n                  <option value=\"events\">Calendar Events</option>\n                </select>\n              </div>\n\n              <div>\n                <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>\n                  Export Format\n                </label>\n                <select\n                  value={exportFormat}\n                  onChange={(e) => setExportFormat(e.target.value as any)}\n                  style={{ width: '100%', padding: '0.75rem', border: '1px solid #d1d5db', borderRadius: '6px', fontSize: '0.875rem' }}\n                >\n                  <option value=\"csv\">CSV</option>\n                  <option value=\"xlsx\">Excel (XLSX)</option>\n                  <option value=\"json\">JSON</option>\n                </select>\n              </div>\n            </div>\n\n            <button\n              onClick={handleExport}\n              disabled={loading}\n              style={{\n                padding: '0.75rem 1.5rem',\n                background: loading ? '#9ca3af' : '#10b981',\n                color: 'white',\n                border: 'none',\n                borderRadius: '6px',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                cursor: loading ? 'not-allowed' : 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              }}\n            >\n              <Download size={16} />\n              {loading ? 'Exporting...' : 'Export Data'}\n            </button>\n          </div>\n        )}\n\n        {activeTab === 'bulk_actions' && (\n          <div>\n            <h3 style={{ margin: '0 0 1.5rem', fontSize: '1.25rem', fontWeight: '600' }}>\n              Bulk Actions\n            </h3>\n            \n            <div style={{ marginBottom: '2rem' }}>\n              <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>\n                Select Action\n              </label>\n              <select\n                value={bulkActionType}\n                onChange={(e) => setBulkActionType(e.target.value as any)}\n                style={{ width: '100%', maxWidth: '400px', padding: '0.75rem', border: '1px solid #d1d5db', borderRadius: '6px', fontSize: '0.875rem' }}\n              >\n                <option value=\"delete_inactive_students\">Delete Inactive Students (30+ days)</option>\n                <option value=\"archive_old_announcements\">Archive Old Announcements (90+ days)</option>\n                <option value=\"cleanup_old_events\">Cleanup Old Events (180+ days)</option>\n              </select>\n            </div>\n\n            <button\n              onClick={handleBulkAction}\n              disabled={loading}\n              style={{\n                padding: '0.75rem 1.5rem',\n                background: loading ? '#9ca3af' : '#ef4444',\n                color: 'white',\n                border: 'none',\n                borderRadius: '6px',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                cursor: loading ? 'not-allowed' : 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              }}\n            >\n              <Trash2 size={16} />\n              {loading ? 'Processing...' : 'Execute Bulk Action'}\n            </button>\n          </div>\n        )}\n\n        {activeTab === 'history' && (\n          <div>\n            <h3 style={{ margin: '0 0 1.5rem', fontSize: '1.25rem', fontWeight: '600' }}>\n              Operation History\n            </h3>\n            \n            {operations.length === 0 ? (\n              <div style={{ textAlign: 'center', padding: '3rem', color: '#6b7280' }}>\n                <FileText size={48} style={{ marginBottom: '1rem', opacity: 0.5 }} />\n                <p>No operations performed yet</p>\n              </div>\n            ) : (\n              <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n                {operations.map((operation) => (\n                  <div\n                    key={operation.id}\n                    style={{\n                      padding: '1rem',\n                      border: '1px solid #e5e7eb',\n                      borderRadius: '8px',\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      alignItems: 'center'\n                    }}\n                  >\n                    <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>\n                      {getStatusIcon(operation.status)}\n                      <div>\n                        <div style={{ fontWeight: '600', color: '#1f2937' }}>\n                          {operation.type.replace(/_/g, ' ').toUpperCase()}\n                        </div>\n                        <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>\n                          {operation.createdAt.toLocaleString()}\n                        </div>\n                      </div>\n                    </div>\n                    \n                    <div style={{ textAlign: 'right' }}>\n                      <div style={{ fontSize: '0.875rem', fontWeight: '600', color: '#1f2937' }}>\n                        {operation.processed}/{operation.total}\n                      </div>\n                      <div style={{ fontSize: '0.75rem', color: '#6b7280' }}>\n                        {operation.status === 'processing' ? `${operation.progress}%` : operation.status}\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default BulkOperations;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SAASC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,QAAQ,EAAYC,MAAM,EAAEC,WAAW,EAAEC,aAAa,EAAEC,SAAS,QAAQ,cAAc;AACzH,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAczD,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM;IAAEC;EAAK,CAAC,GAAGN,YAAY,CAAC,CAAC;EAC/B,MAAMO,WAAW,GAAGN,cAAc,CAACK,IAAI,CAAC;EACxC,MAAME,YAAY,GAAGjB,MAAM,CAAmB,IAAI,CAAC;EAEnD,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAmD,QAAQ,CAAC;EACtG,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAkB,EAAE,CAAC;EACjE,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAgB,IAAI,CAAC;;EAE3D;EACA,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAA0C,UAAU,CAAC;EACjG,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAA0C,UAAU,CAAC;EACjG,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAA0B,KAAK,CAAC;EAChF,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAc,IAAI,CAAC;;EAEnE;EACA,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAkF,0BAA0B,CAAC;;EAEjK;EACA,IAAI,CAACiB,WAAW,CAACsB,YAAY,EAAE;IAC7B,oBACE1B,OAAA;MAAK2B,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE,OAAO;QAClBC,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACAnC,OAAA,CAACX,MAAM;QAAC+C,IAAI,EAAE,EAAG;QAACT,KAAK,EAAE;UAAEU,YAAY,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAI;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnE1C,OAAA;QAAI2B,KAAK,EAAE;UAAEgB,MAAM,EAAE,YAAY;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAM,CAAE;QAAAV,QAAA,EAAC;MAE5E;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL1C,OAAA;QAAG2B,KAAK,EAAE;UAAEgB,MAAM,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAT,QAAA,EAAC;MAE3C;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJ1C,OAAA;QAAK2B,KAAK,EAAE;UACVmB,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAE5C,WAAW,CAAC6C,qBAAqB,CAAC,CAAC;UAC/CC,YAAY,EAAE,KAAK;UACnBhB,KAAK,EAAE,OAAO;UACdU,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE;QACd,CAAE;QAAAV,QAAA,GAAC,gBACa,EAAC/B,WAAW,CAAC+C,sBAAsB,CAAC,CAAC;MAAA;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMU,gBAAgB,GAAIC,KAA0C,IAAK;IAAA,IAAAC,mBAAA;IACvE,MAAMC,IAAI,IAAAD,mBAAA,GAAGD,KAAK,CAACG,MAAM,CAACC,KAAK,cAAAH,mBAAA,uBAAlBA,mBAAA,CAAqB,CAAC,CAAC;IACpC,IAAIC,IAAI,EAAE;MACRhC,eAAe,CAACgC,IAAI,CAAC;MACrB1C,QAAQ,CAAC,IAAI,CAAC;IAChB;EACF,CAAC;EAED,MAAM6C,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAACpC,YAAY,EAAE;MACjBT,QAAQ,CAAC,gCAAgC,CAAC;MAC1C;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF;MACA;MACA;MACA;MACA;;MAEA;MACA,MAAM4C,YAA2B,GAAG;QAClCC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QACzBC,IAAI,EAAE,gBAAgB;QACtBC,MAAM,EAAE,YAAY;QACpBC,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE,GAAG;QACVC,SAAS,EAAE,CAAC;QACZC,MAAM,EAAE,EAAE;QACVC,SAAS,EAAE,IAAIT,IAAI,CAAC;MACtB,CAAC;MAEDpD,aAAa,CAAC8D,IAAI,IAAI,CAACZ,YAAY,EAAE,GAAGY,IAAI,CAAC,CAAC;;MAE9C;MACA,IAAIL,QAAQ,GAAG,CAAC;MAChB,MAAMM,QAAQ,GAAGC,WAAW,CAAC,MAAM;QACjCP,QAAQ,IAAI,EAAE;QACdzD,aAAa,CAAC8D,IAAI,IAAIA,IAAI,CAACG,GAAG,CAACC,EAAE,IAC/BA,EAAE,CAACf,EAAE,KAAKD,YAAY,CAACC,EAAE,GACrB;UAAE,GAAGe,EAAE;UAAET,QAAQ;UAAEE,SAAS,EAAEF;QAAS,CAAC,GACxCS,EACN,CAAC,CAAC;QAEF,IAAIT,QAAQ,IAAI,GAAG,EAAE;UACnBU,aAAa,CAACJ,QAAQ,CAAC;UACvB/D,aAAa,CAAC8D,IAAI,IAAIA,IAAI,CAACG,GAAG,CAACC,EAAE,IAC/BA,EAAE,CAACf,EAAE,KAAKD,YAAY,CAACC,EAAE,GACrB;YAAE,GAAGe,EAAE;YAAEV,MAAM,EAAE,WAAW;YAAEY,WAAW,EAAE,IAAIhB,IAAI,CAAC;UAAE,CAAC,GACvDc,EACN,CAAC,CAAC;UACF5D,UAAU,CAAC,yBAAyBC,UAAU,OAAO,CAAC;UACtDO,eAAe,CAAC,IAAI,CAAC;UACrB,IAAIlB,YAAY,CAACyE,OAAO,EAAE;YACxBzE,YAAY,CAACyE,OAAO,CAACC,KAAK,GAAG,EAAE;UACjC;QACF;MACF,CAAC,EAAE,GAAG,CAAC;IAET,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBnE,QAAQ,CAACmE,GAAG,CAACC,OAAO,IAAI,eAAe,CAAC;IAC1C,CAAC,SAAS;MACRtE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuE,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BvE,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF;MACA;MACA;MACA;MACA;;MAEA;MACA,MAAM4C,YAA2B,GAAG;QAClCC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QACzBC,IAAI,EAAE,gBAAgB;QACtBC,MAAM,EAAE,YAAY;QACpBC,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE,GAAG;QACVC,SAAS,EAAE,CAAC;QACZC,MAAM,EAAE,EAAE;QACVC,SAAS,EAAE,IAAIT,IAAI,CAAC;MACtB,CAAC;MAEDpD,aAAa,CAAC8D,IAAI,IAAI,CAACZ,YAAY,EAAE,GAAGY,IAAI,CAAC,CAAC;;MAE9C;MACA,IAAIL,QAAQ,GAAG,CAAC;MAChB,MAAMM,QAAQ,GAAGC,WAAW,CAAC,MAAM;QACjCP,QAAQ,IAAI,EAAE;QACdzD,aAAa,CAAC8D,IAAI,IAAIA,IAAI,CAACG,GAAG,CAACC,EAAE,IAC/BA,EAAE,CAACf,EAAE,KAAKD,YAAY,CAACC,EAAE,GACrB;UAAE,GAAGe,EAAE;UAAET,QAAQ;UAAEE,SAAS,EAAEF;QAAS,CAAC,GACxCS,EACN,CAAC,CAAC;QAEF,IAAIT,QAAQ,IAAI,GAAG,EAAE;UACnBU,aAAa,CAACJ,QAAQ,CAAC;UACvB/D,aAAa,CAAC8D,IAAI,IAAIA,IAAI,CAACG,GAAG,CAACC,EAAE,IAC/BA,EAAE,CAACf,EAAE,KAAKD,YAAY,CAACC,EAAE,GACrB;YAAE,GAAGe,EAAE;YAAEV,MAAM,EAAE,WAAW;YAAEY,WAAW,EAAE,IAAIhB,IAAI,CAAC;UAAE,CAAC,GACvDc,EACN,CAAC,CAAC;UACF5D,UAAU,CAAC,yBAAyBG,UAAU,YAAYE,YAAY,CAAC+D,WAAW,CAAC,CAAC,EAAE,CAAC;;UAEvF;UACA,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC,oBAAoB,CAAC,EAAE;YAAErB,IAAI,EAAE;UAAa,CAAC,CAAC;UACrE,MAAMsB,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;UACrC,MAAMK,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACrCF,CAAC,CAACG,IAAI,GAAGN,GAAG;UACZG,CAAC,CAACI,QAAQ,GAAG,GAAG3E,UAAU,WAAWE,YAAY,EAAE;UACnDsE,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,CAAC,CAAC;UAC5BA,CAAC,CAACO,KAAK,CAAC,CAAC;UACTN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,CAAC,CAAC;UAC5BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC;QAC1B;MACF,CAAC,EAAE,GAAG,CAAC;IAET,CAAC,CAAC,OAAON,GAAQ,EAAE;MACjBnE,QAAQ,CAACmE,GAAG,CAACC,OAAO,IAAI,eAAe,CAAC;IAC1C,CAAC,SAAS;MACRtE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwF,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnCxF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF;MACA;;MAEA;MACA,MAAMqF,WAAW,GAAG;QAClBC,wBAAwB,EAAE,0BAA0B;QACpDC,yBAAyB,EAAE,2BAA2B;QACtDC,kBAAkB,EAAE;MACtB,CAAC;MAED,MAAM5C,YAA2B,GAAG;QAClCC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QACzBC,IAAI,EAAE,0BAA0B;QAChCC,MAAM,EAAE,YAAY;QACpBC,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE,CAAC;QACZC,MAAM,EAAE,EAAE;QACVC,SAAS,EAAE,IAAIT,IAAI,CAAC;MACtB,CAAC;MAEDpD,aAAa,CAAC8D,IAAI,IAAI,CAACZ,YAAY,EAAE,GAAGY,IAAI,CAAC,CAAC;;MAE9C;MACA,IAAIL,QAAQ,GAAG,CAAC;MAChB,MAAMM,QAAQ,GAAGC,WAAW,CAAC,MAAM;QACjCP,QAAQ,IAAI,EAAE;QACdzD,aAAa,CAAC8D,IAAI,IAAIA,IAAI,CAACG,GAAG,CAACC,EAAE,IAC/BA,EAAE,CAACf,EAAE,KAAKD,YAAY,CAACC,EAAE,GACrB;UAAE,GAAGe,EAAE;UAAET,QAAQ;UAAEE,SAAS,EAAEoC,IAAI,CAACC,KAAK,CAACvC,QAAQ,GAAG,GAAG;QAAE,CAAC,GAC1DS,EACN,CAAC,CAAC;QAEF,IAAIT,QAAQ,IAAI,GAAG,EAAE;UACnBU,aAAa,CAACJ,QAAQ,CAAC;UACvB/D,aAAa,CAAC8D,IAAI,IAAIA,IAAI,CAACG,GAAG,CAACC,EAAE,IAC/BA,EAAE,CAACf,EAAE,KAAKD,YAAY,CAACC,EAAE,GACrB;YAAE,GAAGe,EAAE;YAAEV,MAAM,EAAE,WAAW;YAAEY,WAAW,EAAE,IAAIhB,IAAI,CAAC;UAAE,CAAC,GACvDc,EACN,CAAC,CAAC;UACF5D,UAAU,CAAC,2BAA2BqF,WAAW,CAAC5E,cAAc,CAAC,EAAE,CAAC;QACtE;MACF,CAAC,EAAE,GAAG,CAAC;IAET,CAAC,CAAC,OAAOwD,GAAQ,EAAE;MACjBnE,QAAQ,CAACmE,GAAG,CAACC,OAAO,IAAI,oBAAoB,CAAC;IAC/C,CAAC,SAAS;MACRtE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+F,aAAa,GAAIzC,MAA+B,IAAK;IACzD,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,oBAAOjE,OAAA,CAACN,WAAW;UAAC0C,IAAI,EAAE,EAAG;UAACF,KAAK,EAAC;QAAS;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAClD,KAAK,QAAQ;QACX,oBAAO1C,OAAA,CAACL,aAAa;UAACyC,IAAI,EAAE,EAAG;UAACF,KAAK,EAAC;QAAS;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpD,KAAK,YAAY;QACf,oBAAO1C,OAAA,CAACJ,SAAS;UAACwC,IAAI,EAAE,EAAG;UAACF,KAAK,EAAC,SAAS;UAACyE,SAAS,EAAC;QAAc;UAAApE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzE;QACE,oBAAO1C,OAAA,CAACJ,SAAS;UAACwC,IAAI,EAAE,EAAG;UAACF,KAAK,EAAC;QAAS;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAClD;EACF,CAAC;EAED,MAAMkE,IAAI,GAAG,CACX;IAAEC,GAAG,EAAE,QAAQ;IAAEC,KAAK,EAAE,aAAa;IAAEC,IAAI,EAAE1H;EAAO,CAAC,EACrD;IAAEwH,GAAG,EAAE,QAAQ;IAAEC,KAAK,EAAE,aAAa;IAAEC,IAAI,EAAEzH;EAAS,CAAC,EACvD;IAAEuH,GAAG,EAAE,cAAc;IAAEC,KAAK,EAAE,cAAc;IAAEC,IAAI,EAAEtH;EAAO,CAAC,EAC5D;IAAEoH,GAAG,EAAE,SAAS;IAAEC,KAAK,EAAE,mBAAmB;IAAEC,IAAI,EAAEvH;EAAS,CAAC,CAC/D;EAED,oBACEQ,OAAA;IAAK2B,KAAK,EAAE;MAAEqF,QAAQ,EAAE,QAAQ;MAAErE,MAAM,EAAE;IAAS,CAAE;IAAAR,QAAA,gBAEnDnC,OAAA;MAAK2B,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfG,cAAc,EAAE,eAAe;QAC/BD,UAAU,EAAE,QAAQ;QACpBO,YAAY,EAAE;MAChB,CAAE;MAAAF,QAAA,gBACAnC,OAAA;QAAAmC,QAAA,gBACEnC,OAAA;UAAI2B,KAAK,EAAE;YACTgB,MAAM,EAAE,YAAY;YACpBC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjBX,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,EAAC;QAEH;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL1C,OAAA;UAAG2B,KAAK,EAAE;YACRgB,MAAM,EAAE,CAAC;YACTT,KAAK,EAAE,SAAS;YAChBU,QAAQ,EAAE;UACZ,CAAE;UAAAT,QAAA,EAAC;QAEH;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN1C,OAAA;QAAK2B,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBmF,GAAG,EAAE,QAAQ;UACblE,OAAO,EAAE,cAAc;UACvBC,UAAU,EAAE,SAAS;UACrBd,KAAK,EAAE,OAAO;UACdgB,YAAY,EAAE,KAAK;UACnBN,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE;QACd,CAAE;QAAAV,QAAA,gBACAnC,OAAA,CAACT,KAAK;UAAC6C,IAAI,EAAE;QAAG;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,oBAErB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL9B,KAAK,iBACJZ,OAAA;MAAK2B,KAAK,EAAE;QACVoB,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,SAAS;QACrBkE,MAAM,EAAE,mBAAmB;QAC3BhE,YAAY,EAAE,KAAK;QACnBhB,KAAK,EAAE,SAAS;QAChBG,YAAY,EAAE,MAAM;QACpBT,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBmF,GAAG,EAAE;MACP,CAAE;MAAA9E,QAAA,gBACAnC,OAAA,CAACL,aAAa;QAACyC,IAAI,EAAE;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAC1B9B,KAAK;IAAA;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEA5B,OAAO,iBACNd,OAAA;MAAK2B,KAAK,EAAE;QACVoB,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,SAAS;QACrBkE,MAAM,EAAE,mBAAmB;QAC3BhE,YAAY,EAAE,KAAK;QACnBhB,KAAK,EAAE,SAAS;QAChBG,YAAY,EAAE,MAAM;QACpBT,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBmF,GAAG,EAAE;MACP,CAAE;MAAA9E,QAAA,gBACAnC,OAAA,CAACN,WAAW;QAAC0C,IAAI,EAAE;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACxB5B,OAAO;IAAA;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,eAGD1C,OAAA;MAAK2B,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfuF,YAAY,EAAE,mBAAmB;QACjC9E,YAAY,EAAE;MAChB,CAAE;MAAAF,QAAA,EACCyE,IAAI,CAAClC,GAAG,CAAE0C,GAAG,IAAK;QACjB,MAAMC,IAAI,GAAGD,GAAG,CAACL,IAAI;QACrB,oBACE/G,OAAA;UAEEsH,OAAO,EAAEA,CAAA,KAAM/G,YAAY,CAAC6G,GAAG,CAACP,GAAU,CAAE;UAC5ClF,KAAK,EAAE;YACLC,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBmF,GAAG,EAAE,QAAQ;YACblE,OAAO,EAAE,aAAa;YACtBmE,MAAM,EAAE,MAAM;YACdlE,UAAU,EAAE,aAAa;YACzBd,KAAK,EAAE5B,SAAS,KAAK8G,GAAG,CAACP,GAAG,GAAG,SAAS,GAAG,SAAS;YACpDM,YAAY,EAAE7G,SAAS,KAAK8G,GAAG,CAACP,GAAG,GAAG,mBAAmB,GAAG,uBAAuB;YACnFU,MAAM,EAAE,SAAS;YACjB3E,QAAQ,EAAE,UAAU;YACpBC,UAAU,EAAE,KAAK;YACjB2E,UAAU,EAAE;UACd,CAAE;UAAArF,QAAA,gBAEFnC,OAAA,CAACqH,IAAI;YAACjF,IAAI,EAAE;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACjB0E,GAAG,CAACN,KAAK;QAAA,GAlBLM,GAAG,CAACP,GAAG;UAAAtE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBN,CAAC;MAEb,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN1C,OAAA;MAAK2B,KAAK,EAAE;QACVqB,UAAU,EAAE,OAAO;QACnBE,YAAY,EAAE,MAAM;QACpBuE,SAAS,EAAE,8BAA8B;QACzC1E,OAAO,EAAE;MACX,CAAE;MAAAZ,QAAA,GACC7B,SAAS,KAAK,QAAQ,iBACrBN,OAAA;QAAAmC,QAAA,gBACEnC,OAAA;UAAI2B,KAAK,EAAE;YAAEgB,MAAM,EAAE,YAAY;YAAEC,QAAQ,EAAE,SAAS;YAAEC,UAAU,EAAE;UAAM,CAAE;UAAAV,QAAA,EAAC;QAE7E;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEL1C,OAAA;UAAK2B,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAE8F,mBAAmB,EAAE,sCAAsC;YAAET,GAAG,EAAE,QAAQ;YAAE5E,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,gBAChInC,OAAA;YAAAmC,QAAA,gBACEnC,OAAA;cAAO2B,KAAK,EAAE;gBAAEC,OAAO,EAAE,OAAO;gBAAEgB,QAAQ,EAAE,UAAU;gBAAEC,UAAU,EAAE,KAAK;gBAAEX,KAAK,EAAE,SAAS;gBAAEG,YAAY,EAAE;cAAS,CAAE;cAAAF,QAAA,EAAC;YAEvH;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR1C,OAAA;cACE+E,KAAK,EAAE/D,UAAW;cAClB2G,QAAQ,EAAGC,CAAC,IAAK3G,aAAa,CAAC2G,CAAC,CAACpE,MAAM,CAACuB,KAAY,CAAE;cACtDpD,KAAK,EAAE;gBAAEkG,KAAK,EAAE,MAAM;gBAAE9E,OAAO,EAAE,SAAS;gBAAEmE,MAAM,EAAE,mBAAmB;gBAAEhE,YAAY,EAAE,KAAK;gBAAEN,QAAQ,EAAE;cAAW,CAAE;cAAAT,QAAA,gBAErHnC,OAAA;gBAAQ+E,KAAK,EAAC,UAAU;gBAAA5C,QAAA,EAAC;cAAQ;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1C1C,OAAA;gBAAQ+E,KAAK,EAAC,eAAe;gBAAA5C,QAAA,EAAC;cAAa;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpD1C,OAAA;gBAAQ+E,KAAK,EAAC,QAAQ;gBAAA5C,QAAA,EAAC;cAAe;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN1C,OAAA;YAAAmC,QAAA,gBACEnC,OAAA;cAAO2B,KAAK,EAAE;gBAAEC,OAAO,EAAE,OAAO;gBAAEgB,QAAQ,EAAE,UAAU;gBAAEC,UAAU,EAAE,KAAK;gBAAEX,KAAK,EAAE,SAAS;gBAAEG,YAAY,EAAE;cAAS,CAAE;cAAAF,QAAA,EAAC;YAEvH;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR1C,OAAA;cACE8H,GAAG,EAAEzH,YAAa;cAClB2D,IAAI,EAAC,MAAM;cACX+D,MAAM,EAAC,kBAAkB;cACzBJ,QAAQ,EAAEvE,gBAAiB;cAC3BzB,KAAK,EAAE;gBAAEkG,KAAK,EAAE,MAAM;gBAAE9E,OAAO,EAAE,SAAS;gBAAEmE,MAAM,EAAE,mBAAmB;gBAAEhE,YAAY,EAAE,KAAK;gBAAEN,QAAQ,EAAE;cAAW;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1C,OAAA;UACEsH,OAAO,EAAE5D,YAAa;UACtBsE,QAAQ,EAAEtH,OAAO,IAAI,CAACY,YAAa;UACnCK,KAAK,EAAE;YACLoB,OAAO,EAAE,gBAAgB;YACzBC,UAAU,EAAEtC,OAAO,IAAI,CAACY,YAAY,GAAG,SAAS,GAAG,SAAS;YAC5DY,KAAK,EAAE,OAAO;YACdgF,MAAM,EAAE,MAAM;YACdhE,YAAY,EAAE,KAAK;YACnBN,QAAQ,EAAE,UAAU;YACpBC,UAAU,EAAE,KAAK;YACjB0E,MAAM,EAAE7G,OAAO,IAAI,CAACY,YAAY,GAAG,aAAa,GAAG,SAAS;YAC5DM,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBmF,GAAG,EAAE;UACP,CAAE;UAAA9E,QAAA,gBAEFnC,OAAA,CAACX,MAAM;YAAC+C,IAAI,EAAE;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACnBhC,OAAO,GAAG,cAAc,GAAG,aAAa;QAAA;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAEApC,SAAS,KAAK,QAAQ,iBACrBN,OAAA;QAAAmC,QAAA,gBACEnC,OAAA;UAAI2B,KAAK,EAAE;YAAEgB,MAAM,EAAE,YAAY;YAAEC,QAAQ,EAAE,SAAS;YAAEC,UAAU,EAAE;UAAM,CAAE;UAAAV,QAAA,EAAC;QAE7E;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEL1C,OAAA;UAAK2B,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAE8F,mBAAmB,EAAE,sCAAsC;YAAET,GAAG,EAAE,QAAQ;YAAE5E,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,gBAChInC,OAAA;YAAAmC,QAAA,gBACEnC,OAAA;cAAO2B,KAAK,EAAE;gBAAEC,OAAO,EAAE,OAAO;gBAAEgB,QAAQ,EAAE,UAAU;gBAAEC,UAAU,EAAE,KAAK;gBAAEX,KAAK,EAAE,SAAS;gBAAEG,YAAY,EAAE;cAAS,CAAE;cAAAF,QAAA,EAAC;YAEvH;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR1C,OAAA;cACE+E,KAAK,EAAE7D,UAAW;cAClByG,QAAQ,EAAGC,CAAC,IAAKzG,aAAa,CAACyG,CAAC,CAACpE,MAAM,CAACuB,KAAY,CAAE;cACtDpD,KAAK,EAAE;gBAAEkG,KAAK,EAAE,MAAM;gBAAE9E,OAAO,EAAE,SAAS;gBAAEmE,MAAM,EAAE,mBAAmB;gBAAEhE,YAAY,EAAE,KAAK;gBAAEN,QAAQ,EAAE;cAAW,CAAE;cAAAT,QAAA,gBAErHnC,OAAA;gBAAQ+E,KAAK,EAAC,UAAU;gBAAA5C,QAAA,EAAC;cAAQ;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1C1C,OAAA;gBAAQ+E,KAAK,EAAC,eAAe;gBAAA5C,QAAA,EAAC;cAAa;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpD1C,OAAA;gBAAQ+E,KAAK,EAAC,QAAQ;gBAAA5C,QAAA,EAAC;cAAe;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN1C,OAAA;YAAAmC,QAAA,gBACEnC,OAAA;cAAO2B,KAAK,EAAE;gBAAEC,OAAO,EAAE,OAAO;gBAAEgB,QAAQ,EAAE,UAAU;gBAAEC,UAAU,EAAE,KAAK;gBAAEX,KAAK,EAAE,SAAS;gBAAEG,YAAY,EAAE;cAAS,CAAE;cAAAF,QAAA,EAAC;YAEvH;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR1C,OAAA;cACE+E,KAAK,EAAE3D,YAAa;cACpBuG,QAAQ,EAAGC,CAAC,IAAKvG,eAAe,CAACuG,CAAC,CAACpE,MAAM,CAACuB,KAAY,CAAE;cACxDpD,KAAK,EAAE;gBAAEkG,KAAK,EAAE,MAAM;gBAAE9E,OAAO,EAAE,SAAS;gBAAEmE,MAAM,EAAE,mBAAmB;gBAAEhE,YAAY,EAAE,KAAK;gBAAEN,QAAQ,EAAE;cAAW,CAAE;cAAAT,QAAA,gBAErHnC,OAAA;gBAAQ+E,KAAK,EAAC,KAAK;gBAAA5C,QAAA,EAAC;cAAG;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChC1C,OAAA;gBAAQ+E,KAAK,EAAC,MAAM;gBAAA5C,QAAA,EAAC;cAAY;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1C1C,OAAA;gBAAQ+E,KAAK,EAAC,MAAM;gBAAA5C,QAAA,EAAC;cAAI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1C,OAAA;UACEsH,OAAO,EAAEpC,YAAa;UACtB8C,QAAQ,EAAEtH,OAAQ;UAClBiB,KAAK,EAAE;YACLoB,OAAO,EAAE,gBAAgB;YACzBC,UAAU,EAAEtC,OAAO,GAAG,SAAS,GAAG,SAAS;YAC3CwB,KAAK,EAAE,OAAO;YACdgF,MAAM,EAAE,MAAM;YACdhE,YAAY,EAAE,KAAK;YACnBN,QAAQ,EAAE,UAAU;YACpBC,UAAU,EAAE,KAAK;YACjB0E,MAAM,EAAE7G,OAAO,GAAG,aAAa,GAAG,SAAS;YAC3CkB,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBmF,GAAG,EAAE;UACP,CAAE;UAAA9E,QAAA,gBAEFnC,OAAA,CAACV,QAAQ;YAAC8C,IAAI,EAAE;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACrBhC,OAAO,GAAG,cAAc,GAAG,aAAa;QAAA;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAEApC,SAAS,KAAK,cAAc,iBAC3BN,OAAA;QAAAmC,QAAA,gBACEnC,OAAA;UAAI2B,KAAK,EAAE;YAAEgB,MAAM,EAAE,YAAY;YAAEC,QAAQ,EAAE,SAAS;YAAEC,UAAU,EAAE;UAAM,CAAE;UAAAV,QAAA,EAAC;QAE7E;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEL1C,OAAA;UAAK2B,KAAK,EAAE;YAAEU,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,gBACnCnC,OAAA;YAAO2B,KAAK,EAAE;cAAEC,OAAO,EAAE,OAAO;cAAEgB,QAAQ,EAAE,UAAU;cAAEC,UAAU,EAAE,KAAK;cAAEX,KAAK,EAAE,SAAS;cAAEG,YAAY,EAAE;YAAS,CAAE;YAAAF,QAAA,EAAC;UAEvH;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR1C,OAAA;YACE+E,KAAK,EAAEvD,cAAe;YACtBmG,QAAQ,EAAGC,CAAC,IAAKnG,iBAAiB,CAACmG,CAAC,CAACpE,MAAM,CAACuB,KAAY,CAAE;YAC1DpD,KAAK,EAAE;cAAEkG,KAAK,EAAE,MAAM;cAAEb,QAAQ,EAAE,OAAO;cAAEjE,OAAO,EAAE,SAAS;cAAEmE,MAAM,EAAE,mBAAmB;cAAEhE,YAAY,EAAE,KAAK;cAAEN,QAAQ,EAAE;YAAW,CAAE;YAAAT,QAAA,gBAExInC,OAAA;cAAQ+E,KAAK,EAAC,0BAA0B;cAAA5C,QAAA,EAAC;YAAmC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrF1C,OAAA;cAAQ+E,KAAK,EAAC,2BAA2B;cAAA5C,QAAA,EAAC;YAAoC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvF1C,OAAA;cAAQ+E,KAAK,EAAC,oBAAoB;cAAA5C,QAAA,EAAC;YAA8B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN1C,OAAA;UACEsH,OAAO,EAAEnB,gBAAiB;UAC1B6B,QAAQ,EAAEtH,OAAQ;UAClBiB,KAAK,EAAE;YACLoB,OAAO,EAAE,gBAAgB;YACzBC,UAAU,EAAEtC,OAAO,GAAG,SAAS,GAAG,SAAS;YAC3CwB,KAAK,EAAE,OAAO;YACdgF,MAAM,EAAE,MAAM;YACdhE,YAAY,EAAE,KAAK;YACnBN,QAAQ,EAAE,UAAU;YACpBC,UAAU,EAAE,KAAK;YACjB0E,MAAM,EAAE7G,OAAO,GAAG,aAAa,GAAG,SAAS;YAC3CkB,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBmF,GAAG,EAAE;UACP,CAAE;UAAA9E,QAAA,gBAEFnC,OAAA,CAACP,MAAM;YAAC2C,IAAI,EAAE;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACnBhC,OAAO,GAAG,eAAe,GAAG,qBAAqB;QAAA;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAEApC,SAAS,KAAK,SAAS,iBACtBN,OAAA;QAAAmC,QAAA,gBACEnC,OAAA;UAAI2B,KAAK,EAAE;YAAEgB,MAAM,EAAE,YAAY;YAAEC,QAAQ,EAAE,SAAS;YAAEC,UAAU,EAAE;UAAM,CAAE;UAAAV,QAAA,EAAC;QAE7E;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAEJlC,UAAU,CAACyH,MAAM,KAAK,CAAC,gBACtBjI,OAAA;UAAK2B,KAAK,EAAE;YAAEM,SAAS,EAAE,QAAQ;YAAEc,OAAO,EAAE,MAAM;YAAEb,KAAK,EAAE;UAAU,CAAE;UAAAC,QAAA,gBACrEnC,OAAA,CAACR,QAAQ;YAAC4C,IAAI,EAAE,EAAG;YAACT,KAAK,EAAE;cAAEU,YAAY,EAAE,MAAM;cAAEC,OAAO,EAAE;YAAI;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrE1C,OAAA;YAAAmC,QAAA,EAAG;UAA2B;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,gBAEN1C,OAAA;UAAK2B,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,aAAa,EAAE,QAAQ;YAAEoF,GAAG,EAAE;UAAO,CAAE;UAAA9E,QAAA,EACnE3B,UAAU,CAACkE,GAAG,CAAEwD,SAAS,iBACxBlI,OAAA;YAEE2B,KAAK,EAAE;cACLoB,OAAO,EAAE,MAAM;cACfmE,MAAM,EAAE,mBAAmB;cAC3BhE,YAAY,EAAE,KAAK;cACnBtB,OAAO,EAAE,MAAM;cACfG,cAAc,EAAE,eAAe;cAC/BD,UAAU,EAAE;YACd,CAAE;YAAAK,QAAA,gBAEFnC,OAAA;cAAK2B,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEmF,GAAG,EAAE;cAAO,CAAE;cAAA9E,QAAA,GAChEuE,aAAa,CAACwB,SAAS,CAACjE,MAAM,CAAC,eAChCjE,OAAA;gBAAAmC,QAAA,gBACEnC,OAAA;kBAAK2B,KAAK,EAAE;oBAAEkB,UAAU,EAAE,KAAK;oBAAEX,KAAK,EAAE;kBAAU,CAAE;kBAAAC,QAAA,EACjD+F,SAAS,CAAClE,IAAI,CAACmE,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAChD,WAAW,CAAC;gBAAC;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,eACN1C,OAAA;kBAAK2B,KAAK,EAAE;oBAAEiB,QAAQ,EAAE,UAAU;oBAAEV,KAAK,EAAE;kBAAU,CAAE;kBAAAC,QAAA,EACpD+F,SAAS,CAAC5D,SAAS,CAAC8D,cAAc,CAAC;gBAAC;kBAAA7F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN1C,OAAA;cAAK2B,KAAK,EAAE;gBAAEM,SAAS,EAAE;cAAQ,CAAE;cAAAE,QAAA,gBACjCnC,OAAA;gBAAK2B,KAAK,EAAE;kBAAEiB,QAAQ,EAAE,UAAU;kBAAEC,UAAU,EAAE,KAAK;kBAAEX,KAAK,EAAE;gBAAU,CAAE;gBAAAC,QAAA,GACvE+F,SAAS,CAAC9D,SAAS,EAAC,GAAC,EAAC8D,SAAS,CAAC/D,KAAK;cAAA;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACN1C,OAAA;gBAAK2B,KAAK,EAAE;kBAAEiB,QAAQ,EAAE,SAAS;kBAAEV,KAAK,EAAE;gBAAU,CAAE;gBAAAC,QAAA,EACnD+F,SAAS,CAACjE,MAAM,KAAK,YAAY,GAAG,GAAGiE,SAAS,CAAChE,QAAQ,GAAG,GAAGgE,SAAS,CAACjE;cAAM;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GA7BDwF,SAAS,CAACtE,EAAE;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA8Bd,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxC,EAAA,CA5lBID,cAAwB;EAAA,QACXJ,YAAY,EACTC,cAAc;AAAA;AAAAuI,EAAA,GAF9BpI,cAAwB;AA8lB9B,eAAeA,cAAc;AAAC,IAAAoI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}