{
  error: Error: Route.post() requires a callback function but got a [object Undefined]
      at Route.<computed> [as post] (D:\online-e-bulletin-reactjs\BACK-VCBA-E-BULLETIN-BOARD\node_modules\express\lib\router\route.js:216:15)
      at proto.<computed> [as post] (D:\online-e-bulletin-reactjs\BACK-VCBA-E-BULLETIN-BOARD\node_modules\express\lib\router\index.js:521:19)
      at Object.<anonymous> (D:\online-e-bulletin-reactjs\BACK-VCBA-E-BULLETIN-BOARD\src\routes\adminManagementRoutes.js:180:8)
      at Module._compile (node:internal/modules/cjs/loader:1469:14)
      at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)
      at Module.load (node:internal/modules/cjs/loader:1288:32)
      at Module._load (node:internal/modules/cjs/loader:1104:12)
      at Module.require (node:internal/modules/cjs/loader:1311:19)
      at require (node:internal/modules/helpers:179:18)
      at Object.<anonymous> (D:\online-e-bulletin-reactjs\BACK-VCBA-E-BULLETIN-BOARD\src\server.js:162:34),
  level: 'error',
  message: 'uncaughtException: Route.post() requires a callback function but got a [object Undefined]\n' +
    'Error: Route.post() requires a callback function but got a [object Undefined]\n' +
    '    at Route.<computed> [as post] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\route.js:216:15)\n' +
    '    at proto.<computed> [as post] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:521:19)\n' +
    '    at Object.<anonymous> (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\routes\\adminManagementRoutes.js:180:8)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n' +
    '    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1288:32)\n' +
    '    at Module._load (node:internal/modules/cjs/loader:1104:12)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1311:19)\n' +
    '    at require (node:internal/modules/helpers:179:18)\n' +
    '    at Object.<anonymous> (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\server.js:162:34)',
  stack: 'Error: Route.post() requires a callback function but got a [object Undefined]\n' +
    '    at Route.<computed> [as post] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\route.js:216:15)\n' +
    '    at proto.<computed> [as post] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:521:19)\n' +
    '    at Object.<anonymous> (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\routes\\adminManagementRoutes.js:180:8)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n' +
    '    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1288:32)\n' +
    '    at Module._load (node:internal/modules/cjs/loader:1104:12)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1311:19)\n' +
    '    at require (node:internal/modules/helpers:179:18)\n' +
    '    at Object.<anonymous> (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\server.js:162:34)',
  exception: true,
  date: 'Wed Aug 06 2025 02:10:28 GMT+0800 (Philippine Standard Time)',
  process: {
    pid: 24632,
    uid: null,
    gid: null,
    cwd: 'D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v20.18.3',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\server.js'
    ],
    memoryUsage: {
      rss: 91230208,
      heapTotal: 61288448,
      heapUsed: 39491048,
      external: 2245366,
      arrayBuffers: 16799
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 43309.328 },
  trace: [
    {
      column: 15,
      file: 'D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\route.js',
      function: 'Route.<computed> [as post]',
      line: 216,
      method: '<computed> [as post]',
      native: false
    },
    {
      column: 19,
      file: 'D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js',
      function: 'proto.<computed> [as post]',
      line: 521,
      method: '<computed> [as post]',
      native: false
    },
    {
      column: 8,
      file: 'D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\routes\\adminManagementRoutes.js',
      function: null,
      line: 180,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1469,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._extensions..js',
      line: 1548,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1288,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._load',
      line: 1104,
      method: '_load',
      native: false
    },
    {
      column: 19,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1311,
      method: 'require',
      native: false
    },
    {
      column: 18,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 179,
      method: null,
      native: false
    },
    {
      column: 34,
      file: 'D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\server.js',
      function: null,
      line: 162,
      method: null,
      native: false
    }
  ],
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-08-06 02:10:28'
}
