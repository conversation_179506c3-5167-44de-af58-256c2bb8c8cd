{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\admin\\\\Settings.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\nimport { Lock, CheckCircle, Eye, EyeOff, AlertCircle } from 'lucide-react';\nimport ProfilePictureUpload from '../../components/admin/ProfilePictureUpload';\nimport { AdminAuthService } from '../../services/admin-auth.service';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Settings = () => {\n  _s();\n  const {\n    user,\n    checkAuthStatus\n  } = useAdminAuth();\n  const permissions = usePermissions(user);\n  // Removed activeTab state - showing profile settings directly\n  const [isUploadingPicture, setIsUploadingPicture] = useState(false);\n\n  // Password change state\n  const [passwordData, setPasswordData] = useState({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: ''\n  });\n  const [showPasswords, setShowPasswords] = useState({\n    current: false,\n    new: false,\n    confirm: false\n  });\n  const [passwordErrors, setPasswordErrors] = useState([]);\n  const [passwordSuccess, setPasswordSuccess] = useState(null);\n  const [isChangingPassword, setIsChangingPassword] = useState(false);\n\n  // Removed tabs - showing profile settings directly\n\n  // Profile picture handlers\n  const handleProfilePictureUpload = async file => {\n    setIsUploadingPicture(true);\n    try {\n      console.log('🔍 Settings - Starting profile picture upload...');\n      const result = await AdminAuthService.uploadProfilePicture(file);\n      console.log('🔍 Settings - Upload result:', result);\n\n      // Refresh user data to get updated profile picture\n      console.log('🔍 Settings - Refreshing auth status...');\n      await checkAuthStatus();\n      console.log('🔍 Settings - Auth status refreshed, new user:', user);\n    } catch (error) {\n      console.error('❌ Settings - Upload failed:', error);\n      throw new Error(error.message || 'Failed to upload profile picture');\n    } finally {\n      setIsUploadingPicture(false);\n    }\n  };\n  const handleProfilePictureRemove = async () => {\n    setIsUploadingPicture(true);\n    try {\n      await AdminAuthService.removeProfilePicture();\n      // Refresh user data to remove profile picture\n      await checkAuthStatus();\n    } catch (error) {\n      throw new Error(error.message || 'Failed to remove profile picture');\n    } finally {\n      setIsUploadingPicture(false);\n    }\n  };\n\n  // Password validation\n  const validatePassword = password => {\n    const errors = [];\n    if (password.length < 8) {\n      errors.push('Password must be at least 8 characters long');\n    }\n    if (!/(?=.*[a-z])/.test(password)) {\n      errors.push('Password must contain at least one lowercase letter');\n    }\n    if (!/(?=.*[A-Z])/.test(password)) {\n      errors.push('Password must contain at least one uppercase letter');\n    }\n    if (!/(?=.*\\d)/.test(password)) {\n      errors.push('Password must contain at least one number');\n    }\n    if (!/(?=.*[@$!%*?&])/.test(password)) {\n      errors.push('Password must contain at least one special character (@$!%*?&)');\n    }\n    return errors;\n  };\n\n  // Handle password input change\n  const handlePasswordChange = (field, value) => {\n    setPasswordData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    setPasswordErrors([]);\n    setPasswordSuccess(null);\n  };\n\n  // Toggle password visibility\n  const togglePasswordVisibility = field => {\n    setShowPasswords(prev => ({\n      ...prev,\n      [field]: !prev[field]\n    }));\n  };\n\n  // Handle password change submission\n  const handlePasswordSubmit = async e => {\n    e.preventDefault();\n    setPasswordErrors([]);\n    setPasswordSuccess(null);\n\n    // Validation\n    const errors = [];\n    if (!passwordData.currentPassword) {\n      errors.push('Current password is required');\n    }\n    if (!passwordData.newPassword) {\n      errors.push('New password is required');\n    } else {\n      const passwordValidationErrors = validatePassword(passwordData.newPassword);\n      errors.push(...passwordValidationErrors);\n    }\n    if (!passwordData.confirmPassword) {\n      errors.push('Password confirmation is required');\n    } else if (passwordData.newPassword !== passwordData.confirmPassword) {\n      errors.push('New password and confirmation do not match');\n    }\n    if (passwordData.currentPassword === passwordData.newPassword) {\n      errors.push('New password must be different from current password');\n    }\n    if (errors.length > 0) {\n      setPasswordErrors(errors);\n      return;\n    }\n    setIsChangingPassword(true);\n    try {\n      // Call API to change password\n      await AdminAuthService.changePassword({\n        currentPassword: passwordData.currentPassword,\n        newPassword: passwordData.newPassword\n      });\n      setPasswordSuccess('Password changed successfully!');\n      setPasswordData({\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n      });\n\n      // Clear success message after 5 seconds\n      setTimeout(() => setPasswordSuccess(null), 5000);\n    } catch (error) {\n      setPasswordErrors([error.message || 'Failed to change password']);\n    } finally {\n      setIsChangingPassword(false);\n    }\n  };\n  const renderProfileSettings = () => {\n    var _user$firstName, _user$lastName;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-card hover-lift\",\n        style: {\n          background: 'white',\n          borderRadius: '16px',\n          padding: '2rem',\n          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n          border: '1px solid #e8f5e8'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-layout\",\n          style: {\n            display: 'flex',\n            gap: '2rem',\n            alignItems: 'flex-start',\n            marginBottom: '2rem',\n            flexWrap: 'wrap'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flexShrink: 0\n            },\n            children: /*#__PURE__*/_jsxDEV(ProfilePictureUpload, {\n              currentPicture: user !== null && user !== void 0 && user.profilePicture ? `http://localhost:5000${user.profilePicture}` : undefined,\n              userInitials: `${(user === null || user === void 0 ? void 0 : (_user$firstName = user.firstName) === null || _user$firstName === void 0 ? void 0 : _user$firstName.charAt(0)) || ''}${(user === null || user === void 0 ? void 0 : (_user$lastName = user.lastName) === null || _user$lastName === void 0 ? void 0 : _user$lastName.charAt(0)) || ''}`,\n              onUpload: handleProfilePictureUpload,\n              onRemove: handleProfilePictureRemove,\n              isLoading: isUploadingPicture,\n              size: 140\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"profile-details\",\n            style: {\n              flex: 1,\n              minWidth: '300px',\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '1.5rem',\n              paddingTop: '0.5rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '1.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#111827',\n                  fontSize: '1.5rem',\n                  fontWeight: '700',\n                  marginBottom: '0.5rem'\n                },\n                children: [`${(user === null || user === void 0 ? void 0 : user.firstName) || ''} ${(user === null || user === void 0 ? void 0 : user.lastName) || ''}`, (user === null || user === void 0 ? void 0 : user.suffix) && /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: '400',\n                    color: '#6b7280'\n                  },\n                  children: [\" \", user.suffix]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 34\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#6b7280',\n                  fontSize: '1rem',\n                  fontWeight: '500'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: (user === null || user === void 0 ? void 0 : user.email) || 'Not provided'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-card hover-lift\",\n        style: {\n          background: 'white',\n          borderRadius: '16px',\n          padding: '2rem',\n          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n          border: '1px solid #e8f5e8'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 1.5rem 0',\n            color: '#2d5016',\n            fontSize: '1.25rem',\n            fontWeight: '600',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Lock, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 11\n          }, this), \"Change Password\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handlePasswordSubmit,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '1.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  color: '#374151',\n                  marginBottom: '0.5rem'\n                },\n                children: \"Current Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'relative'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: showPasswords.current ? 'text' : 'password',\n                  value: passwordData.currentPassword,\n                  onChange: e => handlePasswordChange('currentPassword', e.target.value),\n                  required: true,\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem 3rem 0.75rem 1rem',\n                    border: '2px solid #e8f5e8',\n                    borderRadius: '8px',\n                    fontSize: '1rem',\n                    transition: 'border-color 0.2s ease',\n                    outline: 'none'\n                  },\n                  onFocus: e => e.target.style.borderColor = '#22c55e',\n                  onBlur: e => e.target.style.borderColor = '#e8f5e8',\n                  placeholder: \"Enter your current password\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => togglePasswordVisibility('current'),\n                  style: {\n                    position: 'absolute',\n                    right: '0.75rem',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    background: 'none',\n                    border: 'none',\n                    cursor: 'pointer',\n                    color: '#6b7280',\n                    padding: '0.25rem'\n                  },\n                  children: showPasswords.current ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                    size: 20\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 291,\n                    columnNumber: 44\n                  }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                    size: 20\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 291,\n                    columnNumber: 67\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  color: '#374151',\n                  marginBottom: '0.5rem'\n                },\n                children: \"New Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'relative'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: showPasswords.new ? 'text' : 'password',\n                  value: passwordData.newPassword,\n                  onChange: e => handlePasswordChange('newPassword', e.target.value),\n                  required: true,\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem 3rem 0.75rem 1rem',\n                    border: '2px solid #e8f5e8',\n                    borderRadius: '8px',\n                    fontSize: '1rem',\n                    transition: 'border-color 0.2s ease',\n                    outline: 'none'\n                  },\n                  onFocus: e => e.target.style.borderColor = '#22c55e',\n                  onBlur: e => e.target.style.borderColor = '#e8f5e8',\n                  placeholder: \"Enter your new password\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => togglePasswordVisibility('new'),\n                  style: {\n                    position: 'absolute',\n                    right: '0.75rem',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    background: 'none',\n                    border: 'none',\n                    cursor: 'pointer',\n                    color: '#6b7280',\n                    padding: '0.25rem'\n                  },\n                  children: showPasswords.new ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                    size: 20\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 341,\n                    columnNumber: 40\n                  }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                    size: 20\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 341,\n                    columnNumber: 63\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  color: '#374151',\n                  marginBottom: '0.5rem'\n                },\n                children: \"Confirm New Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'relative'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: showPasswords.confirm ? 'text' : 'password',\n                  value: passwordData.confirmPassword,\n                  onChange: e => handlePasswordChange('confirmPassword', e.target.value),\n                  required: true,\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem 3rem 0.75rem 1rem',\n                    border: '2px solid #e8f5e8',\n                    borderRadius: '8px',\n                    fontSize: '1rem',\n                    transition: 'border-color 0.2s ease',\n                    outline: 'none'\n                  },\n                  onFocus: e => e.target.style.borderColor = '#22c55e',\n                  onBlur: e => e.target.style.borderColor = '#e8f5e8',\n                  placeholder: \"Confirm your new password\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => togglePasswordVisibility('confirm'),\n                  style: {\n                    position: 'absolute',\n                    right: '0.75rem',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    background: 'none',\n                    border: 'none',\n                    cursor: 'pointer',\n                    color: '#6b7280',\n                    padding: '0.25rem'\n                  },\n                  children: showPasswords.confirm ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                    size: 20\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 391,\n                    columnNumber: 44\n                  }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                    size: 20\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 391,\n                    columnNumber: 67\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#f0fdf4',\n                border: '1px solid #bbf7d0',\n                borderRadius: '8px',\n                padding: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                style: {\n                  margin: '0 0 0.5rem 0',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  color: '#16a34a'\n                },\n                children: \"Password Requirements:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                style: {\n                  margin: 0,\n                  paddingLeft: '1.25rem',\n                  fontSize: '0.75rem',\n                  color: '#16a34a',\n                  lineHeight: '1.5'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"At least 8 characters long\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Contains uppercase and lowercase letters\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Contains at least one number\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Contains at least one special character (@$!%*?&)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 13\n            }, this), passwordErrors.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#fef2f2',\n                border: '1px solid #fecaca',\n                borderRadius: '8px',\n                padding: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  marginBottom: '0.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 16,\n                  color: \"#dc2626\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '0.875rem',\n                    fontWeight: '600',\n                    color: '#dc2626'\n                  },\n                  children: \"Please fix the following errors:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                style: {\n                  margin: 0,\n                  paddingLeft: '1.25rem',\n                  fontSize: '0.75rem',\n                  color: '#dc2626',\n                  lineHeight: '1.5'\n                },\n                children: passwordErrors.map((error, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: error\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 15\n            }, this), passwordSuccess && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: '#f0fdf4',\n                border: '1px solid #bbf7d0',\n                borderRadius: '8px',\n                padding: '1rem',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                size: 16,\n                color: \"#16a34a\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 473,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  color: '#16a34a'\n                },\n                children: passwordSuccess\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: isChangingPassword,\n              style: {\n                background: isChangingPassword ? '#9ca3af' : 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '8px',\n                padding: '0.875rem 2rem',\n                fontSize: '1rem',\n                fontWeight: '600',\n                cursor: isChangingPassword ? 'not-allowed' : 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                gap: '0.5rem',\n                transition: 'all 0.2s ease',\n                boxShadow: isChangingPassword ? 'none' : '0 2px 8px rgba(34, 197, 94, 0.2)',\n                alignSelf: 'flex-start'\n              },\n              onMouseEnter: e => {\n                if (!isChangingPassword) {\n                  e.currentTarget.style.transform = 'translateY(-1px)';\n                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n                }\n              },\n              onMouseLeave: e => {\n                if (!isChangingPassword) {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = '0 2px 8px rgba(34, 197, 94, 0.2)';\n                }\n              },\n              children: isChangingPassword ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '16px',\n                    height: '16px',\n                    border: '2px solid #ffffff',\n                    borderTop: '2px solid transparent',\n                    borderRadius: '50%',\n                    animation: 'spin 1s linear infinite'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 522,\n                  columnNumber: 19\n                }, this), \"Changing Password...\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Lock, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 534,\n                  columnNumber: 19\n                }, this), \"Change Password\"]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 5\n    }, this);\n  };\n\n  // Removed renderSystemSettings function - showing profile settings directly\n\n  // Removed renderContent function - showing profile settings directly\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @media (max-width: 768px) {\n          .profile-layout {\n            flex-direction: column !important;\n            align-items: center !important;\n            text-align: center;\n          }\n\n          .profile-details {\n            min-width: unset !important;\n            width: 100% !important;\n          }\n\n          .profile-grid {\n            grid-template-columns: 1fr !important;\n          }\n\n          .role-status-grid {\n            grid-template-columns: 1fr !important;\n          }\n        }\n\n        @media (max-width: 640px) {\n          .settings-container {\n            padding: 1rem !important;\n          }\n\n          .settings-card {\n            padding: 1.5rem !important;\n          }\n\n          .tab-container {\n            padding: 1rem !important;\n          }\n\n          .tab-button {\n            padding: 0.5rem 1rem !important;\n            font-size: 0.875rem !important;\n          }\n        }\n\n        .fade-in {\n          animation: fadeInUp 0.5s ease-out;\n        }\n\n        @keyframes fadeInUp {\n          0% {\n            opacity: 0;\n            transform: translateY(20px);\n          }\n          100% {\n            opacity: 1;\n            transform: translateY(0);\n          }\n        }\n\n        .hover-lift {\n          transition: all 0.3s ease;\n        }\n\n        .hover-lift:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12) !important;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 552,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fade-in\",\n      children: renderProfileSettings()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 619,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 550,\n    columnNumber: 5\n  }, this);\n};\n_s(Settings, \"A+CGQg3/9Cco007RouWKgSvQ1zQ=\", false, function () {\n  return [useAdminAuth, usePermissions];\n});\n_c = Settings;\nexport default Settings;\nvar _c;\n$RefreshReg$(_c, \"Settings\");", "map": {"version": 3, "names": ["React", "useState", "useAdminAuth", "usePermissions", "Lock", "CheckCircle", "Eye", "Eye<PERSON>ff", "AlertCircle", "ProfilePictureUpload", "AdminAuthService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Settings", "_s", "user", "checkAuthStatus", "permissions", "isUploadingPicture", "setIsUploadingPicture", "passwordData", "setPasswordData", "currentPassword", "newPassword", "confirmPassword", "showPasswords", "setShowPasswords", "current", "new", "confirm", "passwordErrors", "setPasswordErrors", "passwordSuccess", "setPasswordSuccess", "isChangingPassword", "setIsChangingPassword", "handleProfilePictureUpload", "file", "console", "log", "result", "uploadProfilePicture", "error", "Error", "message", "handleProfilePictureRemove", "removeProfilePicture", "validatePassword", "password", "errors", "length", "push", "test", "handlePasswordChange", "field", "value", "prev", "togglePasswordVisibility", "handlePasswordSubmit", "e", "preventDefault", "passwordValidationErrors", "changePassword", "setTimeout", "renderProfileSettings", "_user$firstName", "_user$lastName", "style", "display", "flexDirection", "gap", "children", "className", "background", "borderRadius", "padding", "boxShadow", "border", "alignItems", "marginBottom", "flexWrap", "flexShrink", "currentPicture", "profilePicture", "undefined", "userInitials", "firstName", "char<PERSON>t", "lastName", "onUpload", "onRemove", "isLoading", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flex", "min<PERSON><PERSON><PERSON>", "paddingTop", "color", "fontSize", "fontWeight", "suffix", "email", "margin", "onSubmit", "position", "type", "onChange", "target", "required", "width", "transition", "outline", "onFocus", "borderColor", "onBlur", "placeholder", "onClick", "right", "top", "transform", "cursor", "paddingLeft", "lineHeight", "map", "index", "disabled", "justifyContent", "alignSelf", "onMouseEnter", "currentTarget", "onMouseLeave", "height", "borderTop", "animation", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/admin/Settings.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\nimport { User, Settings as SettingsIcon, Lock, Bell, CheckCircle, Eye, EyeOff, AlertCircle } from 'lucide-react';\nimport ProfilePictureUpload from '../../components/admin/ProfilePictureUpload';\nimport { AdminAuthService } from '../../services/admin-auth.service';\n\nconst Settings: React.FC = () => {\n  const { user, checkAuthStatus } = useAdminAuth();\n  const permissions = usePermissions(user);\n  // Removed activeTab state - showing profile settings directly\n  const [isUploadingPicture, setIsUploadingPicture] = useState(false);\n\n  // Password change state\n  const [passwordData, setPasswordData] = useState({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: ''\n  });\n  const [showPasswords, setShowPasswords] = useState({\n    current: false,\n    new: false,\n    confirm: false\n  });\n  const [passwordErrors, setPasswordErrors] = useState<string[]>([]);\n  const [passwordSuccess, setPasswordSuccess] = useState<string | null>(null);\n  const [isChangingPassword, setIsChangingPassword] = useState(false);\n\n  // Removed tabs - showing profile settings directly\n\n  // Profile picture handlers\n  const handleProfilePictureUpload = async (file: File) => {\n    setIsUploadingPicture(true);\n    try {\n      console.log('🔍 Settings - Starting profile picture upload...');\n      const result = await AdminAuthService.uploadProfilePicture(file);\n      console.log('🔍 Settings - Upload result:', result);\n\n      // Refresh user data to get updated profile picture\n      console.log('🔍 Settings - Refreshing auth status...');\n      await checkAuthStatus();\n      console.log('🔍 Settings - Auth status refreshed, new user:', user);\n    } catch (error: any) {\n      console.error('❌ Settings - Upload failed:', error);\n      throw new Error(error.message || 'Failed to upload profile picture');\n    } finally {\n      setIsUploadingPicture(false);\n    }\n  };\n\n  const handleProfilePictureRemove = async () => {\n    setIsUploadingPicture(true);\n    try {\n      await AdminAuthService.removeProfilePicture();\n      // Refresh user data to remove profile picture\n      await checkAuthStatus();\n    } catch (error: any) {\n      throw new Error(error.message || 'Failed to remove profile picture');\n    } finally {\n      setIsUploadingPicture(false);\n    }\n  };\n\n  // Password validation\n  const validatePassword = (password: string): string[] => {\n    const errors: string[] = [];\n    if (password.length < 8) {\n      errors.push('Password must be at least 8 characters long');\n    }\n    if (!/(?=.*[a-z])/.test(password)) {\n      errors.push('Password must contain at least one lowercase letter');\n    }\n    if (!/(?=.*[A-Z])/.test(password)) {\n      errors.push('Password must contain at least one uppercase letter');\n    }\n    if (!/(?=.*\\d)/.test(password)) {\n      errors.push('Password must contain at least one number');\n    }\n    if (!/(?=.*[@$!%*?&])/.test(password)) {\n      errors.push('Password must contain at least one special character (@$!%*?&)');\n    }\n    return errors;\n  };\n\n  // Handle password input change\n  const handlePasswordChange = (field: keyof typeof passwordData, value: string) => {\n    setPasswordData(prev => ({ ...prev, [field]: value }));\n    setPasswordErrors([]);\n    setPasswordSuccess(null);\n  };\n\n  // Toggle password visibility\n  const togglePasswordVisibility = (field: keyof typeof showPasswords) => {\n    setShowPasswords(prev => ({ ...prev, [field]: !prev[field] }));\n  };\n\n  // Handle password change submission\n  const handlePasswordSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setPasswordErrors([]);\n    setPasswordSuccess(null);\n\n    // Validation\n    const errors: string[] = [];\n\n    if (!passwordData.currentPassword) {\n      errors.push('Current password is required');\n    }\n\n    if (!passwordData.newPassword) {\n      errors.push('New password is required');\n    } else {\n      const passwordValidationErrors = validatePassword(passwordData.newPassword);\n      errors.push(...passwordValidationErrors);\n    }\n\n    if (!passwordData.confirmPassword) {\n      errors.push('Password confirmation is required');\n    } else if (passwordData.newPassword !== passwordData.confirmPassword) {\n      errors.push('New password and confirmation do not match');\n    }\n\n    if (passwordData.currentPassword === passwordData.newPassword) {\n      errors.push('New password must be different from current password');\n    }\n\n    if (errors.length > 0) {\n      setPasswordErrors(errors);\n      return;\n    }\n\n    setIsChangingPassword(true);\n    try {\n      // Call API to change password\n      await AdminAuthService.changePassword({\n        currentPassword: passwordData.currentPassword,\n        newPassword: passwordData.newPassword\n      });\n\n      setPasswordSuccess('Password changed successfully!');\n      setPasswordData({\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n      });\n\n      // Clear success message after 5 seconds\n      setTimeout(() => setPasswordSuccess(null), 5000);\n    } catch (error: any) {\n      setPasswordErrors([error.message || 'Failed to change password']);\n    } finally {\n      setIsChangingPassword(false);\n    }\n  };\n\n  const renderProfileSettings = () => (\n    <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>\n      {/* Profile Information Section */}\n      <div className=\"settings-card hover-lift\" style={{\n        background: 'white',\n        borderRadius: '16px',\n        padding: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      }}>\n\n        {/* Horizontal Layout: Profile Picture + Profile Details */}\n        <div className=\"profile-layout\" style={{\n          display: 'flex',\n          gap: '2rem',\n          alignItems: 'flex-start',\n          marginBottom: '2rem',\n          flexWrap: 'wrap'\n        }}>\n          {/* Profile Picture (Left Side) */}\n          <div style={{ flexShrink: 0 }}>\n            <ProfilePictureUpload\n              currentPicture={user?.profilePicture ? `http://localhost:5000${user.profilePicture}` : undefined}\n              userInitials={`${user?.firstName?.charAt(0) || ''}${user?.lastName?.charAt(0) || ''}`}\n              onUpload={handleProfilePictureUpload}\n              onRemove={handleProfilePictureRemove}\n              isLoading={isUploadingPicture}\n              size={140}\n            />\n          </div>\n\n          {/* Profile Details (Right Side) */}\n          <div className=\"profile-details\" style={{\n            flex: 1,\n            minWidth: '300px',\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '1.5rem',\n            paddingTop: '0.5rem'\n          }}>\n            {/* Name */}\n            <div style={{ marginBottom: '1.5rem' }}>\n              <div style={{\n                color: '#111827',\n                fontSize: '1.5rem',\n                fontWeight: '700',\n                marginBottom: '0.5rem'\n              }}>\n                {`${user?.firstName || ''} ${user?.lastName || ''}`}\n                {user?.suffix && <span style={{ fontWeight: '400', color: '#6b7280' }}> {user.suffix}</span>}\n              </div>\n              <div style={{\n                color: '#6b7280',\n                fontSize: '1rem',\n                fontWeight: '500'\n              }}>\n                  <span>{user?.email || 'Not provided'}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Password Change Section */}\n      <div className=\"settings-card hover-lift\" style={{\n        background: 'white',\n        borderRadius: '16px',\n        padding: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      }}>\n        <h3 style={{\n          margin: '0 0 1.5rem 0',\n          color: '#2d5016',\n          fontSize: '1.25rem',\n          fontWeight: '600',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        }}>\n          <Lock size={20} />\n          Change Password\n        </h3>\n\n        <form onSubmit={handlePasswordSubmit}>\n          <div style={{\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '1.5rem'\n          }}>\n            {/* Current Password */}\n            <div>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                Current Password\n              </label>\n              <div style={{ position: 'relative' }}>\n                <input\n                  type={showPasswords.current ? 'text' : 'password'}\n                  value={passwordData.currentPassword}\n                  onChange={(e) => handlePasswordChange('currentPassword', e.target.value)}\n                  required\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem 3rem 0.75rem 1rem',\n                    border: '2px solid #e8f5e8',\n                    borderRadius: '8px',\n                    fontSize: '1rem',\n                    transition: 'border-color 0.2s ease',\n                    outline: 'none'\n                  }}\n                  onFocus={(e) => e.target.style.borderColor = '#22c55e'}\n                  onBlur={(e) => e.target.style.borderColor = '#e8f5e8'}\n                  placeholder=\"Enter your current password\"\n                />\n                <button\n                  type=\"button\"\n                  onClick={() => togglePasswordVisibility('current')}\n                  style={{\n                    position: 'absolute',\n                    right: '0.75rem',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    background: 'none',\n                    border: 'none',\n                    cursor: 'pointer',\n                    color: '#6b7280',\n                    padding: '0.25rem'\n                  }}\n                >\n                  {showPasswords.current ? <EyeOff size={20} /> : <Eye size={20} />}\n                </button>\n              </div>\n            </div>\n\n            {/* New Password */}\n            <div>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                New Password\n              </label>\n              <div style={{ position: 'relative' }}>\n                <input\n                  type={showPasswords.new ? 'text' : 'password'}\n                  value={passwordData.newPassword}\n                  onChange={(e) => handlePasswordChange('newPassword', e.target.value)}\n                  required\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem 3rem 0.75rem 1rem',\n                    border: '2px solid #e8f5e8',\n                    borderRadius: '8px',\n                    fontSize: '1rem',\n                    transition: 'border-color 0.2s ease',\n                    outline: 'none'\n                  }}\n                  onFocus={(e) => e.target.style.borderColor = '#22c55e'}\n                  onBlur={(e) => e.target.style.borderColor = '#e8f5e8'}\n                  placeholder=\"Enter your new password\"\n                />\n                <button\n                  type=\"button\"\n                  onClick={() => togglePasswordVisibility('new')}\n                  style={{\n                    position: 'absolute',\n                    right: '0.75rem',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    background: 'none',\n                    border: 'none',\n                    cursor: 'pointer',\n                    color: '#6b7280',\n                    padding: '0.25rem'\n                  }}\n                >\n                  {showPasswords.new ? <EyeOff size={20} /> : <Eye size={20} />}\n                </button>\n              </div>\n            </div>\n\n            {/* Confirm Password */}\n            <div>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                Confirm New Password\n              </label>\n              <div style={{ position: 'relative' }}>\n                <input\n                  type={showPasswords.confirm ? 'text' : 'password'}\n                  value={passwordData.confirmPassword}\n                  onChange={(e) => handlePasswordChange('confirmPassword', e.target.value)}\n                  required\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem 3rem 0.75rem 1rem',\n                    border: '2px solid #e8f5e8',\n                    borderRadius: '8px',\n                    fontSize: '1rem',\n                    transition: 'border-color 0.2s ease',\n                    outline: 'none'\n                  }}\n                  onFocus={(e) => e.target.style.borderColor = '#22c55e'}\n                  onBlur={(e) => e.target.style.borderColor = '#e8f5e8'}\n                  placeholder=\"Confirm your new password\"\n                />\n                <button\n                  type=\"button\"\n                  onClick={() => togglePasswordVisibility('confirm')}\n                  style={{\n                    position: 'absolute',\n                    right: '0.75rem',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    background: 'none',\n                    border: 'none',\n                    cursor: 'pointer',\n                    color: '#6b7280',\n                    padding: '0.25rem'\n                  }}\n                >\n                  {showPasswords.confirm ? <EyeOff size={20} /> : <Eye size={20} />}\n                </button>\n              </div>\n            </div>\n\n            {/* Password Requirements */}\n            <div style={{\n              background: '#f0fdf4',\n              border: '1px solid #bbf7d0',\n              borderRadius: '8px',\n              padding: '1rem'\n            }}>\n              <h4 style={{\n                margin: '0 0 0.5rem 0',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#16a34a'\n              }}>\n                Password Requirements:\n              </h4>\n              <ul style={{\n                margin: 0,\n                paddingLeft: '1.25rem',\n                fontSize: '0.75rem',\n                color: '#16a34a',\n                lineHeight: '1.5'\n              }}>\n                <li>At least 8 characters long</li>\n                <li>Contains uppercase and lowercase letters</li>\n                <li>Contains at least one number</li>\n                <li>Contains at least one special character (@$!%*?&)</li>\n              </ul>\n            </div>\n\n            {/* Error Messages */}\n            {passwordErrors.length > 0 && (\n              <div style={{\n                background: '#fef2f2',\n                border: '1px solid #fecaca',\n                borderRadius: '8px',\n                padding: '1rem'\n              }}>\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  marginBottom: '0.5rem'\n                }}>\n                  <AlertCircle size={16} color=\"#dc2626\" />\n                  <span style={{\n                    fontSize: '0.875rem',\n                    fontWeight: '600',\n                    color: '#dc2626'\n                  }}>\n                    Please fix the following errors:\n                  </span>\n                </div>\n                <ul style={{\n                  margin: 0,\n                  paddingLeft: '1.25rem',\n                  fontSize: '0.75rem',\n                  color: '#dc2626',\n                  lineHeight: '1.5'\n                }}>\n                  {passwordErrors.map((error, index) => (\n                    <li key={index}>{error}</li>\n                  ))}\n                </ul>\n              </div>\n            )}\n\n            {/* Success Message */}\n            {passwordSuccess && (\n              <div style={{\n                background: '#f0fdf4',\n                border: '1px solid #bbf7d0',\n                borderRadius: '8px',\n                padding: '1rem',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              }}>\n                <CheckCircle size={16} color=\"#16a34a\" />\n                <span style={{\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  color: '#16a34a'\n                }}>\n                  {passwordSuccess}\n                </span>\n              </div>\n            )}\n\n            {/* Submit Button */}\n            <button\n              type=\"submit\"\n              disabled={isChangingPassword}\n              style={{\n                background: isChangingPassword\n                  ? '#9ca3af'\n                  : 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '8px',\n                padding: '0.875rem 2rem',\n                fontSize: '1rem',\n                fontWeight: '600',\n                cursor: isChangingPassword ? 'not-allowed' : 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                gap: '0.5rem',\n                transition: 'all 0.2s ease',\n                boxShadow: isChangingPassword ? 'none' : '0 2px 8px rgba(34, 197, 94, 0.2)',\n                alignSelf: 'flex-start'\n              }}\n              onMouseEnter={(e) => {\n                if (!isChangingPassword) {\n                  e.currentTarget.style.transform = 'translateY(-1px)';\n                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n                }\n              }}\n              onMouseLeave={(e) => {\n                if (!isChangingPassword) {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = '0 2px 8px rgba(34, 197, 94, 0.2)';\n                }\n              }}\n            >\n              {isChangingPassword ? (\n                <>\n                  <div style={{\n                    width: '16px',\n                    height: '16px',\n                    border: '2px solid #ffffff',\n                    borderTop: '2px solid transparent',\n                    borderRadius: '50%',\n                    animation: 'spin 1s linear infinite'\n                  }} />\n                  Changing Password...\n                </>\n              ) : (\n                <>\n                  <Lock size={16} />\n                  Change Password\n                </>\n              )}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n\n  // Removed renderSystemSettings function - showing profile settings directly\n\n  // Removed renderContent function - showing profile settings directly\n\n  return (\n    <div>\n      {/* CSS for responsive design and animations */}\n      <style>{`\n        @media (max-width: 768px) {\n          .profile-layout {\n            flex-direction: column !important;\n            align-items: center !important;\n            text-align: center;\n          }\n\n          .profile-details {\n            min-width: unset !important;\n            width: 100% !important;\n          }\n\n          .profile-grid {\n            grid-template-columns: 1fr !important;\n          }\n\n          .role-status-grid {\n            grid-template-columns: 1fr !important;\n          }\n        }\n\n        @media (max-width: 640px) {\n          .settings-container {\n            padding: 1rem !important;\n          }\n\n          .settings-card {\n            padding: 1.5rem !important;\n          }\n\n          .tab-container {\n            padding: 1rem !important;\n          }\n\n          .tab-button {\n            padding: 0.5rem 1rem !important;\n            font-size: 0.875rem !important;\n          }\n        }\n\n        .fade-in {\n          animation: fadeInUp 0.5s ease-out;\n        }\n\n        @keyframes fadeInUp {\n          0% {\n            opacity: 0;\n            transform: translateY(20px);\n          }\n          100% {\n            opacity: 1;\n            transform: translateY(0);\n          }\n        }\n\n        .hover-lift {\n          transition: all 0.3s ease;\n        }\n\n        .hover-lift:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12) !important;\n        }\n      `}</style>\n\n      {/* Profile Settings Content - Direct Display */}\n      <div className=\"fade-in\">\n        {renderProfileSettings()}\n      </div>\n    </div>\n  );\n};\n\nexport default Settings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAAyCC,IAAI,EAAQC,WAAW,EAAEC,GAAG,EAAEC,MAAM,EAAEC,WAAW,QAAQ,cAAc;AAChH,OAAOC,oBAAoB,MAAM,6CAA6C;AAC9E,SAASC,gBAAgB,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErE,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM;IAAEC,IAAI;IAAEC;EAAgB,CAAC,GAAGhB,YAAY,CAAC,CAAC;EAChD,MAAMiB,WAAW,GAAGhB,cAAc,CAACc,IAAI,CAAC;EACxC;EACA,MAAM,CAACG,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;;EAEnE;EACA,MAAM,CAACqB,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAC;IAC/CuB,eAAe,EAAE,EAAE;IACnBC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAC;IACjD4B,OAAO,EAAE,KAAK;IACdC,GAAG,EAAE,KAAK;IACVC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAW,EAAE,CAAC;EAClE,MAAM,CAACiC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlC,QAAQ,CAAgB,IAAI,CAAC;EAC3E,MAAM,CAACmC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;;EAEnE;;EAEA;EACA,MAAMqC,0BAA0B,GAAG,MAAOC,IAAU,IAAK;IACvDlB,qBAAqB,CAAC,IAAI,CAAC;IAC3B,IAAI;MACFmB,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;MAC/D,MAAMC,MAAM,GAAG,MAAMhC,gBAAgB,CAACiC,oBAAoB,CAACJ,IAAI,CAAC;MAChEC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEC,MAAM,CAAC;;MAEnD;MACAF,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtD,MAAMvB,eAAe,CAAC,CAAC;MACvBsB,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAExB,IAAI,CAAC;IACrE,CAAC,CAAC,OAAO2B,KAAU,EAAE;MACnBJ,OAAO,CAACI,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAM,IAAIC,KAAK,CAACD,KAAK,CAACE,OAAO,IAAI,kCAAkC,CAAC;IACtE,CAAC,SAAS;MACRzB,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;EAED,MAAM0B,0BAA0B,GAAG,MAAAA,CAAA,KAAY;IAC7C1B,qBAAqB,CAAC,IAAI,CAAC;IAC3B,IAAI;MACF,MAAMX,gBAAgB,CAACsC,oBAAoB,CAAC,CAAC;MAC7C;MACA,MAAM9B,eAAe,CAAC,CAAC;IACzB,CAAC,CAAC,OAAO0B,KAAU,EAAE;MACnB,MAAM,IAAIC,KAAK,CAACD,KAAK,CAACE,OAAO,IAAI,kCAAkC,CAAC;IACtE,CAAC,SAAS;MACRzB,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,MAAM4B,gBAAgB,GAAIC,QAAgB,IAAe;IACvD,MAAMC,MAAgB,GAAG,EAAE;IAC3B,IAAID,QAAQ,CAACE,MAAM,GAAG,CAAC,EAAE;MACvBD,MAAM,CAACE,IAAI,CAAC,6CAA6C,CAAC;IAC5D;IACA,IAAI,CAAC,aAAa,CAACC,IAAI,CAACJ,QAAQ,CAAC,EAAE;MACjCC,MAAM,CAACE,IAAI,CAAC,qDAAqD,CAAC;IACpE;IACA,IAAI,CAAC,aAAa,CAACC,IAAI,CAACJ,QAAQ,CAAC,EAAE;MACjCC,MAAM,CAACE,IAAI,CAAC,qDAAqD,CAAC;IACpE;IACA,IAAI,CAAC,UAAU,CAACC,IAAI,CAACJ,QAAQ,CAAC,EAAE;MAC9BC,MAAM,CAACE,IAAI,CAAC,2CAA2C,CAAC;IAC1D;IACA,IAAI,CAAC,iBAAiB,CAACC,IAAI,CAACJ,QAAQ,CAAC,EAAE;MACrCC,MAAM,CAACE,IAAI,CAAC,gEAAgE,CAAC;IAC/E;IACA,OAAOF,MAAM;EACf,CAAC;;EAED;EACA,MAAMI,oBAAoB,GAAGA,CAACC,KAAgC,EAAEC,KAAa,KAAK;IAChFlC,eAAe,CAACmC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;IACtDxB,iBAAiB,CAAC,EAAE,CAAC;IACrBE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMwB,wBAAwB,GAAIH,KAAiC,IAAK;IACtE5B,gBAAgB,CAAC8B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAG,CAACE,IAAI,CAACF,KAAK;IAAE,CAAC,CAAC,CAAC;EAChE,CAAC;;EAED;EACA,MAAMI,oBAAoB,GAAG,MAAOC,CAAkB,IAAK;IACzDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB7B,iBAAiB,CAAC,EAAE,CAAC;IACrBE,kBAAkB,CAAC,IAAI,CAAC;;IAExB;IACA,MAAMgB,MAAgB,GAAG,EAAE;IAE3B,IAAI,CAAC7B,YAAY,CAACE,eAAe,EAAE;MACjC2B,MAAM,CAACE,IAAI,CAAC,8BAA8B,CAAC;IAC7C;IAEA,IAAI,CAAC/B,YAAY,CAACG,WAAW,EAAE;MAC7B0B,MAAM,CAACE,IAAI,CAAC,0BAA0B,CAAC;IACzC,CAAC,MAAM;MACL,MAAMU,wBAAwB,GAAGd,gBAAgB,CAAC3B,YAAY,CAACG,WAAW,CAAC;MAC3E0B,MAAM,CAACE,IAAI,CAAC,GAAGU,wBAAwB,CAAC;IAC1C;IAEA,IAAI,CAACzC,YAAY,CAACI,eAAe,EAAE;MACjCyB,MAAM,CAACE,IAAI,CAAC,mCAAmC,CAAC;IAClD,CAAC,MAAM,IAAI/B,YAAY,CAACG,WAAW,KAAKH,YAAY,CAACI,eAAe,EAAE;MACpEyB,MAAM,CAACE,IAAI,CAAC,4CAA4C,CAAC;IAC3D;IAEA,IAAI/B,YAAY,CAACE,eAAe,KAAKF,YAAY,CAACG,WAAW,EAAE;MAC7D0B,MAAM,CAACE,IAAI,CAAC,sDAAsD,CAAC;IACrE;IAEA,IAAIF,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;MACrBnB,iBAAiB,CAACkB,MAAM,CAAC;MACzB;IACF;IAEAd,qBAAqB,CAAC,IAAI,CAAC;IAC3B,IAAI;MACF;MACA,MAAM3B,gBAAgB,CAACsD,cAAc,CAAC;QACpCxC,eAAe,EAAEF,YAAY,CAACE,eAAe;QAC7CC,WAAW,EAAEH,YAAY,CAACG;MAC5B,CAAC,CAAC;MAEFU,kBAAkB,CAAC,gCAAgC,CAAC;MACpDZ,eAAe,CAAC;QACdC,eAAe,EAAE,EAAE;QACnBC,WAAW,EAAE,EAAE;QACfC,eAAe,EAAE;MACnB,CAAC,CAAC;;MAEF;MACAuC,UAAU,CAAC,MAAM9B,kBAAkB,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IAClD,CAAC,CAAC,OAAOS,KAAU,EAAE;MACnBX,iBAAiB,CAAC,CAACW,KAAK,CAACE,OAAO,IAAI,2BAA2B,CAAC,CAAC;IACnE,CAAC,SAAS;MACRT,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;EAED,MAAM6B,qBAAqB,GAAGA,CAAA;IAAA,IAAAC,eAAA,EAAAC,cAAA;IAAA,oBAC5BxD,OAAA;MAAKyD,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAO,CAAE;MAAAC,QAAA,gBAEpE7D,OAAA;QAAK8D,SAAS,EAAC,0BAA0B;QAACL,KAAK,EAAE;UAC/CM,UAAU,EAAE,OAAO;UACnBC,YAAY,EAAE,MAAM;UACpBC,OAAO,EAAE,MAAM;UACfC,SAAS,EAAE,gCAAgC;UAC3CC,MAAM,EAAE;QACV,CAAE;QAAAN,QAAA,eAGA7D,OAAA;UAAK8D,SAAS,EAAC,gBAAgB;UAACL,KAAK,EAAE;YACrCC,OAAO,EAAE,MAAM;YACfE,GAAG,EAAE,MAAM;YACXQ,UAAU,EAAE,YAAY;YACxBC,YAAY,EAAE,MAAM;YACpBC,QAAQ,EAAE;UACZ,CAAE;UAAAT,QAAA,gBAEA7D,OAAA;YAAKyD,KAAK,EAAE;cAAEc,UAAU,EAAE;YAAE,CAAE;YAAAV,QAAA,eAC5B7D,OAAA,CAACH,oBAAoB;cACnB2E,cAAc,EAAEnE,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEoE,cAAc,GAAG,wBAAwBpE,IAAI,CAACoE,cAAc,EAAE,GAAGC,SAAU;cACjGC,YAAY,EAAE,GAAG,CAAAtE,IAAI,aAAJA,IAAI,wBAAAkD,eAAA,GAAJlD,IAAI,CAAEuE,SAAS,cAAArB,eAAA,uBAAfA,eAAA,CAAiBsB,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE,GAAG,CAAAxE,IAAI,aAAJA,IAAI,wBAAAmD,cAAA,GAAJnD,IAAI,CAAEyE,QAAQ,cAAAtB,cAAA,uBAAdA,cAAA,CAAgBqB,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE,EAAG;cACtFE,QAAQ,EAAErD,0BAA2B;cACrCsD,QAAQ,EAAE7C,0BAA2B;cACrC8C,SAAS,EAAEzE,kBAAmB;cAC9B0E,IAAI,EAAE;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNtF,OAAA;YAAK8D,SAAS,EAAC,iBAAiB;YAACL,KAAK,EAAE;cACtC8B,IAAI,EAAE,CAAC;cACPC,QAAQ,EAAE,OAAO;cACjB9B,OAAO,EAAE,MAAM;cACfC,aAAa,EAAE,QAAQ;cACvBC,GAAG,EAAE,QAAQ;cACb6B,UAAU,EAAE;YACd,CAAE;YAAA5B,QAAA,eAEA7D,OAAA;cAAKyD,KAAK,EAAE;gBAAEY,YAAY,EAAE;cAAS,CAAE;cAAAR,QAAA,gBACrC7D,OAAA;gBAAKyD,KAAK,EAAE;kBACViC,KAAK,EAAE,SAAS;kBAChBC,QAAQ,EAAE,QAAQ;kBAClBC,UAAU,EAAE,KAAK;kBACjBvB,YAAY,EAAE;gBAChB,CAAE;gBAAAR,QAAA,GACC,GAAG,CAAAxD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuE,SAAS,KAAI,EAAE,IAAI,CAAAvE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyE,QAAQ,KAAI,EAAE,EAAE,EAClD,CAAAzE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwF,MAAM,kBAAI7F,OAAA;kBAAMyD,KAAK,EAAE;oBAAEmC,UAAU,EAAE,KAAK;oBAAEF,KAAK,EAAE;kBAAU,CAAE;kBAAA7B,QAAA,GAAC,GAAC,EAACxD,IAAI,CAACwF,MAAM;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzF,CAAC,eACNtF,OAAA;gBAAKyD,KAAK,EAAE;kBACViC,KAAK,EAAE,SAAS;kBAChBC,QAAQ,EAAE,MAAM;kBAChBC,UAAU,EAAE;gBACd,CAAE;gBAAA/B,QAAA,eACE7D,OAAA;kBAAA6D,QAAA,EAAO,CAAAxD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyF,KAAK,KAAI;gBAAc;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtF,OAAA;QAAK8D,SAAS,EAAC,0BAA0B;QAACL,KAAK,EAAE;UAC/CM,UAAU,EAAE,OAAO;UACnBC,YAAY,EAAE,MAAM;UACpBC,OAAO,EAAE,MAAM;UACfC,SAAS,EAAE,gCAAgC;UAC3CC,MAAM,EAAE;QACV,CAAE;QAAAN,QAAA,gBACA7D,OAAA;UAAIyD,KAAK,EAAE;YACTsC,MAAM,EAAE,cAAc;YACtBL,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,SAAS;YACnBC,UAAU,EAAE,KAAK;YACjBlC,OAAO,EAAE,MAAM;YACfU,UAAU,EAAE,QAAQ;YACpBR,GAAG,EAAE;UACP,CAAE;UAAAC,QAAA,gBACA7D,OAAA,CAACR,IAAI;YAAC0F,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBAEpB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELtF,OAAA;UAAMgG,QAAQ,EAAEhD,oBAAqB;UAAAa,QAAA,eACnC7D,OAAA;YAAKyD,KAAK,EAAE;cACVC,OAAO,EAAE,MAAM;cACfC,aAAa,EAAE,QAAQ;cACvBC,GAAG,EAAE;YACP,CAAE;YAAAC,QAAA,gBAEA7D,OAAA;cAAA6D,QAAA,gBACE7D,OAAA;gBAAOyD,KAAK,EAAE;kBACZC,OAAO,EAAE,OAAO;kBAChBiC,QAAQ,EAAE,UAAU;kBACpBC,UAAU,EAAE,KAAK;kBACjBF,KAAK,EAAE,SAAS;kBAChBrB,YAAY,EAAE;gBAChB,CAAE;gBAAAR,QAAA,EAAC;cAEH;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRtF,OAAA;gBAAKyD,KAAK,EAAE;kBAAEwC,QAAQ,EAAE;gBAAW,CAAE;gBAAApC,QAAA,gBACnC7D,OAAA;kBACEkG,IAAI,EAAEnF,aAAa,CAACE,OAAO,GAAG,MAAM,GAAG,UAAW;kBAClD4B,KAAK,EAAEnC,YAAY,CAACE,eAAgB;kBACpCuF,QAAQ,EAAGlD,CAAC,IAAKN,oBAAoB,CAAC,iBAAiB,EAAEM,CAAC,CAACmD,MAAM,CAACvD,KAAK,CAAE;kBACzEwD,QAAQ;kBACR5C,KAAK,EAAE;oBACL6C,KAAK,EAAE,MAAM;oBACbrC,OAAO,EAAE,2BAA2B;oBACpCE,MAAM,EAAE,mBAAmB;oBAC3BH,YAAY,EAAE,KAAK;oBACnB2B,QAAQ,EAAE,MAAM;oBAChBY,UAAU,EAAE,wBAAwB;oBACpCC,OAAO,EAAE;kBACX,CAAE;kBACFC,OAAO,EAAGxD,CAAC,IAAKA,CAAC,CAACmD,MAAM,CAAC3C,KAAK,CAACiD,WAAW,GAAG,SAAU;kBACvDC,MAAM,EAAG1D,CAAC,IAAKA,CAAC,CAACmD,MAAM,CAAC3C,KAAK,CAACiD,WAAW,GAAG,SAAU;kBACtDE,WAAW,EAAC;gBAA6B;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,eACFtF,OAAA;kBACEkG,IAAI,EAAC,QAAQ;kBACbW,OAAO,EAAEA,CAAA,KAAM9D,wBAAwB,CAAC,SAAS,CAAE;kBACnDU,KAAK,EAAE;oBACLwC,QAAQ,EAAE,UAAU;oBACpBa,KAAK,EAAE,SAAS;oBAChBC,GAAG,EAAE,KAAK;oBACVC,SAAS,EAAE,kBAAkB;oBAC7BjD,UAAU,EAAE,MAAM;oBAClBI,MAAM,EAAE,MAAM;oBACd8C,MAAM,EAAE,SAAS;oBACjBvB,KAAK,EAAE,SAAS;oBAChBzB,OAAO,EAAE;kBACX,CAAE;kBAAAJ,QAAA,EAED9C,aAAa,CAACE,OAAO,gBAAGjB,OAAA,CAACL,MAAM;oBAACuF,IAAI,EAAE;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGtF,OAAA,CAACN,GAAG;oBAACwF,IAAI,EAAE;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNtF,OAAA;cAAA6D,QAAA,gBACE7D,OAAA;gBAAOyD,KAAK,EAAE;kBACZC,OAAO,EAAE,OAAO;kBAChBiC,QAAQ,EAAE,UAAU;kBACpBC,UAAU,EAAE,KAAK;kBACjBF,KAAK,EAAE,SAAS;kBAChBrB,YAAY,EAAE;gBAChB,CAAE;gBAAAR,QAAA,EAAC;cAEH;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRtF,OAAA;gBAAKyD,KAAK,EAAE;kBAAEwC,QAAQ,EAAE;gBAAW,CAAE;gBAAApC,QAAA,gBACnC7D,OAAA;kBACEkG,IAAI,EAAEnF,aAAa,CAACG,GAAG,GAAG,MAAM,GAAG,UAAW;kBAC9C2B,KAAK,EAAEnC,YAAY,CAACG,WAAY;kBAChCsF,QAAQ,EAAGlD,CAAC,IAAKN,oBAAoB,CAAC,aAAa,EAAEM,CAAC,CAACmD,MAAM,CAACvD,KAAK,CAAE;kBACrEwD,QAAQ;kBACR5C,KAAK,EAAE;oBACL6C,KAAK,EAAE,MAAM;oBACbrC,OAAO,EAAE,2BAA2B;oBACpCE,MAAM,EAAE,mBAAmB;oBAC3BH,YAAY,EAAE,KAAK;oBACnB2B,QAAQ,EAAE,MAAM;oBAChBY,UAAU,EAAE,wBAAwB;oBACpCC,OAAO,EAAE;kBACX,CAAE;kBACFC,OAAO,EAAGxD,CAAC,IAAKA,CAAC,CAACmD,MAAM,CAAC3C,KAAK,CAACiD,WAAW,GAAG,SAAU;kBACvDC,MAAM,EAAG1D,CAAC,IAAKA,CAAC,CAACmD,MAAM,CAAC3C,KAAK,CAACiD,WAAW,GAAG,SAAU;kBACtDE,WAAW,EAAC;gBAAyB;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACFtF,OAAA;kBACEkG,IAAI,EAAC,QAAQ;kBACbW,OAAO,EAAEA,CAAA,KAAM9D,wBAAwB,CAAC,KAAK,CAAE;kBAC/CU,KAAK,EAAE;oBACLwC,QAAQ,EAAE,UAAU;oBACpBa,KAAK,EAAE,SAAS;oBAChBC,GAAG,EAAE,KAAK;oBACVC,SAAS,EAAE,kBAAkB;oBAC7BjD,UAAU,EAAE,MAAM;oBAClBI,MAAM,EAAE,MAAM;oBACd8C,MAAM,EAAE,SAAS;oBACjBvB,KAAK,EAAE,SAAS;oBAChBzB,OAAO,EAAE;kBACX,CAAE;kBAAAJ,QAAA,EAED9C,aAAa,CAACG,GAAG,gBAAGlB,OAAA,CAACL,MAAM;oBAACuF,IAAI,EAAE;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGtF,OAAA,CAACN,GAAG;oBAACwF,IAAI,EAAE;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNtF,OAAA;cAAA6D,QAAA,gBACE7D,OAAA;gBAAOyD,KAAK,EAAE;kBACZC,OAAO,EAAE,OAAO;kBAChBiC,QAAQ,EAAE,UAAU;kBACpBC,UAAU,EAAE,KAAK;kBACjBF,KAAK,EAAE,SAAS;kBAChBrB,YAAY,EAAE;gBAChB,CAAE;gBAAAR,QAAA,EAAC;cAEH;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRtF,OAAA;gBAAKyD,KAAK,EAAE;kBAAEwC,QAAQ,EAAE;gBAAW,CAAE;gBAAApC,QAAA,gBACnC7D,OAAA;kBACEkG,IAAI,EAAEnF,aAAa,CAACI,OAAO,GAAG,MAAM,GAAG,UAAW;kBAClD0B,KAAK,EAAEnC,YAAY,CAACI,eAAgB;kBACpCqF,QAAQ,EAAGlD,CAAC,IAAKN,oBAAoB,CAAC,iBAAiB,EAAEM,CAAC,CAACmD,MAAM,CAACvD,KAAK,CAAE;kBACzEwD,QAAQ;kBACR5C,KAAK,EAAE;oBACL6C,KAAK,EAAE,MAAM;oBACbrC,OAAO,EAAE,2BAA2B;oBACpCE,MAAM,EAAE,mBAAmB;oBAC3BH,YAAY,EAAE,KAAK;oBACnB2B,QAAQ,EAAE,MAAM;oBAChBY,UAAU,EAAE,wBAAwB;oBACpCC,OAAO,EAAE;kBACX,CAAE;kBACFC,OAAO,EAAGxD,CAAC,IAAKA,CAAC,CAACmD,MAAM,CAAC3C,KAAK,CAACiD,WAAW,GAAG,SAAU;kBACvDC,MAAM,EAAG1D,CAAC,IAAKA,CAAC,CAACmD,MAAM,CAAC3C,KAAK,CAACiD,WAAW,GAAG,SAAU;kBACtDE,WAAW,EAAC;gBAA2B;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACFtF,OAAA;kBACEkG,IAAI,EAAC,QAAQ;kBACbW,OAAO,EAAEA,CAAA,KAAM9D,wBAAwB,CAAC,SAAS,CAAE;kBACnDU,KAAK,EAAE;oBACLwC,QAAQ,EAAE,UAAU;oBACpBa,KAAK,EAAE,SAAS;oBAChBC,GAAG,EAAE,KAAK;oBACVC,SAAS,EAAE,kBAAkB;oBAC7BjD,UAAU,EAAE,MAAM;oBAClBI,MAAM,EAAE,MAAM;oBACd8C,MAAM,EAAE,SAAS;oBACjBvB,KAAK,EAAE,SAAS;oBAChBzB,OAAO,EAAE;kBACX,CAAE;kBAAAJ,QAAA,EAED9C,aAAa,CAACI,OAAO,gBAAGnB,OAAA,CAACL,MAAM;oBAACuF,IAAI,EAAE;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGtF,OAAA,CAACN,GAAG;oBAACwF,IAAI,EAAE;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNtF,OAAA;cAAKyD,KAAK,EAAE;gBACVM,UAAU,EAAE,SAAS;gBACrBI,MAAM,EAAE,mBAAmB;gBAC3BH,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE;cACX,CAAE;cAAAJ,QAAA,gBACA7D,OAAA;gBAAIyD,KAAK,EAAE;kBACTsC,MAAM,EAAE,cAAc;kBACtBJ,QAAQ,EAAE,UAAU;kBACpBC,UAAU,EAAE,KAAK;kBACjBF,KAAK,EAAE;gBACT,CAAE;gBAAA7B,QAAA,EAAC;cAEH;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLtF,OAAA;gBAAIyD,KAAK,EAAE;kBACTsC,MAAM,EAAE,CAAC;kBACTmB,WAAW,EAAE,SAAS;kBACtBvB,QAAQ,EAAE,SAAS;kBACnBD,KAAK,EAAE,SAAS;kBAChByB,UAAU,EAAE;gBACd,CAAE;gBAAAtD,QAAA,gBACA7D,OAAA;kBAAA6D,QAAA,EAAI;gBAA0B;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnCtF,OAAA;kBAAA6D,QAAA,EAAI;gBAAwC;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjDtF,OAAA;kBAAA6D,QAAA,EAAI;gBAA4B;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrCtF,OAAA;kBAAA6D,QAAA,EAAI;gBAAiD;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,EAGLlE,cAAc,CAACoB,MAAM,GAAG,CAAC,iBACxBxC,OAAA;cAAKyD,KAAK,EAAE;gBACVM,UAAU,EAAE,SAAS;gBACrBI,MAAM,EAAE,mBAAmB;gBAC3BH,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE;cACX,CAAE;cAAAJ,QAAA,gBACA7D,OAAA;gBAAKyD,KAAK,EAAE;kBACVC,OAAO,EAAE,MAAM;kBACfU,UAAU,EAAE,QAAQ;kBACpBR,GAAG,EAAE,QAAQ;kBACbS,YAAY,EAAE;gBAChB,CAAE;gBAAAR,QAAA,gBACA7D,OAAA,CAACJ,WAAW;kBAACsF,IAAI,EAAE,EAAG;kBAACQ,KAAK,EAAC;gBAAS;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACzCtF,OAAA;kBAAMyD,KAAK,EAAE;oBACXkC,QAAQ,EAAE,UAAU;oBACpBC,UAAU,EAAE,KAAK;oBACjBF,KAAK,EAAE;kBACT,CAAE;kBAAA7B,QAAA,EAAC;gBAEH;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNtF,OAAA;gBAAIyD,KAAK,EAAE;kBACTsC,MAAM,EAAE,CAAC;kBACTmB,WAAW,EAAE,SAAS;kBACtBvB,QAAQ,EAAE,SAAS;kBACnBD,KAAK,EAAE,SAAS;kBAChByB,UAAU,EAAE;gBACd,CAAE;gBAAAtD,QAAA,EACCzC,cAAc,CAACgG,GAAG,CAAC,CAACpF,KAAK,EAAEqF,KAAK,kBAC/BrH,OAAA;kBAAA6D,QAAA,EAAiB7B;gBAAK,GAAbqF,KAAK;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAC5B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CACN,EAGAhE,eAAe,iBACdtB,OAAA;cAAKyD,KAAK,EAAE;gBACVM,UAAU,EAAE,SAAS;gBACrBI,MAAM,EAAE,mBAAmB;gBAC3BH,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE,MAAM;gBACfP,OAAO,EAAE,MAAM;gBACfU,UAAU,EAAE,QAAQ;gBACpBR,GAAG,EAAE;cACP,CAAE;cAAAC,QAAA,gBACA7D,OAAA,CAACP,WAAW;gBAACyF,IAAI,EAAE,EAAG;gBAACQ,KAAK,EAAC;cAAS;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzCtF,OAAA;gBAAMyD,KAAK,EAAE;kBACXkC,QAAQ,EAAE,UAAU;kBACpBC,UAAU,EAAE,KAAK;kBACjBF,KAAK,EAAE;gBACT,CAAE;gBAAA7B,QAAA,EACCvC;cAAe;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN,eAGDtF,OAAA;cACEkG,IAAI,EAAC,QAAQ;cACboB,QAAQ,EAAE9F,kBAAmB;cAC7BiC,KAAK,EAAE;gBACLM,UAAU,EAAEvC,kBAAkB,GAC1B,SAAS,GACT,mDAAmD;gBACvDkE,KAAK,EAAE,OAAO;gBACdvB,MAAM,EAAE,MAAM;gBACdH,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE,eAAe;gBACxB0B,QAAQ,EAAE,MAAM;gBAChBC,UAAU,EAAE,KAAK;gBACjBqB,MAAM,EAAEzF,kBAAkB,GAAG,aAAa,GAAG,SAAS;gBACtDkC,OAAO,EAAE,MAAM;gBACfU,UAAU,EAAE,QAAQ;gBACpBmD,cAAc,EAAE,QAAQ;gBACxB3D,GAAG,EAAE,QAAQ;gBACb2C,UAAU,EAAE,eAAe;gBAC3BrC,SAAS,EAAE1C,kBAAkB,GAAG,MAAM,GAAG,kCAAkC;gBAC3EgG,SAAS,EAAE;cACb,CAAE;cACFC,YAAY,EAAGxE,CAAC,IAAK;gBACnB,IAAI,CAACzB,kBAAkB,EAAE;kBACvByB,CAAC,CAACyE,aAAa,CAACjE,KAAK,CAACuD,SAAS,GAAG,kBAAkB;kBACpD/D,CAAC,CAACyE,aAAa,CAACjE,KAAK,CAACS,SAAS,GAAG,mCAAmC;gBACvE;cACF,CAAE;cACFyD,YAAY,EAAG1E,CAAC,IAAK;gBACnB,IAAI,CAACzB,kBAAkB,EAAE;kBACvByB,CAAC,CAACyE,aAAa,CAACjE,KAAK,CAACuD,SAAS,GAAG,eAAe;kBACjD/D,CAAC,CAACyE,aAAa,CAACjE,KAAK,CAACS,SAAS,GAAG,kCAAkC;gBACtE;cACF,CAAE;cAAAL,QAAA,EAEDrC,kBAAkB,gBACjBxB,OAAA,CAAAE,SAAA;gBAAA2D,QAAA,gBACE7D,OAAA;kBAAKyD,KAAK,EAAE;oBACV6C,KAAK,EAAE,MAAM;oBACbsB,MAAM,EAAE,MAAM;oBACdzD,MAAM,EAAE,mBAAmB;oBAC3B0D,SAAS,EAAE,uBAAuB;oBAClC7D,YAAY,EAAE,KAAK;oBACnB8D,SAAS,EAAE;kBACb;gBAAE;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,wBAEP;cAAA,eAAE,CAAC,gBAEHtF,OAAA,CAAAE,SAAA;gBAAA2D,QAAA,gBACE7D,OAAA,CAACR,IAAI;kBAAC0F,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,mBAEpB;cAAA,eAAE;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACP;;EAED;;EAEA;;EAEA,oBACEtF,OAAA;IAAA6D,QAAA,gBAEE7D,OAAA;MAAA6D,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAGVtF,OAAA;MAAK8D,SAAS,EAAC,SAAS;MAAAD,QAAA,EACrBP,qBAAqB,CAAC;IAAC;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClF,EAAA,CAxmBID,QAAkB;EAAA,QACYb,YAAY,EAC1BC,cAAc;AAAA;AAAAwI,EAAA,GAF9B5H,QAAkB;AA0mBxB,eAAeA,QAAQ;AAAC,IAAA4H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}