{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\admin\\\\AuditLogs.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Shield, Filter, Download, Eye, Calendar, User, Activity, AlertTriangle } from 'lucide-react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuditLogs = () => {\n  _s();\n  const {\n    user\n  } = useAdminAuth();\n  const permissions = usePermissions(user);\n  const [logs, setLogs] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Filters\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedUser, setSelectedUser] = useState('');\n  const [selectedAction, setSelectedAction] = useState('');\n  const [selectedSeverity, setSelectedSeverity] = useState('');\n  const [dateRange, setDateRange] = useState({\n    start: '',\n    end: ''\n  });\n  const [showFilters, setShowFilters] = useState(false);\n\n  // Pagination\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const logsPerPage = 20;\n\n  // Permission check\n  if (!permissions.isSuperAdmin) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Shield, {\n        size: 64,\n        style: {\n          marginBottom: '1rem',\n          opacity: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          margin: '0 0 0.5rem',\n          fontSize: '1.5rem',\n          fontWeight: '600'\n        },\n        children: \"Access Denied\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: 0,\n          fontSize: '1rem'\n        },\n        children: \"System audit logs are restricted to Super Administrators only.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        },\n        children: [\"Current Role: \", permissions.getPositionDisplayName()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this);\n  }\n  useEffect(() => {\n    loadAuditLogs();\n  }, [currentPage, searchTerm, selectedUser, selectedAction, selectedSeverity, dateRange]);\n  const loadAuditLogs = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // TODO: Implement actual API call\n      // const response = await auditService.getLogs({\n      //   page: currentPage,\n      //   limit: logsPerPage,\n      //   search: searchTerm,\n      //   user: selectedUser,\n      //   action: selectedAction,\n      //   severity: selectedSeverity,\n      //   startDate: dateRange.start,\n      //   endDate: dateRange.end\n      // });\n\n      // Mock data for demonstration\n      const mockLogs = [{\n        id: '1',\n        timestamp: new Date('2025-08-05T10:30:00Z'),\n        userId: 1,\n        userEmail: '<EMAIL>',\n        userName: 'Von Christian Admin',\n        action: 'LOGIN',\n        resource: 'AUTH',\n        details: {\n          loginMethod: 'email_password'\n        },\n        ipAddress: '*************',\n        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n        success: true,\n        severity: 'low'\n      }, {\n        id: '2',\n        timestamp: new Date('2025-08-05T10:25:00Z'),\n        userId: 1,\n        userEmail: '<EMAIL>',\n        userName: 'Von Christian Admin',\n        action: 'CREATE_STUDENT',\n        resource: 'STUDENT',\n        resourceId: '123',\n        details: {\n          studentEmail: '<EMAIL>',\n          gradeLevel: 11\n        },\n        ipAddress: '*************',\n        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n        success: true,\n        severity: 'medium'\n      }, {\n        id: '3',\n        timestamp: new Date('2025-08-05T10:20:00Z'),\n        userId: 2,\n        userEmail: '<EMAIL>',\n        userName: 'Zaira Professor',\n        action: 'UPDATE_STUDENT',\n        resource: 'STUDENT',\n        resourceId: '456',\n        details: {\n          changes: {\n            phone_number: '+639123456789'\n          }\n        },\n        ipAddress: '*************',\n        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n        success: true,\n        severity: 'low'\n      }, {\n        id: '4',\n        timestamp: new Date('2025-08-05T10:15:00Z'),\n        userId: 1,\n        userEmail: '<EMAIL>',\n        userName: 'Von Christian Admin',\n        action: 'DELETE_ANNOUNCEMENT',\n        resource: 'ANNOUNCEMENT',\n        resourceId: '789',\n        details: {\n          title: 'Old Announcement',\n          reason: 'Outdated content'\n        },\n        ipAddress: '*************',\n        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n        success: true,\n        severity: 'high'\n      }, {\n        id: '5',\n        timestamp: new Date('2025-08-05T10:10:00Z'),\n        userId: 3,\n        userEmail: '<EMAIL>',\n        userName: 'Unknown User',\n        action: 'FAILED_LOGIN',\n        resource: 'AUTH',\n        details: {\n          reason: 'Invalid credentials',\n          attempts: 3\n        },\n        ipAddress: '*************',\n        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n        success: false,\n        severity: 'critical'\n      }];\n      setLogs(mockLogs);\n      setTotalPages(Math.ceil(mockLogs.length / logsPerPage));\n    } catch (err) {\n      setError(err.message || 'Failed to load audit logs');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getSeverityColor = severity => {\n    switch (severity) {\n      case 'low':\n        return '#10b981';\n      case 'medium':\n        return '#f59e0b';\n      case 'high':\n        return '#ef4444';\n      case 'critical':\n        return '#dc2626';\n      default:\n        return '#6b7280';\n    }\n  };\n  const getSeverityBadge = severity => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      padding: '0.25rem 0.5rem',\n      background: getSeverityColor(severity),\n      color: 'white',\n      borderRadius: '4px',\n      fontSize: '0.75rem',\n      fontWeight: '600',\n      textTransform: 'uppercase'\n    },\n    children: severity\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 196,\n    columnNumber: 5\n  }, this);\n  const getActionIcon = action => {\n    if (action.includes('LOGIN')) return /*#__PURE__*/_jsxDEV(User, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 42\n    }, this);\n    if (action.includes('CREATE')) return /*#__PURE__*/_jsxDEV(Activity, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 43\n    }, this);\n    if (action.includes('UPDATE')) return /*#__PURE__*/_jsxDEV(Eye, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 43\n    }, this);\n    if (action.includes('DELETE')) return /*#__PURE__*/_jsxDEV(AlertTriangle, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 43\n    }, this);\n    return /*#__PURE__*/_jsxDEV(Activity, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 12\n    }, this);\n  };\n  const exportLogs = () => {\n    const csvContent = [['Timestamp', 'User', 'Action', 'Resource', 'Success', 'Severity', 'IP Address'].join(','), ...logs.map(log => [log.timestamp.toISOString(), log.userName, log.action, log.resource, log.success ? 'Yes' : 'No', log.severity, log.ipAddress].join(','))].join('\\n');\n    const blob = new Blob([csvContent], {\n      type: 'text/csv'\n    });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `audit_logs_${new Date().toISOString().split('T')[0]}.csv`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      maxWidth: '1400px',\n      margin: '0 auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            margin: '0 0 0.5rem',\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          },\n          children: \"System Audit Logs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          },\n          children: \"Monitor and track all system activities and user actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowFilters(!showFilters),\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            padding: '0.75rem 1rem',\n            background: showFilters ? '#3b82f6' : 'white',\n            color: showFilters ? 'white' : '#374151',\n            border: '1px solid #d1d5db',\n            borderRadius: '6px',\n            fontSize: '0.875rem',\n            fontWeight: '600',\n            cursor: 'pointer'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Filter, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this), \"Filters\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: exportLogs,\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            padding: '0.75rem 1rem',\n            background: '#10b981',\n            color: 'white',\n            border: 'none',\n            borderRadius: '6px',\n            fontSize: '0.875rem',\n            fontWeight: '600',\n            cursor: 'pointer'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Download, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this), \"Export CSV\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 7\n    }, this), showFilters && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '8px',\n        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n        padding: '1.5rem',\n        marginBottom: '2rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n          gap: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '600',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            },\n            children: \"Search\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            placeholder: \"Search logs...\",\n            style: {\n              width: '100%',\n              padding: '0.5rem',\n              border: '1px solid #d1d5db',\n              borderRadius: '6px',\n              fontSize: '0.875rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '600',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            },\n            children: \"Action\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedAction,\n            onChange: e => setSelectedAction(e.target.value),\n            style: {\n              width: '100%',\n              padding: '0.5rem',\n              border: '1px solid #d1d5db',\n              borderRadius: '6px',\n              fontSize: '0.875rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"All Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"LOGIN\",\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"CREATE\",\n              children: \"Create\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"UPDATE\",\n              children: \"Update\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"DELETE\",\n              children: \"Delete\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '600',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            },\n            children: \"Severity\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedSeverity,\n            onChange: e => setSelectedSeverity(e.target.value),\n            style: {\n              width: '100%',\n              padding: '0.5rem',\n              border: '1px solid #d1d5db',\n              borderRadius: '6px',\n              fontSize: '0.875rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"All Severities\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"low\",\n              children: \"Low\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"medium\",\n              children: \"Medium\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"high\",\n              children: \"High\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"critical\",\n              children: \"Critical\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '600',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            },\n            children: \"Start Date\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"date\",\n            value: dateRange.start,\n            onChange: e => setDateRange(prev => ({\n              ...prev,\n              start: e.target.value\n            })),\n            style: {\n              width: '100%',\n              padding: '0.5rem',\n              border: '1px solid #d1d5db',\n              borderRadius: '6px',\n              fontSize: '0.875rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '600',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            },\n            children: \"End Date\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"date\",\n            value: dateRange.end,\n            onChange: e => setDateRange(prev => ({\n              ...prev,\n              end: e.target.value\n            })),\n            style: {\n              width: '100%',\n              padding: '0.5rem',\n              border: '1px solid #d1d5db',\n              borderRadius: '6px',\n              fontSize: '0.875rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 9\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '1rem',\n        background: '#fef2f2',\n        border: '1px solid #fecaca',\n        borderRadius: '8px',\n        color: '#dc2626',\n        marginBottom: '1rem',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n        size: 16\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 473,\n        columnNumber: 11\n      }, this), error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 462,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '12px',\n        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n        overflow: 'hidden'\n      },\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          padding: '3rem'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '40px',\n            height: '40px',\n            border: '4px solid #f3f4f6',\n            borderTop: '4px solid #3b82f6',\n            borderRadius: '50%',\n            animation: 'spin 1s linear infinite'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 486,\n        columnNumber: 11\n      }, this) : logs.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '3rem',\n          color: '#6b7280'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Shield, {\n          size: 48,\n          style: {\n            marginBottom: '1rem',\n            opacity: 0.5\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 0.5rem',\n            fontSize: '1.125rem',\n            fontWeight: '600'\n          },\n          children: \"No audit logs found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            fontSize: '0.875rem'\n          },\n          children: \"No logs match your current filters\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 502,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          overflowX: 'auto'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          style: {\n            width: '100%',\n            borderCollapse: 'collapse'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            style: {\n              background: '#f9fafb'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                style: {\n                  padding: '1rem',\n                  textAlign: 'left',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  color: '#374151'\n                },\n                children: \"Timestamp\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                style: {\n                  padding: '1rem',\n                  textAlign: 'left',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  color: '#374151'\n                },\n                children: \"User\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                style: {\n                  padding: '1rem',\n                  textAlign: 'left',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  color: '#374151'\n                },\n                children: \"Action\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                style: {\n                  padding: '1rem',\n                  textAlign: 'left',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  color: '#374151'\n                },\n                children: \"Resource\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                style: {\n                  padding: '1rem',\n                  textAlign: 'left',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  color: '#374151'\n                },\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                style: {\n                  padding: '1rem',\n                  textAlign: 'left',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  color: '#374151'\n                },\n                children: \"Severity\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                style: {\n                  padding: '1rem',\n                  textAlign: 'left',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  color: '#374151'\n                },\n                children: \"IP Address\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: logs.map((log, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n              style: {\n                borderBottom: index < logs.length - 1 ? '1px solid #f3f4f6' : 'none',\n                background: log.success ? 'transparent' : '#fef2f2'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                style: {\n                  padding: '1rem',\n                  fontSize: '0.875rem',\n                  color: '#374151'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                    size: 14,\n                    color: \"#6b7280\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 554,\n                    columnNumber: 25\n                  }, this), log.timestamp.toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                style: {\n                  padding: '1rem',\n                  fontSize: '0.875rem',\n                  color: '#374151'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontWeight: '600'\n                    },\n                    children: log.userName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 560,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.75rem',\n                      color: '#6b7280'\n                    },\n                    children: log.userEmail\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 561,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 559,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                style: {\n                  padding: '1rem',\n                  fontSize: '0.875rem',\n                  color: '#374151'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem'\n                  },\n                  children: [getActionIcon(log.action), log.action.replace(/_/g, ' ')]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 565,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                style: {\n                  padding: '1rem',\n                  fontSize: '0.875rem',\n                  color: '#374151'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontWeight: '600'\n                    },\n                    children: log.resource\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 572,\n                    columnNumber: 25\n                  }, this), log.resourceId && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.75rem',\n                      color: '#6b7280'\n                    },\n                    children: [\"ID: \", log.resourceId]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 574,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 571,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                style: {\n                  padding: '1rem',\n                  fontSize: '0.875rem'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    padding: '0.25rem 0.5rem',\n                    background: log.success ? '#dcfce7' : '#fef2f2',\n                    color: log.success ? '#166534' : '#dc2626',\n                    borderRadius: '4px',\n                    fontSize: '0.75rem',\n                    fontWeight: '600'\n                  },\n                  children: log.success ? 'Success' : 'Failed'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 579,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 578,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                style: {\n                  padding: '1rem'\n                },\n                children: getSeverityBadge(log.severity)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 590,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                style: {\n                  padding: '1rem',\n                  fontSize: '0.875rem',\n                  color: '#374151',\n                  fontFamily: 'monospace'\n                },\n                children: log.ipAddress\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 593,\n                columnNumber: 21\n              }, this)]\n            }, log.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 517,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 516,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 479,\n      columnNumber: 7\n    }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        gap: '1rem',\n        marginTop: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setCurrentPage(prev => Math.max(1, prev - 1)),\n        disabled: currentPage === 1,\n        style: {\n          padding: '0.5rem 1rem',\n          background: currentPage === 1 ? '#f3f4f6' : '#3b82f6',\n          color: currentPage === 1 ? '#9ca3af' : 'white',\n          border: 'none',\n          borderRadius: '6px',\n          cursor: currentPage === 1 ? 'not-allowed' : 'pointer'\n        },\n        children: \"Previous\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 613,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          fontSize: '0.875rem',\n          color: '#6b7280'\n        },\n        children: [\"Page \", currentPage, \" of \", totalPages]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 628,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setCurrentPage(prev => Math.min(totalPages, prev + 1)),\n        disabled: currentPage === totalPages,\n        style: {\n          padding: '0.5rem 1rem',\n          background: currentPage === totalPages ? '#f3f4f6' : '#3b82f6',\n          color: currentPage === totalPages ? '#9ca3af' : 'white',\n          border: 'none',\n          borderRadius: '6px',\n          cursor: currentPage === totalPages ? 'not-allowed' : 'pointer'\n        },\n        children: \"Next\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 632,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 606,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 243,\n    columnNumber: 5\n  }, this);\n};\n_s(AuditLogs, \"FKHH+SoeBSJ6OefGwLTQRvYs9uw=\", false, function () {\n  return [useAdminAuth, usePermissions];\n});\n_c = AuditLogs;\nexport default AuditLogs;\nvar _c;\n$RefreshReg$(_c, \"AuditLogs\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Shield", "Filter", "Download", "Eye", "Calendar", "User", "Activity", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useAdminAuth", "usePermissions", "jsxDEV", "_jsxDEV", "AuditLogs", "_s", "user", "permissions", "logs", "setLogs", "loading", "setLoading", "error", "setError", "searchTerm", "setSearchTerm", "selected<PERSON>ser", "setSelectedUser", "selectedAction", "setSelectedAction", "selectedSeverity", "setSelectedSeverity", "date<PERSON><PERSON><PERSON>", "setDateRange", "start", "end", "showFilters", "setShowFilters", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "logsPerPage", "isSuperAdmin", "style", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "textAlign", "color", "children", "size", "marginBottom", "opacity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "margin", "fontSize", "fontWeight", "marginTop", "padding", "background", "getPositionBadgeColor", "borderRadius", "getPositionDisplayName", "loadAuditLogs", "mockLogs", "id", "timestamp", "Date", "userId", "userEmail", "userName", "action", "resource", "details", "loginMethod", "ip<PERSON><PERSON><PERSON>", "userAgent", "success", "severity", "resourceId", "studentEmail", "gradeLevel", "changes", "phone_number", "title", "reason", "attempts", "Math", "ceil", "length", "err", "message", "getSeverityColor", "getSeverityBadge", "textTransform", "getActionIcon", "includes", "exportLogs", "csv<PERSON><PERSON>nt", "join", "map", "log", "toISOString", "blob", "Blob", "type", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "split", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "max<PERSON><PERSON><PERSON>", "gap", "onClick", "border", "cursor", "boxShadow", "gridTemplateColumns", "value", "onChange", "e", "target", "placeholder", "width", "prev", "overflow", "height", "borderTop", "animation", "overflowX", "borderCollapse", "index", "borderBottom", "toLocaleString", "replace", "fontFamily", "max", "disabled", "min", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/admin/AuditLogs.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Shield, Search, Filter, Download, Eye, Calendar, User, Activity, AlertTriangle } from 'lucide-react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\n\ninterface AuditLog {\n  id: string;\n  timestamp: Date;\n  userId: number;\n  userEmail: string;\n  userName: string;\n  action: string;\n  resource: string;\n  resourceId?: string;\n  details: Record<string, any>;\n  ipAddress: string;\n  userAgent: string;\n  success: boolean;\n  severity: 'low' | 'medium' | 'high' | 'critical';\n}\n\nconst AuditLogs: React.FC = () => {\n  const { user } = useAdminAuth();\n  const permissions = usePermissions(user);\n  \n  const [logs, setLogs] = useState<AuditLog[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  \n  // Filters\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedUser, setSelectedUser] = useState('');\n  const [selectedAction, setSelectedAction] = useState('');\n  const [selectedSeverity, setSelectedSeverity] = useState('');\n  const [dateRange, setDateRange] = useState({ start: '', end: '' });\n  const [showFilters, setShowFilters] = useState(false);\n  \n  // Pagination\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const logsPerPage = 20;\n\n  // Permission check\n  if (!permissions.isSuperAdmin) {\n    return (\n      <div style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      }}>\n        <Shield size={64} style={{ marginBottom: '1rem', opacity: 0.5 }} />\n        <h2 style={{ margin: '0 0 0.5rem', fontSize: '1.5rem', fontWeight: '600' }}>\n          Access Denied\n        </h2>\n        <p style={{ margin: 0, fontSize: '1rem' }}>\n          System audit logs are restricted to Super Administrators only.\n        </p>\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        }}>\n          Current Role: {permissions.getPositionDisplayName()}\n        </div>\n      </div>\n    );\n  }\n\n  useEffect(() => {\n    loadAuditLogs();\n  }, [currentPage, searchTerm, selectedUser, selectedAction, selectedSeverity, dateRange]);\n\n  const loadAuditLogs = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      // TODO: Implement actual API call\n      // const response = await auditService.getLogs({\n      //   page: currentPage,\n      //   limit: logsPerPage,\n      //   search: searchTerm,\n      //   user: selectedUser,\n      //   action: selectedAction,\n      //   severity: selectedSeverity,\n      //   startDate: dateRange.start,\n      //   endDate: dateRange.end\n      // });\n      \n      // Mock data for demonstration\n      const mockLogs: AuditLog[] = [\n        {\n          id: '1',\n          timestamp: new Date('2025-08-05T10:30:00Z'),\n          userId: 1,\n          userEmail: '<EMAIL>',\n          userName: 'Von Christian Admin',\n          action: 'LOGIN',\n          resource: 'AUTH',\n          details: { loginMethod: 'email_password' },\n          ipAddress: '*************',\n          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n          success: true,\n          severity: 'low'\n        },\n        {\n          id: '2',\n          timestamp: new Date('2025-08-05T10:25:00Z'),\n          userId: 1,\n          userEmail: '<EMAIL>',\n          userName: 'Von Christian Admin',\n          action: 'CREATE_STUDENT',\n          resource: 'STUDENT',\n          resourceId: '123',\n          details: { studentEmail: '<EMAIL>', gradeLevel: 11 },\n          ipAddress: '*************',\n          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n          success: true,\n          severity: 'medium'\n        },\n        {\n          id: '3',\n          timestamp: new Date('2025-08-05T10:20:00Z'),\n          userId: 2,\n          userEmail: '<EMAIL>',\n          userName: 'Zaira Professor',\n          action: 'UPDATE_STUDENT',\n          resource: 'STUDENT',\n          resourceId: '456',\n          details: { changes: { phone_number: '+639123456789' } },\n          ipAddress: '*************',\n          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n          success: true,\n          severity: 'low'\n        },\n        {\n          id: '4',\n          timestamp: new Date('2025-08-05T10:15:00Z'),\n          userId: 1,\n          userEmail: '<EMAIL>',\n          userName: 'Von Christian Admin',\n          action: 'DELETE_ANNOUNCEMENT',\n          resource: 'ANNOUNCEMENT',\n          resourceId: '789',\n          details: { title: 'Old Announcement', reason: 'Outdated content' },\n          ipAddress: '*************',\n          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n          success: true,\n          severity: 'high'\n        },\n        {\n          id: '5',\n          timestamp: new Date('2025-08-05T10:10:00Z'),\n          userId: 3,\n          userEmail: '<EMAIL>',\n          userName: 'Unknown User',\n          action: 'FAILED_LOGIN',\n          resource: 'AUTH',\n          details: { reason: 'Invalid credentials', attempts: 3 },\n          ipAddress: '*************',\n          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n          success: false,\n          severity: 'critical'\n        }\n      ];\n      \n      setLogs(mockLogs);\n      setTotalPages(Math.ceil(mockLogs.length / logsPerPage));\n      \n    } catch (err: any) {\n      setError(err.message || 'Failed to load audit logs');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getSeverityColor = (severity: AuditLog['severity']) => {\n    switch (severity) {\n      case 'low': return '#10b981';\n      case 'medium': return '#f59e0b';\n      case 'high': return '#ef4444';\n      case 'critical': return '#dc2626';\n      default: return '#6b7280';\n    }\n  };\n\n  const getSeverityBadge = (severity: AuditLog['severity']) => (\n    <span style={{\n      padding: '0.25rem 0.5rem',\n      background: getSeverityColor(severity),\n      color: 'white',\n      borderRadius: '4px',\n      fontSize: '0.75rem',\n      fontWeight: '600',\n      textTransform: 'uppercase'\n    }}>\n      {severity}\n    </span>\n  );\n\n  const getActionIcon = (action: string) => {\n    if (action.includes('LOGIN')) return <User size={16} />;\n    if (action.includes('CREATE')) return <Activity size={16} />;\n    if (action.includes('UPDATE')) return <Eye size={16} />;\n    if (action.includes('DELETE')) return <AlertTriangle size={16} />;\n    return <Activity size={16} />;\n  };\n\n  const exportLogs = () => {\n    const csvContent = [\n      ['Timestamp', 'User', 'Action', 'Resource', 'Success', 'Severity', 'IP Address'].join(','),\n      ...logs.map(log => [\n        log.timestamp.toISOString(),\n        log.userName,\n        log.action,\n        log.resource,\n        log.success ? 'Yes' : 'No',\n        log.severity,\n        log.ipAddress\n      ].join(','))\n    ].join('\\n');\n\n    const blob = new Blob([csvContent], { type: 'text/csv' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `audit_logs_${new Date().toISOString().split('T')[0]}.csv`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  return (\n    <div style={{ maxWidth: '1400px', margin: '0 auto' }}>\n      {/* Header */}\n      <div style={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      }}>\n        <div>\n          <h1 style={{\n            margin: '0 0 0.5rem',\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          }}>\n            System Audit Logs\n          </h1>\n          <p style={{\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          }}>\n            Monitor and track all system activities and user actions\n          </p>\n        </div>\n        \n        <div style={{ display: 'flex', gap: '1rem' }}>\n          <button\n            onClick={() => setShowFilters(!showFilters)}\n            style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              padding: '0.75rem 1rem',\n              background: showFilters ? '#3b82f6' : 'white',\n              color: showFilters ? 'white' : '#374151',\n              border: '1px solid #d1d5db',\n              borderRadius: '6px',\n              fontSize: '0.875rem',\n              fontWeight: '600',\n              cursor: 'pointer'\n            }}\n          >\n            <Filter size={16} />\n            Filters\n          </button>\n          \n          <button\n            onClick={exportLogs}\n            style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              padding: '0.75rem 1rem',\n              background: '#10b981',\n              color: 'white',\n              border: 'none',\n              borderRadius: '6px',\n              fontSize: '0.875rem',\n              fontWeight: '600',\n              cursor: 'pointer'\n            }}\n          >\n            <Download size={16} />\n            Export CSV\n          </button>\n        </div>\n      </div>\n\n      {/* Filters */}\n      {showFilters && (\n        <div style={{\n          background: 'white',\n          borderRadius: '8px',\n          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n          padding: '1.5rem',\n          marginBottom: '2rem'\n        }}>\n          <div style={{\n            display: 'grid',\n            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n            gap: '1rem'\n          }}>\n            <div>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                Search\n              </label>\n              <input\n                type=\"text\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                placeholder=\"Search logs...\"\n                style={{\n                  width: '100%',\n                  padding: '0.5rem',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '6px',\n                  fontSize: '0.875rem'\n                }}\n              />\n            </div>\n\n            <div>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                Action\n              </label>\n              <select\n                value={selectedAction}\n                onChange={(e) => setSelectedAction(e.target.value)}\n                style={{\n                  width: '100%',\n                  padding: '0.5rem',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '6px',\n                  fontSize: '0.875rem'\n                }}\n              >\n                <option value=\"\">All Actions</option>\n                <option value=\"LOGIN\">Login</option>\n                <option value=\"CREATE\">Create</option>\n                <option value=\"UPDATE\">Update</option>\n                <option value=\"DELETE\">Delete</option>\n              </select>\n            </div>\n\n            <div>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                Severity\n              </label>\n              <select\n                value={selectedSeverity}\n                onChange={(e) => setSelectedSeverity(e.target.value)}\n                style={{\n                  width: '100%',\n                  padding: '0.5rem',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '6px',\n                  fontSize: '0.875rem'\n                }}\n              >\n                <option value=\"\">All Severities</option>\n                <option value=\"low\">Low</option>\n                <option value=\"medium\">Medium</option>\n                <option value=\"high\">High</option>\n                <option value=\"critical\">Critical</option>\n              </select>\n            </div>\n\n            <div>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                Start Date\n              </label>\n              <input\n                type=\"date\"\n                value={dateRange.start}\n                onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}\n                style={{\n                  width: '100%',\n                  padding: '0.5rem',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '6px',\n                  fontSize: '0.875rem'\n                }}\n              />\n            </div>\n\n            <div>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                End Date\n              </label>\n              <input\n                type=\"date\"\n                value={dateRange.end}\n                onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}\n                style={{\n                  width: '100%',\n                  padding: '0.5rem',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '6px',\n                  fontSize: '0.875rem'\n                }}\n              />\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Error Message */}\n      {error && (\n        <div style={{\n          padding: '1rem',\n          background: '#fef2f2',\n          border: '1px solid #fecaca',\n          borderRadius: '8px',\n          color: '#dc2626',\n          marginBottom: '1rem',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        }}>\n          <AlertTriangle size={16} />\n          {error}\n        </div>\n      )}\n\n      {/* Logs Table */}\n      <div style={{\n        background: 'white',\n        borderRadius: '12px',\n        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n        overflow: 'hidden'\n      }}>\n        {loading ? (\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            padding: '3rem'\n          }}>\n            <div style={{\n              width: '40px',\n              height: '40px',\n              border: '4px solid #f3f4f6',\n              borderTop: '4px solid #3b82f6',\n              borderRadius: '50%',\n              animation: 'spin 1s linear infinite'\n            }} />\n          </div>\n        ) : logs.length === 0 ? (\n          <div style={{\n            textAlign: 'center',\n            padding: '3rem',\n            color: '#6b7280'\n          }}>\n            <Shield size={48} style={{ marginBottom: '1rem', opacity: 0.5 }} />\n            <h3 style={{ margin: '0 0 0.5rem', fontSize: '1.125rem', fontWeight: '600' }}>\n              No audit logs found\n            </h3>\n            <p style={{ margin: 0, fontSize: '0.875rem' }}>\n              No logs match your current filters\n            </p>\n          </div>\n        ) : (\n          <div style={{ overflowX: 'auto' }}>\n            <table style={{ width: '100%', borderCollapse: 'collapse' }}>\n              <thead style={{ background: '#f9fafb' }}>\n                <tr>\n                  <th style={{ padding: '1rem', textAlign: 'left', fontSize: '0.875rem', fontWeight: '600', color: '#374151' }}>\n                    Timestamp\n                  </th>\n                  <th style={{ padding: '1rem', textAlign: 'left', fontSize: '0.875rem', fontWeight: '600', color: '#374151' }}>\n                    User\n                  </th>\n                  <th style={{ padding: '1rem', textAlign: 'left', fontSize: '0.875rem', fontWeight: '600', color: '#374151' }}>\n                    Action\n                  </th>\n                  <th style={{ padding: '1rem', textAlign: 'left', fontSize: '0.875rem', fontWeight: '600', color: '#374151' }}>\n                    Resource\n                  </th>\n                  <th style={{ padding: '1rem', textAlign: 'left', fontSize: '0.875rem', fontWeight: '600', color: '#374151' }}>\n                    Status\n                  </th>\n                  <th style={{ padding: '1rem', textAlign: 'left', fontSize: '0.875rem', fontWeight: '600', color: '#374151' }}>\n                    Severity\n                  </th>\n                  <th style={{ padding: '1rem', textAlign: 'left', fontSize: '0.875rem', fontWeight: '600', color: '#374151' }}>\n                    IP Address\n                  </th>\n                </tr>\n              </thead>\n              <tbody>\n                {logs.map((log, index) => (\n                  <tr\n                    key={log.id}\n                    style={{\n                      borderBottom: index < logs.length - 1 ? '1px solid #f3f4f6' : 'none',\n                      background: log.success ? 'transparent' : '#fef2f2'\n                    }}\n                  >\n                    <td style={{ padding: '1rem', fontSize: '0.875rem', color: '#374151' }}>\n                      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                        <Calendar size={14} color=\"#6b7280\" />\n                        {log.timestamp.toLocaleString()}\n                      </div>\n                    </td>\n                    <td style={{ padding: '1rem', fontSize: '0.875rem', color: '#374151' }}>\n                      <div>\n                        <div style={{ fontWeight: '600' }}>{log.userName}</div>\n                        <div style={{ fontSize: '0.75rem', color: '#6b7280' }}>{log.userEmail}</div>\n                      </div>\n                    </td>\n                    <td style={{ padding: '1rem', fontSize: '0.875rem', color: '#374151' }}>\n                      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                        {getActionIcon(log.action)}\n                        {log.action.replace(/_/g, ' ')}\n                      </div>\n                    </td>\n                    <td style={{ padding: '1rem', fontSize: '0.875rem', color: '#374151' }}>\n                      <div>\n                        <div style={{ fontWeight: '600' }}>{log.resource}</div>\n                        {log.resourceId && (\n                          <div style={{ fontSize: '0.75rem', color: '#6b7280' }}>ID: {log.resourceId}</div>\n                        )}\n                      </div>\n                    </td>\n                    <td style={{ padding: '1rem', fontSize: '0.875rem' }}>\n                      <span style={{\n                        padding: '0.25rem 0.5rem',\n                        background: log.success ? '#dcfce7' : '#fef2f2',\n                        color: log.success ? '#166534' : '#dc2626',\n                        borderRadius: '4px',\n                        fontSize: '0.75rem',\n                        fontWeight: '600'\n                      }}>\n                        {log.success ? 'Success' : 'Failed'}\n                      </span>\n                    </td>\n                    <td style={{ padding: '1rem' }}>\n                      {getSeverityBadge(log.severity)}\n                    </td>\n                    <td style={{ padding: '1rem', fontSize: '0.875rem', color: '#374151', fontFamily: 'monospace' }}>\n                      {log.ipAddress}\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        )}\n      </div>\n\n      {/* Pagination */}\n      {totalPages > 1 && (\n        <div style={{\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          gap: '1rem',\n          marginTop: '2rem'\n        }}>\n          <button\n            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}\n            disabled={currentPage === 1}\n            style={{\n              padding: '0.5rem 1rem',\n              background: currentPage === 1 ? '#f3f4f6' : '#3b82f6',\n              color: currentPage === 1 ? '#9ca3af' : 'white',\n              border: 'none',\n              borderRadius: '6px',\n              cursor: currentPage === 1 ? 'not-allowed' : 'pointer'\n            }}\n          >\n            Previous\n          </button>\n          \n          <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>\n            Page {currentPage} of {totalPages}\n          </span>\n          \n          <button\n            onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}\n            disabled={currentPage === totalPages}\n            style={{\n              padding: '0.5rem 1rem',\n              background: currentPage === totalPages ? '#f3f4f6' : '#3b82f6',\n              color: currentPage === totalPages ? '#9ca3af' : 'white',\n              border: 'none',\n              borderRadius: '6px',\n              cursor: currentPage === totalPages ? 'not-allowed' : 'pointer'\n            }}\n          >\n            Next\n          </button>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AuditLogs;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAUC,MAAM,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,aAAa,QAAQ,cAAc;AAC7G,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAkBzD,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC;EAAK,CAAC,GAAGN,YAAY,CAAC,CAAC;EAC/B,MAAMO,WAAW,GAAGN,cAAc,CAACK,IAAI,CAAC;EAExC,MAAM,CAACE,IAAI,EAAEC,OAAO,CAAC,GAAGnB,QAAQ,CAAa,EAAE,CAAC;EAChD,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAgB,IAAI,CAAC;;EAEvD;EACA,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC8B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC;IAAEkC,KAAK,EAAE,EAAE;IAAEC,GAAG,EAAE;EAAG,CAAC,CAAC;EAClE,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACA,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACwC,UAAU,EAAEC,aAAa,CAAC,GAAGzC,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM0C,WAAW,GAAG,EAAE;;EAEtB;EACA,IAAI,CAACzB,WAAW,CAAC0B,YAAY,EAAE;IAC7B,oBACE9B,OAAA;MAAK+B,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE,OAAO;QAClBC,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACAvC,OAAA,CAACX,MAAM;QAACmD,IAAI,EAAE,EAAG;QAACT,KAAK,EAAE;UAAEU,YAAY,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAI;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnE9C,OAAA;QAAI+B,KAAK,EAAE;UAAEgB,MAAM,EAAE,YAAY;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAM,CAAE;QAAAV,QAAA,EAAC;MAE5E;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL9C,OAAA;QAAG+B,KAAK,EAAE;UAAEgB,MAAM,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAT,QAAA,EAAC;MAE3C;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJ9C,OAAA;QAAK+B,KAAK,EAAE;UACVmB,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAEhD,WAAW,CAACiD,qBAAqB,CAAC,CAAC;UAC/CC,YAAY,EAAE,KAAK;UACnBhB,KAAK,EAAE,OAAO;UACdU,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE;QACd,CAAE;QAAAV,QAAA,GAAC,gBACa,EAACnC,WAAW,CAACmD,sBAAsB,CAAC,CAAC;MAAA;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA1D,SAAS,CAAC,MAAM;IACdoE,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAAC/B,WAAW,EAAEd,UAAU,EAAEE,YAAY,EAAEE,cAAc,EAAEE,gBAAgB,EAAEE,SAAS,CAAC,CAAC;EAExF,MAAMqC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFhD,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA,MAAM+C,QAAoB,GAAG,CAC3B;QACEC,EAAE,EAAE,GAAG;QACPC,SAAS,EAAE,IAAIC,IAAI,CAAC,sBAAsB,CAAC;QAC3CC,MAAM,EAAE,CAAC;QACTC,SAAS,EAAE,0BAA0B;QACrCC,QAAQ,EAAE,qBAAqB;QAC/BC,MAAM,EAAE,OAAO;QACfC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE;UAAEC,WAAW,EAAE;QAAiB,CAAC;QAC1CC,SAAS,EAAE,eAAe;QAC1BC,SAAS,EAAE,8DAA8D;QACzEC,OAAO,EAAE,IAAI;QACbC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEb,EAAE,EAAE,GAAG;QACPC,SAAS,EAAE,IAAIC,IAAI,CAAC,sBAAsB,CAAC;QAC3CC,MAAM,EAAE,CAAC;QACTC,SAAS,EAAE,0BAA0B;QACrCC,QAAQ,EAAE,qBAAqB;QAC/BC,MAAM,EAAE,gBAAgB;QACxBC,QAAQ,EAAE,SAAS;QACnBO,UAAU,EAAE,KAAK;QACjBN,OAAO,EAAE;UAAEO,YAAY,EAAE,wBAAwB;UAAEC,UAAU,EAAE;QAAG,CAAC;QACnEN,SAAS,EAAE,eAAe;QAC1BC,SAAS,EAAE,8DAA8D;QACzEC,OAAO,EAAE,IAAI;QACbC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEb,EAAE,EAAE,GAAG;QACPC,SAAS,EAAE,IAAIC,IAAI,CAAC,sBAAsB,CAAC;QAC3CC,MAAM,EAAE,CAAC;QACTC,SAAS,EAAE,oBAAoB;QAC/BC,QAAQ,EAAE,iBAAiB;QAC3BC,MAAM,EAAE,gBAAgB;QACxBC,QAAQ,EAAE,SAAS;QACnBO,UAAU,EAAE,KAAK;QACjBN,OAAO,EAAE;UAAES,OAAO,EAAE;YAAEC,YAAY,EAAE;UAAgB;QAAE,CAAC;QACvDR,SAAS,EAAE,eAAe;QAC1BC,SAAS,EAAE,8DAA8D;QACzEC,OAAO,EAAE,IAAI;QACbC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEb,EAAE,EAAE,GAAG;QACPC,SAAS,EAAE,IAAIC,IAAI,CAAC,sBAAsB,CAAC;QAC3CC,MAAM,EAAE,CAAC;QACTC,SAAS,EAAE,0BAA0B;QACrCC,QAAQ,EAAE,qBAAqB;QAC/BC,MAAM,EAAE,qBAAqB;QAC7BC,QAAQ,EAAE,cAAc;QACxBO,UAAU,EAAE,KAAK;QACjBN,OAAO,EAAE;UAAEW,KAAK,EAAE,kBAAkB;UAAEC,MAAM,EAAE;QAAmB,CAAC;QAClEV,SAAS,EAAE,eAAe;QAC1BC,SAAS,EAAE,8DAA8D;QACzEC,OAAO,EAAE,IAAI;QACbC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEb,EAAE,EAAE,GAAG;QACPC,SAAS,EAAE,IAAIC,IAAI,CAAC,sBAAsB,CAAC;QAC3CC,MAAM,EAAE,CAAC;QACTC,SAAS,EAAE,qBAAqB;QAChCC,QAAQ,EAAE,cAAc;QACxBC,MAAM,EAAE,cAAc;QACtBC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE;UAAEY,MAAM,EAAE,qBAAqB;UAAEC,QAAQ,EAAE;QAAE,CAAC;QACvDX,SAAS,EAAE,eAAe;QAC1BC,SAAS,EAAE,8DAA8D;QACzEC,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE;MACZ,CAAC,CACF;MAEDjE,OAAO,CAACmD,QAAQ,CAAC;MACjB7B,aAAa,CAACoD,IAAI,CAACC,IAAI,CAACxB,QAAQ,CAACyB,MAAM,GAAGrD,WAAW,CAAC,CAAC;IAEzD,CAAC,CAAC,OAAOsD,GAAQ,EAAE;MACjBzE,QAAQ,CAACyE,GAAG,CAACC,OAAO,IAAI,2BAA2B,CAAC;IACtD,CAAC,SAAS;MACR5E,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM6E,gBAAgB,GAAId,QAA8B,IAAK;IAC3D,QAAQA,QAAQ;MACd,KAAK,KAAK;QAAE,OAAO,SAAS;MAC5B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMe,gBAAgB,GAAIf,QAA8B,iBACtDvE,OAAA;IAAM+B,KAAK,EAAE;MACXoB,OAAO,EAAE,gBAAgB;MACzBC,UAAU,EAAEiC,gBAAgB,CAACd,QAAQ,CAAC;MACtCjC,KAAK,EAAE,OAAO;MACdgB,YAAY,EAAE,KAAK;MACnBN,QAAQ,EAAE,SAAS;MACnBC,UAAU,EAAE,KAAK;MACjBsC,aAAa,EAAE;IACjB,CAAE;IAAAhD,QAAA,EACCgC;EAAQ;IAAA5B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACP;EAED,MAAM0C,aAAa,GAAIxB,MAAc,IAAK;IACxC,IAAIA,MAAM,CAACyB,QAAQ,CAAC,OAAO,CAAC,EAAE,oBAAOzF,OAAA,CAACN,IAAI;MAAC8C,IAAI,EAAE;IAAG;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvD,IAAIkB,MAAM,CAACyB,QAAQ,CAAC,QAAQ,CAAC,EAAE,oBAAOzF,OAAA,CAACL,QAAQ;MAAC6C,IAAI,EAAE;IAAG;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5D,IAAIkB,MAAM,CAACyB,QAAQ,CAAC,QAAQ,CAAC,EAAE,oBAAOzF,OAAA,CAACR,GAAG;MAACgD,IAAI,EAAE;IAAG;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvD,IAAIkB,MAAM,CAACyB,QAAQ,CAAC,QAAQ,CAAC,EAAE,oBAAOzF,OAAA,CAACJ,aAAa;MAAC4C,IAAI,EAAE;IAAG;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjE,oBAAO9C,OAAA,CAACL,QAAQ;MAAC6C,IAAI,EAAE;IAAG;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC/B,CAAC;EAED,MAAM4C,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMC,UAAU,GAAG,CACjB,CAAC,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,EAC1F,GAAGvF,IAAI,CAACwF,GAAG,CAACC,GAAG,IAAI,CACjBA,GAAG,CAACnC,SAAS,CAACoC,WAAW,CAAC,CAAC,EAC3BD,GAAG,CAAC/B,QAAQ,EACZ+B,GAAG,CAAC9B,MAAM,EACV8B,GAAG,CAAC7B,QAAQ,EACZ6B,GAAG,CAACxB,OAAO,GAAG,KAAK,GAAG,IAAI,EAC1BwB,GAAG,CAACvB,QAAQ,EACZuB,GAAG,CAAC1B,SAAS,CACd,CAACwB,IAAI,CAAC,GAAG,CAAC,CAAC,CACb,CAACA,IAAI,CAAC,IAAI,CAAC;IAEZ,MAAMI,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACN,UAAU,CAAC,EAAE;MAAEO,IAAI,EAAE;IAAW,CAAC,CAAC;IACzD,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;IACrC,MAAMM,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGN,GAAG;IACZG,CAAC,CAACI,QAAQ,GAAG,cAAc,IAAI9C,IAAI,CAAC,CAAC,CAACmC,WAAW,CAAC,CAAC,CAACY,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;IACvEJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,CAAC,CAAC;IAC5BA,CAAC,CAACQ,KAAK,CAAC,CAAC;IACTP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,CAAC,CAAC;IAC5BF,GAAG,CAACY,eAAe,CAACb,GAAG,CAAC;EAC1B,CAAC;EAED,oBACEnG,OAAA;IAAK+B,KAAK,EAAE;MAAEkF,QAAQ,EAAE,QAAQ;MAAElE,MAAM,EAAE;IAAS,CAAE;IAAAR,QAAA,gBAEnDvC,OAAA;MAAK+B,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfG,cAAc,EAAE,eAAe;QAC/BD,UAAU,EAAE,QAAQ;QACpBO,YAAY,EAAE;MAChB,CAAE;MAAAF,QAAA,gBACAvC,OAAA;QAAAuC,QAAA,gBACEvC,OAAA;UAAI+B,KAAK,EAAE;YACTgB,MAAM,EAAE,YAAY;YACpBC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjBX,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,EAAC;QAEH;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL9C,OAAA;UAAG+B,KAAK,EAAE;YACRgB,MAAM,EAAE,CAAC;YACTT,KAAK,EAAE,SAAS;YAChBU,QAAQ,EAAE;UACZ,CAAE;UAAAT,QAAA,EAAC;QAEH;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN9C,OAAA;QAAK+B,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEkF,GAAG,EAAE;QAAO,CAAE;QAAA3E,QAAA,gBAC3CvC,OAAA;UACEmH,OAAO,EAAEA,CAAA,KAAM3F,cAAc,CAAC,CAACD,WAAW,CAAE;UAC5CQ,KAAK,EAAE;YACLC,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBgF,GAAG,EAAE,QAAQ;YACb/D,OAAO,EAAE,cAAc;YACvBC,UAAU,EAAE7B,WAAW,GAAG,SAAS,GAAG,OAAO;YAC7Ce,KAAK,EAAEf,WAAW,GAAG,OAAO,GAAG,SAAS;YACxC6F,MAAM,EAAE,mBAAmB;YAC3B9D,YAAY,EAAE,KAAK;YACnBN,QAAQ,EAAE,UAAU;YACpBC,UAAU,EAAE,KAAK;YACjBoE,MAAM,EAAE;UACV,CAAE;UAAA9E,QAAA,gBAEFvC,OAAA,CAACV,MAAM;YAACkD,IAAI,EAAE;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAEtB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET9C,OAAA;UACEmH,OAAO,EAAEzB,UAAW;UACpB3D,KAAK,EAAE;YACLC,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBgF,GAAG,EAAE,QAAQ;YACb/D,OAAO,EAAE,cAAc;YACvBC,UAAU,EAAE,SAAS;YACrBd,KAAK,EAAE,OAAO;YACd8E,MAAM,EAAE,MAAM;YACd9D,YAAY,EAAE,KAAK;YACnBN,QAAQ,EAAE,UAAU;YACpBC,UAAU,EAAE,KAAK;YACjBoE,MAAM,EAAE;UACV,CAAE;UAAA9E,QAAA,gBAEFvC,OAAA,CAACT,QAAQ;YAACiD,IAAI,EAAE;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,cAExB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLvB,WAAW,iBACVvB,OAAA;MAAK+B,KAAK,EAAE;QACVqB,UAAU,EAAE,OAAO;QACnBE,YAAY,EAAE,KAAK;QACnBgE,SAAS,EAAE,8BAA8B;QACzCnE,OAAO,EAAE,QAAQ;QACjBV,YAAY,EAAE;MAChB,CAAE;MAAAF,QAAA,eACAvC,OAAA;QAAK+B,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACfuF,mBAAmB,EAAE,sCAAsC;UAC3DL,GAAG,EAAE;QACP,CAAE;QAAA3E,QAAA,gBACAvC,OAAA;UAAAuC,QAAA,gBACEvC,OAAA;YAAO+B,KAAK,EAAE;cACZC,OAAO,EAAE,OAAO;cAChBgB,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBX,KAAK,EAAE,SAAS;cAChBG,YAAY,EAAE;YAChB,CAAE;YAAAF,QAAA,EAAC;UAEH;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR9C,OAAA;YACEkG,IAAI,EAAC,MAAM;YACXsB,KAAK,EAAE7G,UAAW;YAClB8G,QAAQ,EAAGC,CAAC,IAAK9G,aAAa,CAAC8G,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC/CI,WAAW,EAAC,gBAAgB;YAC5B7F,KAAK,EAAE;cACL8F,KAAK,EAAE,MAAM;cACb1E,OAAO,EAAE,QAAQ;cACjBiE,MAAM,EAAE,mBAAmB;cAC3B9D,YAAY,EAAE,KAAK;cACnBN,QAAQ,EAAE;YACZ;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN9C,OAAA;UAAAuC,QAAA,gBACEvC,OAAA;YAAO+B,KAAK,EAAE;cACZC,OAAO,EAAE,OAAO;cAChBgB,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBX,KAAK,EAAE,SAAS;cAChBG,YAAY,EAAE;YAChB,CAAE;YAAAF,QAAA,EAAC;UAEH;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR9C,OAAA;YACEwH,KAAK,EAAEzG,cAAe;YACtB0G,QAAQ,EAAGC,CAAC,IAAK1G,iBAAiB,CAAC0G,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YACnDzF,KAAK,EAAE;cACL8F,KAAK,EAAE,MAAM;cACb1E,OAAO,EAAE,QAAQ;cACjBiE,MAAM,EAAE,mBAAmB;cAC3B9D,YAAY,EAAE,KAAK;cACnBN,QAAQ,EAAE;YACZ,CAAE;YAAAT,QAAA,gBAEFvC,OAAA;cAAQwH,KAAK,EAAC,EAAE;cAAAjF,QAAA,EAAC;YAAW;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrC9C,OAAA;cAAQwH,KAAK,EAAC,OAAO;cAAAjF,QAAA,EAAC;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpC9C,OAAA;cAAQwH,KAAK,EAAC,QAAQ;cAAAjF,QAAA,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtC9C,OAAA;cAAQwH,KAAK,EAAC,QAAQ;cAAAjF,QAAA,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtC9C,OAAA;cAAQwH,KAAK,EAAC,QAAQ;cAAAjF,QAAA,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN9C,OAAA;UAAAuC,QAAA,gBACEvC,OAAA;YAAO+B,KAAK,EAAE;cACZC,OAAO,EAAE,OAAO;cAChBgB,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBX,KAAK,EAAE,SAAS;cAChBG,YAAY,EAAE;YAChB,CAAE;YAAAF,QAAA,EAAC;UAEH;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR9C,OAAA;YACEwH,KAAK,EAAEvG,gBAAiB;YACxBwG,QAAQ,EAAGC,CAAC,IAAKxG,mBAAmB,CAACwG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YACrDzF,KAAK,EAAE;cACL8F,KAAK,EAAE,MAAM;cACb1E,OAAO,EAAE,QAAQ;cACjBiE,MAAM,EAAE,mBAAmB;cAC3B9D,YAAY,EAAE,KAAK;cACnBN,QAAQ,EAAE;YACZ,CAAE;YAAAT,QAAA,gBAEFvC,OAAA;cAAQwH,KAAK,EAAC,EAAE;cAAAjF,QAAA,EAAC;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxC9C,OAAA;cAAQwH,KAAK,EAAC,KAAK;cAAAjF,QAAA,EAAC;YAAG;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChC9C,OAAA;cAAQwH,KAAK,EAAC,QAAQ;cAAAjF,QAAA,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtC9C,OAAA;cAAQwH,KAAK,EAAC,MAAM;cAAAjF,QAAA,EAAC;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClC9C,OAAA;cAAQwH,KAAK,EAAC,UAAU;cAAAjF,QAAA,EAAC;YAAQ;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN9C,OAAA;UAAAuC,QAAA,gBACEvC,OAAA;YAAO+B,KAAK,EAAE;cACZC,OAAO,EAAE,OAAO;cAChBgB,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBX,KAAK,EAAE,SAAS;cAChBG,YAAY,EAAE;YAChB,CAAE;YAAAF,QAAA,EAAC;UAEH;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR9C,OAAA;YACEkG,IAAI,EAAC,MAAM;YACXsB,KAAK,EAAErG,SAAS,CAACE,KAAM;YACvBoG,QAAQ,EAAGC,CAAC,IAAKtG,YAAY,CAAC0G,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAEzG,KAAK,EAAEqG,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAC,CAAE;YAC5EzF,KAAK,EAAE;cACL8F,KAAK,EAAE,MAAM;cACb1E,OAAO,EAAE,QAAQ;cACjBiE,MAAM,EAAE,mBAAmB;cAC3B9D,YAAY,EAAE,KAAK;cACnBN,QAAQ,EAAE;YACZ;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN9C,OAAA;UAAAuC,QAAA,gBACEvC,OAAA;YAAO+B,KAAK,EAAE;cACZC,OAAO,EAAE,OAAO;cAChBgB,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBX,KAAK,EAAE,SAAS;cAChBG,YAAY,EAAE;YAChB,CAAE;YAAAF,QAAA,EAAC;UAEH;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR9C,OAAA;YACEkG,IAAI,EAAC,MAAM;YACXsB,KAAK,EAAErG,SAAS,CAACG,GAAI;YACrBmG,QAAQ,EAAGC,CAAC,IAAKtG,YAAY,CAAC0G,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAExG,GAAG,EAAEoG,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAC,CAAE;YAC1EzF,KAAK,EAAE;cACL8F,KAAK,EAAE,MAAM;cACb1E,OAAO,EAAE,QAAQ;cACjBiE,MAAM,EAAE,mBAAmB;cAC3B9D,YAAY,EAAE,KAAK;cACnBN,QAAQ,EAAE;YACZ;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGArC,KAAK,iBACJT,OAAA;MAAK+B,KAAK,EAAE;QACVoB,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,SAAS;QACrBgE,MAAM,EAAE,mBAAmB;QAC3B9D,YAAY,EAAE,KAAK;QACnBhB,KAAK,EAAE,SAAS;QAChBG,YAAY,EAAE,MAAM;QACpBT,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBgF,GAAG,EAAE;MACP,CAAE;MAAA3E,QAAA,gBACAvC,OAAA,CAACJ,aAAa;QAAC4C,IAAI,EAAE;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAC1BrC,KAAK;IAAA;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD9C,OAAA;MAAK+B,KAAK,EAAE;QACVqB,UAAU,EAAE,OAAO;QACnBE,YAAY,EAAE,MAAM;QACpBgE,SAAS,EAAE,8BAA8B;QACzCS,QAAQ,EAAE;MACZ,CAAE;MAAAxF,QAAA,EACChC,OAAO,gBACNP,OAAA;QAAK+B,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBgB,OAAO,EAAE;QACX,CAAE;QAAAZ,QAAA,eACAvC,OAAA;UAAK+B,KAAK,EAAE;YACV8F,KAAK,EAAE,MAAM;YACbG,MAAM,EAAE,MAAM;YACdZ,MAAM,EAAE,mBAAmB;YAC3Ba,SAAS,EAAE,mBAAmB;YAC9B3E,YAAY,EAAE,KAAK;YACnB4E,SAAS,EAAE;UACb;QAAE;UAAAvF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,GACJzC,IAAI,CAAC6E,MAAM,KAAK,CAAC,gBACnBlF,OAAA;QAAK+B,KAAK,EAAE;UACVM,SAAS,EAAE,QAAQ;UACnBc,OAAO,EAAE,MAAM;UACfb,KAAK,EAAE;QACT,CAAE;QAAAC,QAAA,gBACAvC,OAAA,CAACX,MAAM;UAACmD,IAAI,EAAE,EAAG;UAACT,KAAK,EAAE;YAAEU,YAAY,EAAE,MAAM;YAAEC,OAAO,EAAE;UAAI;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnE9C,OAAA;UAAI+B,KAAK,EAAE;YAAEgB,MAAM,EAAE,YAAY;YAAEC,QAAQ,EAAE,UAAU;YAAEC,UAAU,EAAE;UAAM,CAAE;UAAAV,QAAA,EAAC;QAE9E;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL9C,OAAA;UAAG+B,KAAK,EAAE;YAAEgB,MAAM,EAAE,CAAC;YAAEC,QAAQ,EAAE;UAAW,CAAE;UAAAT,QAAA,EAAC;QAE/C;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,gBAEN9C,OAAA;QAAK+B,KAAK,EAAE;UAAEoG,SAAS,EAAE;QAAO,CAAE;QAAA5F,QAAA,eAChCvC,OAAA;UAAO+B,KAAK,EAAE;YAAE8F,KAAK,EAAE,MAAM;YAAEO,cAAc,EAAE;UAAW,CAAE;UAAA7F,QAAA,gBAC1DvC,OAAA;YAAO+B,KAAK,EAAE;cAAEqB,UAAU,EAAE;YAAU,CAAE;YAAAb,QAAA,eACtCvC,OAAA;cAAAuC,QAAA,gBACEvC,OAAA;gBAAI+B,KAAK,EAAE;kBAAEoB,OAAO,EAAE,MAAM;kBAAEd,SAAS,EAAE,MAAM;kBAAEW,QAAQ,EAAE,UAAU;kBAAEC,UAAU,EAAE,KAAK;kBAAEX,KAAK,EAAE;gBAAU,CAAE;gBAAAC,QAAA,EAAC;cAE9G;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9C,OAAA;gBAAI+B,KAAK,EAAE;kBAAEoB,OAAO,EAAE,MAAM;kBAAEd,SAAS,EAAE,MAAM;kBAAEW,QAAQ,EAAE,UAAU;kBAAEC,UAAU,EAAE,KAAK;kBAAEX,KAAK,EAAE;gBAAU,CAAE;gBAAAC,QAAA,EAAC;cAE9G;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9C,OAAA;gBAAI+B,KAAK,EAAE;kBAAEoB,OAAO,EAAE,MAAM;kBAAEd,SAAS,EAAE,MAAM;kBAAEW,QAAQ,EAAE,UAAU;kBAAEC,UAAU,EAAE,KAAK;kBAAEX,KAAK,EAAE;gBAAU,CAAE;gBAAAC,QAAA,EAAC;cAE9G;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9C,OAAA;gBAAI+B,KAAK,EAAE;kBAAEoB,OAAO,EAAE,MAAM;kBAAEd,SAAS,EAAE,MAAM;kBAAEW,QAAQ,EAAE,UAAU;kBAAEC,UAAU,EAAE,KAAK;kBAAEX,KAAK,EAAE;gBAAU,CAAE;gBAAAC,QAAA,EAAC;cAE9G;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9C,OAAA;gBAAI+B,KAAK,EAAE;kBAAEoB,OAAO,EAAE,MAAM;kBAAEd,SAAS,EAAE,MAAM;kBAAEW,QAAQ,EAAE,UAAU;kBAAEC,UAAU,EAAE,KAAK;kBAAEX,KAAK,EAAE;gBAAU,CAAE;gBAAAC,QAAA,EAAC;cAE9G;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9C,OAAA;gBAAI+B,KAAK,EAAE;kBAAEoB,OAAO,EAAE,MAAM;kBAAEd,SAAS,EAAE,MAAM;kBAAEW,QAAQ,EAAE,UAAU;kBAAEC,UAAU,EAAE,KAAK;kBAAEX,KAAK,EAAE;gBAAU,CAAE;gBAAAC,QAAA,EAAC;cAE9G;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9C,OAAA;gBAAI+B,KAAK,EAAE;kBAAEoB,OAAO,EAAE,MAAM;kBAAEd,SAAS,EAAE,MAAM;kBAAEW,QAAQ,EAAE,UAAU;kBAAEC,UAAU,EAAE,KAAK;kBAAEX,KAAK,EAAE;gBAAU,CAAE;gBAAAC,QAAA,EAAC;cAE9G;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR9C,OAAA;YAAAuC,QAAA,EACGlC,IAAI,CAACwF,GAAG,CAAC,CAACC,GAAG,EAAEuC,KAAK,kBACnBrI,OAAA;cAEE+B,KAAK,EAAE;gBACLuG,YAAY,EAAED,KAAK,GAAGhI,IAAI,CAAC6E,MAAM,GAAG,CAAC,GAAG,mBAAmB,GAAG,MAAM;gBACpE9B,UAAU,EAAE0C,GAAG,CAACxB,OAAO,GAAG,aAAa,GAAG;cAC5C,CAAE;cAAA/B,QAAA,gBAEFvC,OAAA;gBAAI+B,KAAK,EAAE;kBAAEoB,OAAO,EAAE,MAAM;kBAAEH,QAAQ,EAAE,UAAU;kBAAEV,KAAK,EAAE;gBAAU,CAAE;gBAAAC,QAAA,eACrEvC,OAAA;kBAAK+B,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEgF,GAAG,EAAE;kBAAS,CAAE;kBAAA3E,QAAA,gBACnEvC,OAAA,CAACP,QAAQ;oBAAC+C,IAAI,EAAE,EAAG;oBAACF,KAAK,EAAC;kBAAS;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACrCgD,GAAG,CAACnC,SAAS,CAAC4E,cAAc,CAAC,CAAC;gBAAA;kBAAA5F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL9C,OAAA;gBAAI+B,KAAK,EAAE;kBAAEoB,OAAO,EAAE,MAAM;kBAAEH,QAAQ,EAAE,UAAU;kBAAEV,KAAK,EAAE;gBAAU,CAAE;gBAAAC,QAAA,eACrEvC,OAAA;kBAAAuC,QAAA,gBACEvC,OAAA;oBAAK+B,KAAK,EAAE;sBAAEkB,UAAU,EAAE;oBAAM,CAAE;oBAAAV,QAAA,EAAEuD,GAAG,CAAC/B;kBAAQ;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvD9C,OAAA;oBAAK+B,KAAK,EAAE;sBAAEiB,QAAQ,EAAE,SAAS;sBAAEV,KAAK,EAAE;oBAAU,CAAE;oBAAAC,QAAA,EAAEuD,GAAG,CAAChC;kBAAS;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL9C,OAAA;gBAAI+B,KAAK,EAAE;kBAAEoB,OAAO,EAAE,MAAM;kBAAEH,QAAQ,EAAE,UAAU;kBAAEV,KAAK,EAAE;gBAAU,CAAE;gBAAAC,QAAA,eACrEvC,OAAA;kBAAK+B,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEgF,GAAG,EAAE;kBAAS,CAAE;kBAAA3E,QAAA,GAClEiD,aAAa,CAACM,GAAG,CAAC9B,MAAM,CAAC,EACzB8B,GAAG,CAAC9B,MAAM,CAACwE,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;gBAAA;kBAAA7F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL9C,OAAA;gBAAI+B,KAAK,EAAE;kBAAEoB,OAAO,EAAE,MAAM;kBAAEH,QAAQ,EAAE,UAAU;kBAAEV,KAAK,EAAE;gBAAU,CAAE;gBAAAC,QAAA,eACrEvC,OAAA;kBAAAuC,QAAA,gBACEvC,OAAA;oBAAK+B,KAAK,EAAE;sBAAEkB,UAAU,EAAE;oBAAM,CAAE;oBAAAV,QAAA,EAAEuD,GAAG,CAAC7B;kBAAQ;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EACtDgD,GAAG,CAACtB,UAAU,iBACbxE,OAAA;oBAAK+B,KAAK,EAAE;sBAAEiB,QAAQ,EAAE,SAAS;sBAAEV,KAAK,EAAE;oBAAU,CAAE;oBAAAC,QAAA,GAAC,MAAI,EAACuD,GAAG,CAACtB,UAAU;kBAAA;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACjF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL9C,OAAA;gBAAI+B,KAAK,EAAE;kBAAEoB,OAAO,EAAE,MAAM;kBAAEH,QAAQ,EAAE;gBAAW,CAAE;gBAAAT,QAAA,eACnDvC,OAAA;kBAAM+B,KAAK,EAAE;oBACXoB,OAAO,EAAE,gBAAgB;oBACzBC,UAAU,EAAE0C,GAAG,CAACxB,OAAO,GAAG,SAAS,GAAG,SAAS;oBAC/ChC,KAAK,EAAEwD,GAAG,CAACxB,OAAO,GAAG,SAAS,GAAG,SAAS;oBAC1ChB,YAAY,EAAE,KAAK;oBACnBN,QAAQ,EAAE,SAAS;oBACnBC,UAAU,EAAE;kBACd,CAAE;kBAAAV,QAAA,EACCuD,GAAG,CAACxB,OAAO,GAAG,SAAS,GAAG;gBAAQ;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACL9C,OAAA;gBAAI+B,KAAK,EAAE;kBAAEoB,OAAO,EAAE;gBAAO,CAAE;gBAAAZ,QAAA,EAC5B+C,gBAAgB,CAACQ,GAAG,CAACvB,QAAQ;cAAC;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACL9C,OAAA;gBAAI+B,KAAK,EAAE;kBAAEoB,OAAO,EAAE,MAAM;kBAAEH,QAAQ,EAAE,UAAU;kBAAEV,KAAK,EAAE,SAAS;kBAAEmG,UAAU,EAAE;gBAAY,CAAE;gBAAAlG,QAAA,EAC7FuD,GAAG,CAAC1B;cAAS;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA,GAjDAgD,GAAG,CAACpC,EAAE;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkDT,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLnB,UAAU,GAAG,CAAC,iBACb3B,OAAA;MAAK+B,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfG,cAAc,EAAE,QAAQ;QACxBD,UAAU,EAAE,QAAQ;QACpBgF,GAAG,EAAE,MAAM;QACXhE,SAAS,EAAE;MACb,CAAE;MAAAX,QAAA,gBACAvC,OAAA;QACEmH,OAAO,EAAEA,CAAA,KAAMzF,cAAc,CAACoG,IAAI,IAAI9C,IAAI,CAAC0D,GAAG,CAAC,CAAC,EAAEZ,IAAI,GAAG,CAAC,CAAC,CAAE;QAC7Da,QAAQ,EAAElH,WAAW,KAAK,CAAE;QAC5BM,KAAK,EAAE;UACLoB,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAE3B,WAAW,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;UACrDa,KAAK,EAAEb,WAAW,KAAK,CAAC,GAAG,SAAS,GAAG,OAAO;UAC9C2F,MAAM,EAAE,MAAM;UACd9D,YAAY,EAAE,KAAK;UACnB+D,MAAM,EAAE5F,WAAW,KAAK,CAAC,GAAG,aAAa,GAAG;QAC9C,CAAE;QAAAc,QAAA,EACH;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAET9C,OAAA;QAAM+B,KAAK,EAAE;UAAEiB,QAAQ,EAAE,UAAU;UAAEV,KAAK,EAAE;QAAU,CAAE;QAAAC,QAAA,GAAC,OAClD,EAACd,WAAW,EAAC,MAAI,EAACE,UAAU;MAAA;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eAEP9C,OAAA;QACEmH,OAAO,EAAEA,CAAA,KAAMzF,cAAc,CAACoG,IAAI,IAAI9C,IAAI,CAAC4D,GAAG,CAACjH,UAAU,EAAEmG,IAAI,GAAG,CAAC,CAAC,CAAE;QACtEa,QAAQ,EAAElH,WAAW,KAAKE,UAAW;QACrCI,KAAK,EAAE;UACLoB,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAE3B,WAAW,KAAKE,UAAU,GAAG,SAAS,GAAG,SAAS;UAC9DW,KAAK,EAAEb,WAAW,KAAKE,UAAU,GAAG,SAAS,GAAG,OAAO;UACvDyF,MAAM,EAAE,MAAM;UACd9D,YAAY,EAAE,KAAK;UACnB+D,MAAM,EAAE5F,WAAW,KAAKE,UAAU,GAAG,aAAa,GAAG;QACvD,CAAE;QAAAY,QAAA,EACH;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC5C,EAAA,CApnBID,SAAmB;EAAA,QACNJ,YAAY,EACTC,cAAc;AAAA;AAAA+I,EAAA,GAF9B5I,SAAmB;AAsnBzB,eAAeA,SAAS;AAAC,IAAA4I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}