const CategoryModel = require('../models/CategoryModel');
const { asyncHandler } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

class CategoryController {
  // Get all categories
  getCategories = asyncHandler(async (req, res) => {
    const categories = await CategoryModel.getCategories();

    res.status(200).json({
      success: true,
      message: 'Categories retrieved successfully',
      data: { categories },
    });
  });

  // Get categories with their subcategories
  getCategoriesWithSubcategories = asyncHandler(async (req, res) => {
    const categories = await CategoryModel.getCategoriesWithSubcategories();

    res.status(200).json({
      success: true,
      message: 'Categories with subcategories retrieved successfully',
      data: { categories },
    });
  });

  // Get subcategories by category ID
  getSubcategoriesByCategory = asyncHandler(async (req, res) => {
    const { categoryId } = req.params;
    const subcategories = await CategoryModel.getSubcategoriesByCategory(categoryId);

    res.status(200).json({
      success: true,
      message: 'Subcategories retrieved successfully',
      data: { subcategories },
    });
  });

  // Get all subcategories
  getSubcategories = asyncHandler(async (req, res) => {
    const subcategories = await CategoryModel.getSubcategories();

    res.status(200).json({
      success: true,
      message: 'Subcategories retrieved successfully',
      data: { subcategories },
    });
  });

  // Create category (super_admin only)
  createCategory = asyncHandler(async (req, res) => {
    const { name, description, color_code, is_active = true } = req.body;

    const categoryData = {
      name,
      description,
      color_code,
      is_active,
      created_by: req.user.id,
    };

    const category = await CategoryModel.createCategory(categoryData);

    logger.info('Category created', {
      categoryId: category.category_id,
      name: category.name,
      createdBy: req.user.id,
      userEmail: req.user.email,
    });

    res.status(201).json({
      success: true,
      message: 'Category created successfully',
      data: { category },
    });
  });

  // Update category (super_admin only)
  updateCategory = asyncHandler(async (req, res) => {
    const { categoryId } = req.params;
    const { name, description, color_code, is_active } = req.body;

    const updateData = {
      name,
      description,
      color_code,
      is_active,
      updated_by: req.user.id,
    };

    const category = await CategoryModel.updateCategory(categoryId, updateData);

    logger.info('Category updated', {
      categoryId,
      updatedBy: req.user.id,
      userEmail: req.user.email,
    });

    res.status(200).json({
      success: true,
      message: 'Category updated successfully',
      data: { category },
    });
  });

  // Delete category (super_admin only)
  deleteCategory = asyncHandler(async (req, res) => {
    const { categoryId } = req.params;

    await CategoryModel.deleteCategory(categoryId);

    logger.info('Category deleted', {
      categoryId,
      deletedBy: req.user.id,
      userEmail: req.user.email,
    });

    res.status(200).json({
      success: true,
      message: 'Category deleted successfully',
    });
  });

  // Create subcategory (super_admin only)
  createSubcategory = asyncHandler(async (req, res) => {
    const { name, category_id, description, color_code, display_order, is_active = true } = req.body;

    const subcategoryData = {
      name,
      category_id,
      description,
      color_code,
      display_order,
      is_active,
      created_by: req.user.id,
    };

    const subcategory = await CategoryModel.createSubcategory(subcategoryData);

    logger.info('Subcategory created', {
      subcategoryId: subcategory.subcategory_id,
      name: subcategory.name,
      categoryId: category_id,
      createdBy: req.user.id,
      userEmail: req.user.email,
    });

    res.status(201).json({
      success: true,
      message: 'Subcategory created successfully',
      data: { subcategory },
    });
  });

  // Update subcategory (super_admin only)
  updateSubcategory = asyncHandler(async (req, res) => {
    const { subcategoryId } = req.params;
    const { name, category_id, description, color_code, display_order, is_active } = req.body;

    const updateData = {
      name,
      category_id,
      description,
      color_code,
      display_order,
      is_active,
      updated_by: req.user.id,
    };

    const subcategory = await CategoryModel.updateSubcategory(subcategoryId, updateData);

    logger.info('Subcategory updated', {
      subcategoryId,
      updatedBy: req.user.id,
      userEmail: req.user.email,
    });

    res.status(200).json({
      success: true,
      message: 'Subcategory updated successfully',
      data: { subcategory },
    });
  });

  // Delete subcategory (super_admin only)
  deleteSubcategory = asyncHandler(async (req, res) => {
    const { subcategoryId } = req.params;

    await CategoryModel.deleteSubcategory(subcategoryId);

    logger.info('Subcategory deleted', {
      subcategoryId,
      deletedBy: req.user.id,
      userEmail: req.user.email,
    });

    res.status(200).json({
      success: true,
      message: 'Subcategory deleted successfully',
    });
  });

  // Activate category (super_admin only)
  activateCategory = asyncHandler(async (req, res) => {
    const { categoryId } = req.params;

    await CategoryModel.updateCategoryStatus(categoryId, true);

    logger.info('Category activated', {
      categoryId,
      activatedBy: req.user.id,
      userEmail: req.user.email,
    });

    res.status(200).json({
      success: true,
      message: 'Category activated successfully',
    });
  });

  // Deactivate category (super_admin only)
  deactivateCategory = asyncHandler(async (req, res) => {
    const { categoryId } = req.params;

    await CategoryModel.updateCategoryStatus(categoryId, false);

    logger.info('Category deactivated', {
      categoryId,
      deactivatedBy: req.user.id,
      userEmail: req.user.email,
    });

    res.status(200).json({
      success: true,
      message: 'Category deactivated successfully',
    });
  });

  // Activate subcategory (super_admin only)
  activateSubcategory = asyncHandler(async (req, res) => {
    const { subcategoryId } = req.params;

    await CategoryModel.updateSubcategoryStatus(subcategoryId, true);

    logger.info('Subcategory activated', {
      subcategoryId,
      activatedBy: req.user.id,
      userEmail: req.user.email,
    });

    res.status(200).json({
      success: true,
      message: 'Subcategory activated successfully',
    });
  });

  // Deactivate subcategory (super_admin only)
  deactivateSubcategory = asyncHandler(async (req, res) => {
    const { subcategoryId } = req.params;

    await CategoryModel.updateSubcategoryStatus(subcategoryId, false);

    logger.info('Subcategory deactivated', {
      subcategoryId,
      deactivatedBy: req.user.id,
      userEmail: req.user.email,
    });

    res.status(200).json({
      success: true,
      message: 'Subcategory deactivated successfully',
    });
  });

  // Update subcategory order (super_admin only)
  updateSubcategoryOrder = asyncHandler(async (req, res) => {
    const { subcategoryId } = req.params;
    const { display_order } = req.body;

    await CategoryModel.updateSubcategoryOrder(subcategoryId, display_order);

    logger.info('Subcategory order updated', {
      subcategoryId,
      displayOrder: display_order,
      updatedBy: req.user.id,
      userEmail: req.user.email,
    });

    res.status(200).json({
      success: true,
      message: 'Subcategory order updated successfully',
    });
  });
}

module.exports = new CategoryController();
