import React, { useState, useEffect } from 'react';
import { X, User, Mail, Phone, Building, GraduationCap, FileText, Eye, EyeOff } from 'lucide-react';

interface AdminAccount {
  admin_id?: number;
  email: string;
  is_active: boolean;
  last_login?: string | null;
  created_at?: string;
  profile: {
    first_name: string;
    middle_name?: string;
    last_name: string;
    suffix?: string;
    full_name: string;
    phone_number?: string;
    department?: string;
    position: 'super_admin' | 'professor';
    grade_level?: number;
    bio?: string;
    profile_picture?: string;
  };
}

interface AdminAccountModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (adminData: AdminAccount) => Promise<void>;
  admin?: AdminAccount | null;
  loading?: boolean;
}

const AdminAccountModal: React.FC<AdminAccountModalProps> = ({
  isOpen,
  onClose,
  onSave,
  admin,
  loading = false
}) => {
  const [formData, setFormData] = useState<AdminAccount>({
    email: '',
    is_active: true,
    profile: {
      first_name: '',
      middle_name: '',
      last_name: '',
      suffix: '',
      full_name: '',
      phone_number: '',
      department: '',
      position: 'professor',
      grade_level: undefined,
      bio: ''
    }
  });

  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const isEditing = !!admin?.admin_id;

  useEffect(() => {
    if (admin) {
      setFormData(admin);
    } else {
      // Reset form for new admin
      setFormData({
        email: '',
        is_active: true,
        profile: {
          first_name: '',
          middle_name: '',
          last_name: '',
          suffix: '',
          full_name: '',
          phone_number: '',
          department: '',
          position: 'professor',
          grade_level: undefined,
          bio: ''
        }
      });
    }
    setPassword('');
    setConfirmPassword('');
    setErrors({});
  }, [admin, isOpen]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Password validation (only for new admins)
    if (!isEditing) {
      if (!password.trim()) {
        newErrors.password = 'Password is required';
      } else if (password.length < 6) {
        newErrors.password = 'Password must be at least 6 characters';
      }

      if (password !== confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
    }

    // Name validation
    if (!formData.profile.first_name.trim()) {
      newErrors.first_name = 'First name is required';
    }
    if (!formData.profile.last_name.trim()) {
      newErrors.last_name = 'Last name is required';
    }

    // Grade level validation for professors
    if (formData.profile.position === 'professor' && !formData.profile.grade_level) {
      newErrors.grade_level = 'Grade level is required for professors';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      const adminData = { ...formData };

      // Generate full_name from individual name components
      const fullNameParts = [
        adminData.profile.first_name,
        adminData.profile.middle_name,
        adminData.profile.last_name,
        adminData.profile.suffix
      ].filter(Boolean);

      adminData.profile.full_name = fullNameParts.join(' ');

      if (!isEditing) {
        // Add password for new admins
        (adminData as any).password = password;
      }

      await onSave(adminData);
      onClose();
    } catch (error) {
      console.error('Error saving admin:', error);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    if (field.startsWith('profile.')) {
      const profileField = field.replace('profile.', '');
      setFormData(prev => ({
        ...prev,
        profile: {
          ...prev.profile,
          [profileField]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: '1rem'
    }}>
      <div style={{
        background: 'white',
        borderRadius: '12px',
        width: '100%',
        maxWidth: '600px',
        maxHeight: '90vh',
        overflow: 'hidden',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '1.5rem',
          borderBottom: '1px solid #e5e7eb'
        }}>
          <h2 style={{
            margin: 0,
            fontSize: '1.5rem',
            fontWeight: '600',
            color: '#1f2937'
          }}>
            {isEditing ? 'Edit Admin Account' : 'Add New Admin'}
          </h2>
          
          <button
            onClick={onClose}
            style={{
              padding: '0.5rem',
              background: 'transparent',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              color: '#6b7280'
            }}
          >
            <X size={20} />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} style={{ padding: '1.5rem', overflowY: 'auto', maxHeight: 'calc(90vh - 140px)' }}>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1.5rem' }}>
            {/* Email */}
            <div>
              <label style={{
                display: 'block',
                fontSize: '0.875rem',
                fontWeight: '600',
                color: '#374151',
                marginBottom: '0.5rem'
              }}>
                <Mail size={16} style={{ display: 'inline', marginRight: '0.5rem' }} />
                Email Address *
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: `1px solid ${errors.email ? '#ef4444' : '#d1d5db'}`,
                  borderRadius: '6px',
                  fontSize: '0.875rem'
                }}
                placeholder="<EMAIL>"
              />
              {errors.email && (
                <p style={{ margin: '0.25rem 0 0', fontSize: '0.75rem', color: '#ef4444' }}>
                  {errors.email}
                </p>
              )}
            </div>

            {/* Position */}
            <div>
              <label style={{
                display: 'block',
                fontSize: '0.875rem',
                fontWeight: '600',
                color: '#374151',
                marginBottom: '0.5rem'
              }}>
                <User size={16} style={{ display: 'inline', marginRight: '0.5rem' }} />
                Position *
              </label>
              <select
                value={formData.profile.position}
                onChange={(e) => handleInputChange('profile.position', e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '0.875rem'
                }}
              >
                <option value="professor">Professor</option>
                <option value="super_admin">Super Admin</option>
              </select>
            </div>

            {/* First Name */}
            <div>
              <label style={{
                display: 'block',
                fontSize: '0.875rem',
                fontWeight: '600',
                color: '#374151',
                marginBottom: '0.5rem'
              }}>
                First Name *
              </label>
              <input
                type="text"
                value={formData.profile.first_name}
                onChange={(e) => handleInputChange('profile.first_name', e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: `1px solid ${errors.first_name ? '#ef4444' : '#d1d5db'}`,
                  borderRadius: '6px',
                  fontSize: '0.875rem'
                }}
                placeholder="John"
              />
              {errors.first_name && (
                <p style={{ margin: '0.25rem 0 0', fontSize: '0.75rem', color: '#ef4444' }}>
                  {errors.first_name}
                </p>
              )}
            </div>

            {/* Middle Name */}
            <div>
              <label style={{
                display: 'block',
                fontSize: '0.875rem',
                fontWeight: '600',
                color: '#374151',
                marginBottom: '0.5rem'
              }}>
                Middle Name
              </label>
              <input
                type="text"
                value={formData.profile.middle_name || ''}
                onChange={(e) => handleInputChange('profile.middle_name', e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '0.875rem'
                }}
                placeholder="Michael"
              />
            </div>

            {/* Last Name */}
            <div>
              <label style={{
                display: 'block',
                fontSize: '0.875rem',
                fontWeight: '600',
                color: '#374151',
                marginBottom: '0.5rem'
              }}>
                Last Name *
              </label>
              <input
                type="text"
                value={formData.profile.last_name}
                onChange={(e) => handleInputChange('profile.last_name', e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: `1px solid ${errors.last_name ? '#ef4444' : '#d1d5db'}`,
                  borderRadius: '6px',
                  fontSize: '0.875rem'
                }}
                placeholder="Doe"
              />
              {errors.last_name && (
                <p style={{ margin: '0.25rem 0 0', fontSize: '0.75rem', color: '#ef4444' }}>
                  {errors.last_name}
                </p>
              )}
            </div>

            {/* Suffix */}
            <div>
              <label style={{
                display: 'block',
                fontSize: '0.875rem',
                fontWeight: '600',
                color: '#374151',
                marginBottom: '0.5rem'
              }}>
                Suffix
              </label>
              <input
                type="text"
                value={formData.profile.suffix || ''}
                onChange={(e) => handleInputChange('profile.suffix', e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '0.875rem'
                }}
                placeholder="Jr., Sr., III"
              />
            </div>

            {/* Phone Number */}
            <div>
              <label style={{
                display: 'block',
                fontSize: '0.875rem',
                fontWeight: '600',
                color: '#374151',
                marginBottom: '0.5rem'
              }}>
                <Phone size={16} style={{ display: 'inline', marginRight: '0.5rem' }} />
                Phone Number
              </label>
              <input
                type="tel"
                value={formData.profile.phone_number || ''}
                onChange={(e) => handleInputChange('profile.phone_number', e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '0.875rem'
                }}
                placeholder="+639123456789"
              />
            </div>

            {/* Department */}
            <div>
              <label style={{
                display: 'block',
                fontSize: '0.875rem',
                fontWeight: '600',
                color: '#374151',
                marginBottom: '0.5rem'
              }}>
                <Building size={16} style={{ display: 'inline', marginRight: '0.5rem' }} />
                Department
              </label>
              <input
                type="text"
                value={formData.profile.department || ''}
                onChange={(e) => handleInputChange('profile.department', e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '0.875rem'
                }}
                placeholder="Mathematics, Science, etc."
              />
            </div>

            {/* Grade Level (for professors) */}
            {formData.profile.position === 'professor' && (
              <div>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  color: '#374151',
                  marginBottom: '0.5rem'
                }}>
                  <GraduationCap size={16} style={{ display: 'inline', marginRight: '0.5rem' }} />
                  Grade Level *
                </label>
                <select
                  value={formData.profile.grade_level || ''}
                  onChange={(e) => handleInputChange('profile.grade_level', e.target.value ? Number(e.target.value) : undefined)}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: `1px solid ${errors.grade_level ? '#ef4444' : '#d1d5db'}`,
                    borderRadius: '6px',
                    fontSize: '0.875rem'
                  }}
                >
                  <option value="">Select Grade Level</option>
                  <option value="11">Grade 11</option>
                  <option value="12">Grade 12</option>
                </select>
                {errors.grade_level && (
                  <p style={{ margin: '0.25rem 0 0', fontSize: '0.75rem', color: '#ef4444' }}>
                    {errors.grade_level}
                  </p>
                )}
              </div>
            )}

            {/* Password (only for new admins) */}
            {!isEditing && (
              <>
                <div>
                  <label style={{
                    display: 'block',
                    fontSize: '0.875rem',
                    fontWeight: '600',
                    color: '#374151',
                    marginBottom: '0.5rem'
                  }}>
                    Password *
                  </label>
                  <div style={{ position: 'relative' }}>
                    <input
                      type={showPassword ? 'text' : 'password'}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        paddingRight: '2.5rem',
                        border: `1px solid ${errors.password ? '#ef4444' : '#d1d5db'}`,
                        borderRadius: '6px',
                        fontSize: '0.875rem'
                      }}
                      placeholder="Enter password"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      style={{
                        position: 'absolute',
                        right: '0.75rem',
                        top: '50%',
                        transform: 'translateY(-50%)',
                        background: 'transparent',
                        border: 'none',
                        cursor: 'pointer',
                        color: '#6b7280'
                      }}
                    >
                      {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                    </button>
                  </div>
                  {errors.password && (
                    <p style={{ margin: '0.25rem 0 0', fontSize: '0.75rem', color: '#ef4444' }}>
                      {errors.password}
                    </p>
                  )}
                </div>

                <div>
                  <label style={{
                    display: 'block',
                    fontSize: '0.875rem',
                    fontWeight: '600',
                    color: '#374151',
                    marginBottom: '0.5rem'
                  }}>
                    Confirm Password *
                  </label>
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      border: `1px solid ${errors.confirmPassword ? '#ef4444' : '#d1d5db'}`,
                      borderRadius: '6px',
                      fontSize: '0.875rem'
                    }}
                    placeholder="Confirm password"
                  />
                  {errors.confirmPassword && (
                    <p style={{ margin: '0.25rem 0 0', fontSize: '0.75rem', color: '#ef4444' }}>
                      {errors.confirmPassword}
                    </p>
                  )}
                </div>
              </>
            )}
          </div>

          {/* Bio */}
          <div style={{ marginTop: '1.5rem' }}>
            <label style={{
              display: 'block',
              fontSize: '0.875rem',
              fontWeight: '600',
              color: '#374151',
              marginBottom: '0.5rem'
            }}>
              <FileText size={16} style={{ display: 'inline', marginRight: '0.5rem' }} />
              Bio
            </label>
            <textarea
              value={formData.profile.bio || ''}
              onChange={(e) => handleInputChange('profile.bio', e.target.value)}
              rows={3}
              style={{
                width: '100%',
                padding: '0.75rem',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                fontSize: '0.875rem',
                resize: 'vertical'
              }}
              placeholder="Brief description about the admin..."
            />
          </div>

          {/* Form Actions */}
          <div style={{
            display: 'flex',
            gap: '1rem',
            justifyContent: 'flex-end',
            marginTop: '2rem',
            paddingTop: '1.5rem',
            borderTop: '1px solid #e5e7eb'
          }}>
            <button
              type="button"
              onClick={onClose}
              disabled={loading}
              style={{
                padding: '0.75rem 1.5rem',
                background: '#f3f4f6',
                color: '#6b7280',
                border: 'none',
                borderRadius: '6px',
                cursor: loading ? 'not-allowed' : 'pointer',
                fontSize: '0.875rem',
                fontWeight: '500'
              }}
            >
              Cancel
            </button>
            
            <button
              type="submit"
              disabled={loading}
              style={{
                padding: '0.75rem 1.5rem',
                background: loading ? '#9ca3af' : '#3b82f6',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: loading ? 'not-allowed' : 'pointer',
                fontSize: '0.875rem',
                fontWeight: '500',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}
            >
              {loading && (
                <div style={{
                  width: '16px',
                  height: '16px',
                  border: '2px solid transparent',
                  borderTop: '2px solid white',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite'
                }} />
              )}
              {loading ? 'Saving...' : (isEditing ? 'Update Admin' : 'Create Admin')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AdminAccountModal;
