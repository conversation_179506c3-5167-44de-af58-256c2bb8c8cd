# Backend API Requirements for Category Management

## Database Schema

The frontend expects the following database tables:

### Categories Table
```sql
CREATE TABLE categories (
    category_id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHA<PERSON>(255) NOT NULL,
    description TEXT,
    color_code VARCHAR(7) NOT NULL, -- Hex color code like #3b82f6
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Subcategories Table
```sql
CREATE TABLE subcategories (
    subcategory_id INTEGER PRIMARY KEY AUTOINCREMENT,
    category_id INTEGER NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    color_code VARCHAR(7) NOT NULL, -- Hex color code like #ef4444
    is_active BOOLEAN DEFAULT TRUE,
    display_order INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIG<PERSON> KEY (category_id) REFERENCES categories(category_id) ON DELETE CASCADE
);
```

## Required API Endpoints

### Base URL: `http://localhost:5000/api/categories`

### 1. Get Categories with Subcategories
```
GET /api/categories?include_subcategories=true
```

**Response Format:**
```json
{
  "success": true,
  "data": {
    "categories": [
      {
        "category_id": 1,
        "name": "Academic",
        "description": "Academic-related announcements and events",
        "color_code": "#3b82f6",
        "is_active": true,
        "created_at": "2025-01-01T00:00:00Z",
        "updated_at": "2025-01-01T00:00:00Z",
        "subcategories": [
          {
            "subcategory_id": 1,
            "category_id": 1,
            "name": "Exams",
            "description": "Examination schedules and updates",
            "color_code": "#ef4444",
            "is_active": true,
            "display_order": 1,
            "created_at": "2025-01-01T00:00:00Z",
            "updated_at": "2025-01-01T00:00:00Z"
          }
        ]
      }
    ]
  },
  "message": "Categories retrieved successfully"
}
```

### 2. Create Category
```
POST /api/categories
```

**Request Body:**
```json
{
  "name": "New Category",
  "description": "Category description",
  "color_code": "#3b82f6",
  "is_active": true
}
```

### 3. Update Category
```
PUT /api/categories/:categoryId
```

### 4. Delete Category
```
DELETE /api/categories/:categoryId
```

### 5. Toggle Category Status
```
PATCH /api/categories/:categoryId/status
```

**Request Body:**
```json
{
  "is_active": false
}
```

### 6. Create Subcategory
```
POST /api/categories/:categoryId/subcategories
```

**Request Body:**
```json
{
  "name": "New Subcategory",
  "description": "Subcategory description",
  "color_code": "#ef4444",
  "is_active": true,
  "display_order": 1
}
```

### 7. Update Subcategory
```
PUT /api/categories/:categoryId/subcategories/:subcategoryId
```

### 8. Delete Subcategory
```
DELETE /api/categories/:categoryId/subcategories/:subcategoryId
```

### 9. Toggle Subcategory Status
```
PATCH /api/categories/:categoryId/subcategories/:subcategoryId/status
```

## Error Response Format

All endpoints should return errors in this format:
```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error information (optional)"
}
```

## Authentication

All endpoints should require authentication. The frontend sends the JWT token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

## CORS Configuration

Make sure your backend allows requests from `http://localhost:3000` (React dev server).

## Sample Data

Here's some sample data you can insert for testing:

```sql
-- Categories
INSERT INTO categories (name, description, color_code, is_active) VALUES
('Academic', 'Academic-related announcements and events', '#3b82f6', TRUE),
('Events', 'School events and activities', '#10b981', TRUE),
('Administrative', 'Administrative notices and updates', '#f97316', TRUE),
('Emergency', 'Emergency announcements and alerts', '#dc2626', TRUE);

-- Subcategories
INSERT INTO subcategories (category_id, name, description, color_code, is_active, display_order) VALUES
(1, 'Exams', 'Examination schedules and updates', '#ef4444', TRUE, 1),
(1, 'Assignments', 'Assignment deadlines and submissions', '#f59e0b', TRUE, 2),
(1, 'Class Schedules', 'Class timing and schedule changes', '#06b6d4', TRUE, 3),
(2, 'Sports', 'Sports events and competitions', '#8b5cf6', TRUE, 1),
(2, 'Cultural', 'Cultural events and celebrations', '#ec4899', TRUE, 2),
(3, 'Policies', 'School policies and regulations', '#6366f1', TRUE, 1);
```

## Current Status

- ✅ Frontend is ready and will use mock data when API is not available
- ⚠️ Backend API endpoints need to be implemented
- ⚠️ Database tables need to be created
- ⚠️ Authentication middleware needs to be set up

## Testing

Once the backend is set up, you can test the integration by:

1. Starting the backend server on `http://localhost:5000`
2. Creating the database tables
3. Inserting sample data
4. Refreshing the Category Management page in the frontend

The frontend will automatically switch from mock data to real API data once the endpoints are available.
