{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\admin\\\\AdminManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminManagement = () => {\n  _s();\n  const {\n    user\n  } = useAdminAuth();\n  const permissions = usePermissions(user);\n\n  // State management\n  const [admins, setAdmins] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n\n  // Modal state\n  const [showModal, setShowModal] = useState(false);\n  const [editingAdmin, setEditingAdmin] = useState(null);\n  const [modalLoading, setModalLoading] = useState(false);\n\n  // Search and filter state\n  const [searchTerm, setSearchTerm] = useState('');\n  const [positionFilter, setPositionFilter] = useState('');\n  const [statusFilter, setStatusFilter] = useState('');\n\n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(10);\n  const [totalItems, setTotalItems] = useState(0);\n  useEffect(() => {\n    // Check if user has permission to manage admins\n    if (!permissions.canManageAdmins) {\n      setError('You do not have permission to manage admin accounts');\n      setLoading(false);\n      return;\n    }\n    loadAdmins();\n  }, [permissions.canManageAdmins]);\n  const loadAdmins = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // TODO: Implement actual API call\n      // const response = await adminManagementService.getAdmins({\n      //   page: currentPage,\n      //   limit: itemsPerPage,\n      //   search: searchTerm,\n      //   position: positionFilter,\n      //   status: statusFilter\n      // });\n\n      // Mock data with proper structure\n      const mockAdmins = [{\n        admin_id: 1,\n        email: '<EMAIL>',\n        is_active: true,\n        last_login: '2025-08-05T10:30:00Z',\n        created_at: '2025-01-01T00:00:00Z',\n        profile: {\n          first_name: 'Josh',\n          middle_name: 'Christian',\n          last_name: 'Mojica',\n          full_name: 'Josh Christian Mojica',\n          phone_number: '+************',\n          department: 'Administration',\n          position: 'super_admin',\n          grade_level: 12,\n          bio: 'System Administrator with expertise in educational technology.'\n        }\n      }, {\n        admin_id: 2,\n        email: '<EMAIL>',\n        is_active: true,\n        last_login: '2025-08-05T09:15:00Z',\n        created_at: '2025-01-15T00:00:00Z',\n        profile: {\n          first_name: 'Zaira',\n          last_name: 'Plarisan',\n          full_name: 'Zaira Plarisan',\n          phone_number: '+***********1',\n          department: 'Mathematics',\n          position: 'professor',\n          grade_level: 11,\n          bio: 'Mathematics professor specializing in Grade 11 curriculum.'\n        }\n      }];\n\n      // Apply filters\n      let filteredAdmins = mockAdmins;\n      if (searchTerm) {\n        filteredAdmins = filteredAdmins.filter(admin => admin.profile.full_name.toLowerCase().includes(searchTerm.toLowerCase()) || admin.email.toLowerCase().includes(searchTerm.toLowerCase()));\n      }\n      if (positionFilter) {\n        filteredAdmins = filteredAdmins.filter(admin => admin.profile.position === positionFilter);\n      }\n      if (statusFilter) {\n        const isActive = statusFilter === 'active';\n        filteredAdmins = filteredAdmins.filter(admin => admin.is_active === isActive);\n      }\n      setTotalItems(filteredAdmins.length);\n\n      // Apply pagination\n      const startIndex = (currentPage - 1) * itemsPerPage;\n      const paginatedAdmins = filteredAdmins.slice(startIndex, startIndex + itemsPerPage);\n      setAdmins(paginatedAdmins);\n    } catch (err) {\n      setError(err.message || 'Failed to load admin accounts');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handler functions\n  const handleAddAdmin = () => {\n    setEditingAdmin(null);\n    setShowModal(true);\n  };\n  const handleEditAdmin = admin => {\n    setEditingAdmin(admin);\n    setShowModal(true);\n  };\n  const handleDeleteAdmin = async admin => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await adminManagementService.deleteAdmin(admin.admin_id);\n\n      // Mock success\n      setSuccess(`Admin account for ${admin.profile.full_name} has been deactivated`);\n      loadAdmins();\n    } catch (err) {\n      setError(err.message || 'Failed to delete admin account');\n    }\n  };\n  const handleToggleStatus = async admin => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await adminManagementService.toggleAdminStatus(admin.admin_id, !admin.is_active);\n\n      // Mock success\n      const action = admin.is_active ? 'deactivated' : 'activated';\n      setSuccess(`Admin account for ${admin.profile.full_name} has been ${action}`);\n      loadAdmins();\n    } catch (err) {\n      setError(err.message || 'Failed to update admin status');\n    }\n  };\n  const handleSaveAdmin = async adminData => {\n    try {\n      setModalLoading(true);\n      setError(null);\n      setSuccess(null);\n      if (editingAdmin) {\n        // TODO: Implement actual API call for update\n        // await adminManagementService.updateAdmin(editingAdmin.admin_id, adminData);\n        setSuccess(`Admin account for ${adminData.profile.first_name} ${adminData.profile.last_name} has been updated`);\n      } else {\n        // TODO: Implement actual API call for create\n        // await adminManagementService.createAdmin(adminData);\n        setSuccess(`Admin account for ${adminData.profile.first_name} ${adminData.profile.last_name} has been created`);\n      }\n      loadAdmins();\n    } catch (err) {\n      setError(err.message || 'Failed to save admin account');\n      throw err; // Re-throw to prevent modal from closing\n    } finally {\n      setModalLoading(false);\n    }\n  };\n  const handlePageChange = page => {\n    setCurrentPage(page);\n  };\n  const handleItemsPerPageChange = itemsPerPage => {\n    setItemsPerPage(itemsPerPage);\n    setCurrentPage(1); // Reset to first page\n  };\n  const clearMessages = () => {\n    setError(null);\n    setSuccess(null);\n  };\n  if (!permissions.canManageAdmins) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      },\n      children: [/*#__PURE__*/_jsxDEV(UserCog, {\n        size: 64,\n        style: {\n          marginBottom: '1rem',\n          opacity: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          margin: '0 0 0.5rem',\n          fontSize: '1.5rem',\n          fontWeight: '600'\n        },\n        children: \"Access Denied\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: 0,\n          fontSize: '1rem'\n        },\n        children: \"You do not have permission to manage admin accounts.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        },\n        children: [\"Current Role: \", permissions.getPositionDisplayName()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '40px',\n          height: '40px',\n          border: '4px solid #f3f4f6',\n          borderTop: '4px solid #3b82f6',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '2rem',\n        textAlign: 'center',\n        color: '#ef4444'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Error\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: loadAdmins,\n        style: {\n          padding: '0.5rem 1rem',\n          background: '#3b82f6',\n          color: 'white',\n          border: 'none',\n          borderRadius: '6px',\n          cursor: 'pointer'\n        },\n        children: \"Retry\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      maxWidth: '1200px',\n      margin: '0 auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            margin: '0 0 0.5rem',\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          },\n          children: \"Admin Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          },\n          children: \"Manage administrator accounts and permissions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          padding: '0.75rem 1.5rem',\n          background: '#3b82f6',\n          color: 'white',\n          border: 'none',\n          borderRadius: '8px',\n          fontSize: '0.875rem',\n          fontWeight: '600',\n          cursor: 'pointer',\n          transition: 'background-color 0.2s'\n        },\n        onMouseEnter: e => e.currentTarget.style.background = '#2563eb',\n        onMouseLeave: e => e.currentTarget.style.background = '#3b82f6',\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 11\n        }, this), \"Add Admin\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fill, minmax(400px, 1fr))',\n        gap: '1.5rem'\n      },\n      children: admins.map(admin => {\n        const PositionIcon = getPositionIcon(admin.position);\n        const positionColor = getPositionColor(admin.position);\n        const positionLabel = getPositionLabel(admin.position);\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'white',\n            borderRadius: '12px',\n            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n            padding: '1.5rem',\n            border: admin.admin_id === (user === null || user === void 0 ? void 0 : user.id) ? '2px solid #facc15' : '1px solid #e5e7eb'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'flex-start',\n              marginBottom: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '48px',\n                  height: '48px',\n                  background: `linear-gradient(135deg, ${positionColor}20, ${positionColor}10)`,\n                  borderRadius: '12px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                },\n                children: /*#__PURE__*/_jsxDEV(PositionIcon, {\n                  size: 24,\n                  color: positionColor\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  style: {\n                    margin: '0 0 0.25rem',\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: '#1f2937'\n                  },\n                  children: [admin.first_name, \" \", admin.last_name, admin.admin_id === (user === null || user === void 0 ? void 0 : user.id) && /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      marginLeft: '0.5rem',\n                      padding: '0.125rem 0.375rem',\n                      background: '#facc15',\n                      color: '#1f2937',\n                      borderRadius: '4px',\n                      fontSize: '0.625rem',\n                      fontWeight: '700'\n                    },\n                    children: \"YOU\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 418,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    padding: '0.25rem 0.5rem',\n                    background: positionColor,\n                    color: 'white',\n                    borderRadius: '4px',\n                    fontSize: '0.75rem',\n                    fontWeight: '600',\n                    display: 'inline-block'\n                  },\n                  children: positionLabel\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '0.25rem 0.5rem',\n                background: admin.is_active ? '#dcfce7' : '#fef2f2',\n                color: admin.is_active ? '#166534' : '#dc2626',\n                borderRadius: '4px',\n                fontSize: '0.75rem',\n                fontWeight: '600'\n              },\n              children: admin.is_active ? 'Active' : 'Inactive'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                marginBottom: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Mail, {\n                size: 16,\n                color: \"#6b7280\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                },\n                children: admin.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 17\n            }, this), admin.phone_number && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                marginBottom: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Phone, {\n                size: 16,\n                color: \"#6b7280\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 478,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                },\n                children: admin.phone_number\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 19\n            }, this), admin.department && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                color: '#6b7280',\n                marginTop: '0.5rem'\n              },\n              children: [\"Department: \", admin.department]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 19\n            }, this), admin.grade_level && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                color: '#6b7280',\n                marginTop: '0.25rem'\n              },\n              children: [\"Grade Level: \", admin.grade_level]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 15\n          }, this), admin.last_login && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.75rem',\n              color: '#9ca3af',\n              marginBottom: '1rem'\n            },\n            children: [\"Last login: \", new Date(admin.last_login).toLocaleDateString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '0.5rem',\n              paddingTop: '1rem',\n              borderTop: '1px solid #f3f4f6'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                flex: 1,\n                padding: '0.5rem',\n                background: 'transparent',\n                border: '1px solid #d1d5db',\n                borderRadius: '6px',\n                cursor: 'pointer',\n                color: '#6b7280',\n                fontSize: '0.875rem',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                gap: '0.25rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Edit, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 19\n              }, this), \"Edit\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 17\n            }, this), admin.admin_id !== (user === null || user === void 0 ? void 0 : user.id) && /*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                flex: 1,\n                padding: '0.5rem',\n                background: 'transparent',\n                border: '1px solid #fca5a5',\n                borderRadius: '6px',\n                cursor: 'pointer',\n                color: '#dc2626',\n                fontSize: '0.875rem',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                gap: '0.25rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Trash2, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 21\n              }, this), \"Delete\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 15\n          }, this)]\n        }, admin.admin_id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 369,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 319,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminManagement, \"1ta6jFr/aZK9eOUImOuE16b5P8E=\", false, function () {\n  return [useAdminAuth, usePermissions];\n});\n_c = AdminManagement;\nexport default AdminManagement;\nvar _c;\n$RefreshReg$(_c, \"AdminManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAdminAuth", "usePermissions", "jsxDEV", "_jsxDEV", "AdminManagement", "_s", "user", "permissions", "admins", "setAdmins", "loading", "setLoading", "error", "setError", "success", "setSuccess", "showModal", "setShowModal", "editingAdmin", "setEditingAdmin", "modalLoading", "setModalLoading", "searchTerm", "setSearchTerm", "positionFilter", "setPositionFilter", "statusFilter", "setStatus<PERSON>ilter", "currentPage", "setCurrentPage", "itemsPerPage", "setItemsPerPage", "totalItems", "setTotalItems", "canManageAdmins", "loadAdmins", "mockAdmins", "admin_id", "email", "is_active", "last_login", "created_at", "profile", "first_name", "middle_name", "last_name", "full_name", "phone_number", "department", "position", "grade_level", "bio", "filteredAdmins", "filter", "admin", "toLowerCase", "includes", "isActive", "length", "startIndex", "paginatedAdmins", "slice", "err", "message", "handleAddAdmin", "handleEditAdmin", "handleDeleteAdmin", "handleToggleStatus", "action", "handleSaveAdmin", "adminData", "handlePageChange", "page", "handleItemsPerPageChange", "clearMessages", "style", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "textAlign", "color", "children", "UserCog", "size", "marginBottom", "opacity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "margin", "fontSize", "fontWeight", "marginTop", "padding", "background", "getPositionBadgeColor", "borderRadius", "getPositionDisplayName", "width", "height", "border", "borderTop", "animation", "onClick", "cursor", "max<PERSON><PERSON><PERSON>", "gap", "transition", "onMouseEnter", "e", "currentTarget", "onMouseLeave", "Plus", "gridTemplateColumns", "map", "PositionIcon", "getPositionIcon", "positionColor", "getPositionColor", "position<PERSON><PERSON><PERSON>", "getPositionLabel", "boxShadow", "id", "marginLeft", "Mail", "Phone", "Date", "toLocaleDateString", "paddingTop", "flex", "Edit", "Trash2", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/admin/AdminManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { UserPlus, Search, Filter, Users, AlertTriangle, CheckCircle } from 'lucide-react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\nimport { adminManagementService } from '../../services/adminManagementService';\nimport AdminAccountList from '../../components/admin/AdminAccountList';\nimport AdminAccountModal from '../../components/admin/AdminAccountModal';\n\ninterface AdminAccount {\n  admin_id: number;\n  email: string;\n  is_active: boolean;\n  last_login: string | null;\n  created_at: string;\n  profile: {\n    first_name: string;\n    middle_name?: string;\n    last_name: string;\n    suffix?: string;\n    full_name: string;\n    phone_number?: string;\n    department?: string;\n    position: 'super_admin' | 'professor';\n    grade_level?: number;\n    bio?: string;\n    profile_picture?: string;\n  };\n}\n\nconst AdminManagement: React.FC = () => {\n  const { user } = useAdminAuth();\n  const permissions = usePermissions(user);\n\n  // State management\n  const [admins, setAdmins] = useState<AdminAccount[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  // Modal state\n  const [showModal, setShowModal] = useState(false);\n  const [editingAdmin, setEditingAdmin] = useState<AdminAccount | null>(null);\n  const [modalLoading, setModalLoading] = useState(false);\n\n  // Search and filter state\n  const [searchTerm, setSearchTerm] = useState('');\n  const [positionFilter, setPositionFilter] = useState<string>('');\n  const [statusFilter, setStatusFilter] = useState<string>('');\n\n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(10);\n  const [totalItems, setTotalItems] = useState(0);\n\n  useEffect(() => {\n    // Check if user has permission to manage admins\n    if (!permissions.canManageAdmins) {\n      setError('You do not have permission to manage admin accounts');\n      setLoading(false);\n      return;\n    }\n\n    loadAdmins();\n  }, [permissions.canManageAdmins]);\n\n  const loadAdmins = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // TODO: Implement actual API call\n      // const response = await adminManagementService.getAdmins({\n      //   page: currentPage,\n      //   limit: itemsPerPage,\n      //   search: searchTerm,\n      //   position: positionFilter,\n      //   status: statusFilter\n      // });\n\n      // Mock data with proper structure\n      const mockAdmins: AdminAccount[] = [\n        {\n          admin_id: 1,\n          email: '<EMAIL>',\n          is_active: true,\n          last_login: '2025-08-05T10:30:00Z',\n          created_at: '2025-01-01T00:00:00Z',\n          profile: {\n            first_name: 'Josh',\n            middle_name: 'Christian',\n            last_name: 'Mojica',\n            full_name: 'Josh Christian Mojica',\n            phone_number: '+************',\n            department: 'Administration',\n            position: 'super_admin',\n            grade_level: 12,\n            bio: 'System Administrator with expertise in educational technology.'\n          }\n        },\n        {\n          admin_id: 2,\n          email: '<EMAIL>',\n          is_active: true,\n          last_login: '2025-08-05T09:15:00Z',\n          created_at: '2025-01-15T00:00:00Z',\n          profile: {\n            first_name: 'Zaira',\n            last_name: 'Plarisan',\n            full_name: 'Zaira Plarisan',\n            phone_number: '+***********1',\n            department: 'Mathematics',\n            position: 'professor',\n            grade_level: 11,\n            bio: 'Mathematics professor specializing in Grade 11 curriculum.'\n          }\n        }\n      ];\n\n      // Apply filters\n      let filteredAdmins = mockAdmins;\n\n      if (searchTerm) {\n        filteredAdmins = filteredAdmins.filter(admin =>\n          admin.profile.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n          admin.email.toLowerCase().includes(searchTerm.toLowerCase())\n        );\n      }\n\n      if (positionFilter) {\n        filteredAdmins = filteredAdmins.filter(admin => admin.profile.position === positionFilter);\n      }\n\n      if (statusFilter) {\n        const isActive = statusFilter === 'active';\n        filteredAdmins = filteredAdmins.filter(admin => admin.is_active === isActive);\n      }\n\n      setTotalItems(filteredAdmins.length);\n\n      // Apply pagination\n      const startIndex = (currentPage - 1) * itemsPerPage;\n      const paginatedAdmins = filteredAdmins.slice(startIndex, startIndex + itemsPerPage);\n\n      setAdmins(paginatedAdmins);\n\n    } catch (err: any) {\n      setError(err.message || 'Failed to load admin accounts');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handler functions\n  const handleAddAdmin = () => {\n    setEditingAdmin(null);\n    setShowModal(true);\n  };\n\n  const handleEditAdmin = (admin: AdminAccount) => {\n    setEditingAdmin(admin);\n    setShowModal(true);\n  };\n\n  const handleDeleteAdmin = async (admin: AdminAccount) => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await adminManagementService.deleteAdmin(admin.admin_id);\n\n      // Mock success\n      setSuccess(`Admin account for ${admin.profile.full_name} has been deactivated`);\n      loadAdmins();\n\n    } catch (err: any) {\n      setError(err.message || 'Failed to delete admin account');\n    }\n  };\n\n  const handleToggleStatus = async (admin: AdminAccount) => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await adminManagementService.toggleAdminStatus(admin.admin_id, !admin.is_active);\n\n      // Mock success\n      const action = admin.is_active ? 'deactivated' : 'activated';\n      setSuccess(`Admin account for ${admin.profile.full_name} has been ${action}`);\n      loadAdmins();\n\n    } catch (err: any) {\n      setError(err.message || 'Failed to update admin status');\n    }\n  };\n\n  const handleSaveAdmin = async (adminData: AdminAccount) => {\n    try {\n      setModalLoading(true);\n      setError(null);\n      setSuccess(null);\n\n      if (editingAdmin) {\n        // TODO: Implement actual API call for update\n        // await adminManagementService.updateAdmin(editingAdmin.admin_id, adminData);\n        setSuccess(`Admin account for ${adminData.profile.first_name} ${adminData.profile.last_name} has been updated`);\n      } else {\n        // TODO: Implement actual API call for create\n        // await adminManagementService.createAdmin(adminData);\n        setSuccess(`Admin account for ${adminData.profile.first_name} ${adminData.profile.last_name} has been created`);\n      }\n\n      loadAdmins();\n\n    } catch (err: any) {\n      setError(err.message || 'Failed to save admin account');\n      throw err; // Re-throw to prevent modal from closing\n    } finally {\n      setModalLoading(false);\n    }\n  };\n\n  const handlePageChange = (page: number) => {\n    setCurrentPage(page);\n  };\n\n  const handleItemsPerPageChange = (itemsPerPage: number) => {\n    setItemsPerPage(itemsPerPage);\n    setCurrentPage(1); // Reset to first page\n  };\n\n  const clearMessages = () => {\n    setError(null);\n    setSuccess(null);\n  };\n\n  if (!permissions.canManageAdmins) {\n    return (\n      <div style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      }}>\n        <UserCog size={64} style={{ marginBottom: '1rem', opacity: 0.5 }} />\n        <h2 style={{ margin: '0 0 0.5rem', fontSize: '1.5rem', fontWeight: '600' }}>\n          Access Denied\n        </h2>\n        <p style={{ margin: 0, fontSize: '1rem' }}>\n          You do not have permission to manage admin accounts.\n        </p>\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        }}>\n          Current Role: {permissions.getPositionDisplayName()}\n        </div>\n      </div>\n    );\n  }\n\n  if (loading) {\n    return (\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px'\n      }}>\n        <div style={{\n          width: '40px',\n          height: '40px',\n          border: '4px solid #f3f4f6',\n          borderTop: '4px solid #3b82f6',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }} />\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div style={{\n        padding: '2rem',\n        textAlign: 'center',\n        color: '#ef4444'\n      }}>\n        <h2>Error</h2>\n        <p>{error}</p>\n        <button\n          onClick={loadAdmins}\n          style={{\n            padding: '0.5rem 1rem',\n            background: '#3b82f6',\n            color: 'white',\n            border: 'none',\n            borderRadius: '6px',\n            cursor: 'pointer'\n          }}\n        >\n          Retry\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ maxWidth: '1200px', margin: '0 auto' }}>\n      {/* Header */}\n      <div style={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      }}>\n        <div>\n          <h1 style={{\n            margin: '0 0 0.5rem',\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          }}>\n            Admin Management\n          </h1>\n          <p style={{\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          }}>\n            Manage administrator accounts and permissions\n          </p>\n        </div>\n        \n        <button\n          style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            padding: '0.75rem 1.5rem',\n            background: '#3b82f6',\n            color: 'white',\n            border: 'none',\n            borderRadius: '8px',\n            fontSize: '0.875rem',\n            fontWeight: '600',\n            cursor: 'pointer',\n            transition: 'background-color 0.2s'\n          }}\n          onMouseEnter={(e) => e.currentTarget.style.background = '#2563eb'}\n          onMouseLeave={(e) => e.currentTarget.style.background = '#3b82f6'}\n        >\n          <Plus size={16} />\n          Add Admin\n        </button>\n      </div>\n\n      {/* Admin Cards */}\n      <div style={{\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fill, minmax(400px, 1fr))',\n        gap: '1.5rem'\n      }}>\n        {admins.map((admin) => {\n          const PositionIcon = getPositionIcon(admin.position);\n          const positionColor = getPositionColor(admin.position);\n          const positionLabel = getPositionLabel(admin.position);\n          \n          return (\n            <div\n              key={admin.admin_id}\n              style={{\n                background: 'white',\n                borderRadius: '12px',\n                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n                padding: '1.5rem',\n                border: admin.admin_id === user?.id ? '2px solid #facc15' : '1px solid #e5e7eb'\n              }}\n            >\n              {/* Header */}\n              <div style={{\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'flex-start',\n                marginBottom: '1rem'\n              }}>\n                <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>\n                  <div style={{\n                    width: '48px',\n                    height: '48px',\n                    background: `linear-gradient(135deg, ${positionColor}20, ${positionColor}10)`,\n                    borderRadius: '12px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  }}>\n                    <PositionIcon size={24} color={positionColor} />\n                  </div>\n                  <div>\n                    <h3 style={{\n                      margin: '0 0 0.25rem',\n                      fontSize: '1.125rem',\n                      fontWeight: '600',\n                      color: '#1f2937'\n                    }}>\n                      {admin.first_name} {admin.last_name}\n                      {admin.admin_id === user?.id && (\n                        <span style={{\n                          marginLeft: '0.5rem',\n                          padding: '0.125rem 0.375rem',\n                          background: '#facc15',\n                          color: '#1f2937',\n                          borderRadius: '4px',\n                          fontSize: '0.625rem',\n                          fontWeight: '700'\n                        }}>\n                          YOU\n                        </span>\n                      )}\n                    </h3>\n                    <div style={{\n                      padding: '0.25rem 0.5rem',\n                      background: positionColor,\n                      color: 'white',\n                      borderRadius: '4px',\n                      fontSize: '0.75rem',\n                      fontWeight: '600',\n                      display: 'inline-block'\n                    }}>\n                      {positionLabel}\n                    </div>\n                  </div>\n                </div>\n                \n                <div style={{\n                  padding: '0.25rem 0.5rem',\n                  background: admin.is_active ? '#dcfce7' : '#fef2f2',\n                  color: admin.is_active ? '#166534' : '#dc2626',\n                  borderRadius: '4px',\n                  fontSize: '0.75rem',\n                  fontWeight: '600'\n                }}>\n                  {admin.is_active ? 'Active' : 'Inactive'}\n                </div>\n              </div>\n\n              {/* Contact Info */}\n              <div style={{ marginBottom: '1rem' }}>\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  marginBottom: '0.5rem'\n                }}>\n                  <Mail size={16} color=\"#6b7280\" />\n                  <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>\n                    {admin.email}\n                  </span>\n                </div>\n                \n                {admin.phone_number && (\n                  <div style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem',\n                    marginBottom: '0.5rem'\n                  }}>\n                    <Phone size={16} color=\"#6b7280\" />\n                    <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>\n                      {admin.phone_number}\n                    </span>\n                  </div>\n                )}\n                \n                {admin.department && (\n                  <div style={{\n                    fontSize: '0.875rem',\n                    color: '#6b7280',\n                    marginTop: '0.5rem'\n                  }}>\n                    Department: {admin.department}\n                  </div>\n                )}\n                \n                {admin.grade_level && (\n                  <div style={{\n                    fontSize: '0.875rem',\n                    color: '#6b7280',\n                    marginTop: '0.25rem'\n                  }}>\n                    Grade Level: {admin.grade_level}\n                  </div>\n                )}\n              </div>\n\n              {/* Last Login */}\n              {admin.last_login && (\n                <div style={{\n                  fontSize: '0.75rem',\n                  color: '#9ca3af',\n                  marginBottom: '1rem'\n                }}>\n                  Last login: {new Date(admin.last_login).toLocaleDateString()}\n                </div>\n              )}\n\n              {/* Actions */}\n              <div style={{\n                display: 'flex',\n                gap: '0.5rem',\n                paddingTop: '1rem',\n                borderTop: '1px solid #f3f4f6'\n              }}>\n                <button\n                  style={{\n                    flex: 1,\n                    padding: '0.5rem',\n                    background: 'transparent',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '6px',\n                    cursor: 'pointer',\n                    color: '#6b7280',\n                    fontSize: '0.875rem',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    gap: '0.25rem'\n                  }}\n                >\n                  <Edit size={14} />\n                  Edit\n                </button>\n                \n                {admin.admin_id !== user?.id && (\n                  <button\n                    style={{\n                      flex: 1,\n                      padding: '0.5rem',\n                      background: 'transparent',\n                      border: '1px solid #fca5a5',\n                      borderRadius: '6px',\n                      cursor: 'pointer',\n                      color: '#dc2626',\n                      fontSize: '0.875rem',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      gap: '0.25rem'\n                    }}\n                  >\n                    <Trash2 size={14} />\n                    Delete\n                  </button>\n                )}\n              </div>\n            </div>\n          );\n        })}\n      </div>\n    </div>\n  );\n};\n\nexport default AdminManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAElD,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA0BzD,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IAAEC;EAAK,CAAC,GAAGN,YAAY,CAAC,CAAC;EAC/B,MAAMO,WAAW,GAAGN,cAAc,CAACK,IAAI,CAAC;;EAExC;EACA,MAAM,CAACE,MAAM,EAAEC,SAAS,CAAC,GAAGX,QAAQ,CAAiB,EAAE,CAAC;EACxD,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAgB,IAAI,CAAC;;EAE3D;EACA,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAsB,IAAI,CAAC;EAC3E,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0B,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAAS,EAAE,CAAC;EAChE,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAS,EAAE,CAAC;;EAE5D;EACA,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACkC,UAAU,EAAEC,aAAa,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;EAE/CC,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACQ,WAAW,CAAC2B,eAAe,EAAE;MAChCrB,QAAQ,CAAC,qDAAqD,CAAC;MAC/DF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEAwB,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAC5B,WAAW,CAAC2B,eAAe,CAAC,CAAC;EAEjC,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFxB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA,MAAMuB,UAA0B,GAAG,CACjC;QACEC,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE,0BAA0B;QACjCC,SAAS,EAAE,IAAI;QACfC,UAAU,EAAE,sBAAsB;QAClCC,UAAU,EAAE,sBAAsB;QAClCC,OAAO,EAAE;UACPC,UAAU,EAAE,MAAM;UAClBC,WAAW,EAAE,WAAW;UACxBC,SAAS,EAAE,QAAQ;UACnBC,SAAS,EAAE,uBAAuB;UAClCC,YAAY,EAAE,eAAe;UAC7BC,UAAU,EAAE,gBAAgB;UAC5BC,QAAQ,EAAE,aAAa;UACvBC,WAAW,EAAE,EAAE;UACfC,GAAG,EAAE;QACP;MACF,CAAC,EACD;QACEd,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE,oBAAoB;QAC3BC,SAAS,EAAE,IAAI;QACfC,UAAU,EAAE,sBAAsB;QAClCC,UAAU,EAAE,sBAAsB;QAClCC,OAAO,EAAE;UACPC,UAAU,EAAE,OAAO;UACnBE,SAAS,EAAE,UAAU;UACrBC,SAAS,EAAE,gBAAgB;UAC3BC,YAAY,EAAE,eAAe;UAC7BC,UAAU,EAAE,aAAa;UACzBC,QAAQ,EAAE,WAAW;UACrBC,WAAW,EAAE,EAAE;UACfC,GAAG,EAAE;QACP;MACF,CAAC,CACF;;MAED;MACA,IAAIC,cAAc,GAAGhB,UAAU;MAE/B,IAAId,UAAU,EAAE;QACd8B,cAAc,GAAGA,cAAc,CAACC,MAAM,CAACC,KAAK,IAC1CA,KAAK,CAACZ,OAAO,CAACI,SAAS,CAACS,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClC,UAAU,CAACiC,WAAW,CAAC,CAAC,CAAC,IACxED,KAAK,CAAChB,KAAK,CAACiB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClC,UAAU,CAACiC,WAAW,CAAC,CAAC,CAC7D,CAAC;MACH;MAEA,IAAI/B,cAAc,EAAE;QAClB4B,cAAc,GAAGA,cAAc,CAACC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACZ,OAAO,CAACO,QAAQ,KAAKzB,cAAc,CAAC;MAC5F;MAEA,IAAIE,YAAY,EAAE;QAChB,MAAM+B,QAAQ,GAAG/B,YAAY,KAAK,QAAQ;QAC1C0B,cAAc,GAAGA,cAAc,CAACC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACf,SAAS,KAAKkB,QAAQ,CAAC;MAC/E;MAEAxB,aAAa,CAACmB,cAAc,CAACM,MAAM,CAAC;;MAEpC;MACA,MAAMC,UAAU,GAAG,CAAC/B,WAAW,GAAG,CAAC,IAAIE,YAAY;MACnD,MAAM8B,eAAe,GAAGR,cAAc,CAACS,KAAK,CAACF,UAAU,EAAEA,UAAU,GAAG7B,YAAY,CAAC;MAEnFrB,SAAS,CAACmD,eAAe,CAAC;IAE5B,CAAC,CAAC,OAAOE,GAAQ,EAAE;MACjBjD,QAAQ,CAACiD,GAAG,CAACC,OAAO,IAAI,+BAA+B,CAAC;IAC1D,CAAC,SAAS;MACRpD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMqD,cAAc,GAAGA,CAAA,KAAM;IAC3B7C,eAAe,CAAC,IAAI,CAAC;IACrBF,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMgD,eAAe,GAAIX,KAAmB,IAAK;IAC/CnC,eAAe,CAACmC,KAAK,CAAC;IACtBrC,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMiD,iBAAiB,GAAG,MAAOZ,KAAmB,IAAK;IACvD,IAAI;MACFzC,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA;;MAEA;MACAA,UAAU,CAAC,qBAAqBuC,KAAK,CAACZ,OAAO,CAACI,SAAS,uBAAuB,CAAC;MAC/EX,UAAU,CAAC,CAAC;IAEd,CAAC,CAAC,OAAO2B,GAAQ,EAAE;MACjBjD,QAAQ,CAACiD,GAAG,CAACC,OAAO,IAAI,gCAAgC,CAAC;IAC3D;EACF,CAAC;EAED,MAAMI,kBAAkB,GAAG,MAAOb,KAAmB,IAAK;IACxD,IAAI;MACFzC,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA;;MAEA;MACA,MAAMqD,MAAM,GAAGd,KAAK,CAACf,SAAS,GAAG,aAAa,GAAG,WAAW;MAC5DxB,UAAU,CAAC,qBAAqBuC,KAAK,CAACZ,OAAO,CAACI,SAAS,aAAasB,MAAM,EAAE,CAAC;MAC7EjC,UAAU,CAAC,CAAC;IAEd,CAAC,CAAC,OAAO2B,GAAQ,EAAE;MACjBjD,QAAQ,CAACiD,GAAG,CAACC,OAAO,IAAI,+BAA+B,CAAC;IAC1D;EACF,CAAC;EAED,MAAMM,eAAe,GAAG,MAAOC,SAAuB,IAAK;IACzD,IAAI;MACFjD,eAAe,CAAC,IAAI,CAAC;MACrBR,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIG,YAAY,EAAE;QAChB;QACA;QACAH,UAAU,CAAC,qBAAqBuD,SAAS,CAAC5B,OAAO,CAACC,UAAU,IAAI2B,SAAS,CAAC5B,OAAO,CAACG,SAAS,mBAAmB,CAAC;MACjH,CAAC,MAAM;QACL;QACA;QACA9B,UAAU,CAAC,qBAAqBuD,SAAS,CAAC5B,OAAO,CAACC,UAAU,IAAI2B,SAAS,CAAC5B,OAAO,CAACG,SAAS,mBAAmB,CAAC;MACjH;MAEAV,UAAU,CAAC,CAAC;IAEd,CAAC,CAAC,OAAO2B,GAAQ,EAAE;MACjBjD,QAAQ,CAACiD,GAAG,CAACC,OAAO,IAAI,8BAA8B,CAAC;MACvD,MAAMD,GAAG,CAAC,CAAC;IACb,CAAC,SAAS;MACRzC,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMkD,gBAAgB,GAAIC,IAAY,IAAK;IACzC3C,cAAc,CAAC2C,IAAI,CAAC;EACtB,CAAC;EAED,MAAMC,wBAAwB,GAAI3C,YAAoB,IAAK;IACzDC,eAAe,CAACD,YAAY,CAAC;IAC7BD,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;EAED,MAAM6C,aAAa,GAAGA,CAAA,KAAM;IAC1B7D,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;EAClB,CAAC;EAED,IAAI,CAACR,WAAW,CAAC2B,eAAe,EAAE;IAChC,oBACE/B,OAAA;MAAKwE,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE,OAAO;QAClBC,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACAhF,OAAA,CAACiF,OAAO;QAACC,IAAI,EAAE,EAAG;QAACV,KAAK,EAAE;UAAEW,YAAY,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAI;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpExF,OAAA;QAAIwE,KAAK,EAAE;UAAEiB,MAAM,EAAE,YAAY;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAM,CAAE;QAAAX,QAAA,EAAC;MAE5E;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLxF,OAAA;QAAGwE,KAAK,EAAE;UAAEiB,MAAM,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAV,QAAA,EAAC;MAE3C;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJxF,OAAA;QAAKwE,KAAK,EAAE;UACVoB,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAE1F,WAAW,CAAC2F,qBAAqB,CAAC,CAAC;UAC/CC,YAAY,EAAE,KAAK;UACnBjB,KAAK,EAAE,OAAO;UACdW,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE;QACd,CAAE;QAAAX,QAAA,GAAC,gBACa,EAAC5E,WAAW,CAAC6F,sBAAsB,CAAC,CAAC;MAAA;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIjF,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKwE,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE;MACb,CAAE;MAAAG,QAAA,eACAhF,OAAA;QAAKwE,KAAK,EAAE;UACV0B,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,mBAAmB;UAC3BC,SAAS,EAAE,mBAAmB;UAC9BL,YAAY,EAAE,KAAK;UACnBM,SAAS,EAAE;QACb;MAAE;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEV;EAEA,IAAI/E,KAAK,EAAE;IACT,oBACET,OAAA;MAAKwE,KAAK,EAAE;QACVqB,OAAO,EAAE,MAAM;QACff,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACAhF,OAAA;QAAAgF,QAAA,EAAI;MAAK;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdxF,OAAA;QAAAgF,QAAA,EAAIvE;MAAK;QAAA4E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdxF,OAAA;QACEuG,OAAO,EAAEvE,UAAW;QACpBwC,KAAK,EAAE;UACLqB,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAE,SAAS;UACrBf,KAAK,EAAE,OAAO;UACdqB,MAAM,EAAE,MAAM;UACdJ,YAAY,EAAE,KAAK;UACnBQ,MAAM,EAAE;QACV,CAAE;QAAAxB,QAAA,EACH;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACExF,OAAA;IAAKwE,KAAK,EAAE;MAAEiC,QAAQ,EAAE,QAAQ;MAAEhB,MAAM,EAAE;IAAS,CAAE;IAAAT,QAAA,gBAEnDhF,OAAA;MAAKwE,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfG,cAAc,EAAE,eAAe;QAC/BD,UAAU,EAAE,QAAQ;QACpBQ,YAAY,EAAE;MAChB,CAAE;MAAAH,QAAA,gBACAhF,OAAA;QAAAgF,QAAA,gBACEhF,OAAA;UAAIwE,KAAK,EAAE;YACTiB,MAAM,EAAE,YAAY;YACpBC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjBZ,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,EAAC;QAEH;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLxF,OAAA;UAAGwE,KAAK,EAAE;YACRiB,MAAM,EAAE,CAAC;YACTV,KAAK,EAAE,SAAS;YAChBW,QAAQ,EAAE;UACZ,CAAE;UAAAV,QAAA,EAAC;QAEH;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENxF,OAAA;QACEwE,KAAK,EAAE;UACLC,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpB+B,GAAG,EAAE,QAAQ;UACbb,OAAO,EAAE,gBAAgB;UACzBC,UAAU,EAAE,SAAS;UACrBf,KAAK,EAAE,OAAO;UACdqB,MAAM,EAAE,MAAM;UACdJ,YAAY,EAAE,KAAK;UACnBN,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE,KAAK;UACjBa,MAAM,EAAE,SAAS;UACjBG,UAAU,EAAE;QACd,CAAE;QACFC,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACtC,KAAK,CAACsB,UAAU,GAAG,SAAU;QAClEiB,YAAY,EAAGF,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACtC,KAAK,CAACsB,UAAU,GAAG,SAAU;QAAAd,QAAA,gBAElEhF,OAAA,CAACgH,IAAI;UAAC9B,IAAI,EAAE;QAAG;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,aAEpB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNxF,OAAA;MAAKwE,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfwC,mBAAmB,EAAE,uCAAuC;QAC5DP,GAAG,EAAE;MACP,CAAE;MAAA1B,QAAA,EACC3E,MAAM,CAAC6G,GAAG,CAAE/D,KAAK,IAAK;QACrB,MAAMgE,YAAY,GAAGC,eAAe,CAACjE,KAAK,CAACL,QAAQ,CAAC;QACpD,MAAMuE,aAAa,GAAGC,gBAAgB,CAACnE,KAAK,CAACL,QAAQ,CAAC;QACtD,MAAMyE,aAAa,GAAGC,gBAAgB,CAACrE,KAAK,CAACL,QAAQ,CAAC;QAEtD,oBACE9C,OAAA;UAEEwE,KAAK,EAAE;YACLsB,UAAU,EAAE,OAAO;YACnBE,YAAY,EAAE,MAAM;YACpByB,SAAS,EAAE,8BAA8B;YACzC5B,OAAO,EAAE,QAAQ;YACjBO,MAAM,EAAEjD,KAAK,CAACjB,QAAQ,MAAK/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuH,EAAE,IAAG,mBAAmB,GAAG;UAC9D,CAAE;UAAA1C,QAAA,gBAGFhF,OAAA;YAAKwE,KAAK,EAAE;cACVC,OAAO,EAAE,MAAM;cACfG,cAAc,EAAE,eAAe;cAC/BD,UAAU,EAAE,YAAY;cACxBQ,YAAY,EAAE;YAChB,CAAE;YAAAH,QAAA,gBACAhF,OAAA;cAAKwE,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAE+B,GAAG,EAAE;cAAU,CAAE;cAAA1B,QAAA,gBACpEhF,OAAA;gBAAKwE,KAAK,EAAE;kBACV0B,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdL,UAAU,EAAE,2BAA2BuB,aAAa,OAAOA,aAAa,KAAK;kBAC7ErB,YAAY,EAAE,MAAM;kBACpBvB,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBC,cAAc,EAAE;gBAClB,CAAE;gBAAAI,QAAA,eACAhF,OAAA,CAACmH,YAAY;kBAACjC,IAAI,EAAE,EAAG;kBAACH,KAAK,EAAEsC;gBAAc;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACNxF,OAAA;gBAAAgF,QAAA,gBACEhF,OAAA;kBAAIwE,KAAK,EAAE;oBACTiB,MAAM,EAAE,aAAa;oBACrBC,QAAQ,EAAE,UAAU;oBACpBC,UAAU,EAAE,KAAK;oBACjBZ,KAAK,EAAE;kBACT,CAAE;kBAAAC,QAAA,GACC7B,KAAK,CAACX,UAAU,EAAC,GAAC,EAACW,KAAK,CAACT,SAAS,EAClCS,KAAK,CAACjB,QAAQ,MAAK/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuH,EAAE,kBAC1B1H,OAAA;oBAAMwE,KAAK,EAAE;sBACXmD,UAAU,EAAE,QAAQ;sBACpB9B,OAAO,EAAE,mBAAmB;sBAC5BC,UAAU,EAAE,SAAS;sBACrBf,KAAK,EAAE,SAAS;sBAChBiB,YAAY,EAAE,KAAK;sBACnBN,QAAQ,EAAE,UAAU;sBACpBC,UAAU,EAAE;oBACd,CAAE;oBAAAX,QAAA,EAAC;kBAEH;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACLxF,OAAA;kBAAKwE,KAAK,EAAE;oBACVqB,OAAO,EAAE,gBAAgB;oBACzBC,UAAU,EAAEuB,aAAa;oBACzBtC,KAAK,EAAE,OAAO;oBACdiB,YAAY,EAAE,KAAK;oBACnBN,QAAQ,EAAE,SAAS;oBACnBC,UAAU,EAAE,KAAK;oBACjBlB,OAAO,EAAE;kBACX,CAAE;kBAAAO,QAAA,EACCuC;gBAAa;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENxF,OAAA;cAAKwE,KAAK,EAAE;gBACVqB,OAAO,EAAE,gBAAgB;gBACzBC,UAAU,EAAE3C,KAAK,CAACf,SAAS,GAAG,SAAS,GAAG,SAAS;gBACnD2C,KAAK,EAAE5B,KAAK,CAACf,SAAS,GAAG,SAAS,GAAG,SAAS;gBAC9C4D,YAAY,EAAE,KAAK;gBACnBN,QAAQ,EAAE,SAAS;gBACnBC,UAAU,EAAE;cACd,CAAE;cAAAX,QAAA,EACC7B,KAAK,CAACf,SAAS,GAAG,QAAQ,GAAG;YAAU;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNxF,OAAA;YAAKwE,KAAK,EAAE;cAAEW,YAAY,EAAE;YAAO,CAAE;YAAAH,QAAA,gBACnChF,OAAA;cAAKwE,KAAK,EAAE;gBACVC,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE,QAAQ;gBACpB+B,GAAG,EAAE,QAAQ;gBACbvB,YAAY,EAAE;cAChB,CAAE;cAAAH,QAAA,gBACAhF,OAAA,CAAC4H,IAAI;gBAAC1C,IAAI,EAAE,EAAG;gBAACH,KAAK,EAAC;cAAS;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClCxF,OAAA;gBAAMwE,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,UAAU;kBAAEX,KAAK,EAAE;gBAAU,CAAE;gBAAAC,QAAA,EACrD7B,KAAK,CAAChB;cAAK;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EAELrC,KAAK,CAACP,YAAY,iBACjB5C,OAAA;cAAKwE,KAAK,EAAE;gBACVC,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE,QAAQ;gBACpB+B,GAAG,EAAE,QAAQ;gBACbvB,YAAY,EAAE;cAChB,CAAE;cAAAH,QAAA,gBACAhF,OAAA,CAAC6H,KAAK;gBAAC3C,IAAI,EAAE,EAAG;gBAACH,KAAK,EAAC;cAAS;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnCxF,OAAA;gBAAMwE,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,UAAU;kBAAEX,KAAK,EAAE;gBAAU,CAAE;gBAAAC,QAAA,EACrD7B,KAAK,CAACP;cAAY;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN,EAEArC,KAAK,CAACN,UAAU,iBACf7C,OAAA;cAAKwE,KAAK,EAAE;gBACVkB,QAAQ,EAAE,UAAU;gBACpBX,KAAK,EAAE,SAAS;gBAChBa,SAAS,EAAE;cACb,CAAE;cAAAZ,QAAA,GAAC,cACW,EAAC7B,KAAK,CAACN,UAAU;YAAA;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CACN,EAEArC,KAAK,CAACJ,WAAW,iBAChB/C,OAAA;cAAKwE,KAAK,EAAE;gBACVkB,QAAQ,EAAE,UAAU;gBACpBX,KAAK,EAAE,SAAS;gBAChBa,SAAS,EAAE;cACb,CAAE;cAAAZ,QAAA,GAAC,eACY,EAAC7B,KAAK,CAACJ,WAAW;YAAA;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGLrC,KAAK,CAACd,UAAU,iBACfrC,OAAA;YAAKwE,KAAK,EAAE;cACVkB,QAAQ,EAAE,SAAS;cACnBX,KAAK,EAAE,SAAS;cAChBI,YAAY,EAAE;YAChB,CAAE;YAAAH,QAAA,GAAC,cACW,EAAC,IAAI8C,IAAI,CAAC3E,KAAK,CAACd,UAAU,CAAC,CAAC0F,kBAAkB,CAAC,CAAC;UAAA;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CACN,eAGDxF,OAAA;YAAKwE,KAAK,EAAE;cACVC,OAAO,EAAE,MAAM;cACfiC,GAAG,EAAE,QAAQ;cACbsB,UAAU,EAAE,MAAM;cAClB3B,SAAS,EAAE;YACb,CAAE;YAAArB,QAAA,gBACAhF,OAAA;cACEwE,KAAK,EAAE;gBACLyD,IAAI,EAAE,CAAC;gBACPpC,OAAO,EAAE,QAAQ;gBACjBC,UAAU,EAAE,aAAa;gBACzBM,MAAM,EAAE,mBAAmB;gBAC3BJ,YAAY,EAAE,KAAK;gBACnBQ,MAAM,EAAE,SAAS;gBACjBzB,KAAK,EAAE,SAAS;gBAChBW,QAAQ,EAAE,UAAU;gBACpBjB,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB8B,GAAG,EAAE;cACP,CAAE;cAAA1B,QAAA,gBAEFhF,OAAA,CAACkI,IAAI;gBAAChD,IAAI,EAAE;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,QAEpB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAERrC,KAAK,CAACjB,QAAQ,MAAK/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuH,EAAE,kBAC1B1H,OAAA;cACEwE,KAAK,EAAE;gBACLyD,IAAI,EAAE,CAAC;gBACPpC,OAAO,EAAE,QAAQ;gBACjBC,UAAU,EAAE,aAAa;gBACzBM,MAAM,EAAE,mBAAmB;gBAC3BJ,YAAY,EAAE,KAAK;gBACnBQ,MAAM,EAAE,SAAS;gBACjBzB,KAAK,EAAE,SAAS;gBAChBW,QAAQ,EAAE,UAAU;gBACpBjB,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB8B,GAAG,EAAE;cACP,CAAE;cAAA1B,QAAA,gBAEFhF,OAAA,CAACmI,MAAM;gBAACjD,IAAI,EAAE;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAEtB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,GAxLDrC,KAAK,CAACjB,QAAQ;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAyLhB,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtF,EAAA,CA9hBID,eAAyB;EAAA,QACZJ,YAAY,EACTC,cAAc;AAAA;AAAAsI,EAAA,GAF9BnI,eAAyB;AAgiB/B,eAAeA,eAAe;AAAC,IAAAmI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}