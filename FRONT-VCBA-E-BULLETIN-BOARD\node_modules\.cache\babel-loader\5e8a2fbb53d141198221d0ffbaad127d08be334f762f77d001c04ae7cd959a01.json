{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\admin\\\\layout\\\\AdminSidebar.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { NavLink, useLocation } from 'react-router-dom';\nimport { BarChart3, Calendar, Newspaper, Users, Settings, Rss, Archive, Monitor, UserCog, FolderTree } from 'lucide-react';\nimport { useAuth } from '../../../contexts/AuthContext';\nimport { usePermissions } from '../../../utils/permissions';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst navItems = [{\n  path: '/admin/dashboard',\n  label: 'Dashboard',\n  icon: BarChart3,\n  description: 'Overview & Analytics'\n}, {\n  path: '/admin/newsfeed',\n  label: 'Newsfeed',\n  icon: Rss,\n  description: 'Monitor Announcements & Events'\n}, {\n  path: '/admin/calendar',\n  label: 'Calendar',\n  icon: Calendar,\n  description: 'Events & Schedule',\n  requiresPermission: permissions => permissions.canCreateCalendarEvents\n}, {\n  path: '/admin/posts',\n  label: 'Post',\n  icon: Newspaper,\n  description: 'Create & Manage Announcements',\n  requiresPermission: permissions => permissions.canCreateAnnouncements\n}, {\n  path: '/admin/student-management',\n  label: 'Student',\n  icon: Users,\n  description: 'Manage Students',\n  requiresPermission: permissions => permissions.canViewStudents\n}, {\n  path: '/admin/categories',\n  label: 'Categories',\n  icon: FolderTree,\n  description: 'Manage Categories & Subcategories',\n  requiresPermission: permissions => permissions.canManageCategories\n}, {\n  path: '/admin/admin-management',\n  label: 'Admin Management',\n  icon: UserCog,\n  description: 'Manage Admin Accounts',\n  requiresPermission: permissions => permissions.canManageAdmins\n}, {\n  path: '/admin/archive',\n  label: 'Archive',\n  icon: Archive,\n  description: 'View Archived Records',\n  requiresPermission: permissions => permissions.canViewArchive\n}, {\n  path: '/admin/tv-control',\n  label: 'TV Display',\n  icon: Monitor,\n  description: 'Manage TV Display & Signage',\n  requiresPermission: permissions => permissions.canManageTVDisplay\n}, {\n  path: '/admin/settings',\n  label: 'Settings',\n  icon: Settings,\n  description: 'System Configuration',\n  requiresPermission: permissions => permissions.canManageSystemSettings\n}];\nconst AdminSidebar = ({\n  isOpen,\n  onToggle\n}) => {\n  _s();\n  const location = useLocation();\n  const {\n    user\n  } = useAuth();\n  const permissions = usePermissions(user);\n  const isActive = path => {\n    return location.pathname === path || path === '/admin/dashboard' && location.pathname === '/admin';\n  };\n\n  // Filter navigation items based on permissions\n  const visibleNavItems = navItems.filter(item => {\n    if (!item.requiresPermission) {\n      return true; // Always show items without permission requirements\n    }\n    return item.requiresPermission(permissions);\n  });\n  return /*#__PURE__*/_jsxDEV(\"aside\", {\n    style: {\n      position: 'fixed',\n      left: 0,\n      top: 0,\n      height: '100vh',\n      width: isOpen ? '280px' : '80px',\n      background: 'linear-gradient(180deg, #2d5016 0%, #1a3009 100%)',\n      transition: 'width 0.3s ease',\n      zIndex: 1000,\n      boxShadow: '4px 0 20px rgba(0, 0, 0, 0.1)',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: isOpen ? '1.5rem' : '1rem',\n        borderBottom: '1px solid rgba(255, 255, 255, 0.1)',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '1rem',\n        minHeight: '80px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"/logo/vcba1.png\",\n        alt: \"VCBA Logo\",\n        style: {\n          width: '48px',\n          height: '48px',\n          objectFit: 'contain',\n          flexShrink: 0\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this), isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            color: 'white',\n            margin: 0,\n            fontSize: '1.1rem',\n            fontWeight: '700',\n            lineHeight: '1.2'\n          },\n          children: \"VCBA Admin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: 'rgba(255, 255, 255, 0.7)',\n            margin: 0,\n            fontSize: '0.75rem',\n            lineHeight: '1.2'\n          },\n          children: \"E-Bulletin Board\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 13\n        }, this), (user === null || user === void 0 ? void 0 : user.position) && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '0.5rem',\n            padding: '0.25rem 0.5rem',\n            background: permissions.getPositionBadgeColor(),\n            borderRadius: '4px',\n            fontSize: '0.65rem',\n            fontWeight: '600',\n            color: 'white',\n            textAlign: 'center',\n            textTransform: 'uppercase',\n            letterSpacing: '0.5px'\n          },\n          children: permissions.getPositionDisplayName()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      style: {\n        padding: '1rem 0'\n      },\n      children: visibleNavItems.map(item => /*#__PURE__*/_jsxDEV(NavLink, {\n        to: item.path,\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '1rem',\n          padding: isOpen ? '1rem 1.5rem' : '1rem',\n          color: isActive(item.path) ? '#facc15' : 'rgba(255, 255, 255, 0.8)',\n          textDecoration: 'none',\n          background: isActive(item.path) ? 'linear-gradient(90deg, rgba(250, 204, 21, 0.2) 0%, transparent 100%)' : 'transparent',\n          borderRight: isActive(item.path) ? '3px solid #facc15' : '3px solid transparent',\n          transition: 'all 0.2s ease',\n          position: 'relative',\n          overflow: 'hidden'\n        },\n        onMouseEnter: e => {\n          if (!isActive(item.path)) {\n            e.currentTarget.style.background = 'rgba(255, 255, 255, 0.05)';\n            e.currentTarget.style.color = 'white';\n          }\n        },\n        onMouseLeave: e => {\n          if (!isActive(item.path)) {\n            e.currentTarget.style.background = 'transparent';\n            e.currentTarget.style.color = 'rgba(255, 255, 255, 0.8)';\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            flexShrink: 0,\n            width: '24px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(item.icon, {\n            size: 20,\n            color: isActive(item.path) ? '#facc15' : 'rgba(255, 255, 255, 0.8)'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 13\n        }, this), isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            minWidth: 0\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: isActive(item.path) ? '600' : '500',\n              fontSize: '0.95rem',\n              marginBottom: '0.25rem'\n            },\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.75rem',\n              color: isActive(item.path) ? 'rgba(250, 204, 21, 0.8)' : 'rgba(255, 255, 255, 0.6)',\n              lineHeight: '1.2'\n            },\n            children: item.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 15\n        }, this)]\n      }, item.path, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 7\n    }, this), isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        bottom: '1rem',\n        left: '1.5rem',\n        right: '1.5rem',\n        padding: '1rem',\n        background: 'rgba(255, 255, 255, 0.05)',\n        borderRadius: '12px',\n        border: '1px solid rgba(255, 255, 255, 0.1)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: 'rgba(255, 255, 255, 0.9)',\n          fontSize: '0.8rem',\n          fontWeight: '600',\n          marginBottom: '0.25rem'\n        },\n        children: \"Villamor College\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: 'rgba(255, 255, 255, 0.6)',\n          fontSize: '0.7rem',\n          lineHeight: '1.3'\n        },\n        children: \"Business and Arts, Inc.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 110,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminSidebar, \"Wcgv9f6BUWwDbCWPkAQ9z790u9I=\", false, function () {\n  return [useLocation, useAuth, usePermissions];\n});\n_c = AdminSidebar;\nexport default AdminSidebar;\nvar _c;\n$RefreshReg$(_c, \"AdminSidebar\");", "map": {"version": 3, "names": ["React", "NavLink", "useLocation", "BarChart3", "Calendar", "Newspaper", "Users", "Settings", "Rss", "Archive", "Monitor", "UserCog", "FolderTree", "useAuth", "usePermissions", "jsxDEV", "_jsxDEV", "navItems", "path", "label", "icon", "description", "requiresPermission", "permissions", "canCreateCalendarEvents", "canCreateAnnouncements", "canViewStudents", "canManageCategories", "canManageAdmins", "canViewArchive", "canManageTVDisplay", "canManageSystemSettings", "AdminSidebar", "isOpen", "onToggle", "_s", "location", "user", "isActive", "pathname", "visibleNavItems", "filter", "item", "style", "position", "left", "top", "height", "width", "background", "transition", "zIndex", "boxShadow", "overflow", "children", "padding", "borderBottom", "display", "alignItems", "gap", "minHeight", "src", "alt", "objectFit", "flexShrink", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "margin", "fontSize", "fontWeight", "lineHeight", "marginTop", "getPositionBadgeColor", "borderRadius", "textAlign", "textTransform", "letterSpacing", "getPositionDisplayName", "map", "to", "textDecoration", "borderRight", "onMouseEnter", "e", "currentTarget", "onMouseLeave", "justifyContent", "size", "flex", "min<PERSON><PERSON><PERSON>", "marginBottom", "bottom", "right", "border", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/admin/layout/AdminSidebar.tsx"], "sourcesContent": ["import React from 'react';\nimport { NavLink, useLocation } from 'react-router-dom';\nimport { BarChart3, Calendar, Newspaper, Users, Settings, Rss, Archive, Monitor, UserCog, FolderTree, ChevronLeft, ChevronRight } from 'lucide-react';\nimport { useAuth } from '../../../contexts/AuthContext';\nimport { usePermissions } from '../../../utils/permissions';\n\ninterface AdminSidebarProps {\n  isOpen: boolean;\n  onToggle: () => void;\n}\n\ninterface NavItem {\n  path: string;\n  label: string;\n  icon: React.ComponentType<{ size?: number; color?: string }>;\n  description: string;\n  requiresPermission?: (permissions: ReturnType<typeof usePermissions>) => boolean;\n}\n\nconst navItems: NavItem[] = [\n  {\n    path: '/admin/dashboard',\n    label: 'Dashboard',\n    icon: BarChart3,\n    description: 'Overview & Analytics'\n  },\n  {\n    path: '/admin/newsfeed',\n    label: 'Newsfeed',\n    icon: Rss,\n    description: 'Monitor Announcements & Events'\n  },\n  {\n    path: '/admin/calendar',\n    label: 'Calendar',\n    icon: Calendar,\n    description: 'Events & Schedule',\n    requiresPermission: (permissions) => permissions.canCreateCalendarEvents\n  },\n  {\n    path: '/admin/posts',\n    label: 'Post',\n    icon: Newspaper,\n    description: 'Create & Manage Announcements',\n    requiresPermission: (permissions) => permissions.canCreateAnnouncements\n  },\n  {\n    path: '/admin/student-management',\n    label: 'Student',\n    icon: Users,\n    description: 'Manage Students',\n    requiresPermission: (permissions) => permissions.canViewStudents\n  },\n  {\n    path: '/admin/categories',\n    label: 'Categories',\n    icon: FolderTree,\n    description: 'Manage Categories & Subcategories',\n    requiresPermission: (permissions) => permissions.canManageCategories\n  },\n  {\n    path: '/admin/admin-management',\n    label: 'Admin Management',\n    icon: UserCog,\n    description: 'Manage Admin Accounts',\n    requiresPermission: (permissions) => permissions.canManageAdmins\n  },\n  {\n    path: '/admin/archive',\n    label: 'Archive',\n    icon: Archive,\n    description: 'View Archived Records',\n    requiresPermission: (permissions) => permissions.canViewArchive\n  },\n  {\n    path: '/admin/tv-control',\n    label: 'TV Display',\n    icon: Monitor,\n    description: 'Manage TV Display & Signage',\n    requiresPermission: (permissions) => permissions.canManageTVDisplay\n  },\n  {\n    path: '/admin/settings',\n    label: 'Settings',\n    icon: Settings,\n    description: 'System Configuration',\n    requiresPermission: (permissions) => permissions.canManageSystemSettings\n  }\n];\n\nconst AdminSidebar: React.FC<AdminSidebarProps> = ({ isOpen, onToggle }) => {\n  const location = useLocation();\n  const { user } = useAuth();\n  const permissions = usePermissions(user);\n\n  const isActive = (path: string) => {\n    return location.pathname === path ||\n           (path === '/admin/dashboard' && location.pathname === '/admin');\n  };\n\n  // Filter navigation items based on permissions\n  const visibleNavItems = navItems.filter(item => {\n    if (!item.requiresPermission) {\n      return true; // Always show items without permission requirements\n    }\n    return item.requiresPermission(permissions);\n  });\n\n  return (\n    <aside style={{\n      position: 'fixed',\n      left: 0,\n      top: 0,\n      height: '100vh',\n      width: isOpen ? '280px' : '80px',\n      background: 'linear-gradient(180deg, #2d5016 0%, #1a3009 100%)',\n      transition: 'width 0.3s ease',\n      zIndex: 1000,\n      boxShadow: '4px 0 20px rgba(0, 0, 0, 0.1)',\n      overflow: 'hidden'\n    }}>\n      {/* Logo Section */}\n      <div style={{\n        padding: isOpen ? '1.5rem' : '1rem',\n        borderBottom: '1px solid rgba(255, 255, 255, 0.1)',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '1rem',\n        minHeight: '80px'\n      }}>\n        <img\n          src=\"/logo/vcba1.png\"\n          alt=\"VCBA Logo\"\n          style={{\n            width: '48px',\n            height: '48px',\n            objectFit: 'contain',\n            flexShrink: 0\n          }}\n        />\n        {isOpen && (\n          <div>\n            <h2 style={{\n              color: 'white',\n              margin: 0,\n              fontSize: '1.1rem',\n              fontWeight: '700',\n              lineHeight: '1.2'\n            }}>\n              VCBA Admin\n            </h2>\n            <p style={{\n              color: 'rgba(255, 255, 255, 0.7)',\n              margin: 0,\n              fontSize: '0.75rem',\n              lineHeight: '1.2'\n            }}>\n              E-Bulletin Board\n            </p>\n            {/* Position Badge */}\n            {user?.position && (\n              <div style={{\n                marginTop: '0.5rem',\n                padding: '0.25rem 0.5rem',\n                background: permissions.getPositionBadgeColor(),\n                borderRadius: '4px',\n                fontSize: '0.65rem',\n                fontWeight: '600',\n                color: 'white',\n                textAlign: 'center',\n                textTransform: 'uppercase',\n                letterSpacing: '0.5px'\n              }}>\n                {permissions.getPositionDisplayName()}\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n\n      {/* Navigation */}\n      <nav style={{ padding: '1rem 0' }}>\n        {visibleNavItems.map((item) => (\n          <NavLink\n            key={item.path}\n            to={item.path}\n            style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1rem',\n              padding: isOpen ? '1rem 1.5rem' : '1rem',\n              color: isActive(item.path) ? '#facc15' : 'rgba(255, 255, 255, 0.8)',\n              textDecoration: 'none',\n              background: isActive(item.path) \n                ? 'linear-gradient(90deg, rgba(250, 204, 21, 0.2) 0%, transparent 100%)'\n                : 'transparent',\n              borderRight: isActive(item.path) ? '3px solid #facc15' : '3px solid transparent',\n              transition: 'all 0.2s ease',\n              position: 'relative',\n              overflow: 'hidden'\n            }}\n            onMouseEnter={(e) => {\n              if (!isActive(item.path)) {\n                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.05)';\n                e.currentTarget.style.color = 'white';\n              }\n            }}\n            onMouseLeave={(e) => {\n              if (!isActive(item.path)) {\n                e.currentTarget.style.background = 'transparent';\n                e.currentTarget.style.color = 'rgba(255, 255, 255, 0.8)';\n              }\n            }}\n          >\n            <span style={{\n              flexShrink: 0,\n              width: '24px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            }}>\n              <item.icon size={20} color={isActive(item.path) ? '#facc15' : 'rgba(255, 255, 255, 0.8)'} />\n            </span>\n            {isOpen && (\n              <div style={{ flex: 1, minWidth: 0 }}>\n                <div style={{\n                  fontWeight: isActive(item.path) ? '600' : '500',\n                  fontSize: '0.95rem',\n                  marginBottom: '0.25rem'\n                }}>\n                  {item.label}\n                </div>\n                <div style={{\n                  fontSize: '0.75rem',\n                  color: isActive(item.path) \n                    ? 'rgba(250, 204, 21, 0.8)' \n                    : 'rgba(255, 255, 255, 0.6)',\n                  lineHeight: '1.2'\n                }}>\n                  {item.description}\n                </div>\n              </div>\n            )}\n          </NavLink>\n        ))}\n      </nav>\n\n      {/* Footer */}\n      {isOpen && (\n        <div style={{\n          position: 'absolute',\n          bottom: '1rem',\n          left: '1.5rem',\n          right: '1.5rem',\n          padding: '1rem',\n          background: 'rgba(255, 255, 255, 0.05)',\n          borderRadius: '12px',\n          border: '1px solid rgba(255, 255, 255, 0.1)'\n        }}>\n          <div style={{\n            color: 'rgba(255, 255, 255, 0.9)',\n            fontSize: '0.8rem',\n            fontWeight: '600',\n            marginBottom: '0.25rem'\n          }}>\n            Villamor College\n          </div>\n          <div style={{\n            color: 'rgba(255, 255, 255, 0.6)',\n            fontSize: '0.7rem',\n            lineHeight: '1.3'\n          }}>\n            Business and Arts, Inc.\n          </div>\n        </div>\n      )}\n    </aside>\n  );\n};\n\nexport default AdminSidebar;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,EAAEC,WAAW,QAAQ,kBAAkB;AACvD,SAASC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,UAAU,QAAmC,cAAc;AACrJ,SAASC,OAAO,QAAQ,+BAA+B;AACvD,SAASC,cAAc,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAe5D,MAAMC,QAAmB,GAAG,CAC1B;EACEC,IAAI,EAAE,kBAAkB;EACxBC,KAAK,EAAE,WAAW;EAClBC,IAAI,EAAEjB,SAAS;EACfkB,WAAW,EAAE;AACf,CAAC,EACD;EACEH,IAAI,EAAE,iBAAiB;EACvBC,KAAK,EAAE,UAAU;EACjBC,IAAI,EAAEZ,GAAG;EACTa,WAAW,EAAE;AACf,CAAC,EACD;EACEH,IAAI,EAAE,iBAAiB;EACvBC,KAAK,EAAE,UAAU;EACjBC,IAAI,EAAEhB,QAAQ;EACdiB,WAAW,EAAE,mBAAmB;EAChCC,kBAAkB,EAAGC,WAAW,IAAKA,WAAW,CAACC;AACnD,CAAC,EACD;EACEN,IAAI,EAAE,cAAc;EACpBC,KAAK,EAAE,MAAM;EACbC,IAAI,EAAEf,SAAS;EACfgB,WAAW,EAAE,+BAA+B;EAC5CC,kBAAkB,EAAGC,WAAW,IAAKA,WAAW,CAACE;AACnD,CAAC,EACD;EACEP,IAAI,EAAE,2BAA2B;EACjCC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAEd,KAAK;EACXe,WAAW,EAAE,iBAAiB;EAC9BC,kBAAkB,EAAGC,WAAW,IAAKA,WAAW,CAACG;AACnD,CAAC,EACD;EACER,IAAI,EAAE,mBAAmB;EACzBC,KAAK,EAAE,YAAY;EACnBC,IAAI,EAAER,UAAU;EAChBS,WAAW,EAAE,mCAAmC;EAChDC,kBAAkB,EAAGC,WAAW,IAAKA,WAAW,CAACI;AACnD,CAAC,EACD;EACET,IAAI,EAAE,yBAAyB;EAC/BC,KAAK,EAAE,kBAAkB;EACzBC,IAAI,EAAET,OAAO;EACbU,WAAW,EAAE,uBAAuB;EACpCC,kBAAkB,EAAGC,WAAW,IAAKA,WAAW,CAACK;AACnD,CAAC,EACD;EACEV,IAAI,EAAE,gBAAgB;EACtBC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAEX,OAAO;EACbY,WAAW,EAAE,uBAAuB;EACpCC,kBAAkB,EAAGC,WAAW,IAAKA,WAAW,CAACM;AACnD,CAAC,EACD;EACEX,IAAI,EAAE,mBAAmB;EACzBC,KAAK,EAAE,YAAY;EACnBC,IAAI,EAAEV,OAAO;EACbW,WAAW,EAAE,6BAA6B;EAC1CC,kBAAkB,EAAGC,WAAW,IAAKA,WAAW,CAACO;AACnD,CAAC,EACD;EACEZ,IAAI,EAAE,iBAAiB;EACvBC,KAAK,EAAE,UAAU;EACjBC,IAAI,EAAEb,QAAQ;EACdc,WAAW,EAAE,sBAAsB;EACnCC,kBAAkB,EAAGC,WAAW,IAAKA,WAAW,CAACQ;AACnD,CAAC,CACF;AAED,MAAMC,YAAyC,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC1E,MAAMC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEmC;EAAK,CAAC,GAAGxB,OAAO,CAAC,CAAC;EAC1B,MAAMU,WAAW,GAAGT,cAAc,CAACuB,IAAI,CAAC;EAExC,MAAMC,QAAQ,GAAIpB,IAAY,IAAK;IACjC,OAAOkB,QAAQ,CAACG,QAAQ,KAAKrB,IAAI,IACzBA,IAAI,KAAK,kBAAkB,IAAIkB,QAAQ,CAACG,QAAQ,KAAK,QAAS;EACxE,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGvB,QAAQ,CAACwB,MAAM,CAACC,IAAI,IAAI;IAC9C,IAAI,CAACA,IAAI,CAACpB,kBAAkB,EAAE;MAC5B,OAAO,IAAI,CAAC,CAAC;IACf;IACA,OAAOoB,IAAI,CAACpB,kBAAkB,CAACC,WAAW,CAAC;EAC7C,CAAC,CAAC;EAEF,oBACEP,OAAA;IAAO2B,KAAK,EAAE;MACZC,QAAQ,EAAE,OAAO;MACjBC,IAAI,EAAE,CAAC;MACPC,GAAG,EAAE,CAAC;MACNC,MAAM,EAAE,OAAO;MACfC,KAAK,EAAEf,MAAM,GAAG,OAAO,GAAG,MAAM;MAChCgB,UAAU,EAAE,mDAAmD;MAC/DC,UAAU,EAAE,iBAAiB;MAC7BC,MAAM,EAAE,IAAI;MACZC,SAAS,EAAE,+BAA+B;MAC1CC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,gBAEAtC,OAAA;MAAK2B,KAAK,EAAE;QACVY,OAAO,EAAEtB,MAAM,GAAG,QAAQ,GAAG,MAAM;QACnCuB,YAAY,EAAE,oCAAoC;QAClDC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE,MAAM;QACXC,SAAS,EAAE;MACb,CAAE;MAAAN,QAAA,gBACAtC,OAAA;QACE6C,GAAG,EAAC,iBAAiB;QACrBC,GAAG,EAAC,WAAW;QACfnB,KAAK,EAAE;UACLK,KAAK,EAAE,MAAM;UACbD,MAAM,EAAE,MAAM;UACdgB,SAAS,EAAE,SAAS;UACpBC,UAAU,EAAE;QACd;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACDnC,MAAM,iBACLjB,OAAA;QAAAsC,QAAA,gBACEtC,OAAA;UAAI2B,KAAK,EAAE;YACT0B,KAAK,EAAE,OAAO;YACdC,MAAM,EAAE,CAAC;YACTC,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE,KAAK;YACjBC,UAAU,EAAE;UACd,CAAE;UAAAnB,QAAA,EAAC;QAEH;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLpD,OAAA;UAAG2B,KAAK,EAAE;YACR0B,KAAK,EAAE,0BAA0B;YACjCC,MAAM,EAAE,CAAC;YACTC,QAAQ,EAAE,SAAS;YACnBE,UAAU,EAAE;UACd,CAAE;UAAAnB,QAAA,EAAC;QAEH;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAEH,CAAA/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,QAAQ,kBACb5B,OAAA;UAAK2B,KAAK,EAAE;YACV+B,SAAS,EAAE,QAAQ;YACnBnB,OAAO,EAAE,gBAAgB;YACzBN,UAAU,EAAE1B,WAAW,CAACoD,qBAAqB,CAAC,CAAC;YAC/CC,YAAY,EAAE,KAAK;YACnBL,QAAQ,EAAE,SAAS;YACnBC,UAAU,EAAE,KAAK;YACjBH,KAAK,EAAE,OAAO;YACdQ,SAAS,EAAE,QAAQ;YACnBC,aAAa,EAAE,WAAW;YAC1BC,aAAa,EAAE;UACjB,CAAE;UAAAzB,QAAA,EACC/B,WAAW,CAACyD,sBAAsB,CAAC;QAAC;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNpD,OAAA;MAAK2B,KAAK,EAAE;QAAEY,OAAO,EAAE;MAAS,CAAE;MAAAD,QAAA,EAC/Bd,eAAe,CAACyC,GAAG,CAAEvC,IAAI,iBACxB1B,OAAA,CAACf,OAAO;QAENiF,EAAE,EAAExC,IAAI,CAACxB,IAAK;QACdyB,KAAK,EAAE;UACLc,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,GAAG,EAAE,MAAM;UACXJ,OAAO,EAAEtB,MAAM,GAAG,aAAa,GAAG,MAAM;UACxCoC,KAAK,EAAE/B,QAAQ,CAACI,IAAI,CAACxB,IAAI,CAAC,GAAG,SAAS,GAAG,0BAA0B;UACnEiE,cAAc,EAAE,MAAM;UACtBlC,UAAU,EAAEX,QAAQ,CAACI,IAAI,CAACxB,IAAI,CAAC,GAC3B,sEAAsE,GACtE,aAAa;UACjBkE,WAAW,EAAE9C,QAAQ,CAACI,IAAI,CAACxB,IAAI,CAAC,GAAG,mBAAmB,GAAG,uBAAuB;UAChFgC,UAAU,EAAE,eAAe;UAC3BN,QAAQ,EAAE,UAAU;UACpBS,QAAQ,EAAE;QACZ,CAAE;QACFgC,YAAY,EAAGC,CAAC,IAAK;UACnB,IAAI,CAAChD,QAAQ,CAACI,IAAI,CAACxB,IAAI,CAAC,EAAE;YACxBoE,CAAC,CAACC,aAAa,CAAC5C,KAAK,CAACM,UAAU,GAAG,2BAA2B;YAC9DqC,CAAC,CAACC,aAAa,CAAC5C,KAAK,CAAC0B,KAAK,GAAG,OAAO;UACvC;QACF,CAAE;QACFmB,YAAY,EAAGF,CAAC,IAAK;UACnB,IAAI,CAAChD,QAAQ,CAACI,IAAI,CAACxB,IAAI,CAAC,EAAE;YACxBoE,CAAC,CAACC,aAAa,CAAC5C,KAAK,CAACM,UAAU,GAAG,aAAa;YAChDqC,CAAC,CAACC,aAAa,CAAC5C,KAAK,CAAC0B,KAAK,GAAG,0BAA0B;UAC1D;QACF,CAAE;QAAAf,QAAA,gBAEFtC,OAAA;UAAM2B,KAAK,EAAE;YACXqB,UAAU,EAAE,CAAC;YACbhB,KAAK,EAAE,MAAM;YACbS,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpB+B,cAAc,EAAE;UAClB,CAAE;UAAAnC,QAAA,eACAtC,OAAA,CAAC0B,IAAI,CAACtB,IAAI;YAACsE,IAAI,EAAE,EAAG;YAACrB,KAAK,EAAE/B,QAAQ,CAACI,IAAI,CAACxB,IAAI,CAAC,GAAG,SAAS,GAAG;UAA2B;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxF,CAAC,EACNnC,MAAM,iBACLjB,OAAA;UAAK2B,KAAK,EAAE;YAAEgD,IAAI,EAAE,CAAC;YAAEC,QAAQ,EAAE;UAAE,CAAE;UAAAtC,QAAA,gBACnCtC,OAAA;YAAK2B,KAAK,EAAE;cACV6B,UAAU,EAAElC,QAAQ,CAACI,IAAI,CAACxB,IAAI,CAAC,GAAG,KAAK,GAAG,KAAK;cAC/CqD,QAAQ,EAAE,SAAS;cACnBsB,YAAY,EAAE;YAChB,CAAE;YAAAvC,QAAA,EACCZ,IAAI,CAACvB;UAAK;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACNpD,OAAA;YAAK2B,KAAK,EAAE;cACV4B,QAAQ,EAAE,SAAS;cACnBF,KAAK,EAAE/B,QAAQ,CAACI,IAAI,CAACxB,IAAI,CAAC,GACtB,yBAAyB,GACzB,0BAA0B;cAC9BuD,UAAU,EAAE;YACd,CAAE;YAAAnB,QAAA,EACCZ,IAAI,CAACrB;UAAW;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA,GA1DI1B,IAAI,CAACxB,IAAI;QAAA+C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA2DP,CACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGLnC,MAAM,iBACLjB,OAAA;MAAK2B,KAAK,EAAE;QACVC,QAAQ,EAAE,UAAU;QACpBkD,MAAM,EAAE,MAAM;QACdjD,IAAI,EAAE,QAAQ;QACdkD,KAAK,EAAE,QAAQ;QACfxC,OAAO,EAAE,MAAM;QACfN,UAAU,EAAE,2BAA2B;QACvC2B,YAAY,EAAE,MAAM;QACpBoB,MAAM,EAAE;MACV,CAAE;MAAA1C,QAAA,gBACAtC,OAAA;QAAK2B,KAAK,EAAE;UACV0B,KAAK,EAAE,0BAA0B;UACjCE,QAAQ,EAAE,QAAQ;UAClBC,UAAU,EAAE,KAAK;UACjBqB,YAAY,EAAE;QAChB,CAAE;QAAAvC,QAAA,EAAC;MAEH;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNpD,OAAA;QAAK2B,KAAK,EAAE;UACV0B,KAAK,EAAE,0BAA0B;UACjCE,QAAQ,EAAE,QAAQ;UAClBE,UAAU,EAAE;QACd,CAAE;QAAAnB,QAAA,EAAC;MAEH;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEZ,CAAC;AAACjC,EAAA,CA5LIH,YAAyC;EAAA,QAC5B9B,WAAW,EACXW,OAAO,EACJC,cAAc;AAAA;AAAAmF,EAAA,GAH9BjE,YAAyC;AA8L/C,eAAeA,YAAY;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}