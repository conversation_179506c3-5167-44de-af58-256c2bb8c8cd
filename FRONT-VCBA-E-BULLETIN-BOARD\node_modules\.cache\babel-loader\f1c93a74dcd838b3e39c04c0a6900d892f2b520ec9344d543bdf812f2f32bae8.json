{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\admin\\\\CategoryManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { FolderTree, Plus } from 'lucide-react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CategoryManagement = () => {\n  _s();\n  const {\n    user\n  } = useAdminAuth();\n  const permissions = usePermissions(user);\n\n  // State management\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n\n  // Modal state\n  const [showModal, setShowModal] = useState(false);\n  const [modalMode, setModalMode] = useState('add_category');\n  const [editingCategory, setEditingCategory] = useState(null);\n  const [editingSubcategory, setEditingSubcategory] = useState(null);\n  const [parentCategory, setParentCategory] = useState(null);\n  const [modalLoading, setModalLoading] = useState(false);\n  useEffect(() => {\n    // Check if user has permission to manage categories\n    if (!permissions.canManageCategories) {\n      setError('You do not have permission to manage categories');\n      setLoading(false);\n      return;\n    }\n    loadCategories();\n  }, [permissions.canManageCategories]);\n  const loadCategories = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // TODO: Implement API call to fetch categories\n      // const response = await categoryService.getCategoriesWithSubcategories();\n      // setCategories(response.data.categories);\n\n      // Mock data with proper structure\n      const mockCategories = [{\n        category_id: 1,\n        name: 'Academic',\n        description: 'Academic-related announcements and events',\n        color_code: '#3b82f6',\n        is_active: true,\n        subcategories: [{\n          subcategory_id: 1,\n          name: 'Exams',\n          description: 'Examination schedules and updates',\n          color_code: '#ef4444',\n          display_order: 1,\n          is_active: true\n        }, {\n          subcategory_id: 2,\n          name: 'Assignments',\n          description: 'Assignment deadlines and submissions',\n          color_code: '#f59e0b',\n          display_order: 2,\n          is_active: true\n        }, {\n          subcategory_id: 3,\n          name: 'Class Schedules',\n          description: 'Class timing and schedule changes',\n          color_code: '#06b6d4',\n          display_order: 3,\n          is_active: true\n        }]\n      }, {\n        category_id: 2,\n        name: 'Events',\n        description: 'School events and activities',\n        color_code: '#10b981',\n        is_active: true,\n        subcategories: [{\n          subcategory_id: 4,\n          name: 'Sports',\n          description: 'Sports events and competitions',\n          color_code: '#8b5cf6',\n          display_order: 1,\n          is_active: true\n        }, {\n          subcategory_id: 5,\n          name: 'Cultural',\n          description: 'Cultural events and celebrations',\n          color_code: '#ec4899',\n          display_order: 2,\n          is_active: true\n        }]\n      }, {\n        category_id: 3,\n        name: 'Administrative',\n        description: 'Administrative notices and updates',\n        color_code: '#f97316',\n        is_active: true,\n        subcategories: [{\n          subcategory_id: 6,\n          name: 'Policies',\n          description: 'School policies and regulations',\n          color_code: '#6366f1',\n          display_order: 1,\n          is_active: true\n        }]\n      }, {\n        category_id: 4,\n        name: 'Emergency',\n        description: 'Emergency announcements and alerts',\n        color_code: '#dc2626',\n        is_active: true,\n        subcategories: []\n      }];\n      setCategories(mockCategories);\n    } catch (err) {\n      setError('Failed to load categories');\n      console.error('Error loading categories:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (!permissions.canManageCategories) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      },\n      children: [/*#__PURE__*/_jsxDEV(FolderTree, {\n        size: 64,\n        style: {\n          marginBottom: '1rem',\n          opacity: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          margin: '0 0 0.5rem',\n          fontSize: '1.5rem',\n          fontWeight: '600'\n        },\n        children: \"Access Denied\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: 0,\n          fontSize: '1rem'\n        },\n        children: \"You do not have permission to manage categories and subcategories.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        },\n        children: [\"Current Role: \", permissions.getPositionDisplayName()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '40px',\n          height: '40px',\n          border: '4px solid #f3f4f6',\n          borderTop: '4px solid #3b82f6',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '2rem',\n        textAlign: 'center',\n        color: '#ef4444'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Error\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: loadCategories,\n        style: {\n          padding: '0.5rem 1rem',\n          background: '#3b82f6',\n          color: 'white',\n          border: 'none',\n          borderRadius: '6px',\n          cursor: 'pointer'\n        },\n        children: \"Retry\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      maxWidth: '1200px',\n      margin: '0 auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            margin: '0 0 0.5rem',\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          },\n          children: \"Category Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          },\n          children: \"Manage categories and subcategories for announcements and events\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          padding: '0.75rem 1.5rem',\n          background: '#3b82f6',\n          color: 'white',\n          border: 'none',\n          borderRadius: '8px',\n          fontSize: '0.875rem',\n          fontWeight: '600',\n          cursor: 'pointer',\n          transition: 'background-color 0.2s'\n        },\n        onMouseEnter: e => e.currentTarget.style.background = '#2563eb',\n        onMouseLeave: e => e.currentTarget.style.background = '#3b82f6',\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), \"Add Category\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '12px',\n        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n        overflow: 'hidden'\n      },\n      children: categories.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '3rem',\n          textAlign: 'center',\n          color: '#6b7280'\n        },\n        children: [/*#__PURE__*/_jsxDEV(FolderTree, {\n          size: 48,\n          style: {\n            marginBottom: '1rem',\n            opacity: 0.5\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 0.5rem',\n            fontSize: '1.125rem',\n            fontWeight: '600'\n          },\n          children: \"No categories found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            fontSize: '0.875rem'\n          },\n          children: \"Create your first category to get started\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 11\n      }, this) : categories.map((category, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '1.5rem',\n          borderBottom: index < categories.length - 1 ? '1px solid #f3f4f6' : 'none'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '16px',\n                height: '16px',\n                background: category.color_code,\n                borderRadius: '4px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: '0 0 0.25rem',\n                  fontSize: '1.125rem',\n                  fontWeight: '600',\n                  color: '#1f2937'\n                },\n                children: category.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 21\n              }, this), category.description && /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                },\n                children: category.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                padding: '0.25rem 0.5rem',\n                background: category.is_active ? '#dcfce7' : '#fef2f2',\n                color: category.is_active ? '#166534' : '#dc2626',\n                borderRadius: '4px',\n                fontSize: '0.75rem',\n                fontWeight: '600'\n              },\n              children: category.is_active ? 'Active' : 'Inactive'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                padding: '0.5rem',\n                background: 'transparent',\n                border: '1px solid #d1d5db',\n                borderRadius: '6px',\n                cursor: 'pointer',\n                color: '#6b7280'\n              },\n              children: /*#__PURE__*/_jsxDEV(Edit, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                padding: '0.5rem',\n                background: 'transparent',\n                border: '1px solid #d1d5db',\n                borderRadius: '6px',\n                cursor: 'pointer',\n                color: '#6b7280'\n              },\n              children: category.is_active ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 43\n              }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 66\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                padding: '0.5rem',\n                background: 'transparent',\n                border: '1px solid #fca5a5',\n                borderRadius: '6px',\n                cursor: 'pointer',\n                color: '#dc2626'\n              },\n              children: /*#__PURE__*/_jsxDEV(Trash2, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 15\n        }, this), category.subcategories && category.subcategories.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginLeft: '2rem',\n            paddingLeft: '1rem',\n            borderLeft: '2px solid #f3f4f6'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              margin: '0 0 0.75rem',\n              fontSize: '0.875rem',\n              fontWeight: '600',\n              color: '#6b7280',\n              textTransform: 'uppercase',\n              letterSpacing: '0.5px'\n            },\n            children: [\"Subcategories (\", category.subcategories.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 19\n          }, this), category.subcategories.map(subcategory => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              padding: '0.75rem',\n              background: '#f9fafb',\n              borderRadius: '6px',\n              marginBottom: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '12px',\n                  height: '12px',\n                  background: subcategory.color_code,\n                  borderRadius: '3px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '0.875rem',\n                    fontWeight: '500',\n                    color: '#1f2937'\n                  },\n                  children: subcategory.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 451,\n                  columnNumber: 27\n                }, this), subcategory.description && /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: '0.25rem 0 0',\n                    fontSize: '0.75rem',\n                    color: '#6b7280'\n                  },\n                  children: subcategory.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 459,\n                  columnNumber: 29\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  padding: '0.125rem 0.375rem',\n                  background: subcategory.is_active ? '#dcfce7' : '#fef2f2',\n                  color: subcategory.is_active ? '#166534' : '#dc2626',\n                  borderRadius: '3px',\n                  fontSize: '0.625rem',\n                  fontWeight: '600'\n                },\n                children: subcategory.is_active ? 'Active' : 'Inactive'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 25\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 23\n            }, this)]\n          }, subcategory.subcategory_id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 21\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 17\n        }, this)]\n      }, category.category_id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 241,\n    columnNumber: 5\n  }, this);\n};\n_s(CategoryManagement, \"Fi2eW2xY3Tqt2uCjsGG8TTkc8Lc=\", false, function () {\n  return [useAdminAuth, usePermissions];\n});\n_c = CategoryManagement;\nexport default CategoryManagement;\nvar _c;\n$RefreshReg$(_c, \"CategoryManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FolderTree", "Plus", "useAdminAuth", "usePermissions", "jsxDEV", "_jsxDEV", "CategoryManagement", "_s", "user", "permissions", "categories", "setCategories", "loading", "setLoading", "error", "setError", "success", "setSuccess", "showModal", "setShowModal", "modalMode", "setModalMode", "editingCategory", "setEditingCategory", "editingSubcategory", "setEditingSubcategory", "parentCategory", "setParentCategory", "modalLoading", "setModalLoading", "canManageCategories", "loadCategories", "mockCategories", "category_id", "name", "description", "color_code", "is_active", "subcategories", "subcategory_id", "display_order", "err", "console", "style", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "textAlign", "color", "children", "size", "marginBottom", "opacity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "margin", "fontSize", "fontWeight", "marginTop", "padding", "background", "getPositionBadgeColor", "borderRadius", "getPositionDisplayName", "width", "height", "border", "borderTop", "animation", "onClick", "cursor", "max<PERSON><PERSON><PERSON>", "gap", "transition", "onMouseEnter", "e", "currentTarget", "onMouseLeave", "boxShadow", "overflow", "length", "map", "category", "index", "borderBottom", "Edit", "Eye<PERSON>ff", "Eye", "Trash2", "marginLeft", "paddingLeft", "borderLeft", "textTransform", "letterSpacing", "subcategory", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/admin/CategoryManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { FolderTree, Plus, AlertTriangle, CheckCircle } from 'lucide-react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\nimport { categoryService } from '../../services/categoryService';\nimport CategoryList from '../../components/admin/CategoryList';\nimport CategoryModal from '../../components/admin/CategoryModal';\n\ninterface Category {\n  category_id: number;\n  name: string;\n  description?: string;\n  color_code: string;\n  is_active: boolean;\n  subcategories?: Subcategory[];\n}\n\ninterface Subcategory {\n  subcategory_id: number;\n  name: string;\n  description?: string;\n  color_code: string;\n  display_order: number;\n  is_active: boolean;\n}\n\nconst CategoryManagement: React.FC = () => {\n  const { user } = useAdminAuth();\n  const permissions = usePermissions(user);\n\n  // State management\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  // Modal state\n  const [showModal, setShowModal] = useState(false);\n  const [modalMode, setModalMode] = useState<'add_category' | 'edit_category' | 'add_subcategory' | 'edit_subcategory'>('add_category');\n  const [editingCategory, setEditingCategory] = useState<Category | null>(null);\n  const [editingSubcategory, setEditingSubcategory] = useState<Subcategory | null>(null);\n  const [parentCategory, setParentCategory] = useState<Category | null>(null);\n  const [modalLoading, setModalLoading] = useState(false);\n\n  useEffect(() => {\n    // Check if user has permission to manage categories\n    if (!permissions.canManageCategories) {\n      setError('You do not have permission to manage categories');\n      setLoading(false);\n      return;\n    }\n\n    loadCategories();\n  }, [permissions.canManageCategories]);\n\n  const loadCategories = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      // TODO: Implement API call to fetch categories\n      // const response = await categoryService.getCategoriesWithSubcategories();\n      // setCategories(response.data.categories);\n      \n      // Mock data with proper structure\n      const mockCategories: Category[] = [\n        {\n          category_id: 1,\n          name: 'Academic',\n          description: 'Academic-related announcements and events',\n          color_code: '#3b82f6',\n          is_active: true,\n          subcategories: [\n            {\n              subcategory_id: 1,\n              name: 'Exams',\n              description: 'Examination schedules and updates',\n              color_code: '#ef4444',\n              display_order: 1,\n              is_active: true\n            },\n            {\n              subcategory_id: 2,\n              name: 'Assignments',\n              description: 'Assignment deadlines and submissions',\n              color_code: '#f59e0b',\n              display_order: 2,\n              is_active: true\n            },\n            {\n              subcategory_id: 3,\n              name: 'Class Schedules',\n              description: 'Class timing and schedule changes',\n              color_code: '#06b6d4',\n              display_order: 3,\n              is_active: true\n            }\n          ]\n        },\n        {\n          category_id: 2,\n          name: 'Events',\n          description: 'School events and activities',\n          color_code: '#10b981',\n          is_active: true,\n          subcategories: [\n            {\n              subcategory_id: 4,\n              name: 'Sports',\n              description: 'Sports events and competitions',\n              color_code: '#8b5cf6',\n              display_order: 1,\n              is_active: true\n            },\n            {\n              subcategory_id: 5,\n              name: 'Cultural',\n              description: 'Cultural events and celebrations',\n              color_code: '#ec4899',\n              display_order: 2,\n              is_active: true\n            }\n          ]\n        },\n        {\n          category_id: 3,\n          name: 'Administrative',\n          description: 'Administrative notices and updates',\n          color_code: '#f97316',\n          is_active: true,\n          subcategories: [\n            {\n              subcategory_id: 6,\n              name: 'Policies',\n              description: 'School policies and regulations',\n              color_code: '#6366f1',\n              display_order: 1,\n              is_active: true\n            }\n          ]\n        },\n        {\n          category_id: 4,\n          name: 'Emergency',\n          description: 'Emergency announcements and alerts',\n          color_code: '#dc2626',\n          is_active: true,\n          subcategories: []\n        }\n      ];\n\n      setCategories(mockCategories);\n    } catch (err) {\n      setError('Failed to load categories');\n      console.error('Error loading categories:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (!permissions.canManageCategories) {\n    return (\n      <div style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      }}>\n        <FolderTree size={64} style={{ marginBottom: '1rem', opacity: 0.5 }} />\n        <h2 style={{ margin: '0 0 0.5rem', fontSize: '1.5rem', fontWeight: '600' }}>\n          Access Denied\n        </h2>\n        <p style={{ margin: 0, fontSize: '1rem' }}>\n          You do not have permission to manage categories and subcategories.\n        </p>\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        }}>\n          Current Role: {permissions.getPositionDisplayName()}\n        </div>\n      </div>\n    );\n  }\n\n  if (loading) {\n    return (\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px'\n      }}>\n        <div style={{\n          width: '40px',\n          height: '40px',\n          border: '4px solid #f3f4f6',\n          borderTop: '4px solid #3b82f6',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }} />\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div style={{\n        padding: '2rem',\n        textAlign: 'center',\n        color: '#ef4444'\n      }}>\n        <h2>Error</h2>\n        <p>{error}</p>\n        <button\n          onClick={loadCategories}\n          style={{\n            padding: '0.5rem 1rem',\n            background: '#3b82f6',\n            color: 'white',\n            border: 'none',\n            borderRadius: '6px',\n            cursor: 'pointer'\n          }}\n        >\n          Retry\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ maxWidth: '1200px', margin: '0 auto' }}>\n      {/* Header */}\n      <div style={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      }}>\n        <div>\n          <h1 style={{\n            margin: '0 0 0.5rem',\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          }}>\n            Category Management\n          </h1>\n          <p style={{\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          }}>\n            Manage categories and subcategories for announcements and events\n          </p>\n        </div>\n        \n        <button\n          style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            padding: '0.75rem 1.5rem',\n            background: '#3b82f6',\n            color: 'white',\n            border: 'none',\n            borderRadius: '8px',\n            fontSize: '0.875rem',\n            fontWeight: '600',\n            cursor: 'pointer',\n            transition: 'background-color 0.2s'\n          }}\n          onMouseEnter={(e) => e.currentTarget.style.background = '#2563eb'}\n          onMouseLeave={(e) => e.currentTarget.style.background = '#3b82f6'}\n        >\n          <Plus size={16} />\n          Add Category\n        </button>\n      </div>\n\n      {/* Categories List */}\n      <div style={{\n        background: 'white',\n        borderRadius: '12px',\n        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n        overflow: 'hidden'\n      }}>\n        {categories.length === 0 ? (\n          <div style={{\n            padding: '3rem',\n            textAlign: 'center',\n            color: '#6b7280'\n          }}>\n            <FolderTree size={48} style={{ marginBottom: '1rem', opacity: 0.5 }} />\n            <h3 style={{ margin: '0 0 0.5rem', fontSize: '1.125rem', fontWeight: '600' }}>\n              No categories found\n            </h3>\n            <p style={{ margin: 0, fontSize: '0.875rem' }}>\n              Create your first category to get started\n            </p>\n          </div>\n        ) : (\n          categories.map((category, index) => (\n            <div\n              key={category.category_id}\n              style={{\n                padding: '1.5rem',\n                borderBottom: index < categories.length - 1 ? '1px solid #f3f4f6' : 'none'\n              }}\n            >\n              {/* Category Header */}\n              <div style={{\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                marginBottom: '1rem'\n              }}>\n                <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>\n                  <div\n                    style={{\n                      width: '16px',\n                      height: '16px',\n                      background: category.color_code,\n                      borderRadius: '4px'\n                    }}\n                  />\n                  <div>\n                    <h3 style={{\n                      margin: '0 0 0.25rem',\n                      fontSize: '1.125rem',\n                      fontWeight: '600',\n                      color: '#1f2937'\n                    }}>\n                      {category.name}\n                    </h3>\n                    {category.description && (\n                      <p style={{\n                        margin: 0,\n                        fontSize: '0.875rem',\n                        color: '#6b7280'\n                      }}>\n                        {category.description}\n                      </p>\n                    )}\n                  </div>\n                </div>\n                \n                <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                  <span style={{\n                    padding: '0.25rem 0.5rem',\n                    background: category.is_active ? '#dcfce7' : '#fef2f2',\n                    color: category.is_active ? '#166534' : '#dc2626',\n                    borderRadius: '4px',\n                    fontSize: '0.75rem',\n                    fontWeight: '600'\n                  }}>\n                    {category.is_active ? 'Active' : 'Inactive'}\n                  </span>\n                  \n                  <button\n                    style={{\n                      padding: '0.5rem',\n                      background: 'transparent',\n                      border: '1px solid #d1d5db',\n                      borderRadius: '6px',\n                      cursor: 'pointer',\n                      color: '#6b7280'\n                    }}\n                  >\n                    <Edit size={16} />\n                  </button>\n                  \n                  <button\n                    style={{\n                      padding: '0.5rem',\n                      background: 'transparent',\n                      border: '1px solid #d1d5db',\n                      borderRadius: '6px',\n                      cursor: 'pointer',\n                      color: '#6b7280'\n                    }}\n                  >\n                    {category.is_active ? <EyeOff size={16} /> : <Eye size={16} />}\n                  </button>\n                  \n                  <button\n                    style={{\n                      padding: '0.5rem',\n                      background: 'transparent',\n                      border: '1px solid #fca5a5',\n                      borderRadius: '6px',\n                      cursor: 'pointer',\n                      color: '#dc2626'\n                    }}\n                  >\n                    <Trash2 size={16} />\n                  </button>\n                </div>\n              </div>\n\n              {/* Subcategories */}\n              {category.subcategories && category.subcategories.length > 0 && (\n                <div style={{\n                  marginLeft: '2rem',\n                  paddingLeft: '1rem',\n                  borderLeft: '2px solid #f3f4f6'\n                }}>\n                  <h4 style={{\n                    margin: '0 0 0.75rem',\n                    fontSize: '0.875rem',\n                    fontWeight: '600',\n                    color: '#6b7280',\n                    textTransform: 'uppercase',\n                    letterSpacing: '0.5px'\n                  }}>\n                    Subcategories ({category.subcategories.length})\n                  </h4>\n                  \n                  {category.subcategories.map((subcategory) => (\n                    <div\n                      key={subcategory.subcategory_id}\n                      style={{\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        padding: '0.75rem',\n                        background: '#f9fafb',\n                        borderRadius: '6px',\n                        marginBottom: '0.5rem'\n                      }}\n                    >\n                      <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>\n                        <div\n                          style={{\n                            width: '12px',\n                            height: '12px',\n                            background: subcategory.color_code,\n                            borderRadius: '3px'\n                          }}\n                        />\n                        <div>\n                          <span style={{\n                            fontSize: '0.875rem',\n                            fontWeight: '500',\n                            color: '#1f2937'\n                          }}>\n                            {subcategory.name}\n                          </span>\n                          {subcategory.description && (\n                            <p style={{\n                              margin: '0.25rem 0 0',\n                              fontSize: '0.75rem',\n                              color: '#6b7280'\n                            }}>\n                              {subcategory.description}\n                            </p>\n                          )}\n                        </div>\n                      </div>\n                      \n                      <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\n                        <span style={{\n                          padding: '0.125rem 0.375rem',\n                          background: subcategory.is_active ? '#dcfce7' : '#fef2f2',\n                          color: subcategory.is_active ? '#166534' : '#dc2626',\n                          borderRadius: '3px',\n                          fontSize: '0.625rem',\n                          fontWeight: '600'\n                        }}>\n                          {subcategory.is_active ? 'Active' : 'Inactive'}\n                        </span>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n          ))\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default CategoryManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,UAAU,EAAEC,IAAI,QAAoC,cAAc;AAC3E,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAuBzD,MAAMC,kBAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM;IAAEC;EAAK,CAAC,GAAGN,YAAY,CAAC,CAAC;EAC/B,MAAMO,WAAW,GAAGN,cAAc,CAACK,IAAI,CAAC;;EAExC;EACA,MAAM,CAACE,UAAU,EAAEC,aAAa,CAAC,GAAGb,QAAQ,CAAa,EAAE,CAAC;EAC5D,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAgB,IAAI,CAAC;;EAE3D;EACA,MAAM,CAACoB,SAAS,EAAEC,YAAY,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACsB,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAA4E,cAAc,CAAC;EACrI,MAAM,CAACwB,eAAe,EAAEC,kBAAkB,CAAC,GAAGzB,QAAQ,CAAkB,IAAI,CAAC;EAC7E,MAAM,CAAC0B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3B,QAAQ,CAAqB,IAAI,CAAC;EACtF,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAkB,IAAI,CAAC;EAC3E,MAAM,CAAC8B,YAAY,EAAEC,eAAe,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACU,WAAW,CAACqB,mBAAmB,EAAE;MACpCf,QAAQ,CAAC,iDAAiD,CAAC;MAC3DF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEAkB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACtB,WAAW,CAACqB,mBAAmB,CAAC,CAAC;EAErC,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFlB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA;MACA;;MAEA;MACA,MAAMiB,cAA0B,GAAG,CACjC;QACEC,WAAW,EAAE,CAAC;QACdC,IAAI,EAAE,UAAU;QAChBC,WAAW,EAAE,2CAA2C;QACxDC,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,IAAI;QACfC,aAAa,EAAE,CACb;UACEC,cAAc,EAAE,CAAC;UACjBL,IAAI,EAAE,OAAO;UACbC,WAAW,EAAE,mCAAmC;UAChDC,UAAU,EAAE,SAAS;UACrBI,aAAa,EAAE,CAAC;UAChBH,SAAS,EAAE;QACb,CAAC,EACD;UACEE,cAAc,EAAE,CAAC;UACjBL,IAAI,EAAE,aAAa;UACnBC,WAAW,EAAE,sCAAsC;UACnDC,UAAU,EAAE,SAAS;UACrBI,aAAa,EAAE,CAAC;UAChBH,SAAS,EAAE;QACb,CAAC,EACD;UACEE,cAAc,EAAE,CAAC;UACjBL,IAAI,EAAE,iBAAiB;UACvBC,WAAW,EAAE,mCAAmC;UAChDC,UAAU,EAAE,SAAS;UACrBI,aAAa,EAAE,CAAC;UAChBH,SAAS,EAAE;QACb,CAAC;MAEL,CAAC,EACD;QACEJ,WAAW,EAAE,CAAC;QACdC,IAAI,EAAE,QAAQ;QACdC,WAAW,EAAE,8BAA8B;QAC3CC,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,IAAI;QACfC,aAAa,EAAE,CACb;UACEC,cAAc,EAAE,CAAC;UACjBL,IAAI,EAAE,QAAQ;UACdC,WAAW,EAAE,gCAAgC;UAC7CC,UAAU,EAAE,SAAS;UACrBI,aAAa,EAAE,CAAC;UAChBH,SAAS,EAAE;QACb,CAAC,EACD;UACEE,cAAc,EAAE,CAAC;UACjBL,IAAI,EAAE,UAAU;UAChBC,WAAW,EAAE,kCAAkC;UAC/CC,UAAU,EAAE,SAAS;UACrBI,aAAa,EAAE,CAAC;UAChBH,SAAS,EAAE;QACb,CAAC;MAEL,CAAC,EACD;QACEJ,WAAW,EAAE,CAAC;QACdC,IAAI,EAAE,gBAAgB;QACtBC,WAAW,EAAE,oCAAoC;QACjDC,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,IAAI;QACfC,aAAa,EAAE,CACb;UACEC,cAAc,EAAE,CAAC;UACjBL,IAAI,EAAE,UAAU;UAChBC,WAAW,EAAE,iCAAiC;UAC9CC,UAAU,EAAE,SAAS;UACrBI,aAAa,EAAE,CAAC;UAChBH,SAAS,EAAE;QACb,CAAC;MAEL,CAAC,EACD;QACEJ,WAAW,EAAE,CAAC;QACdC,IAAI,EAAE,WAAW;QACjBC,WAAW,EAAE,oCAAoC;QACjDC,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,IAAI;QACfC,aAAa,EAAE;MACjB,CAAC,CACF;MAED3B,aAAa,CAACqB,cAAc,CAAC;IAC/B,CAAC,CAAC,OAAOS,GAAG,EAAE;MACZ1B,QAAQ,CAAC,2BAA2B,CAAC;MACrC2B,OAAO,CAAC5B,KAAK,CAAC,2BAA2B,EAAE2B,GAAG,CAAC;IACjD,CAAC,SAAS;MACR5B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAI,CAACJ,WAAW,CAACqB,mBAAmB,EAAE;IACpC,oBACEzB,OAAA;MAAKsC,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE,OAAO;QAClBC,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACA9C,OAAA,CAACL,UAAU;QAACoD,IAAI,EAAE,EAAG;QAACT,KAAK,EAAE;UAAEU,YAAY,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAI;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvErD,OAAA;QAAIsC,KAAK,EAAE;UAAEgB,MAAM,EAAE,YAAY;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAM,CAAE;QAAAV,QAAA,EAAC;MAE5E;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLrD,OAAA;QAAGsC,KAAK,EAAE;UAAEgB,MAAM,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAT,QAAA,EAAC;MAE3C;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJrD,OAAA;QAAKsC,KAAK,EAAE;UACVmB,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAEvD,WAAW,CAACwD,qBAAqB,CAAC,CAAC;UAC/CC,YAAY,EAAE,KAAK;UACnBhB,KAAK,EAAE,OAAO;UACdU,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE;QACd,CAAE;QAAAV,QAAA,GAAC,gBACa,EAAC1C,WAAW,CAAC0D,sBAAsB,CAAC,CAAC;MAAA;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI9C,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKsC,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE;MACb,CAAE;MAAAG,QAAA,eACA9C,OAAA;QAAKsC,KAAK,EAAE;UACVyB,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,mBAAmB;UAC3BC,SAAS,EAAE,mBAAmB;UAC9BL,YAAY,EAAE,KAAK;UACnBM,SAAS,EAAE;QACb;MAAE;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEV;EAEA,IAAI5C,KAAK,EAAE;IACT,oBACET,OAAA;MAAKsC,KAAK,EAAE;QACVoB,OAAO,EAAE,MAAM;QACfd,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACA9C,OAAA;QAAA8C,QAAA,EAAI;MAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdrD,OAAA;QAAA8C,QAAA,EAAIrC;MAAK;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdrD,OAAA;QACEoE,OAAO,EAAE1C,cAAe;QACxBY,KAAK,EAAE;UACLoB,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAE,SAAS;UACrBd,KAAK,EAAE,OAAO;UACdoB,MAAM,EAAE,MAAM;UACdJ,YAAY,EAAE,KAAK;UACnBQ,MAAM,EAAE;QACV,CAAE;QAAAvB,QAAA,EACH;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACErD,OAAA;IAAKsC,KAAK,EAAE;MAAEgC,QAAQ,EAAE,QAAQ;MAAEhB,MAAM,EAAE;IAAS,CAAE;IAAAR,QAAA,gBAEnD9C,OAAA;MAAKsC,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfG,cAAc,EAAE,eAAe;QAC/BD,UAAU,EAAE,QAAQ;QACpBO,YAAY,EAAE;MAChB,CAAE;MAAAF,QAAA,gBACA9C,OAAA;QAAA8C,QAAA,gBACE9C,OAAA;UAAIsC,KAAK,EAAE;YACTgB,MAAM,EAAE,YAAY;YACpBC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjBX,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,EAAC;QAEH;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLrD,OAAA;UAAGsC,KAAK,EAAE;YACRgB,MAAM,EAAE,CAAC;YACTT,KAAK,EAAE,SAAS;YAChBU,QAAQ,EAAE;UACZ,CAAE;UAAAT,QAAA,EAAC;QAEH;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENrD,OAAA;QACEsC,KAAK,EAAE;UACLC,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpB8B,GAAG,EAAE,QAAQ;UACbb,OAAO,EAAE,gBAAgB;UACzBC,UAAU,EAAE,SAAS;UACrBd,KAAK,EAAE,OAAO;UACdoB,MAAM,EAAE,MAAM;UACdJ,YAAY,EAAE,KAAK;UACnBN,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE,KAAK;UACjBa,MAAM,EAAE,SAAS;UACjBG,UAAU,EAAE;QACd,CAAE;QACFC,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACrC,KAAK,CAACqB,UAAU,GAAG,SAAU;QAClEiB,YAAY,EAAGF,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACrC,KAAK,CAACqB,UAAU,GAAG,SAAU;QAAAb,QAAA,gBAElE9C,OAAA,CAACJ,IAAI;UAACmD,IAAI,EAAE;QAAG;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEpB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNrD,OAAA;MAAKsC,KAAK,EAAE;QACVqB,UAAU,EAAE,OAAO;QACnBE,YAAY,EAAE,MAAM;QACpBgB,SAAS,EAAE,8BAA8B;QACzCC,QAAQ,EAAE;MACZ,CAAE;MAAAhC,QAAA,EACCzC,UAAU,CAAC0E,MAAM,KAAK,CAAC,gBACtB/E,OAAA;QAAKsC,KAAK,EAAE;UACVoB,OAAO,EAAE,MAAM;UACfd,SAAS,EAAE,QAAQ;UACnBC,KAAK,EAAE;QACT,CAAE;QAAAC,QAAA,gBACA9C,OAAA,CAACL,UAAU;UAACoD,IAAI,EAAE,EAAG;UAACT,KAAK,EAAE;YAAEU,YAAY,EAAE,MAAM;YAAEC,OAAO,EAAE;UAAI;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvErD,OAAA;UAAIsC,KAAK,EAAE;YAAEgB,MAAM,EAAE,YAAY;YAAEC,QAAQ,EAAE,UAAU;YAAEC,UAAU,EAAE;UAAM,CAAE;UAAAV,QAAA,EAAC;QAE9E;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLrD,OAAA;UAAGsC,KAAK,EAAE;YAAEgB,MAAM,EAAE,CAAC;YAAEC,QAAQ,EAAE;UAAW,CAAE;UAAAT,QAAA,EAAC;QAE/C;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,GAENhD,UAAU,CAAC2E,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,kBAC7BlF,OAAA;QAEEsC,KAAK,EAAE;UACLoB,OAAO,EAAE,QAAQ;UACjByB,YAAY,EAAED,KAAK,GAAG7E,UAAU,CAAC0E,MAAM,GAAG,CAAC,GAAG,mBAAmB,GAAG;QACtE,CAAE;QAAAjC,QAAA,gBAGF9C,OAAA;UAAKsC,KAAK,EAAE;YACVC,OAAO,EAAE,MAAM;YACfG,cAAc,EAAE,eAAe;YAC/BD,UAAU,EAAE,QAAQ;YACpBO,YAAY,EAAE;UAChB,CAAE;UAAAF,QAAA,gBACA9C,OAAA;YAAKsC,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAE8B,GAAG,EAAE;YAAO,CAAE;YAAAzB,QAAA,gBACjE9C,OAAA;cACEsC,KAAK,EAAE;gBACLyB,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdL,UAAU,EAAEsB,QAAQ,CAAClD,UAAU;gBAC/B8B,YAAY,EAAE;cAChB;YAAE;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFrD,OAAA;cAAA8C,QAAA,gBACE9C,OAAA;gBAAIsC,KAAK,EAAE;kBACTgB,MAAM,EAAE,aAAa;kBACrBC,QAAQ,EAAE,UAAU;kBACpBC,UAAU,EAAE,KAAK;kBACjBX,KAAK,EAAE;gBACT,CAAE;gBAAAC,QAAA,EACCmC,QAAQ,CAACpD;cAAI;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,EACJ4B,QAAQ,CAACnD,WAAW,iBACnB9B,OAAA;gBAAGsC,KAAK,EAAE;kBACRgB,MAAM,EAAE,CAAC;kBACTC,QAAQ,EAAE,UAAU;kBACpBV,KAAK,EAAE;gBACT,CAAE;gBAAAC,QAAA,EACCmC,QAAQ,CAACnD;cAAW;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrD,OAAA;YAAKsC,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAE8B,GAAG,EAAE;YAAS,CAAE;YAAAzB,QAAA,gBACnE9C,OAAA;cAAMsC,KAAK,EAAE;gBACXoB,OAAO,EAAE,gBAAgB;gBACzBC,UAAU,EAAEsB,QAAQ,CAACjD,SAAS,GAAG,SAAS,GAAG,SAAS;gBACtDa,KAAK,EAAEoC,QAAQ,CAACjD,SAAS,GAAG,SAAS,GAAG,SAAS;gBACjD6B,YAAY,EAAE,KAAK;gBACnBN,QAAQ,EAAE,SAAS;gBACnBC,UAAU,EAAE;cACd,CAAE;cAAAV,QAAA,EACCmC,QAAQ,CAACjD,SAAS,GAAG,QAAQ,GAAG;YAAU;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eAEPrD,OAAA;cACEsC,KAAK,EAAE;gBACLoB,OAAO,EAAE,QAAQ;gBACjBC,UAAU,EAAE,aAAa;gBACzBM,MAAM,EAAE,mBAAmB;gBAC3BJ,YAAY,EAAE,KAAK;gBACnBQ,MAAM,EAAE,SAAS;gBACjBxB,KAAK,EAAE;cACT,CAAE;cAAAC,QAAA,eAEF9C,OAAA,CAACoF,IAAI;gBAACrC,IAAI,EAAE;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eAETrD,OAAA;cACEsC,KAAK,EAAE;gBACLoB,OAAO,EAAE,QAAQ;gBACjBC,UAAU,EAAE,aAAa;gBACzBM,MAAM,EAAE,mBAAmB;gBAC3BJ,YAAY,EAAE,KAAK;gBACnBQ,MAAM,EAAE,SAAS;gBACjBxB,KAAK,EAAE;cACT,CAAE;cAAAC,QAAA,EAEDmC,QAAQ,CAACjD,SAAS,gBAAGhC,OAAA,CAACqF,MAAM;gBAACtC,IAAI,EAAE;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGrD,OAAA,CAACsF,GAAG;gBAACvC,IAAI,EAAE;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eAETrD,OAAA;cACEsC,KAAK,EAAE;gBACLoB,OAAO,EAAE,QAAQ;gBACjBC,UAAU,EAAE,aAAa;gBACzBM,MAAM,EAAE,mBAAmB;gBAC3BJ,YAAY,EAAE,KAAK;gBACnBQ,MAAM,EAAE,SAAS;gBACjBxB,KAAK,EAAE;cACT,CAAE;cAAAC,QAAA,eAEF9C,OAAA,CAACuF,MAAM;gBAACxC,IAAI,EAAE;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL4B,QAAQ,CAAChD,aAAa,IAAIgD,QAAQ,CAAChD,aAAa,CAAC8C,MAAM,GAAG,CAAC,iBAC1D/E,OAAA;UAAKsC,KAAK,EAAE;YACVkD,UAAU,EAAE,MAAM;YAClBC,WAAW,EAAE,MAAM;YACnBC,UAAU,EAAE;UACd,CAAE;UAAA5C,QAAA,gBACA9C,OAAA;YAAIsC,KAAK,EAAE;cACTgB,MAAM,EAAE,aAAa;cACrBC,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBX,KAAK,EAAE,SAAS;cAChB8C,aAAa,EAAE,WAAW;cAC1BC,aAAa,EAAE;YACjB,CAAE;YAAA9C,QAAA,GAAC,iBACc,EAACmC,QAAQ,CAAChD,aAAa,CAAC8C,MAAM,EAAC,GAChD;UAAA;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAEJ4B,QAAQ,CAAChD,aAAa,CAAC+C,GAAG,CAAEa,WAAW,iBACtC7F,OAAA;YAEEsC,KAAK,EAAE;cACLC,OAAO,EAAE,MAAM;cACfG,cAAc,EAAE,eAAe;cAC/BD,UAAU,EAAE,QAAQ;cACpBiB,OAAO,EAAE,SAAS;cAClBC,UAAU,EAAE,SAAS;cACrBE,YAAY,EAAE,KAAK;cACnBb,YAAY,EAAE;YAChB,CAAE;YAAAF,QAAA,gBAEF9C,OAAA;cAAKsC,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAE8B,GAAG,EAAE;cAAU,CAAE;cAAAzB,QAAA,gBACpE9C,OAAA;gBACEsC,KAAK,EAAE;kBACLyB,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdL,UAAU,EAAEkC,WAAW,CAAC9D,UAAU;kBAClC8B,YAAY,EAAE;gBAChB;cAAE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFrD,OAAA;gBAAA8C,QAAA,gBACE9C,OAAA;kBAAMsC,KAAK,EAAE;oBACXiB,QAAQ,EAAE,UAAU;oBACpBC,UAAU,EAAE,KAAK;oBACjBX,KAAK,EAAE;kBACT,CAAE;kBAAAC,QAAA,EACC+C,WAAW,CAAChE;gBAAI;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,EACNwC,WAAW,CAAC/D,WAAW,iBACtB9B,OAAA;kBAAGsC,KAAK,EAAE;oBACRgB,MAAM,EAAE,aAAa;oBACrBC,QAAQ,EAAE,SAAS;oBACnBV,KAAK,EAAE;kBACT,CAAE;kBAAAC,QAAA,EACC+C,WAAW,CAAC/D;gBAAW;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CACJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrD,OAAA;cAAKsC,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAE8B,GAAG,EAAE;cAAU,CAAE;cAAAzB,QAAA,eACpE9C,OAAA;gBAAMsC,KAAK,EAAE;kBACXoB,OAAO,EAAE,mBAAmB;kBAC5BC,UAAU,EAAEkC,WAAW,CAAC7D,SAAS,GAAG,SAAS,GAAG,SAAS;kBACzDa,KAAK,EAAEgD,WAAW,CAAC7D,SAAS,GAAG,SAAS,GAAG,SAAS;kBACpD6B,YAAY,EAAE,KAAK;kBACnBN,QAAQ,EAAE,UAAU;kBACpBC,UAAU,EAAE;gBACd,CAAE;gBAAAV,QAAA,EACC+C,WAAW,CAAC7D,SAAS,GAAG,QAAQ,GAAG;cAAU;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,GAnDDwC,WAAW,CAAC3D,cAAc;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoD5B,CACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA,GA3KI4B,QAAQ,CAACrD,WAAW;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA4KtB,CACN;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnD,EAAA,CAjdID,kBAA4B;EAAA,QACfJ,YAAY,EACTC,cAAc;AAAA;AAAAgG,EAAA,GAF9B7F,kBAA4B;AAmdlC,eAAeA,kBAAkB;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}