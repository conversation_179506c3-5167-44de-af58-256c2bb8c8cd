{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\admin\\\\AdminManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { UserPlus, Search, Users, AlertTriangle, CheckCircle } from 'lucide-react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\nimport { adminManagementService } from '../../services/adminManagementService';\nimport AdminAccountList from '../../components/admin/AdminAccountList';\nimport AdminAccountModal from '../../components/admin/AdminAccountModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminManagement = () => {\n  _s();\n  const {\n    user\n  } = useAdminAuth();\n  const permissions = usePermissions(user);\n\n  // State management\n  const [admins, setAdmins] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n\n  // Modal state\n  const [showModal, setShowModal] = useState(false);\n  const [editingAdmin, setEditingAdmin] = useState(null);\n  const [modalLoading, setModalLoading] = useState(false);\n\n  // Search and filter state\n  const [searchTerm, setSearchTerm] = useState('');\n  const [positionFilter, setPositionFilter] = useState('');\n  const [statusFilter, setStatusFilter] = useState('');\n\n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(10);\n  const [totalItems, setTotalItems] = useState(0);\n  useEffect(() => {\n    // Check if user has permission to manage admins\n    console.log('🔍 AdminManagement - Permission check:', {\n      canManageAdmins: permissions.canManageAdmins,\n      user: user,\n      permissions: permissions\n    });\n    if (!permissions.canManageAdmins) {\n      console.log('❌ AdminManagement - No permission to manage admin accounts');\n      setError('You do not have permission to manage admin accounts');\n      setLoading(false);\n      return;\n    }\n    console.log('✅ AdminManagement - Permission granted, loading admins');\n    loadAdmins();\n  }, [permissions.canManageAdmins, currentPage, itemsPerPage, searchTerm, positionFilter, statusFilter]);\n\n  // Reset to page 1 when filters change\n  useEffect(() => {\n    setCurrentPage(1);\n  }, [searchTerm, positionFilter, statusFilter]);\n  const loadAdmins = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await adminManagementService.getAdmins({\n        page: currentPage,\n        limit: itemsPerPage,\n        search: searchTerm,\n        position: positionFilter,\n        status: statusFilter\n      });\n      if (response.success) {\n        setAdmins(response.data.admins);\n        setTotalItems(response.data.pagination.totalItems);\n        return;\n      }\n\n      // Fallback to mock data if API fails\n      const mockAdmins = [{\n        admin_id: 1,\n        email: '<EMAIL>',\n        is_active: true,\n        last_login: '2025-08-05T10:30:00Z',\n        created_at: '2025-01-01T00:00:00Z',\n        profile: {\n          first_name: 'Josh',\n          middle_name: 'Christian',\n          last_name: 'Mojica',\n          full_name: 'Josh Christian Mojica',\n          phone_number: '+************',\n          department: 'Administration',\n          position: 'super_admin',\n          grade_level: 12,\n          bio: 'System Administrator with expertise in educational technology.'\n        }\n      }, {\n        admin_id: 2,\n        email: '<EMAIL>',\n        is_active: true,\n        last_login: '2025-08-05T09:15:00Z',\n        created_at: '2025-01-15T00:00:00Z',\n        profile: {\n          first_name: 'Zaira',\n          last_name: 'Plarisan',\n          full_name: 'Zaira Plarisan',\n          phone_number: '+***********1',\n          department: 'Mathematics',\n          position: 'professor',\n          grade_level: 11,\n          bio: 'Mathematics professor specializing in Grade 11 curriculum.'\n        }\n      }];\n\n      // Apply filters\n      let filteredAdmins = [...mockAdmins];\n      if (searchTerm.trim()) {\n        const searchLower = searchTerm.toLowerCase().trim();\n        filteredAdmins = filteredAdmins.filter(admin => admin.profile.full_name.toLowerCase().includes(searchLower) || admin.email.toLowerCase().includes(searchLower) || admin.profile.first_name.toLowerCase().includes(searchLower) || admin.profile.last_name.toLowerCase().includes(searchLower) || admin.profile.department && admin.profile.department.toLowerCase().includes(searchLower));\n      }\n      if (positionFilter) {\n        filteredAdmins = filteredAdmins.filter(admin => admin.profile.position === positionFilter);\n      }\n      if (statusFilter) {\n        const isActive = statusFilter === 'active';\n        filteredAdmins = filteredAdmins.filter(admin => admin.is_active === isActive);\n      }\n      setTotalItems(filteredAdmins.length);\n\n      // Apply pagination\n      const startIndex = (currentPage - 1) * itemsPerPage;\n      const paginatedAdmins = filteredAdmins.slice(startIndex, startIndex + itemsPerPage);\n      setAdmins(paginatedAdmins);\n    } catch (err) {\n      var _err$response;\n      console.error('❌ AdminManagement - Error loading admins:', err);\n\n      // Check if it's an authentication error\n      const isAuthError = ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status) === 401 || err.message.includes('Authorization header missing') || err.message.includes('Access token expired');\n      if (isAuthError) {\n        console.log('🔐 AdminManagement - Authentication error, redirecting to login');\n        // Clear any stored tokens and redirect to login\n        localStorage.removeItem('vcba_admin_auth_token');\n        localStorage.removeItem('vcba_admin_refresh_token');\n        localStorage.removeItem('vcba_admin_user_data');\n        window.location.href = '/admin/login';\n        return;\n      }\n      setError(err.message || 'Failed to load admin accounts');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handler functions\n  const handleAddAdmin = () => {\n    setEditingAdmin(null);\n    setShowModal(true);\n  };\n  const handleEditAdmin = admin => {\n    setEditingAdmin(admin);\n    setShowModal(true);\n  };\n  const handleDeleteAdmin = async admin => {\n    try {\n      setError(null);\n      setSuccess(null);\n      const response = await adminManagementService.deleteAdmin(admin.admin_id);\n      if (response.success) {\n        setSuccess(`Admin account for ${admin.profile.full_name} has been deactivated`);\n        loadAdmins();\n      } else {\n        throw new Error(response.message || 'Failed to delete admin account');\n      }\n    } catch (err) {\n      setError(err.message || 'Failed to delete admin account');\n    }\n  };\n  const handleToggleStatus = async admin => {\n    try {\n      setError(null);\n      setSuccess(null);\n      const response = await adminManagementService.toggleAdminStatus(admin.admin_id, !admin.is_active);\n      if (response.success) {\n        const action = admin.is_active ? 'deactivated' : 'activated';\n        setSuccess(`Admin account for ${admin.profile.full_name} has been ${action}`);\n        loadAdmins();\n      } else {\n        throw new Error(response.message || 'Failed to update admin status');\n      }\n    } catch (err) {\n      setError(err.message || 'Failed to update admin status');\n    }\n  };\n  const handleSaveAdmin = async adminData => {\n    try {\n      setModalLoading(true);\n      setError(null);\n      setSuccess(null);\n      if (editingAdmin) {\n        // Update existing admin\n        const response = await adminManagementService.updateAdmin(editingAdmin.admin_id, adminData);\n        if (response.success) {\n          setSuccess(`Admin account for ${adminData.profile.first_name} ${adminData.profile.last_name} has been updated`);\n        } else {\n          throw new Error(response.message || 'Failed to update admin account');\n        }\n      } else {\n        // Create new admin - include password in the data\n        const createData = {\n          ...adminData,\n          password: adminData.password\n        };\n        const response = await adminManagementService.createAdmin(createData);\n        if (response.success) {\n          setSuccess(`Admin account for ${adminData.profile.first_name} ${adminData.profile.last_name} has been created`);\n        } else {\n          throw new Error(response.message || 'Failed to create admin account');\n        }\n      }\n      loadAdmins();\n    } catch (err) {\n      setError(err.message || 'Failed to save admin account');\n      throw err; // Re-throw to prevent modal from closing\n    } finally {\n      setModalLoading(false);\n    }\n  };\n  const handlePageChange = page => {\n    setCurrentPage(page);\n  };\n  const handleItemsPerPageChange = itemsPerPage => {\n    setItemsPerPage(itemsPerPage);\n    setCurrentPage(1); // Reset to first page\n  };\n  const clearMessages = () => {\n    setError(null);\n    setSuccess(null);\n  };\n  if (!permissions.canManageAdmins) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Users, {\n        size: 64,\n        style: {\n          marginBottom: '1rem',\n          opacity: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          margin: '0 0 0.5rem',\n          fontSize: '1.5rem',\n          fontWeight: '600'\n        },\n        children: \"Access Denied\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: 0,\n          fontSize: '1rem'\n        },\n        children: \"You do not have permission to manage admin accounts.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        },\n        children: [\"Current Role: \", permissions.getPositionDisplayName()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '40px',\n          height: '40px',\n          border: '4px solid #f3f4f6',\n          borderTop: '4px solid #3b82f6',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '2rem',\n        textAlign: 'center',\n        color: '#ef4444'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Error\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: loadAdmins,\n        style: {\n          padding: '0.5rem 1rem',\n          background: '#3b82f6',\n          color: 'white',\n          border: 'none',\n          borderRadius: '6px',\n          cursor: 'pointer'\n        },\n        children: \"Retry\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 344,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      maxWidth: '1400px',\n      margin: '0 auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            margin: '0 0 0.5rem',\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          },\n          children: \"Admin Account Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          },\n          children: \"Manage administrator accounts and permissions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleAddAdmin,\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          padding: '0.75rem 1.5rem',\n          background: '#3b82f6',\n          color: 'white',\n          border: 'none',\n          borderRadius: '8px',\n          fontSize: '0.875rem',\n          fontWeight: '600',\n          cursor: 'pointer',\n          transition: 'background-color 0.2s'\n        },\n        onMouseEnter: e => e.currentTarget.style.background = '#2563eb',\n        onMouseLeave: e => e.currentTarget.style.background = '#3b82f6',\n        children: [/*#__PURE__*/_jsxDEV(UserPlus, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 11\n        }, this), \"Add Admin\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 371,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        gap: '1rem',\n        marginBottom: '2rem',\n        flexWrap: 'wrap'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          minWidth: '300px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'relative'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Search, {\n            size: 20,\n            style: {\n              position: 'absolute',\n              left: '0.75rem',\n              top: '50%',\n              transform: 'translateY(-50%)',\n              color: '#6b7280'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search admins by name or email...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            style: {\n              width: '100%',\n              padding: '0.75rem 0.75rem 0.75rem 2.5rem',\n              border: '1px solid #d1d5db',\n              borderRadius: '8px',\n              fontSize: '0.875rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n        value: positionFilter,\n        onChange: e => setPositionFilter(e.target.value),\n        style: {\n          padding: '0.75rem',\n          border: '1px solid #d1d5db',\n          borderRadius: '8px',\n          fontSize: '0.875rem',\n          minWidth: '150px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"\",\n          children: \"All Positions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"super_admin\",\n          children: \"Super Admin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"professor\",\n          children: \"Professor\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 451,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n        value: statusFilter,\n        onChange: e => setStatusFilter(e.target.value),\n        style: {\n          padding: '0.75rem',\n          border: '1px solid #d1d5db',\n          borderRadius: '8px',\n          fontSize: '0.875rem',\n          minWidth: '120px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"\",\n          children: \"All Status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"active\",\n          children: \"Active\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"inactive\",\n          children: \"Inactive\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 480,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 420,\n      columnNumber: 7\n    }, this), (error || success) && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '1.5rem'\n      },\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '1rem',\n          background: '#fef2f2',\n          border: '1px solid #fecaca',\n          borderRadius: '8px',\n          color: '#dc2626',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 17\n          }, this), error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 498,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: clearMessages,\n          style: {\n            background: 'transparent',\n            border: 'none',\n            color: '#dc2626',\n            cursor: 'pointer',\n            padding: '0.25rem'\n          },\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 488,\n        columnNumber: 13\n      }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '1rem',\n          background: '#f0fdf4',\n          border: '1px solid #bbf7d0',\n          borderRadius: '8px',\n          color: '#166534',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 17\n          }, this), success]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 528,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: clearMessages,\n          style: {\n            background: 'transparent',\n            border: 'none',\n            color: '#166534',\n            cursor: 'pointer',\n            padding: '0.25rem'\n          },\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 518,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 486,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(AdminAccountList, {\n      admins: admins,\n      loading: loading,\n      onEdit: handleEditAdmin,\n      onDelete: handleDeleteAdmin,\n      onToggleStatus: handleToggleStatus,\n      currentPage: currentPage,\n      totalPages: Math.ceil(totalItems / itemsPerPage),\n      itemsPerPage: itemsPerPage,\n      totalItems: totalItems,\n      onPageChange: handlePageChange,\n      onItemsPerPageChange: handleItemsPerPageChange\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 550,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AdminAccountModal, {\n      isOpen: showModal,\n      onClose: () => setShowModal(false),\n      onSave: handleSaveAdmin,\n      admin: editingAdmin,\n      loading: modalLoading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 565,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 369,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminManagement, \"2LV4tC1oMDp47W/1IsKQ7F5LLfg=\", false, function () {\n  return [useAdminAuth, usePermissions];\n});\n_c = AdminManagement;\nexport default AdminManagement;\nvar _c;\n$RefreshReg$(_c, \"AdminManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "UserPlus", "Search", "Users", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CheckCircle", "useAdminAuth", "usePermissions", "adminManagementService", "AdminAccountList", "AdminAccountModal", "jsxDEV", "_jsxDEV", "AdminManagement", "_s", "user", "permissions", "admins", "setAdmins", "loading", "setLoading", "error", "setError", "success", "setSuccess", "showModal", "setShowModal", "editingAdmin", "setEditingAdmin", "modalLoading", "setModalLoading", "searchTerm", "setSearchTerm", "positionFilter", "setPositionFilter", "statusFilter", "setStatus<PERSON>ilter", "currentPage", "setCurrentPage", "itemsPerPage", "setItemsPerPage", "totalItems", "setTotalItems", "console", "log", "canManageAdmins", "loadAdmins", "response", "get<PERSON>dmins", "page", "limit", "search", "position", "status", "data", "pagination", "mockAdmins", "admin_id", "email", "is_active", "last_login", "created_at", "profile", "first_name", "middle_name", "last_name", "full_name", "phone_number", "department", "grade_level", "bio", "filteredAdmins", "trim", "searchLower", "toLowerCase", "filter", "admin", "includes", "isActive", "length", "startIndex", "paginatedAdmins", "slice", "err", "_err$response", "isAuthError", "message", "localStorage", "removeItem", "window", "location", "href", "handleAddAdmin", "handleEditAdmin", "handleDeleteAdmin", "deleteAdmin", "Error", "handleToggleStatus", "toggleAdminStatus", "action", "handleSaveAdmin", "adminData", "updateAdmin", "createData", "password", "createAdmin", "handlePageChange", "handleItemsPerPageChange", "clearMessages", "style", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "textAlign", "color", "children", "size", "marginBottom", "opacity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "margin", "fontSize", "fontWeight", "marginTop", "padding", "background", "getPositionBadgeColor", "borderRadius", "getPositionDisplayName", "width", "height", "border", "borderTop", "animation", "onClick", "cursor", "max<PERSON><PERSON><PERSON>", "gap", "transition", "onMouseEnter", "e", "currentTarget", "onMouseLeave", "flexWrap", "flex", "min<PERSON><PERSON><PERSON>", "left", "top", "transform", "type", "placeholder", "value", "onChange", "target", "onEdit", "onDelete", "onToggleStatus", "totalPages", "Math", "ceil", "onPageChange", "onItemsPerPageChange", "isOpen", "onClose", "onSave", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/admin/AdminManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { UserPlus, Search, Filter, Users, AlertTriangle, CheckCircle } from 'lucide-react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\nimport { adminManagementService } from '../../services/adminManagementService';\nimport AdminAccountList from '../../components/admin/AdminAccountList';\nimport AdminAccountModal from '../../components/admin/AdminAccountModal';\n\ninterface AdminAccount {\n  admin_id?: number;\n  email: string;\n  is_active: boolean;\n  last_login?: string | null;\n  created_at?: string;\n  profile: {\n    first_name: string;\n    middle_name?: string;\n    last_name: string;\n    suffix?: string;\n    full_name: string;\n    phone_number?: string;\n    department?: string;\n    position: 'super_admin' | 'professor';\n    grade_level?: number;\n    bio?: string;\n    profile_picture?: string;\n  };\n}\n\nconst AdminManagement: React.FC = () => {\n  const { user } = useAdminAuth();\n  const permissions = usePermissions(user);\n\n  // State management\n  const [admins, setAdmins] = useState<AdminAccount[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  // Modal state\n  const [showModal, setShowModal] = useState(false);\n  const [editingAdmin, setEditingAdmin] = useState<AdminAccount | null>(null);\n  const [modalLoading, setModalLoading] = useState(false);\n\n  // Search and filter state\n  const [searchTerm, setSearchTerm] = useState('');\n  const [positionFilter, setPositionFilter] = useState<string>('');\n  const [statusFilter, setStatusFilter] = useState<string>('');\n\n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(10);\n  const [totalItems, setTotalItems] = useState(0);\n\n  useEffect(() => {\n    // Check if user has permission to manage admins\n    console.log('🔍 AdminManagement - Permission check:', {\n      canManageAdmins: permissions.canManageAdmins,\n      user: user,\n      permissions: permissions\n    });\n\n    if (!permissions.canManageAdmins) {\n      console.log('❌ AdminManagement - No permission to manage admin accounts');\n      setError('You do not have permission to manage admin accounts');\n      setLoading(false);\n      return;\n    }\n\n    console.log('✅ AdminManagement - Permission granted, loading admins');\n    loadAdmins();\n  }, [permissions.canManageAdmins, currentPage, itemsPerPage, searchTerm, positionFilter, statusFilter]);\n\n  // Reset to page 1 when filters change\n  useEffect(() => {\n    setCurrentPage(1);\n  }, [searchTerm, positionFilter, statusFilter]);\n\n  const loadAdmins = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await adminManagementService.getAdmins({\n        page: currentPage,\n        limit: itemsPerPage,\n        search: searchTerm,\n        position: positionFilter,\n        status: statusFilter\n      });\n\n      if (response.success) {\n        setAdmins(response.data.admins);\n        setTotalItems(response.data.pagination.totalItems);\n        return;\n      }\n\n      // Fallback to mock data if API fails\n      const mockAdmins: AdminAccount[] = [\n        {\n          admin_id: 1,\n          email: '<EMAIL>',\n          is_active: true,\n          last_login: '2025-08-05T10:30:00Z',\n          created_at: '2025-01-01T00:00:00Z',\n          profile: {\n            first_name: 'Josh',\n            middle_name: 'Christian',\n            last_name: 'Mojica',\n            full_name: 'Josh Christian Mojica',\n            phone_number: '+************',\n            department: 'Administration',\n            position: 'super_admin',\n            grade_level: 12,\n            bio: 'System Administrator with expertise in educational technology.'\n          }\n        },\n        {\n          admin_id: 2,\n          email: '<EMAIL>',\n          is_active: true,\n          last_login: '2025-08-05T09:15:00Z',\n          created_at: '2025-01-15T00:00:00Z',\n          profile: {\n            first_name: 'Zaira',\n            last_name: 'Plarisan',\n            full_name: 'Zaira Plarisan',\n            phone_number: '+***********1',\n            department: 'Mathematics',\n            position: 'professor',\n            grade_level: 11,\n            bio: 'Mathematics professor specializing in Grade 11 curriculum.'\n          }\n        }\n      ];\n\n      // Apply filters\n      let filteredAdmins = [...mockAdmins];\n\n      if (searchTerm.trim()) {\n        const searchLower = searchTerm.toLowerCase().trim();\n        filteredAdmins = filteredAdmins.filter(admin =>\n          admin.profile.full_name.toLowerCase().includes(searchLower) ||\n          admin.email.toLowerCase().includes(searchLower) ||\n          admin.profile.first_name.toLowerCase().includes(searchLower) ||\n          admin.profile.last_name.toLowerCase().includes(searchLower) ||\n          (admin.profile.department && admin.profile.department.toLowerCase().includes(searchLower))\n        );\n      }\n\n      if (positionFilter) {\n        filteredAdmins = filteredAdmins.filter(admin => admin.profile.position === positionFilter);\n      }\n\n      if (statusFilter) {\n        const isActive = statusFilter === 'active';\n        filteredAdmins = filteredAdmins.filter(admin => admin.is_active === isActive);\n      }\n\n      setTotalItems(filteredAdmins.length);\n\n      // Apply pagination\n      const startIndex = (currentPage - 1) * itemsPerPage;\n      const paginatedAdmins = filteredAdmins.slice(startIndex, startIndex + itemsPerPage);\n\n      setAdmins(paginatedAdmins);\n\n    } catch (err: any) {\n      console.error('❌ AdminManagement - Error loading admins:', err);\n\n      // Check if it's an authentication error\n      const isAuthError = err.response?.status === 401 ||\n                         err.message.includes('Authorization header missing') ||\n                         err.message.includes('Access token expired');\n\n      if (isAuthError) {\n        console.log('🔐 AdminManagement - Authentication error, redirecting to login');\n        // Clear any stored tokens and redirect to login\n        localStorage.removeItem('vcba_admin_auth_token');\n        localStorage.removeItem('vcba_admin_refresh_token');\n        localStorage.removeItem('vcba_admin_user_data');\n        window.location.href = '/admin/login';\n        return;\n      }\n\n      setError(err.message || 'Failed to load admin accounts');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handler functions\n  const handleAddAdmin = () => {\n    setEditingAdmin(null);\n    setShowModal(true);\n  };\n\n  const handleEditAdmin = (admin: AdminAccount) => {\n    setEditingAdmin(admin);\n    setShowModal(true);\n  };\n\n  const handleDeleteAdmin = async (admin: AdminAccount) => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      const response = await adminManagementService.deleteAdmin(admin.admin_id!);\n      if (response.success) {\n        setSuccess(`Admin account for ${admin.profile.full_name} has been deactivated`);\n        loadAdmins();\n      } else {\n        throw new Error(response.message || 'Failed to delete admin account');\n      }\n\n    } catch (err: any) {\n      setError(err.message || 'Failed to delete admin account');\n    }\n  };\n\n  const handleToggleStatus = async (admin: AdminAccount) => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      const response = await adminManagementService.toggleAdminStatus(admin.admin_id!, !admin.is_active);\n      if (response.success) {\n        const action = admin.is_active ? 'deactivated' : 'activated';\n        setSuccess(`Admin account for ${admin.profile.full_name} has been ${action}`);\n        loadAdmins();\n      } else {\n        throw new Error(response.message || 'Failed to update admin status');\n      }\n\n    } catch (err: any) {\n      setError(err.message || 'Failed to update admin status');\n    }\n  };\n\n  const handleSaveAdmin = async (adminData: AdminAccount) => {\n    try {\n      setModalLoading(true);\n      setError(null);\n      setSuccess(null);\n\n      if (editingAdmin) {\n        // Update existing admin\n        const response = await adminManagementService.updateAdmin(editingAdmin.admin_id!, adminData);\n        if (response.success) {\n          setSuccess(`Admin account for ${adminData.profile.first_name} ${adminData.profile.last_name} has been updated`);\n        } else {\n          throw new Error(response.message || 'Failed to update admin account');\n        }\n      } else {\n        // Create new admin - include password in the data\n        const createData = { ...adminData, password: (adminData as any).password };\n        const response = await adminManagementService.createAdmin(createData);\n        if (response.success) {\n          setSuccess(`Admin account for ${adminData.profile.first_name} ${adminData.profile.last_name} has been created`);\n        } else {\n          throw new Error(response.message || 'Failed to create admin account');\n        }\n      }\n\n      loadAdmins();\n\n    } catch (err: any) {\n      setError(err.message || 'Failed to save admin account');\n      throw err; // Re-throw to prevent modal from closing\n    } finally {\n      setModalLoading(false);\n    }\n  };\n\n  const handlePageChange = (page: number) => {\n    setCurrentPage(page);\n  };\n\n  const handleItemsPerPageChange = (itemsPerPage: number) => {\n    setItemsPerPage(itemsPerPage);\n    setCurrentPage(1); // Reset to first page\n  };\n\n  const clearMessages = () => {\n    setError(null);\n    setSuccess(null);\n  };\n\n  if (!permissions.canManageAdmins) {\n    return (\n      <div style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      }}>\n        <Users size={64} style={{ marginBottom: '1rem', opacity: 0.5 }} />\n        <h2 style={{ margin: '0 0 0.5rem', fontSize: '1.5rem', fontWeight: '600' }}>\n          Access Denied\n        </h2>\n        <p style={{ margin: 0, fontSize: '1rem' }}>\n          You do not have permission to manage admin accounts.\n        </p>\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        }}>\n          Current Role: {permissions.getPositionDisplayName()}\n        </div>\n      </div>\n    );\n  }\n\n  if (loading) {\n    return (\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px'\n      }}>\n        <div style={{\n          width: '40px',\n          height: '40px',\n          border: '4px solid #f3f4f6',\n          borderTop: '4px solid #3b82f6',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }} />\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div style={{\n        padding: '2rem',\n        textAlign: 'center',\n        color: '#ef4444'\n      }}>\n        <h2>Error</h2>\n        <p>{error}</p>\n        <button\n          onClick={loadAdmins}\n          style={{\n            padding: '0.5rem 1rem',\n            background: '#3b82f6',\n            color: 'white',\n            border: 'none',\n            borderRadius: '6px',\n            cursor: 'pointer'\n          }}\n        >\n          Retry\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ maxWidth: '1400px', margin: '0 auto' }}>\n      {/* Header */}\n      <div style={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      }}>\n        <div>\n          <h1 style={{\n            margin: '0 0 0.5rem',\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          }}>\n            Admin Account Management\n          </h1>\n          <p style={{\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          }}>\n            Manage administrator accounts and permissions\n          </p>\n        </div>\n\n        <button\n          onClick={handleAddAdmin}\n          style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            padding: '0.75rem 1.5rem',\n            background: '#3b82f6',\n            color: 'white',\n            border: 'none',\n            borderRadius: '8px',\n            fontSize: '0.875rem',\n            fontWeight: '600',\n            cursor: 'pointer',\n            transition: 'background-color 0.2s'\n          }}\n          onMouseEnter={(e) => e.currentTarget.style.background = '#2563eb'}\n          onMouseLeave={(e) => e.currentTarget.style.background = '#3b82f6'}\n        >\n          <UserPlus size={16} />\n          Add Admin\n        </button>\n      </div>\n\n      {/* Search and Filter Bar */}\n      <div style={{\n        display: 'flex',\n        gap: '1rem',\n        marginBottom: '2rem',\n        flexWrap: 'wrap'\n      }}>\n        <div style={{ flex: 1, minWidth: '300px' }}>\n          <div style={{ position: 'relative' }}>\n            <Search size={20} style={{\n              position: 'absolute',\n              left: '0.75rem',\n              top: '50%',\n              transform: 'translateY(-50%)',\n              color: '#6b7280'\n            }} />\n            <input\n              type=\"text\"\n              placeholder=\"Search admins by name or email...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              style={{\n                width: '100%',\n                padding: '0.75rem 0.75rem 0.75rem 2.5rem',\n                border: '1px solid #d1d5db',\n                borderRadius: '8px',\n                fontSize: '0.875rem'\n              }}\n            />\n          </div>\n        </div>\n\n        <select\n          value={positionFilter}\n          onChange={(e) => setPositionFilter(e.target.value)}\n          style={{\n            padding: '0.75rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '8px',\n            fontSize: '0.875rem',\n            minWidth: '150px'\n          }}\n        >\n          <option value=\"\">All Positions</option>\n          <option value=\"super_admin\">Super Admin</option>\n          <option value=\"professor\">Professor</option>\n        </select>\n\n        <select\n          value={statusFilter}\n          onChange={(e) => setStatusFilter(e.target.value)}\n          style={{\n            padding: '0.75rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '8px',\n            fontSize: '0.875rem',\n            minWidth: '120px'\n          }}\n        >\n          <option value=\"\">All Status</option>\n          <option value=\"active\">Active</option>\n          <option value=\"inactive\">Inactive</option>\n        </select>\n      </div>\n\n      {/* Success/Error Messages */}\n      {(error || success) && (\n        <div style={{ marginBottom: '1.5rem' }}>\n          {error && (\n            <div style={{\n              padding: '1rem',\n              background: '#fef2f2',\n              border: '1px solid #fecaca',\n              borderRadius: '8px',\n              color: '#dc2626',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            }}>\n              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                <AlertTriangle size={16} />\n                {error}\n              </div>\n              <button\n                onClick={clearMessages}\n                style={{\n                  background: 'transparent',\n                  border: 'none',\n                  color: '#dc2626',\n                  cursor: 'pointer',\n                  padding: '0.25rem'\n                }}\n              >\n                ×\n              </button>\n            </div>\n          )}\n\n          {success && (\n            <div style={{\n              padding: '1rem',\n              background: '#f0fdf4',\n              border: '1px solid #bbf7d0',\n              borderRadius: '8px',\n              color: '#166534',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            }}>\n              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                <CheckCircle size={16} />\n                {success}\n              </div>\n              <button\n                onClick={clearMessages}\n                style={{\n                  background: 'transparent',\n                  border: 'none',\n                  color: '#166534',\n                  cursor: 'pointer',\n                  padding: '0.25rem'\n                }}\n              >\n                ×\n              </button>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Admin List */}\n      <AdminAccountList\n        admins={admins}\n        loading={loading}\n        onEdit={handleEditAdmin}\n        onDelete={handleDeleteAdmin}\n        onToggleStatus={handleToggleStatus}\n        currentPage={currentPage}\n        totalPages={Math.ceil(totalItems / itemsPerPage)}\n        itemsPerPage={itemsPerPage}\n        totalItems={totalItems}\n        onPageChange={handlePageChange}\n        onItemsPerPageChange={handleItemsPerPageChange}\n      />\n\n      {/* Admin Modal */}\n      <AdminAccountModal\n        isOpen={showModal}\n        onClose={() => setShowModal(false)}\n        onSave={handleSaveAdmin}\n        admin={editingAdmin}\n        loading={modalLoading}\n      />\n    </div>\n  );\n};\n\nexport default AdminManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,QAAQ,EAAEC,MAAM,EAAUC,KAAK,EAAEC,aAAa,EAAEC,WAAW,QAAQ,cAAc;AAC1F,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,sBAAsB,QAAQ,uCAAuC;AAC9E,OAAOC,gBAAgB,MAAM,yCAAyC;AACtE,OAAOC,iBAAiB,MAAM,0CAA0C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAuBzE,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IAAEC;EAAK,CAAC,GAAGT,YAAY,CAAC,CAAC;EAC/B,MAAMU,WAAW,GAAGT,cAAc,CAACQ,IAAI,CAAC;;EAExC;EACA,MAAM,CAACE,MAAM,EAAEC,SAAS,CAAC,GAAGnB,QAAQ,CAAiB,EAAE,CAAC;EACxD,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAgB,IAAI,CAAC;;EAE3D;EACA,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAsB,IAAI,CAAC;EAC3E,MAAM,CAAC8B,YAAY,EAAEC,eAAe,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAACgC,UAAU,EAAEC,aAAa,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkC,cAAc,EAAEC,iBAAiB,CAAC,GAAGnC,QAAQ,CAAS,EAAE,CAAC;EAChE,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAS,EAAE,CAAC;;EAE5D;EACA,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC0C,UAAU,EAAEC,aAAa,CAAC,GAAG3C,QAAQ,CAAC,CAAC,CAAC;EAE/CC,SAAS,CAAC,MAAM;IACd;IACA2C,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE;MACpDC,eAAe,EAAE7B,WAAW,CAAC6B,eAAe;MAC5C9B,IAAI,EAAEA,IAAI;MACVC,WAAW,EAAEA;IACf,CAAC,CAAC;IAEF,IAAI,CAACA,WAAW,CAAC6B,eAAe,EAAE;MAChCF,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;MACzEtB,QAAQ,CAAC,qDAAqD,CAAC;MAC/DF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEAuB,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;IACrEE,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAC9B,WAAW,CAAC6B,eAAe,EAAER,WAAW,EAAEE,YAAY,EAAER,UAAU,EAAEE,cAAc,EAAEE,YAAY,CAAC,CAAC;;EAEtG;EACAnC,SAAS,CAAC,MAAM;IACdsC,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC,EAAE,CAACP,UAAU,EAAEE,cAAc,EAAEE,YAAY,CAAC,CAAC;EAE9C,MAAMW,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF1B,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMyB,QAAQ,GAAG,MAAMvC,sBAAsB,CAACwC,SAAS,CAAC;QACtDC,IAAI,EAAEZ,WAAW;QACjBa,KAAK,EAAEX,YAAY;QACnBY,MAAM,EAAEpB,UAAU;QAClBqB,QAAQ,EAAEnB,cAAc;QACxBoB,MAAM,EAAElB;MACV,CAAC,CAAC;MAEF,IAAIY,QAAQ,CAACxB,OAAO,EAAE;QACpBL,SAAS,CAAC6B,QAAQ,CAACO,IAAI,CAACrC,MAAM,CAAC;QAC/ByB,aAAa,CAACK,QAAQ,CAACO,IAAI,CAACC,UAAU,CAACd,UAAU,CAAC;QAClD;MACF;;MAEA;MACA,MAAMe,UAA0B,GAAG,CACjC;QACEC,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE,0BAA0B;QACjCC,SAAS,EAAE,IAAI;QACfC,UAAU,EAAE,sBAAsB;QAClCC,UAAU,EAAE,sBAAsB;QAClCC,OAAO,EAAE;UACPC,UAAU,EAAE,MAAM;UAClBC,WAAW,EAAE,WAAW;UACxBC,SAAS,EAAE,QAAQ;UACnBC,SAAS,EAAE,uBAAuB;UAClCC,YAAY,EAAE,eAAe;UAC7BC,UAAU,EAAE,gBAAgB;UAC5BhB,QAAQ,EAAE,aAAa;UACvBiB,WAAW,EAAE,EAAE;UACfC,GAAG,EAAE;QACP;MACF,CAAC,EACD;QACEb,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE,oBAAoB;QAC3BC,SAAS,EAAE,IAAI;QACfC,UAAU,EAAE,sBAAsB;QAClCC,UAAU,EAAE,sBAAsB;QAClCC,OAAO,EAAE;UACPC,UAAU,EAAE,OAAO;UACnBE,SAAS,EAAE,UAAU;UACrBC,SAAS,EAAE,gBAAgB;UAC3BC,YAAY,EAAE,eAAe;UAC7BC,UAAU,EAAE,aAAa;UACzBhB,QAAQ,EAAE,WAAW;UACrBiB,WAAW,EAAE,EAAE;UACfC,GAAG,EAAE;QACP;MACF,CAAC,CACF;;MAED;MACA,IAAIC,cAAc,GAAG,CAAC,GAAGf,UAAU,CAAC;MAEpC,IAAIzB,UAAU,CAACyC,IAAI,CAAC,CAAC,EAAE;QACrB,MAAMC,WAAW,GAAG1C,UAAU,CAAC2C,WAAW,CAAC,CAAC,CAACF,IAAI,CAAC,CAAC;QACnDD,cAAc,GAAGA,cAAc,CAACI,MAAM,CAACC,KAAK,IAC1CA,KAAK,CAACd,OAAO,CAACI,SAAS,CAACQ,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,WAAW,CAAC,IAC3DG,KAAK,CAAClB,KAAK,CAACgB,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,WAAW,CAAC,IAC/CG,KAAK,CAACd,OAAO,CAACC,UAAU,CAACW,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,WAAW,CAAC,IAC5DG,KAAK,CAACd,OAAO,CAACG,SAAS,CAACS,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,WAAW,CAAC,IAC1DG,KAAK,CAACd,OAAO,CAACM,UAAU,IAAIQ,KAAK,CAACd,OAAO,CAACM,UAAU,CAACM,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,WAAW,CAC1F,CAAC;MACH;MAEA,IAAIxC,cAAc,EAAE;QAClBsC,cAAc,GAAGA,cAAc,CAACI,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACd,OAAO,CAACV,QAAQ,KAAKnB,cAAc,CAAC;MAC5F;MAEA,IAAIE,YAAY,EAAE;QAChB,MAAM2C,QAAQ,GAAG3C,YAAY,KAAK,QAAQ;QAC1CoC,cAAc,GAAGA,cAAc,CAACI,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACjB,SAAS,KAAKmB,QAAQ,CAAC;MAC/E;MAEApC,aAAa,CAAC6B,cAAc,CAACQ,MAAM,CAAC;;MAEpC;MACA,MAAMC,UAAU,GAAG,CAAC3C,WAAW,GAAG,CAAC,IAAIE,YAAY;MACnD,MAAM0C,eAAe,GAAGV,cAAc,CAACW,KAAK,CAACF,UAAU,EAAEA,UAAU,GAAGzC,YAAY,CAAC;MAEnFrB,SAAS,CAAC+D,eAAe,CAAC;IAE5B,CAAC,CAAC,OAAOE,GAAQ,EAAE;MAAA,IAAAC,aAAA;MACjBzC,OAAO,CAACtB,KAAK,CAAC,2CAA2C,EAAE8D,GAAG,CAAC;;MAE/D;MACA,MAAME,WAAW,GAAG,EAAAD,aAAA,GAAAD,GAAG,CAACpC,QAAQ,cAAAqC,aAAA,uBAAZA,aAAA,CAAc/B,MAAM,MAAK,GAAG,IAC7B8B,GAAG,CAACG,OAAO,CAACT,QAAQ,CAAC,8BAA8B,CAAC,IACpDM,GAAG,CAACG,OAAO,CAACT,QAAQ,CAAC,sBAAsB,CAAC;MAE/D,IAAIQ,WAAW,EAAE;QACf1C,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC;QAC9E;QACA2C,YAAY,CAACC,UAAU,CAAC,uBAAuB,CAAC;QAChDD,YAAY,CAACC,UAAU,CAAC,0BAA0B,CAAC;QACnDD,YAAY,CAACC,UAAU,CAAC,sBAAsB,CAAC;QAC/CC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,cAAc;QACrC;MACF;MAEArE,QAAQ,CAAC6D,GAAG,CAACG,OAAO,IAAI,+BAA+B,CAAC;IAC1D,CAAC,SAAS;MACRlE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMwE,cAAc,GAAGA,CAAA,KAAM;IAC3BhE,eAAe,CAAC,IAAI,CAAC;IACrBF,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMmE,eAAe,GAAIjB,KAAmB,IAAK;IAC/ChD,eAAe,CAACgD,KAAK,CAAC;IACtBlD,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMoE,iBAAiB,GAAG,MAAOlB,KAAmB,IAAK;IACvD,IAAI;MACFtD,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAMuB,QAAQ,GAAG,MAAMvC,sBAAsB,CAACuF,WAAW,CAACnB,KAAK,CAACnB,QAAS,CAAC;MAC1E,IAAIV,QAAQ,CAACxB,OAAO,EAAE;QACpBC,UAAU,CAAC,qBAAqBoD,KAAK,CAACd,OAAO,CAACI,SAAS,uBAAuB,CAAC;QAC/EpB,UAAU,CAAC,CAAC;MACd,CAAC,MAAM;QACL,MAAM,IAAIkD,KAAK,CAACjD,QAAQ,CAACuC,OAAO,IAAI,gCAAgC,CAAC;MACvE;IAEF,CAAC,CAAC,OAAOH,GAAQ,EAAE;MACjB7D,QAAQ,CAAC6D,GAAG,CAACG,OAAO,IAAI,gCAAgC,CAAC;IAC3D;EACF,CAAC;EAED,MAAMW,kBAAkB,GAAG,MAAOrB,KAAmB,IAAK;IACxD,IAAI;MACFtD,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAMuB,QAAQ,GAAG,MAAMvC,sBAAsB,CAAC0F,iBAAiB,CAACtB,KAAK,CAACnB,QAAQ,EAAG,CAACmB,KAAK,CAACjB,SAAS,CAAC;MAClG,IAAIZ,QAAQ,CAACxB,OAAO,EAAE;QACpB,MAAM4E,MAAM,GAAGvB,KAAK,CAACjB,SAAS,GAAG,aAAa,GAAG,WAAW;QAC5DnC,UAAU,CAAC,qBAAqBoD,KAAK,CAACd,OAAO,CAACI,SAAS,aAAaiC,MAAM,EAAE,CAAC;QAC7ErD,UAAU,CAAC,CAAC;MACd,CAAC,MAAM;QACL,MAAM,IAAIkD,KAAK,CAACjD,QAAQ,CAACuC,OAAO,IAAI,+BAA+B,CAAC;MACtE;IAEF,CAAC,CAAC,OAAOH,GAAQ,EAAE;MACjB7D,QAAQ,CAAC6D,GAAG,CAACG,OAAO,IAAI,+BAA+B,CAAC;IAC1D;EACF,CAAC;EAED,MAAMc,eAAe,GAAG,MAAOC,SAAuB,IAAK;IACzD,IAAI;MACFvE,eAAe,CAAC,IAAI,CAAC;MACrBR,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIG,YAAY,EAAE;QAChB;QACA,MAAMoB,QAAQ,GAAG,MAAMvC,sBAAsB,CAAC8F,WAAW,CAAC3E,YAAY,CAAC8B,QAAQ,EAAG4C,SAAS,CAAC;QAC5F,IAAItD,QAAQ,CAACxB,OAAO,EAAE;UACpBC,UAAU,CAAC,qBAAqB6E,SAAS,CAACvC,OAAO,CAACC,UAAU,IAAIsC,SAAS,CAACvC,OAAO,CAACG,SAAS,mBAAmB,CAAC;QACjH,CAAC,MAAM;UACL,MAAM,IAAI+B,KAAK,CAACjD,QAAQ,CAACuC,OAAO,IAAI,gCAAgC,CAAC;QACvE;MACF,CAAC,MAAM;QACL;QACA,MAAMiB,UAAU,GAAG;UAAE,GAAGF,SAAS;UAAEG,QAAQ,EAAGH,SAAS,CAASG;QAAS,CAAC;QAC1E,MAAMzD,QAAQ,GAAG,MAAMvC,sBAAsB,CAACiG,WAAW,CAACF,UAAU,CAAC;QACrE,IAAIxD,QAAQ,CAACxB,OAAO,EAAE;UACpBC,UAAU,CAAC,qBAAqB6E,SAAS,CAACvC,OAAO,CAACC,UAAU,IAAIsC,SAAS,CAACvC,OAAO,CAACG,SAAS,mBAAmB,CAAC;QACjH,CAAC,MAAM;UACL,MAAM,IAAI+B,KAAK,CAACjD,QAAQ,CAACuC,OAAO,IAAI,gCAAgC,CAAC;QACvE;MACF;MAEAxC,UAAU,CAAC,CAAC;IAEd,CAAC,CAAC,OAAOqC,GAAQ,EAAE;MACjB7D,QAAQ,CAAC6D,GAAG,CAACG,OAAO,IAAI,8BAA8B,CAAC;MACvD,MAAMH,GAAG,CAAC,CAAC;IACb,CAAC,SAAS;MACRrD,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAM4E,gBAAgB,GAAIzD,IAAY,IAAK;IACzCX,cAAc,CAACW,IAAI,CAAC;EACtB,CAAC;EAED,MAAM0D,wBAAwB,GAAIpE,YAAoB,IAAK;IACzDC,eAAe,CAACD,YAAY,CAAC;IAC7BD,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;EAED,MAAMsE,aAAa,GAAGA,CAAA,KAAM;IAC1BtF,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;EAClB,CAAC;EAED,IAAI,CAACR,WAAW,CAAC6B,eAAe,EAAE;IAChC,oBACEjC,OAAA;MAAKiG,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE,OAAO;QAClBC,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACAzG,OAAA,CAACT,KAAK;QAACmH,IAAI,EAAE,EAAG;QAACT,KAAK,EAAE;UAAEU,YAAY,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAI;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClEhH,OAAA;QAAIiG,KAAK,EAAE;UAAEgB,MAAM,EAAE,YAAY;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAM,CAAE;QAAAV,QAAA,EAAC;MAE5E;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLhH,OAAA;QAAGiG,KAAK,EAAE;UAAEgB,MAAM,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAT,QAAA,EAAC;MAE3C;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJhH,OAAA;QAAKiG,KAAK,EAAE;UACVmB,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAElH,WAAW,CAACmH,qBAAqB,CAAC,CAAC;UAC/CC,YAAY,EAAE,KAAK;UACnBhB,KAAK,EAAE,OAAO;UACdU,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE;QACd,CAAE;QAAAV,QAAA,GAAC,gBACa,EAACrG,WAAW,CAACqH,sBAAsB,CAAC,CAAC;MAAA;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIzG,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKiG,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE;MACb,CAAE;MAAAG,QAAA,eACAzG,OAAA;QAAKiG,KAAK,EAAE;UACVyB,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,mBAAmB;UAC3BC,SAAS,EAAE,mBAAmB;UAC9BL,YAAY,EAAE,KAAK;UACnBM,SAAS,EAAE;QACb;MAAE;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEV;EAEA,IAAIvG,KAAK,EAAE;IACT,oBACET,OAAA;MAAKiG,KAAK,EAAE;QACVoB,OAAO,EAAE,MAAM;QACfd,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACAzG,OAAA;QAAAyG,QAAA,EAAI;MAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdhH,OAAA;QAAAyG,QAAA,EAAIhG;MAAK;QAAAoG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdhH,OAAA;QACE+H,OAAO,EAAE7F,UAAW;QACpB+D,KAAK,EAAE;UACLoB,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAE,SAAS;UACrBd,KAAK,EAAE,OAAO;UACdoB,MAAM,EAAE,MAAM;UACdJ,YAAY,EAAE,KAAK;UACnBQ,MAAM,EAAE;QACV,CAAE;QAAAvB,QAAA,EACH;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACEhH,OAAA;IAAKiG,KAAK,EAAE;MAAEgC,QAAQ,EAAE,QAAQ;MAAEhB,MAAM,EAAE;IAAS,CAAE;IAAAR,QAAA,gBAEnDzG,OAAA;MAAKiG,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfG,cAAc,EAAE,eAAe;QAC/BD,UAAU,EAAE,QAAQ;QACpBO,YAAY,EAAE;MAChB,CAAE;MAAAF,QAAA,gBACAzG,OAAA;QAAAyG,QAAA,gBACEzG,OAAA;UAAIiG,KAAK,EAAE;YACTgB,MAAM,EAAE,YAAY;YACpBC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjBX,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,EAAC;QAEH;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLhH,OAAA;UAAGiG,KAAK,EAAE;YACRgB,MAAM,EAAE,CAAC;YACTT,KAAK,EAAE,SAAS;YAChBU,QAAQ,EAAE;UACZ,CAAE;UAAAT,QAAA,EAAC;QAEH;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENhH,OAAA;QACE+H,OAAO,EAAE/C,cAAe;QACxBiB,KAAK,EAAE;UACLC,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpB8B,GAAG,EAAE,QAAQ;UACbb,OAAO,EAAE,gBAAgB;UACzBC,UAAU,EAAE,SAAS;UACrBd,KAAK,EAAE,OAAO;UACdoB,MAAM,EAAE,MAAM;UACdJ,YAAY,EAAE,KAAK;UACnBN,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE,KAAK;UACjBa,MAAM,EAAE,SAAS;UACjBG,UAAU,EAAE;QACd,CAAE;QACFC,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACrC,KAAK,CAACqB,UAAU,GAAG,SAAU;QAClEiB,YAAY,EAAGF,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACrC,KAAK,CAACqB,UAAU,GAAG,SAAU;QAAAb,QAAA,gBAElEzG,OAAA,CAACX,QAAQ;UAACqH,IAAI,EAAE;QAAG;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,aAExB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNhH,OAAA;MAAKiG,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfgC,GAAG,EAAE,MAAM;QACXvB,YAAY,EAAE,MAAM;QACpB6B,QAAQ,EAAE;MACZ,CAAE;MAAA/B,QAAA,gBACAzG,OAAA;QAAKiG,KAAK,EAAE;UAAEwC,IAAI,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAQ,CAAE;QAAAjC,QAAA,eACzCzG,OAAA;UAAKiG,KAAK,EAAE;YAAEzD,QAAQ,EAAE;UAAW,CAAE;UAAAiE,QAAA,gBACnCzG,OAAA,CAACV,MAAM;YAACoH,IAAI,EAAE,EAAG;YAACT,KAAK,EAAE;cACvBzD,QAAQ,EAAE,UAAU;cACpBmG,IAAI,EAAE,SAAS;cACfC,GAAG,EAAE,KAAK;cACVC,SAAS,EAAE,kBAAkB;cAC7BrC,KAAK,EAAE;YACT;UAAE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACLhH,OAAA;YACE8I,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,mCAAmC;YAC/CC,KAAK,EAAE7H,UAAW;YAClB8H,QAAQ,EAAGZ,CAAC,IAAKjH,aAAa,CAACiH,CAAC,CAACa,MAAM,CAACF,KAAK,CAAE;YAC/C/C,KAAK,EAAE;cACLyB,KAAK,EAAE,MAAM;cACbL,OAAO,EAAE,gCAAgC;cACzCO,MAAM,EAAE,mBAAmB;cAC3BJ,YAAY,EAAE,KAAK;cACnBN,QAAQ,EAAE;YACZ;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhH,OAAA;QACEgJ,KAAK,EAAE3H,cAAe;QACtB4H,QAAQ,EAAGZ,CAAC,IAAK/G,iBAAiB,CAAC+G,CAAC,CAACa,MAAM,CAACF,KAAK,CAAE;QACnD/C,KAAK,EAAE;UACLoB,OAAO,EAAE,SAAS;UAClBO,MAAM,EAAE,mBAAmB;UAC3BJ,YAAY,EAAE,KAAK;UACnBN,QAAQ,EAAE,UAAU;UACpBwB,QAAQ,EAAE;QACZ,CAAE;QAAAjC,QAAA,gBAEFzG,OAAA;UAAQgJ,KAAK,EAAC,EAAE;UAAAvC,QAAA,EAAC;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACvChH,OAAA;UAAQgJ,KAAK,EAAC,aAAa;UAAAvC,QAAA,EAAC;QAAW;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAChDhH,OAAA;UAAQgJ,KAAK,EAAC,WAAW;UAAAvC,QAAA,EAAC;QAAS;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eAEThH,OAAA;QACEgJ,KAAK,EAAEzH,YAAa;QACpB0H,QAAQ,EAAGZ,CAAC,IAAK7G,eAAe,CAAC6G,CAAC,CAACa,MAAM,CAACF,KAAK,CAAE;QACjD/C,KAAK,EAAE;UACLoB,OAAO,EAAE,SAAS;UAClBO,MAAM,EAAE,mBAAmB;UAC3BJ,YAAY,EAAE,KAAK;UACnBN,QAAQ,EAAE,UAAU;UACpBwB,QAAQ,EAAE;QACZ,CAAE;QAAAjC,QAAA,gBAEFzG,OAAA;UAAQgJ,KAAK,EAAC,EAAE;UAAAvC,QAAA,EAAC;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACpChH,OAAA;UAAQgJ,KAAK,EAAC,QAAQ;UAAAvC,QAAA,EAAC;QAAM;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACtChH,OAAA;UAAQgJ,KAAK,EAAC,UAAU;UAAAvC,QAAA,EAAC;QAAQ;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGL,CAACvG,KAAK,IAAIE,OAAO,kBAChBX,OAAA;MAAKiG,KAAK,EAAE;QAAEU,YAAY,EAAE;MAAS,CAAE;MAAAF,QAAA,GACpChG,KAAK,iBACJT,OAAA;QAAKiG,KAAK,EAAE;UACVoB,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,SAAS;UACrBM,MAAM,EAAE,mBAAmB;UAC3BJ,YAAY,EAAE,KAAK;UACnBhB,KAAK,EAAE,SAAS;UAChBN,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE;QAClB,CAAE;QAAAI,QAAA,gBACAzG,OAAA;UAAKiG,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAE8B,GAAG,EAAE;UAAS,CAAE;UAAAzB,QAAA,gBACnEzG,OAAA,CAACR,aAAa;YAACkH,IAAI,EAAE;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAC1BvG,KAAK;QAAA;UAAAoG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNhH,OAAA;UACE+H,OAAO,EAAE/B,aAAc;UACvBC,KAAK,EAAE;YACLqB,UAAU,EAAE,aAAa;YACzBM,MAAM,EAAE,MAAM;YACdpB,KAAK,EAAE,SAAS;YAChBwB,MAAM,EAAE,SAAS;YACjBX,OAAO,EAAE;UACX,CAAE;UAAAZ,QAAA,EACH;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAEArG,OAAO,iBACNX,OAAA;QAAKiG,KAAK,EAAE;UACVoB,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,SAAS;UACrBM,MAAM,EAAE,mBAAmB;UAC3BJ,YAAY,EAAE,KAAK;UACnBhB,KAAK,EAAE,SAAS;UAChBN,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE;QAClB,CAAE;QAAAI,QAAA,gBACAzG,OAAA;UAAKiG,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAE8B,GAAG,EAAE;UAAS,CAAE;UAAAzB,QAAA,gBACnEzG,OAAA,CAACP,WAAW;YAACiH,IAAI,EAAE;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACxBrG,OAAO;QAAA;UAAAkG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNhH,OAAA;UACE+H,OAAO,EAAE/B,aAAc;UACvBC,KAAK,EAAE;YACLqB,UAAU,EAAE,aAAa;YACzBM,MAAM,EAAE,MAAM;YACdpB,KAAK,EAAE,SAAS;YAChBwB,MAAM,EAAE,SAAS;YACjBX,OAAO,EAAE;UACX,CAAE;UAAAZ,QAAA,EACH;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAGDhH,OAAA,CAACH,gBAAgB;MACfQ,MAAM,EAAEA,MAAO;MACfE,OAAO,EAAEA,OAAQ;MACjB4I,MAAM,EAAElE,eAAgB;MACxBmE,QAAQ,EAAElE,iBAAkB;MAC5BmE,cAAc,EAAEhE,kBAAmB;MACnC5D,WAAW,EAAEA,WAAY;MACzB6H,UAAU,EAAEC,IAAI,CAACC,IAAI,CAAC3H,UAAU,GAAGF,YAAY,CAAE;MACjDA,YAAY,EAAEA,YAAa;MAC3BE,UAAU,EAAEA,UAAW;MACvB4H,YAAY,EAAE3D,gBAAiB;MAC/B4D,oBAAoB,EAAE3D;IAAyB;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC,eAGFhH,OAAA,CAACF,iBAAiB;MAChB6J,MAAM,EAAE9I,SAAU;MAClB+I,OAAO,EAAEA,CAAA,KAAM9I,YAAY,CAAC,KAAK,CAAE;MACnC+I,MAAM,EAAErE,eAAgB;MACxBxB,KAAK,EAAEjD,YAAa;MACpBR,OAAO,EAAEU;IAAa;MAAA4F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC9G,EAAA,CAhiBID,eAAyB;EAAA,QACZP,YAAY,EACTC,cAAc;AAAA;AAAAmK,EAAA,GAF9B7J,eAAyB;AAkiB/B,eAAeA,eAAe;AAAC,IAAA6J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}