{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\admin\\\\CategoryManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { FolderTree, Plus, AlertTriangle, CheckCircle } from 'lucide-react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\nimport { categoryService } from '../../services/categoryService';\nimport CategoryList from '../../components/admin/CategoryList';\nimport CategoryModal from '../../components/admin/CategoryModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CategoryManagement = () => {\n  _s();\n  const {\n    user\n  } = useAdminAuth();\n  const permissions = usePermissions(user);\n\n  // State management\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n\n  // Modal state\n  const [showModal, setShowModal] = useState(false);\n  const [modalMode, setModalMode] = useState('add_category');\n  const [editingCategory, setEditingCategory] = useState(null);\n  const [editingSubcategory, setEditingSubcategory] = useState(null);\n  const [parentCategory, setParentCategory] = useState(null);\n  const [modalLoading, setModalLoading] = useState(false);\n  useEffect(() => {\n    // Check if user has permission to manage categories\n    if (!permissions.canManageCategories) {\n      setError('You do not have permission to manage categories');\n      setLoading(false);\n      return;\n    }\n    loadCategories();\n  }, [permissions.canManageCategories]);\n  const loadCategories = async () => {\n    try {\n      var _response$data;\n      setLoading(true);\n      setError(null);\n      console.log('🔍 CategoryManagement - Loading categories from API...');\n      const response = await categoryService.getCategoriesWithSubcategories();\n      console.log('🔍 CategoryManagement - API response:', response);\n      if (response.success && (_response$data = response.data) !== null && _response$data !== void 0 && _response$data.categories) {\n        setCategories(response.data.categories);\n        console.log('✅ CategoryManagement - Categories loaded successfully:', response.data.categories.length);\n      } else {\n        throw new Error(response.message || 'Failed to load categories');\n      }\n      const mockCategories = [{\n        category_id: 1,\n        name: 'Academic',\n        description: 'Academic-related announcements and events',\n        color_code: '#3b82f6',\n        is_active: true,\n        subcategories: [{\n          subcategory_id: 1,\n          name: 'Exams',\n          description: 'Examination schedules and updates',\n          color_code: '#ef4444',\n          display_order: 1,\n          is_active: true\n        }, {\n          subcategory_id: 2,\n          name: 'Assignments',\n          description: 'Assignment deadlines and submissions',\n          color_code: '#f59e0b',\n          display_order: 2,\n          is_active: true\n        }, {\n          subcategory_id: 3,\n          name: 'Class Schedules',\n          description: 'Class timing and schedule changes',\n          color_code: '#06b6d4',\n          display_order: 3,\n          is_active: true\n        }]\n      }, {\n        category_id: 2,\n        name: 'Events',\n        description: 'School events and activities',\n        color_code: '#10b981',\n        is_active: true,\n        subcategories: [{\n          subcategory_id: 4,\n          name: 'Sports',\n          description: 'Sports events and competitions',\n          color_code: '#8b5cf6',\n          display_order: 1,\n          is_active: true\n        }, {\n          subcategory_id: 5,\n          name: 'Cultural',\n          description: 'Cultural events and celebrations',\n          color_code: '#ec4899',\n          display_order: 2,\n          is_active: true\n        }]\n      }, {\n        category_id: 3,\n        name: 'Administrative',\n        description: 'Administrative notices and updates',\n        color_code: '#f97316',\n        is_active: true,\n        subcategories: [{\n          subcategory_id: 6,\n          name: 'Policies',\n          description: 'School policies and regulations',\n          color_code: '#6366f1',\n          display_order: 1,\n          is_active: true\n        }]\n      }, {\n        category_id: 4,\n        name: 'Emergency',\n        description: 'Emergency announcements and alerts',\n        color_code: '#dc2626',\n        is_active: true,\n        subcategories: []\n      }];\n      setCategories(mockCategories);\n    } catch (err) {\n      setError(err.message || 'Failed to load categories');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handler functions\n  const handleAddCategory = () => {\n    setModalMode('add_category');\n    setEditingCategory(null);\n    setEditingSubcategory(null);\n    setParentCategory(null);\n    setShowModal(true);\n  };\n  const handleEditCategory = category => {\n    setModalMode('edit_category');\n    setEditingCategory(category);\n    setEditingSubcategory(null);\n    setParentCategory(null);\n    setShowModal(true);\n  };\n  const handleDeleteCategory = async category => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await categoryService.deleteCategory(category.category_id!);\n\n      setSuccess(`Category \"${category.name}\" has been deleted successfully`);\n      loadCategories();\n    } catch (err) {\n      setError(err.message || 'Failed to delete category');\n    }\n  };\n  const handleToggleCategoryStatus = async category => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await categoryService.toggleCategoryStatus(category.category_id!, !category.is_active);\n\n      const action = category.is_active ? 'deactivated' : 'activated';\n      setSuccess(`Category \"${category.name}\" has been ${action} successfully`);\n      loadCategories();\n    } catch (err) {\n      setError(err.message || 'Failed to update category status');\n    }\n  };\n  const handleAddSubcategory = category => {\n    setModalMode('add_subcategory');\n    setEditingCategory(null);\n    setEditingSubcategory(null);\n    setParentCategory(category);\n    setShowModal(true);\n  };\n  const handleEditSubcategory = (category, subcategory) => {\n    setModalMode('edit_subcategory');\n    setEditingCategory(null);\n    setEditingSubcategory(subcategory);\n    setParentCategory(category);\n    setShowModal(true);\n  };\n  const handleDeleteSubcategory = async (category, subcategory) => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await categoryService.deleteSubcategory(category.category_id!, subcategory.subcategory_id!);\n\n      setSuccess(`Subcategory \"${subcategory.name}\" has been deleted successfully`);\n      loadCategories();\n    } catch (err) {\n      setError(err.message || 'Failed to delete subcategory');\n    }\n  };\n  const handleToggleSubcategoryStatus = async (category, subcategory) => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await categoryService.toggleSubcategoryStatus(category.category_id!, subcategory.subcategory_id!, !subcategory.is_active);\n\n      const action = subcategory.is_active ? 'deactivated' : 'activated';\n      setSuccess(`Subcategory \"${subcategory.name}\" has been ${action} successfully`);\n      loadCategories();\n    } catch (err) {\n      setError(err.message || 'Failed to update subcategory status');\n    }\n  };\n  const handleSave = async (data, parentCat) => {\n    try {\n      setModalLoading(true);\n      setError(null);\n      setSuccess(null);\n      if (modalMode === 'add_category') {\n        // TODO: Implement actual API call\n        // await categoryService.createCategory(data as Category);\n        setSuccess(`Category \"${data.name}\" has been created successfully`);\n      } else if (modalMode === 'edit_category') {\n        // TODO: Implement actual API call\n        // await categoryService.updateCategory(editingCategory!.category_id!, data as Category);\n        setSuccess(`Category \"${data.name}\" has been updated successfully`);\n      } else if (modalMode === 'add_subcategory') {\n        // TODO: Implement actual API call\n        // await categoryService.createSubcategory(parentCat!.category_id!, data as Subcategory);\n        setSuccess(`Subcategory \"${data.name}\" has been created successfully`);\n      } else if (modalMode === 'edit_subcategory') {\n        // TODO: Implement actual API call\n        // await categoryService.updateSubcategory(parentCat!.category_id!, editingSubcategory!.subcategory_id!, data as Subcategory);\n        setSuccess(`Subcategory \"${data.name}\" has been updated successfully`);\n      }\n      loadCategories();\n    } catch (err) {\n      setError(err.message || 'Failed to save changes');\n      throw err; // Re-throw to prevent modal from closing\n    } finally {\n      setModalLoading(false);\n    }\n  };\n  const clearMessages = () => {\n    setError(null);\n    setSuccess(null);\n  };\n  if (!permissions.canManageCategories) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      },\n      children: [/*#__PURE__*/_jsxDEV(FolderTree, {\n        size: 64,\n        style: {\n          marginBottom: '1rem',\n          opacity: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          margin: '0 0 0.5rem',\n          fontSize: '1.5rem',\n          fontWeight: '600'\n        },\n        children: \"Access Denied\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: 0,\n          fontSize: '1rem'\n        },\n        children: \"You do not have permission to manage categories and subcategories.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        },\n        children: [\"Current Role: \", permissions.getPositionDisplayName()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '40px',\n          height: '40px',\n          border: '4px solid #f3f4f6',\n          borderTop: '4px solid #3b82f6',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 338,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '2rem',\n        textAlign: 'center',\n        color: '#ef4444'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Error\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: loadCategories,\n        style: {\n          padding: '0.5rem 1rem',\n          background: '#3b82f6',\n          color: 'white',\n          border: 'none',\n          borderRadius: '6px',\n          cursor: 'pointer'\n        },\n        children: \"Retry\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 358,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      maxWidth: '1400px',\n      margin: '0 auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            margin: '0 0 0.5rem',\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          },\n          children: \"Category Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          },\n          children: \"Manage categories and subcategories for announcements and events\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleAddCategory,\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          padding: '0.75rem 1.5rem',\n          background: '#3b82f6',\n          color: 'white',\n          border: 'none',\n          borderRadius: '8px',\n          fontSize: '0.875rem',\n          fontWeight: '600',\n          cursor: 'pointer',\n          transition: 'background-color 0.2s'\n        },\n        onMouseEnter: e => e.currentTarget.style.background = '#2563eb',\n        onMouseLeave: e => e.currentTarget.style.background = '#3b82f6',\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 11\n        }, this), \"Add Category\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 409,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 385,\n      columnNumber: 7\n    }, this), (error || success) && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '1.5rem'\n      },\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '1rem',\n          background: '#fef2f2',\n          border: '1px solid #fecaca',\n          borderRadius: '8px',\n          color: '#dc2626',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 17\n          }, this), error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: clearMessages,\n          style: {\n            background: 'transparent',\n            border: 'none',\n            color: '#dc2626',\n            cursor: 'pointer',\n            padding: '0.25rem'\n          },\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 437,\n        columnNumber: 13\n      }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '1rem',\n          background: '#f0fdf4',\n          border: '1px solid #bbf7d0',\n          borderRadius: '8px',\n          color: '#166534',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 17\n          }, this), success]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: clearMessages,\n          style: {\n            background: 'transparent',\n            border: 'none',\n            color: '#166534',\n            cursor: 'pointer',\n            padding: '0.25rem'\n          },\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 481,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 435,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(CategoryList, {\n      categories: categories,\n      loading: loading,\n      onEditCategory: handleEditCategory,\n      onDeleteCategory: handleDeleteCategory,\n      onToggleCategoryStatus: handleToggleCategoryStatus,\n      onAddSubcategory: handleAddSubcategory,\n      onEditSubcategory: handleEditSubcategory,\n      onDeleteSubcategory: handleDeleteSubcategory,\n      onToggleSubcategoryStatus: handleToggleSubcategoryStatus\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 499,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CategoryModal, {\n      isOpen: showModal,\n      onClose: () => setShowModal(false),\n      onSave: handleSave,\n      category: editingCategory,\n      subcategory: editingSubcategory,\n      parentCategory: parentCategory,\n      mode: modalMode,\n      loading: modalLoading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 512,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 383,\n    columnNumber: 5\n  }, this);\n};\n_s(CategoryManagement, \"Fi2eW2xY3Tqt2uCjsGG8TTkc8Lc=\", false, function () {\n  return [useAdminAuth, usePermissions];\n});\n_c = CategoryManagement;\nexport default CategoryManagement;\nvar _c;\n$RefreshReg$(_c, \"CategoryManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FolderTree", "Plus", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CheckCircle", "useAdminAuth", "usePermissions", "categoryService", "CategoryList", "CategoryModal", "jsxDEV", "_jsxDEV", "CategoryManagement", "_s", "user", "permissions", "categories", "setCategories", "loading", "setLoading", "error", "setError", "success", "setSuccess", "showModal", "setShowModal", "modalMode", "setModalMode", "editingCategory", "setEditingCategory", "editingSubcategory", "setEditingSubcategory", "parentCategory", "setParentCategory", "modalLoading", "setModalLoading", "canManageCategories", "loadCategories", "_response$data", "console", "log", "response", "getCategoriesWithSubcategories", "data", "length", "Error", "message", "mockCategories", "category_id", "name", "description", "color_code", "is_active", "subcategories", "subcategory_id", "display_order", "err", "handleAddCategory", "handleEditCategory", "category", "handleDeleteCategory", "handleToggleCategoryStatus", "action", "handleAddSubcategory", "handleEditSubcategory", "subcategory", "handleDeleteSubcategory", "handleToggleSubcategoryStatus", "handleSave", "parentCat", "clearMessages", "style", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "textAlign", "color", "children", "size", "marginBottom", "opacity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "margin", "fontSize", "fontWeight", "marginTop", "padding", "background", "getPositionBadgeColor", "borderRadius", "getPositionDisplayName", "width", "height", "border", "borderTop", "animation", "onClick", "cursor", "max<PERSON><PERSON><PERSON>", "gap", "transition", "onMouseEnter", "e", "currentTarget", "onMouseLeave", "onEditCategory", "onDeleteCategory", "onToggleCategoryStatus", "onAddSubcategory", "onEditSubcategory", "onDeleteSubcategory", "onToggleSubcategoryStatus", "isOpen", "onClose", "onSave", "mode", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/admin/CategoryManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { FolderTree, Plus, AlertTriangle, CheckCircle } from 'lucide-react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\nimport { categoryService } from '../../services/categoryService';\nimport CategoryList from '../../components/admin/CategoryList';\nimport CategoryModal from '../../components/admin/CategoryModal';\n\ninterface Category {\n  category_id?: number;\n  name: string;\n  description?: string;\n  color_code: string;\n  is_active: boolean;\n  subcategories?: Subcategory[];\n}\n\ninterface Subcategory {\n  subcategory_id?: number;\n  name: string;\n  description?: string;\n  color_code: string;\n  display_order: number;\n  is_active: boolean;\n}\n\nconst CategoryManagement: React.FC = () => {\n  const { user } = useAdminAuth();\n  const permissions = usePermissions(user);\n\n  // State management\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  // Modal state\n  const [showModal, setShowModal] = useState(false);\n  const [modalMode, setModalMode] = useState<'add_category' | 'edit_category' | 'add_subcategory' | 'edit_subcategory'>('add_category');\n  const [editingCategory, setEditingCategory] = useState<Category | null>(null);\n  const [editingSubcategory, setEditingSubcategory] = useState<Subcategory | null>(null);\n  const [parentCategory, setParentCategory] = useState<Category | null>(null);\n  const [modalLoading, setModalLoading] = useState(false);\n\n  useEffect(() => {\n    // Check if user has permission to manage categories\n    if (!permissions.canManageCategories) {\n      setError('You do not have permission to manage categories');\n      setLoading(false);\n      return;\n    }\n\n    loadCategories();\n  }, [permissions.canManageCategories]);\n\n  const loadCategories = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log('🔍 CategoryManagement - Loading categories from API...');\n      const response = await categoryService.getCategoriesWithSubcategories();\n      console.log('🔍 CategoryManagement - API response:', response);\n\n      if (response.success && response.data?.categories) {\n        setCategories(response.data.categories);\n        console.log('✅ CategoryManagement - Categories loaded successfully:', response.data.categories.length);\n      } else {\n        throw new Error(response.message || 'Failed to load categories');\n      }\n      const mockCategories: Category[] = [\n        {\n          category_id: 1,\n          name: 'Academic',\n          description: 'Academic-related announcements and events',\n          color_code: '#3b82f6',\n          is_active: true,\n          subcategories: [\n            {\n              subcategory_id: 1,\n              name: 'Exams',\n              description: 'Examination schedules and updates',\n              color_code: '#ef4444',\n              display_order: 1,\n              is_active: true\n            },\n            {\n              subcategory_id: 2,\n              name: 'Assignments',\n              description: 'Assignment deadlines and submissions',\n              color_code: '#f59e0b',\n              display_order: 2,\n              is_active: true\n            },\n            {\n              subcategory_id: 3,\n              name: 'Class Schedules',\n              description: 'Class timing and schedule changes',\n              color_code: '#06b6d4',\n              display_order: 3,\n              is_active: true\n            }\n          ]\n        },\n        {\n          category_id: 2,\n          name: 'Events',\n          description: 'School events and activities',\n          color_code: '#10b981',\n          is_active: true,\n          subcategories: [\n            {\n              subcategory_id: 4,\n              name: 'Sports',\n              description: 'Sports events and competitions',\n              color_code: '#8b5cf6',\n              display_order: 1,\n              is_active: true\n            },\n            {\n              subcategory_id: 5,\n              name: 'Cultural',\n              description: 'Cultural events and celebrations',\n              color_code: '#ec4899',\n              display_order: 2,\n              is_active: true\n            }\n          ]\n        },\n        {\n          category_id: 3,\n          name: 'Administrative',\n          description: 'Administrative notices and updates',\n          color_code: '#f97316',\n          is_active: true,\n          subcategories: [\n            {\n              subcategory_id: 6,\n              name: 'Policies',\n              description: 'School policies and regulations',\n              color_code: '#6366f1',\n              display_order: 1,\n              is_active: true\n            }\n          ]\n        },\n        {\n          category_id: 4,\n          name: 'Emergency',\n          description: 'Emergency announcements and alerts',\n          color_code: '#dc2626',\n          is_active: true,\n          subcategories: []\n        }\n      ];\n\n      setCategories(mockCategories);\n    } catch (err: any) {\n      setError(err.message || 'Failed to load categories');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handler functions\n  const handleAddCategory = () => {\n    setModalMode('add_category');\n    setEditingCategory(null);\n    setEditingSubcategory(null);\n    setParentCategory(null);\n    setShowModal(true);\n  };\n\n  const handleEditCategory = (category: Category) => {\n    setModalMode('edit_category');\n    setEditingCategory(category);\n    setEditingSubcategory(null);\n    setParentCategory(null);\n    setShowModal(true);\n  };\n\n  const handleDeleteCategory = async (category: Category) => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await categoryService.deleteCategory(category.category_id!);\n\n      setSuccess(`Category \"${category.name}\" has been deleted successfully`);\n      loadCategories();\n\n    } catch (err: any) {\n      setError(err.message || 'Failed to delete category');\n    }\n  };\n\n  const handleToggleCategoryStatus = async (category: Category) => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await categoryService.toggleCategoryStatus(category.category_id!, !category.is_active);\n\n      const action = category.is_active ? 'deactivated' : 'activated';\n      setSuccess(`Category \"${category.name}\" has been ${action} successfully`);\n      loadCategories();\n\n    } catch (err: any) {\n      setError(err.message || 'Failed to update category status');\n    }\n  };\n\n  const handleAddSubcategory = (category: Category) => {\n    setModalMode('add_subcategory');\n    setEditingCategory(null);\n    setEditingSubcategory(null);\n    setParentCategory(category);\n    setShowModal(true);\n  };\n\n  const handleEditSubcategory = (category: Category, subcategory: Subcategory) => {\n    setModalMode('edit_subcategory');\n    setEditingCategory(null);\n    setEditingSubcategory(subcategory);\n    setParentCategory(category);\n    setShowModal(true);\n  };\n\n  const handleDeleteSubcategory = async (category: Category, subcategory: Subcategory) => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await categoryService.deleteSubcategory(category.category_id!, subcategory.subcategory_id!);\n\n      setSuccess(`Subcategory \"${subcategory.name}\" has been deleted successfully`);\n      loadCategories();\n\n    } catch (err: any) {\n      setError(err.message || 'Failed to delete subcategory');\n    }\n  };\n\n  const handleToggleSubcategoryStatus = async (category: Category, subcategory: Subcategory) => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await categoryService.toggleSubcategoryStatus(category.category_id!, subcategory.subcategory_id!, !subcategory.is_active);\n\n      const action = subcategory.is_active ? 'deactivated' : 'activated';\n      setSuccess(`Subcategory \"${subcategory.name}\" has been ${action} successfully`);\n      loadCategories();\n\n    } catch (err: any) {\n      setError(err.message || 'Failed to update subcategory status');\n    }\n  };\n\n  const handleSave = async (data: Category | Subcategory, parentCat?: Category) => {\n    try {\n      setModalLoading(true);\n      setError(null);\n      setSuccess(null);\n\n      if (modalMode === 'add_category') {\n        // TODO: Implement actual API call\n        // await categoryService.createCategory(data as Category);\n        setSuccess(`Category \"${data.name}\" has been created successfully`);\n      } else if (modalMode === 'edit_category') {\n        // TODO: Implement actual API call\n        // await categoryService.updateCategory(editingCategory!.category_id!, data as Category);\n        setSuccess(`Category \"${data.name}\" has been updated successfully`);\n      } else if (modalMode === 'add_subcategory') {\n        // TODO: Implement actual API call\n        // await categoryService.createSubcategory(parentCat!.category_id!, data as Subcategory);\n        setSuccess(`Subcategory \"${data.name}\" has been created successfully`);\n      } else if (modalMode === 'edit_subcategory') {\n        // TODO: Implement actual API call\n        // await categoryService.updateSubcategory(parentCat!.category_id!, editingSubcategory!.subcategory_id!, data as Subcategory);\n        setSuccess(`Subcategory \"${data.name}\" has been updated successfully`);\n      }\n\n      loadCategories();\n\n    } catch (err: any) {\n      setError(err.message || 'Failed to save changes');\n      throw err; // Re-throw to prevent modal from closing\n    } finally {\n      setModalLoading(false);\n    }\n  };\n\n  const clearMessages = () => {\n    setError(null);\n    setSuccess(null);\n  };\n\n  if (!permissions.canManageCategories) {\n    return (\n      <div style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      }}>\n        <FolderTree size={64} style={{ marginBottom: '1rem', opacity: 0.5 }} />\n        <h2 style={{ margin: '0 0 0.5rem', fontSize: '1.5rem', fontWeight: '600' }}>\n          Access Denied\n        </h2>\n        <p style={{ margin: 0, fontSize: '1rem' }}>\n          You do not have permission to manage categories and subcategories.\n        </p>\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        }}>\n          Current Role: {permissions.getPositionDisplayName()}\n        </div>\n      </div>\n    );\n  }\n\n  if (loading) {\n    return (\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px'\n      }}>\n        <div style={{\n          width: '40px',\n          height: '40px',\n          border: '4px solid #f3f4f6',\n          borderTop: '4px solid #3b82f6',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }} />\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div style={{\n        padding: '2rem',\n        textAlign: 'center',\n        color: '#ef4444'\n      }}>\n        <h2>Error</h2>\n        <p>{error}</p>\n        <button\n          onClick={loadCategories}\n          style={{\n            padding: '0.5rem 1rem',\n            background: '#3b82f6',\n            color: 'white',\n            border: 'none',\n            borderRadius: '6px',\n            cursor: 'pointer'\n          }}\n        >\n          Retry\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ maxWidth: '1400px', margin: '0 auto' }}>\n      {/* Header */}\n      <div style={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      }}>\n        <div>\n          <h1 style={{\n            margin: '0 0 0.5rem',\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          }}>\n            Category Management\n          </h1>\n          <p style={{\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          }}>\n            Manage categories and subcategories for announcements and events\n          </p>\n        </div>\n\n        <button\n          onClick={handleAddCategory}\n          style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            padding: '0.75rem 1.5rem',\n            background: '#3b82f6',\n            color: 'white',\n            border: 'none',\n            borderRadius: '8px',\n            fontSize: '0.875rem',\n            fontWeight: '600',\n            cursor: 'pointer',\n            transition: 'background-color 0.2s'\n          }}\n          onMouseEnter={(e) => e.currentTarget.style.background = '#2563eb'}\n          onMouseLeave={(e) => e.currentTarget.style.background = '#3b82f6'}\n        >\n          <Plus size={16} />\n          Add Category\n        </button>\n      </div>\n\n      {/* Success/Error Messages */}\n      {(error || success) && (\n        <div style={{ marginBottom: '1.5rem' }}>\n          {error && (\n            <div style={{\n              padding: '1rem',\n              background: '#fef2f2',\n              border: '1px solid #fecaca',\n              borderRadius: '8px',\n              color: '#dc2626',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            }}>\n              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                <AlertTriangle size={16} />\n                {error}\n              </div>\n              <button\n                onClick={clearMessages}\n                style={{\n                  background: 'transparent',\n                  border: 'none',\n                  color: '#dc2626',\n                  cursor: 'pointer',\n                  padding: '0.25rem'\n                }}\n              >\n                ×\n              </button>\n            </div>\n          )}\n\n          {success && (\n            <div style={{\n              padding: '1rem',\n              background: '#f0fdf4',\n              border: '1px solid #bbf7d0',\n              borderRadius: '8px',\n              color: '#166534',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            }}>\n              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                <CheckCircle size={16} />\n                {success}\n              </div>\n              <button\n                onClick={clearMessages}\n                style={{\n                  background: 'transparent',\n                  border: 'none',\n                  color: '#166534',\n                  cursor: 'pointer',\n                  padding: '0.25rem'\n                }}\n              >\n                ×\n              </button>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Category List */}\n      <CategoryList\n        categories={categories}\n        loading={loading}\n        onEditCategory={handleEditCategory}\n        onDeleteCategory={handleDeleteCategory}\n        onToggleCategoryStatus={handleToggleCategoryStatus}\n        onAddSubcategory={handleAddSubcategory}\n        onEditSubcategory={handleEditSubcategory}\n        onDeleteSubcategory={handleDeleteSubcategory}\n        onToggleSubcategoryStatus={handleToggleSubcategoryStatus}\n      />\n\n      {/* Category Modal */}\n      <CategoryModal\n        isOpen={showModal}\n        onClose={() => setShowModal(false)}\n        onSave={handleSave}\n        category={editingCategory}\n        subcategory={editingSubcategory}\n        parentCategory={parentCategory}\n        mode={modalMode}\n        loading={modalLoading}\n      />\n    </div>\n  );\n};\n\nexport default CategoryManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,UAAU,EAAEC,IAAI,EAAEC,aAAa,EAAEC,WAAW,QAAQ,cAAc;AAC3E,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,gCAAgC;AAChE,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,OAAOC,aAAa,MAAM,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAoBjE,MAAMC,kBAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM;IAAEC;EAAK,CAAC,GAAGT,YAAY,CAAC,CAAC;EAC/B,MAAMU,WAAW,GAAGT,cAAc,CAACQ,IAAI,CAAC;;EAExC;EACA,MAAM,CAACE,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAa,EAAE,CAAC;EAC5D,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAgB,IAAI,CAAC;;EAE3D;EACA,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAA4E,cAAc,CAAC;EACrI,MAAM,CAAC6B,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAkB,IAAI,CAAC;EAC7E,MAAM,CAAC+B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhC,QAAQ,CAAqB,IAAI,CAAC;EACtF,MAAM,CAACiC,cAAc,EAAEC,iBAAiB,CAAC,GAAGlC,QAAQ,CAAkB,IAAI,CAAC;EAC3E,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACe,WAAW,CAACqB,mBAAmB,EAAE;MACpCf,QAAQ,CAAC,iDAAiD,CAAC;MAC3DF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEAkB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACtB,WAAW,CAACqB,mBAAmB,CAAC,CAAC;EAErC,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MAAA,IAAAC,cAAA;MACFnB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEdkB,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;MACrE,MAAMC,QAAQ,GAAG,MAAMlC,eAAe,CAACmC,8BAA8B,CAAC,CAAC;MACvEH,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEC,QAAQ,CAAC;MAE9D,IAAIA,QAAQ,CAACnB,OAAO,KAAAgB,cAAA,GAAIG,QAAQ,CAACE,IAAI,cAAAL,cAAA,eAAbA,cAAA,CAAetB,UAAU,EAAE;QACjDC,aAAa,CAACwB,QAAQ,CAACE,IAAI,CAAC3B,UAAU,CAAC;QACvCuB,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAEC,QAAQ,CAACE,IAAI,CAAC3B,UAAU,CAAC4B,MAAM,CAAC;MACxG,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAACJ,QAAQ,CAACK,OAAO,IAAI,2BAA2B,CAAC;MAClE;MACA,MAAMC,cAA0B,GAAG,CACjC;QACEC,WAAW,EAAE,CAAC;QACdC,IAAI,EAAE,UAAU;QAChBC,WAAW,EAAE,2CAA2C;QACxDC,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,IAAI;QACfC,aAAa,EAAE,CACb;UACEC,cAAc,EAAE,CAAC;UACjBL,IAAI,EAAE,OAAO;UACbC,WAAW,EAAE,mCAAmC;UAChDC,UAAU,EAAE,SAAS;UACrBI,aAAa,EAAE,CAAC;UAChBH,SAAS,EAAE;QACb,CAAC,EACD;UACEE,cAAc,EAAE,CAAC;UACjBL,IAAI,EAAE,aAAa;UACnBC,WAAW,EAAE,sCAAsC;UACnDC,UAAU,EAAE,SAAS;UACrBI,aAAa,EAAE,CAAC;UAChBH,SAAS,EAAE;QACb,CAAC,EACD;UACEE,cAAc,EAAE,CAAC;UACjBL,IAAI,EAAE,iBAAiB;UACvBC,WAAW,EAAE,mCAAmC;UAChDC,UAAU,EAAE,SAAS;UACrBI,aAAa,EAAE,CAAC;UAChBH,SAAS,EAAE;QACb,CAAC;MAEL,CAAC,EACD;QACEJ,WAAW,EAAE,CAAC;QACdC,IAAI,EAAE,QAAQ;QACdC,WAAW,EAAE,8BAA8B;QAC3CC,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,IAAI;QACfC,aAAa,EAAE,CACb;UACEC,cAAc,EAAE,CAAC;UACjBL,IAAI,EAAE,QAAQ;UACdC,WAAW,EAAE,gCAAgC;UAC7CC,UAAU,EAAE,SAAS;UACrBI,aAAa,EAAE,CAAC;UAChBH,SAAS,EAAE;QACb,CAAC,EACD;UACEE,cAAc,EAAE,CAAC;UACjBL,IAAI,EAAE,UAAU;UAChBC,WAAW,EAAE,kCAAkC;UAC/CC,UAAU,EAAE,SAAS;UACrBI,aAAa,EAAE,CAAC;UAChBH,SAAS,EAAE;QACb,CAAC;MAEL,CAAC,EACD;QACEJ,WAAW,EAAE,CAAC;QACdC,IAAI,EAAE,gBAAgB;QACtBC,WAAW,EAAE,oCAAoC;QACjDC,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,IAAI;QACfC,aAAa,EAAE,CACb;UACEC,cAAc,EAAE,CAAC;UACjBL,IAAI,EAAE,UAAU;UAChBC,WAAW,EAAE,iCAAiC;UAC9CC,UAAU,EAAE,SAAS;UACrBI,aAAa,EAAE,CAAC;UAChBH,SAAS,EAAE;QACb,CAAC;MAEL,CAAC,EACD;QACEJ,WAAW,EAAE,CAAC;QACdC,IAAI,EAAE,WAAW;QACjBC,WAAW,EAAE,oCAAoC;QACjDC,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,IAAI;QACfC,aAAa,EAAE;MACjB,CAAC,CACF;MAEDpC,aAAa,CAAC8B,cAAc,CAAC;IAC/B,CAAC,CAAC,OAAOS,GAAQ,EAAE;MACjBnC,QAAQ,CAACmC,GAAG,CAACV,OAAO,IAAI,2BAA2B,CAAC;IACtD,CAAC,SAAS;MACR3B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMsC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B9B,YAAY,CAAC,cAAc,CAAC;IAC5BE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,qBAAqB,CAAC,IAAI,CAAC;IAC3BE,iBAAiB,CAAC,IAAI,CAAC;IACvBR,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMiC,kBAAkB,GAAIC,QAAkB,IAAK;IACjDhC,YAAY,CAAC,eAAe,CAAC;IAC7BE,kBAAkB,CAAC8B,QAAQ,CAAC;IAC5B5B,qBAAqB,CAAC,IAAI,CAAC;IAC3BE,iBAAiB,CAAC,IAAI,CAAC;IACvBR,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMmC,oBAAoB,GAAG,MAAOD,QAAkB,IAAK;IACzD,IAAI;MACFtC,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA;;MAEAA,UAAU,CAAC,aAAaoC,QAAQ,CAACV,IAAI,iCAAiC,CAAC;MACvEZ,cAAc,CAAC,CAAC;IAElB,CAAC,CAAC,OAAOmB,GAAQ,EAAE;MACjBnC,QAAQ,CAACmC,GAAG,CAACV,OAAO,IAAI,2BAA2B,CAAC;IACtD;EACF,CAAC;EAED,MAAMe,0BAA0B,GAAG,MAAOF,QAAkB,IAAK;IAC/D,IAAI;MACFtC,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA;;MAEA,MAAMuC,MAAM,GAAGH,QAAQ,CAACP,SAAS,GAAG,aAAa,GAAG,WAAW;MAC/D7B,UAAU,CAAC,aAAaoC,QAAQ,CAACV,IAAI,cAAca,MAAM,eAAe,CAAC;MACzEzB,cAAc,CAAC,CAAC;IAElB,CAAC,CAAC,OAAOmB,GAAQ,EAAE;MACjBnC,QAAQ,CAACmC,GAAG,CAACV,OAAO,IAAI,kCAAkC,CAAC;IAC7D;EACF,CAAC;EAED,MAAMiB,oBAAoB,GAAIJ,QAAkB,IAAK;IACnDhC,YAAY,CAAC,iBAAiB,CAAC;IAC/BE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,qBAAqB,CAAC,IAAI,CAAC;IAC3BE,iBAAiB,CAAC0B,QAAQ,CAAC;IAC3BlC,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMuC,qBAAqB,GAAGA,CAACL,QAAkB,EAAEM,WAAwB,KAAK;IAC9EtC,YAAY,CAAC,kBAAkB,CAAC;IAChCE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,qBAAqB,CAACkC,WAAW,CAAC;IAClChC,iBAAiB,CAAC0B,QAAQ,CAAC;IAC3BlC,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMyC,uBAAuB,GAAG,MAAAA,CAAOP,QAAkB,EAAEM,WAAwB,KAAK;IACtF,IAAI;MACF5C,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA;;MAEAA,UAAU,CAAC,gBAAgB0C,WAAW,CAAChB,IAAI,iCAAiC,CAAC;MAC7EZ,cAAc,CAAC,CAAC;IAElB,CAAC,CAAC,OAAOmB,GAAQ,EAAE;MACjBnC,QAAQ,CAACmC,GAAG,CAACV,OAAO,IAAI,8BAA8B,CAAC;IACzD;EACF,CAAC;EAED,MAAMqB,6BAA6B,GAAG,MAAAA,CAAOR,QAAkB,EAAEM,WAAwB,KAAK;IAC5F,IAAI;MACF5C,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA;;MAEA,MAAMuC,MAAM,GAAGG,WAAW,CAACb,SAAS,GAAG,aAAa,GAAG,WAAW;MAClE7B,UAAU,CAAC,gBAAgB0C,WAAW,CAAChB,IAAI,cAAca,MAAM,eAAe,CAAC;MAC/EzB,cAAc,CAAC,CAAC;IAElB,CAAC,CAAC,OAAOmB,GAAQ,EAAE;MACjBnC,QAAQ,CAACmC,GAAG,CAACV,OAAO,IAAI,qCAAqC,CAAC;IAChE;EACF,CAAC;EAED,MAAMsB,UAAU,GAAG,MAAAA,CAAOzB,IAA4B,EAAE0B,SAAoB,KAAK;IAC/E,IAAI;MACFlC,eAAe,CAAC,IAAI,CAAC;MACrBd,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIG,SAAS,KAAK,cAAc,EAAE;QAChC;QACA;QACAH,UAAU,CAAC,aAAaoB,IAAI,CAACM,IAAI,iCAAiC,CAAC;MACrE,CAAC,MAAM,IAAIvB,SAAS,KAAK,eAAe,EAAE;QACxC;QACA;QACAH,UAAU,CAAC,aAAaoB,IAAI,CAACM,IAAI,iCAAiC,CAAC;MACrE,CAAC,MAAM,IAAIvB,SAAS,KAAK,iBAAiB,EAAE;QAC1C;QACA;QACAH,UAAU,CAAC,gBAAgBoB,IAAI,CAACM,IAAI,iCAAiC,CAAC;MACxE,CAAC,MAAM,IAAIvB,SAAS,KAAK,kBAAkB,EAAE;QAC3C;QACA;QACAH,UAAU,CAAC,gBAAgBoB,IAAI,CAACM,IAAI,iCAAiC,CAAC;MACxE;MAEAZ,cAAc,CAAC,CAAC;IAElB,CAAC,CAAC,OAAOmB,GAAQ,EAAE;MACjBnC,QAAQ,CAACmC,GAAG,CAACV,OAAO,IAAI,wBAAwB,CAAC;MACjD,MAAMU,GAAG,CAAC,CAAC;IACb,CAAC,SAAS;MACRrB,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMmC,aAAa,GAAGA,CAAA,KAAM;IAC1BjD,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;EAClB,CAAC;EAED,IAAI,CAACR,WAAW,CAACqB,mBAAmB,EAAE;IACpC,oBACEzB,OAAA;MAAK4D,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE,OAAO;QAClBC,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACApE,OAAA,CAACV,UAAU;QAAC+E,IAAI,EAAE,EAAG;QAACT,KAAK,EAAE;UAAEU,YAAY,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAI;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvE3E,OAAA;QAAI4D,KAAK,EAAE;UAAEgB,MAAM,EAAE,YAAY;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAM,CAAE;QAAAV,QAAA,EAAC;MAE5E;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL3E,OAAA;QAAG4D,KAAK,EAAE;UAAEgB,MAAM,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAT,QAAA,EAAC;MAE3C;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJ3E,OAAA;QAAK4D,KAAK,EAAE;UACVmB,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAE7E,WAAW,CAAC8E,qBAAqB,CAAC,CAAC;UAC/CC,YAAY,EAAE,KAAK;UACnBhB,KAAK,EAAE,OAAO;UACdU,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE;QACd,CAAE;QAAAV,QAAA,GAAC,gBACa,EAAChE,WAAW,CAACgF,sBAAsB,CAAC,CAAC;MAAA;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIpE,OAAO,EAAE;IACX,oBACEP,OAAA;MAAK4D,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE;MACb,CAAE;MAAAG,QAAA,eACApE,OAAA;QAAK4D,KAAK,EAAE;UACVyB,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,mBAAmB;UAC3BC,SAAS,EAAE,mBAAmB;UAC9BL,YAAY,EAAE,KAAK;UACnBM,SAAS,EAAE;QACb;MAAE;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEV;EAEA,IAAIlE,KAAK,EAAE;IACT,oBACET,OAAA;MAAK4D,KAAK,EAAE;QACVoB,OAAO,EAAE,MAAM;QACfd,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACApE,OAAA;QAAAoE,QAAA,EAAI;MAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACd3E,OAAA;QAAAoE,QAAA,EAAI3D;MAAK;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACd3E,OAAA;QACE0F,OAAO,EAAEhE,cAAe;QACxBkC,KAAK,EAAE;UACLoB,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAE,SAAS;UACrBd,KAAK,EAAE,OAAO;UACdoB,MAAM,EAAE,MAAM;UACdJ,YAAY,EAAE,KAAK;UACnBQ,MAAM,EAAE;QACV,CAAE;QAAAvB,QAAA,EACH;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACE3E,OAAA;IAAK4D,KAAK,EAAE;MAAEgC,QAAQ,EAAE,QAAQ;MAAEhB,MAAM,EAAE;IAAS,CAAE;IAAAR,QAAA,gBAEnDpE,OAAA;MAAK4D,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfG,cAAc,EAAE,eAAe;QAC/BD,UAAU,EAAE,QAAQ;QACpBO,YAAY,EAAE;MAChB,CAAE;MAAAF,QAAA,gBACApE,OAAA;QAAAoE,QAAA,gBACEpE,OAAA;UAAI4D,KAAK,EAAE;YACTgB,MAAM,EAAE,YAAY;YACpBC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjBX,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,EAAC;QAEH;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL3E,OAAA;UAAG4D,KAAK,EAAE;YACRgB,MAAM,EAAE,CAAC;YACTT,KAAK,EAAE,SAAS;YAChBU,QAAQ,EAAE;UACZ,CAAE;UAAAT,QAAA,EAAC;QAEH;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN3E,OAAA;QACE0F,OAAO,EAAE5C,iBAAkB;QAC3Bc,KAAK,EAAE;UACLC,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpB8B,GAAG,EAAE,QAAQ;UACbb,OAAO,EAAE,gBAAgB;UACzBC,UAAU,EAAE,SAAS;UACrBd,KAAK,EAAE,OAAO;UACdoB,MAAM,EAAE,MAAM;UACdJ,YAAY,EAAE,KAAK;UACnBN,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE,KAAK;UACjBa,MAAM,EAAE,SAAS;UACjBG,UAAU,EAAE;QACd,CAAE;QACFC,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACrC,KAAK,CAACqB,UAAU,GAAG,SAAU;QAClEiB,YAAY,EAAGF,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACrC,KAAK,CAACqB,UAAU,GAAG,SAAU;QAAAb,QAAA,gBAElEpE,OAAA,CAACT,IAAI;UAAC8E,IAAI,EAAE;QAAG;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEpB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGL,CAAClE,KAAK,IAAIE,OAAO,kBAChBX,OAAA;MAAK4D,KAAK,EAAE;QAAEU,YAAY,EAAE;MAAS,CAAE;MAAAF,QAAA,GACpC3D,KAAK,iBACJT,OAAA;QAAK4D,KAAK,EAAE;UACVoB,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,SAAS;UACrBM,MAAM,EAAE,mBAAmB;UAC3BJ,YAAY,EAAE,KAAK;UACnBhB,KAAK,EAAE,SAAS;UAChBN,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE;QAClB,CAAE;QAAAI,QAAA,gBACApE,OAAA;UAAK4D,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAE8B,GAAG,EAAE;UAAS,CAAE;UAAAzB,QAAA,gBACnEpE,OAAA,CAACR,aAAa;YAAC6E,IAAI,EAAE;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAC1BlE,KAAK;QAAA;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3E,OAAA;UACE0F,OAAO,EAAE/B,aAAc;UACvBC,KAAK,EAAE;YACLqB,UAAU,EAAE,aAAa;YACzBM,MAAM,EAAE,MAAM;YACdpB,KAAK,EAAE,SAAS;YAChBwB,MAAM,EAAE,SAAS;YACjBX,OAAO,EAAE;UACX,CAAE;UAAAZ,QAAA,EACH;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAEAhE,OAAO,iBACNX,OAAA;QAAK4D,KAAK,EAAE;UACVoB,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,SAAS;UACrBM,MAAM,EAAE,mBAAmB;UAC3BJ,YAAY,EAAE,KAAK;UACnBhB,KAAK,EAAE,SAAS;UAChBN,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE;QAClB,CAAE;QAAAI,QAAA,gBACApE,OAAA;UAAK4D,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAE8B,GAAG,EAAE;UAAS,CAAE;UAAAzB,QAAA,gBACnEpE,OAAA,CAACP,WAAW;YAAC4E,IAAI,EAAE;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACxBhE,OAAO;QAAA;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN3E,OAAA;UACE0F,OAAO,EAAE/B,aAAc;UACvBC,KAAK,EAAE;YACLqB,UAAU,EAAE,aAAa;YACzBM,MAAM,EAAE,MAAM;YACdpB,KAAK,EAAE,SAAS;YAChBwB,MAAM,EAAE,SAAS;YACjBX,OAAO,EAAE;UACX,CAAE;UAAAZ,QAAA,EACH;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAGD3E,OAAA,CAACH,YAAY;MACXQ,UAAU,EAAEA,UAAW;MACvBE,OAAO,EAAEA,OAAQ;MACjB4F,cAAc,EAAEpD,kBAAmB;MACnCqD,gBAAgB,EAAEnD,oBAAqB;MACvCoD,sBAAsB,EAAEnD,0BAA2B;MACnDoD,gBAAgB,EAAElD,oBAAqB;MACvCmD,iBAAiB,EAAElD,qBAAsB;MACzCmD,mBAAmB,EAAEjD,uBAAwB;MAC7CkD,yBAAyB,EAAEjD;IAA8B;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC,eAGF3E,OAAA,CAACF,aAAa;MACZ4G,MAAM,EAAE7F,SAAU;MAClB8F,OAAO,EAAEA,CAAA,KAAM7F,YAAY,CAAC,KAAK,CAAE;MACnC8F,MAAM,EAAEnD,UAAW;MACnBT,QAAQ,EAAE/B,eAAgB;MAC1BqC,WAAW,EAAEnC,kBAAmB;MAChCE,cAAc,EAAEA,cAAe;MAC/BwF,IAAI,EAAE9F,SAAU;MAChBR,OAAO,EAAEgB;IAAa;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACzE,EAAA,CAjfID,kBAA4B;EAAA,QACfP,YAAY,EACTC,cAAc;AAAA;AAAAmH,EAAA,GAF9B7G,kBAA4B;AAmflC,eAAeA,kBAAkB;AAAC,IAAA6G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}