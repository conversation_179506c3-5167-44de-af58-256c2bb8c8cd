{"ast": null, "code": "import axios from 'axios';\n\n// Create axios instance with base configuration\nexport const apiClient = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Request interceptor to add auth token\napiClient.interceptors.request.use(config => {\n  const token = localStorage.getItem('adminToken');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Response interceptor to handle common errors\napiClient.interceptors.response.use(response => {\n  return response;\n}, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    // Handle unauthorized access\n    localStorage.removeItem('adminToken');\n    window.location.href = '/admin/login';\n  }\n  return Promise.reject(error);\n});\nexport default apiClient;", "map": {"version": 3, "names": ["axios", "apiClient", "create", "baseURL", "process", "env", "REACT_APP_API_URL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "status", "removeItem", "window", "location", "href"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/api.ts"], "sourcesContent": ["import axios from 'axios';\n\n// Create axios instance with base configuration\nexport const apiClient = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napiClient.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('adminToken');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor to handle common errors\napiClient.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  (error) => {\n    if (error.response?.status === 401) {\n      // Handle unauthorized access\n      localStorage.removeItem('adminToken');\n      window.location.href = '/admin/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\nexport default apiClient;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,OAAO,MAAMC,SAAS,GAAGD,KAAK,CAACE,MAAM,CAAC;EACpCC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;EACjEC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAP,SAAS,CAACQ,YAAY,CAACC,OAAO,CAACC,GAAG,CAC/BC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;EAChD,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAhB,SAAS,CAACQ,YAAY,CAACW,QAAQ,CAACT,GAAG,CAChCS,QAAQ,IAAK;EACZ,OAAOA,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClC;IACAR,YAAY,CAACS,UAAU,CAAC,YAAY,CAAC;IACrCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,cAAc;EACvC;EACA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAehB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}