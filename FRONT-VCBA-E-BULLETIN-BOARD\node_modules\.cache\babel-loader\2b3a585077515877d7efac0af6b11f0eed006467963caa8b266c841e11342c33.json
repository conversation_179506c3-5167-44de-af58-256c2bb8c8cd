{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\admin\\\\AdminAccountModal.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { X, User, Mail, Phone, Building, GraduationCap, FileText, Eye, EyeOff } from 'lucide-react';\nimport ProfilePictureUpload from './ProfilePictureUpload';\nimport { adminManagementService } from '../../services/adminManagementService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AdminAccountModal = ({\n  isOpen,\n  onClose,\n  onSave,\n  admin,\n  loading = false\n}) => {\n  _s();\n  var _formData$profile$fir, _formData$profile$las;\n  const [formData, setFormData] = useState({\n    email: '',\n    is_active: true,\n    profile: {\n      first_name: '',\n      middle_name: '',\n      last_name: '',\n      suffix: '',\n      full_name: '',\n      phone_number: '',\n      department: '',\n      position: 'professor',\n      grade_level: undefined,\n      bio: ''\n    }\n  });\n  const [password, setPassword] = useState('');\n  const [confirmPassword, setConfirmPassword] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [isUploadingPicture, setIsUploadingPicture] = useState(false);\n  const isEditing = !!(admin !== null && admin !== void 0 && admin.admin_id);\n  useEffect(() => {\n    if (admin) {\n      setFormData(admin);\n    } else {\n      // Reset form for new admin\n      setFormData({\n        email: '',\n        is_active: true,\n        profile: {\n          first_name: '',\n          middle_name: '',\n          last_name: '',\n          suffix: '',\n          full_name: '',\n          phone_number: '',\n          department: '',\n          position: 'professor',\n          grade_level: undefined,\n          bio: ''\n        }\n      });\n    }\n    setPassword('');\n    setConfirmPassword('');\n    setErrors({});\n  }, [admin, isOpen]);\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Email validation\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.email)) {\n      newErrors.email = 'Please enter a valid email address';\n    }\n\n    // Password validation (only for new admins) - match AdminRegister validation\n    if (!isEditing) {\n      if (!password.trim()) {\n        newErrors.password = 'Password is required';\n      } else if (password.length < 8) {\n        newErrors.password = 'Password must be at least 8 characters long';\n      } else {\n        // Check for uppercase, lowercase, and number\n        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/;\n        if (!passwordRegex.test(password)) {\n          newErrors.password = 'Password must contain at least one uppercase letter, one lowercase letter, and one number';\n        }\n      }\n      if (password !== confirmPassword) {\n        newErrors.confirmPassword = 'Passwords do not match';\n      }\n    }\n\n    // Name validation\n    if (!formData.profile.first_name.trim()) {\n      newErrors.first_name = 'First name is required';\n    }\n    if (!formData.profile.last_name.trim()) {\n      newErrors.last_name = 'Last name is required';\n    }\n\n    // Grade level validation for professors\n    if (formData.profile.position === 'professor' && !formData.profile.grade_level) {\n      newErrors.grade_level = 'Grade level is required for professors';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    try {\n      const adminData = {\n        ...formData\n      };\n\n      // Generate full_name from individual name components\n      const fullNameParts = [adminData.profile.first_name, adminData.profile.middle_name, adminData.profile.last_name, adminData.profile.suffix].filter(Boolean);\n      adminData.profile.full_name = fullNameParts.join(' ');\n      if (!isEditing) {\n        // Add password for new admins\n        adminData.password = password;\n      }\n      await onSave(adminData);\n      onClose();\n    } catch (error) {\n      console.error('Error saving admin:', error);\n    }\n  };\n  const handleInputChange = (field, value) => {\n    if (field.startsWith('profile.')) {\n      const profileField = field.replace('profile.', '');\n      setFormData(prev => ({\n        ...prev,\n        profile: {\n          ...prev.profile,\n          [profileField]: value\n        }\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [field]: value\n      }));\n    }\n\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: ''\n      }));\n    }\n  };\n\n  // Profile picture handlers\n  const handleProfilePictureUpload = async file => {\n    if (!isEditing || !(admin !== null && admin !== void 0 && admin.admin_id)) {\n      throw new Error('Cannot upload profile picture for new admin. Please save the admin first.');\n    }\n    setIsUploadingPicture(true);\n    try {\n      const result = await adminManagementService.uploadProfilePicture(admin.admin_id, file);\n      if (result.success) {\n        var _result$data, _result$data$admin, _result$data2;\n        // Update the form data with the new profile picture\n        const profilePicture = ((_result$data = result.data) === null || _result$data === void 0 ? void 0 : (_result$data$admin = _result$data.admin) === null || _result$data$admin === void 0 ? void 0 : _result$data$admin.profile_picture) || ((_result$data2 = result.data) === null || _result$data2 === void 0 ? void 0 : _result$data2.profile_picture);\n        setFormData(prev => ({\n          ...prev,\n          profile: {\n            ...prev.profile,\n            profile_picture: profilePicture\n          }\n        }));\n      } else {\n        throw new Error(result.message || 'Failed to upload profile picture');\n      }\n    } catch (error) {\n      throw new Error(error.message || 'Failed to upload profile picture');\n    } finally {\n      setIsUploadingPicture(false);\n    }\n  };\n  const handleProfilePictureRemove = async () => {\n    if (!isEditing || !(admin !== null && admin !== void 0 && admin.admin_id)) {\n      throw new Error('Cannot remove profile picture for new admin.');\n    }\n    setIsUploadingPicture(true);\n    try {\n      const result = await adminManagementService.removeProfilePicture(admin.admin_id);\n      if (result.success) {\n        // Update the form data to remove the profile picture\n        setFormData(prev => ({\n          ...prev,\n          profile: {\n            ...prev.profile,\n            profile_picture: undefined\n          }\n        }));\n      } else {\n        throw new Error(result.message || 'Failed to remove profile picture');\n      }\n    } catch (error) {\n      throw new Error(error.message || 'Failed to remove profile picture');\n    } finally {\n      setIsUploadingPicture(false);\n    }\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      background: 'rgba(0, 0, 0, 0.5)',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      zIndex: 1000,\n      padding: '1rem'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '12px',\n        width: '100%',\n        maxWidth: '600px',\n        maxHeight: '90vh',\n        overflow: 'hidden',\n        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          padding: '1.5rem',\n          borderBottom: '1px solid #e5e7eb'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            margin: 0,\n            fontSize: '1.5rem',\n            fontWeight: '600',\n            color: '#1f2937'\n          },\n          children: isEditing ? 'Edit Admin Account' : 'Add New Admin'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          style: {\n            padding: '0.5rem',\n            background: 'transparent',\n            border: 'none',\n            borderRadius: '6px',\n            cursor: 'pointer',\n            color: '#6b7280'\n          },\n          children: /*#__PURE__*/_jsxDEV(X, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        style: {\n          padding: '1.5rem',\n          overflowY: 'auto',\n          maxHeight: 'calc(90vh - 140px)'\n        },\n        children: [isEditing && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '2rem',\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              margin: '0 0 1rem 0',\n              fontSize: '1rem',\n              fontWeight: '600',\n              color: '#374151'\n            },\n            children: \"Profile Picture\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ProfilePictureUpload, {\n            currentPicture: formData.profile.profile_picture ? `http://localhost:5000${formData.profile.profile_picture}` : undefined,\n            userInitials: `${((_formData$profile$fir = formData.profile.first_name) === null || _formData$profile$fir === void 0 ? void 0 : _formData$profile$fir.charAt(0)) || ''}${((_formData$profile$las = formData.profile.last_name) === null || _formData$profile$las === void 0 ? void 0 : _formData$profile$las.charAt(0)) || ''}`,\n            onUpload: handleProfilePictureUpload,\n            onRemove: handleProfilePictureRemove,\n            isLoading: isUploadingPicture,\n            size: 120\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: '0.5rem 0 0 0',\n              fontSize: '0.75rem',\n              color: '#6b7280'\n            },\n            children: \"Upload a profile picture for this admin account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n            gap: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Mail, {\n                size: 16,\n                style: {\n                  display: 'inline',\n                  marginRight: '0.5rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 17\n              }, this), \"Email Address *\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              value: formData.email,\n              onChange: e => handleInputChange('email', e.target.value),\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: `1px solid ${errors.email ? '#ef4444' : '#d1d5db'}`,\n                borderRadius: '6px',\n                fontSize: '0.875rem'\n              },\n              placeholder: \"<EMAIL>\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 15\n            }, this), errors.email && /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0.25rem 0 0',\n                fontSize: '0.75rem',\n                color: '#ef4444'\n              },\n              children: errors.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(User, {\n                size: 16,\n                style: {\n                  display: 'inline',\n                  marginRight: '0.5rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 17\n              }, this), \"Position *\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: formData.profile.position,\n              onChange: e => handleInputChange('profile.position', e.target.value),\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #d1d5db',\n                borderRadius: '6px',\n                fontSize: '0.875rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"professor\",\n                children: \"Professor\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"super_admin\",\n                children: \"Super Admin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              },\n              children: \"First Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: formData.profile.first_name,\n              onChange: e => handleInputChange('profile.first_name', e.target.value),\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: `1px solid ${errors.first_name ? '#ef4444' : '#d1d5db'}`,\n                borderRadius: '6px',\n                fontSize: '0.875rem'\n              },\n              placeholder: \"John\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 15\n            }, this), errors.first_name && /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0.25rem 0 0',\n                fontSize: '0.75rem',\n                color: '#ef4444'\n              },\n              children: errors.first_name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              },\n              children: \"Middle Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: formData.profile.middle_name || '',\n              onChange: e => handleInputChange('profile.middle_name', e.target.value),\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #d1d5db',\n                borderRadius: '6px',\n                fontSize: '0.875rem'\n              },\n              placeholder: \"Michael\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              },\n              children: \"Last Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: formData.profile.last_name,\n              onChange: e => handleInputChange('profile.last_name', e.target.value),\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: `1px solid ${errors.last_name ? '#ef4444' : '#d1d5db'}`,\n                borderRadius: '6px',\n                fontSize: '0.875rem'\n              },\n              placeholder: \"Doe\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 15\n            }, this), errors.last_name && /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0.25rem 0 0',\n                fontSize: '0.75rem',\n                color: '#ef4444'\n              },\n              children: errors.last_name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              },\n              children: \"Suffix\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: formData.profile.suffix || '',\n              onChange: e => handleInputChange('profile.suffix', e.target.value),\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #d1d5db',\n                borderRadius: '6px',\n                fontSize: '0.875rem'\n              },\n              placeholder: \"Jr., Sr., III\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Phone, {\n                size: 16,\n                style: {\n                  display: 'inline',\n                  marginRight: '0.5rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 17\n              }, this), \"Phone Number\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"tel\",\n              value: formData.profile.phone_number || '',\n              onChange: e => handleInputChange('profile.phone_number', e.target.value),\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #d1d5db',\n                borderRadius: '6px',\n                fontSize: '0.875rem'\n              },\n              placeholder: \"+639123456789\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Building, {\n                size: 16,\n                style: {\n                  display: 'inline',\n                  marginRight: '0.5rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 17\n              }, this), \"Department\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: formData.profile.department || '',\n              onChange: e => handleInputChange('profile.department', e.target.value),\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #d1d5db',\n                borderRadius: '6px',\n                fontSize: '0.875rem'\n              },\n              placeholder: \"Mathematics, Science, etc.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 13\n          }, this), formData.profile.position === 'professor' && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(GraduationCap, {\n                size: 16,\n                style: {\n                  display: 'inline',\n                  marginRight: '0.5rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 578,\n                columnNumber: 19\n              }, this), \"Grade Level *\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 571,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: formData.profile.grade_level || '',\n              onChange: e => handleInputChange('profile.grade_level', e.target.value ? Number(e.target.value) : undefined),\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: `1px solid ${errors.grade_level ? '#ef4444' : '#d1d5db'}`,\n                borderRadius: '6px',\n                fontSize: '0.875rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select Grade Level\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"11\",\n                children: \"Grade 11\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 593,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"12\",\n                children: \"Grade 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 594,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 17\n            }, this), errors.grade_level && /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0.25rem 0 0',\n                fontSize: '0.75rem',\n                color: '#ef4444'\n              },\n              children: errors.grade_level\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 15\n          }, this), !isEditing && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  color: '#374151',\n                  marginBottom: '0.5rem'\n                },\n                children: \"Password *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 608,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'relative'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: showPassword ? 'text' : 'password',\n                  value: password,\n                  onChange: e => setPassword(e.target.value),\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    paddingRight: '2.5rem',\n                    border: `1px solid ${errors.password ? '#ef4444' : '#d1d5db'}`,\n                    borderRadius: '6px',\n                    fontSize: '0.875rem'\n                  },\n                  placeholder: \"Enter password\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 618,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => setShowPassword(!showPassword),\n                  style: {\n                    position: 'absolute',\n                    right: '0.75rem',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    background: 'transparent',\n                    border: 'none',\n                    cursor: 'pointer',\n                    color: '#6b7280'\n                  },\n                  children: showPassword ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 646,\n                    columnNumber: 39\n                  }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 646,\n                    columnNumber: 62\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 632,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 617,\n                columnNumber: 19\n              }, this), errors.password && /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: '0.25rem 0 0',\n                  fontSize: '0.75rem',\n                  color: '#ef4444'\n                },\n                children: errors.password\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 650,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: '0.25rem 0 0',\n                  fontSize: '0.75rem',\n                  color: '#6b7280'\n                },\n                children: \"Password must be at least 8 characters and contain at least one uppercase letter, one lowercase letter, and one number.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 654,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'block',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  color: '#374151',\n                  marginBottom: '0.5rem'\n                },\n                children: \"Confirm Password *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 660,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: showPassword ? 'text' : 'password',\n                value: confirmPassword,\n                onChange: e => setConfirmPassword(e.target.value),\n                style: {\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: `1px solid ${errors.confirmPassword ? '#ef4444' : '#d1d5db'}`,\n                  borderRadius: '6px',\n                  fontSize: '0.875rem'\n                },\n                placeholder: \"Confirm password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 669,\n                columnNumber: 19\n              }, this), errors.confirmPassword && /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: '0.25rem 0 0',\n                  fontSize: '0.75rem',\n                  color: '#ef4444'\n                },\n                children: errors.confirmPassword\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 683,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 659,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '600',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(FileText, {\n              size: 16,\n              style: {\n                display: 'inline',\n                marginRight: '0.5rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 701,\n              columnNumber: 15\n            }, this), \"Bio\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 694,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            value: formData.profile.bio || '',\n            onChange: e => handleInputChange('profile.bio', e.target.value),\n            rows: 3,\n            style: {\n              width: '100%',\n              padding: '0.75rem',\n              border: '1px solid #d1d5db',\n              borderRadius: '6px',\n              fontSize: '0.875rem',\n              resize: 'vertical'\n            },\n            placeholder: \"Brief description about the admin...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 704,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 693,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem',\n            justifyContent: 'flex-end',\n            marginTop: '2rem',\n            paddingTop: '1.5rem',\n            borderTop: '1px solid #e5e7eb'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: onClose,\n            disabled: loading,\n            style: {\n              padding: '0.75rem 1.5rem',\n              background: '#f3f4f6',\n              color: '#6b7280',\n              border: 'none',\n              borderRadius: '6px',\n              cursor: loading ? 'not-allowed' : 'pointer',\n              fontSize: '0.875rem',\n              fontWeight: '500'\n            },\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 729,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: loading,\n            style: {\n              padding: '0.75rem 1.5rem',\n              background: loading ? '#9ca3af' : '#3b82f6',\n              color: 'white',\n              border: 'none',\n              borderRadius: '6px',\n              cursor: loading ? 'not-allowed' : 'pointer',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [loading && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '16px',\n                height: '16px',\n                border: '2px solid transparent',\n                borderTop: '2px solid white',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 765,\n              columnNumber: 17\n            }, this), loading ? 'Saving...' : isEditing ? 'Update Admin' : 'Create Admin']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 747,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 721,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 254,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminAccountModal, \"1TljlTlyLOQlOaf2EsbW3bXT3mo=\");\n_c = AdminAccountModal;\nexport default AdminAccountModal;\nvar _c;\n$RefreshReg$(_c, \"AdminAccountModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "X", "User", "Mail", "Phone", "Building", "GraduationCap", "FileText", "Eye", "Eye<PERSON>ff", "ProfilePictureUpload", "adminManagementService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AdminAccountModal", "isOpen", "onClose", "onSave", "admin", "loading", "_s", "_formData$profile$fir", "_formData$profile$las", "formData", "setFormData", "email", "is_active", "profile", "first_name", "middle_name", "last_name", "suffix", "full_name", "phone_number", "department", "position", "grade_level", "undefined", "bio", "password", "setPassword", "confirmPassword", "setConfirmPassword", "showPassword", "setShowPassword", "errors", "setErrors", "isUploadingPicture", "setIsUploadingPicture", "isEditing", "admin_id", "validateForm", "newErrors", "trim", "test", "length", "passwordRegex", "Object", "keys", "handleSubmit", "e", "preventDefault", "adminData", "fullNameParts", "filter", "Boolean", "join", "error", "console", "handleInputChange", "field", "value", "startsWith", "profileField", "replace", "prev", "handleProfilePictureUpload", "file", "Error", "result", "uploadProfilePicture", "success", "_result$data", "_result$data$admin", "_result$data2", "profilePicture", "data", "profile_picture", "message", "handleProfilePictureRemove", "removeProfilePicture", "style", "top", "left", "right", "bottom", "background", "display", "alignItems", "justifyContent", "zIndex", "padding", "children", "borderRadius", "width", "max<PERSON><PERSON><PERSON>", "maxHeight", "overflow", "boxShadow", "borderBottom", "margin", "fontSize", "fontWeight", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "border", "cursor", "size", "onSubmit", "overflowY", "marginBottom", "textAlign", "currentPicture", "userInitials", "char<PERSON>t", "onUpload", "onRemove", "isLoading", "gridTemplateColumns", "gap", "marginRight", "type", "onChange", "target", "placeholder", "Number", "paddingRight", "transform", "marginTop", "rows", "resize", "paddingTop", "borderTop", "disabled", "height", "animation", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/admin/AdminAccountModal.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { X, User, Mail, Phone, Building, GraduationCap, FileText, Eye, EyeOff } from 'lucide-react';\nimport ProfilePictureUpload from './ProfilePictureUpload';\nimport { adminManagementService } from '../../services/adminManagementService';\n\ninterface AdminAccount {\n  admin_id?: number;\n  email: string;\n  is_active: boolean;\n  last_login?: string | null;\n  created_at?: string;\n  profile: {\n    first_name: string;\n    middle_name?: string;\n    last_name: string;\n    suffix?: string;\n    full_name: string;\n    phone_number?: string;\n    department?: string;\n    position: 'super_admin' | 'professor';\n    grade_level?: number;\n    bio?: string;\n    profile_picture?: string;\n  };\n}\n\ninterface AdminAccountModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onSave: (adminData: AdminAccount) => Promise<void>;\n  admin?: AdminAccount | null;\n  loading?: boolean;\n}\n\nconst AdminAccountModal: React.FC<AdminAccountModalProps> = ({\n  isOpen,\n  onClose,\n  onSave,\n  admin,\n  loading = false\n}) => {\n  const [formData, setFormData] = useState<AdminAccount>({\n    email: '',\n    is_active: true,\n    profile: {\n      first_name: '',\n      middle_name: '',\n      last_name: '',\n      suffix: '',\n      full_name: '',\n      phone_number: '',\n      department: '',\n      position: 'professor',\n      grade_level: undefined,\n      bio: ''\n    }\n  });\n\n  const [password, setPassword] = useState('');\n  const [confirmPassword, setConfirmPassword] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const [errors, setErrors] = useState<Record<string, string>>({});\n  const [isUploadingPicture, setIsUploadingPicture] = useState(false);\n\n  const isEditing = !!admin?.admin_id;\n\n  useEffect(() => {\n    if (admin) {\n      setFormData(admin);\n    } else {\n      // Reset form for new admin\n      setFormData({\n        email: '',\n        is_active: true,\n        profile: {\n          first_name: '',\n          middle_name: '',\n          last_name: '',\n          suffix: '',\n          full_name: '',\n          phone_number: '',\n          department: '',\n          position: 'professor',\n          grade_level: undefined,\n          bio: ''\n        }\n      });\n    }\n    setPassword('');\n    setConfirmPassword('');\n    setErrors({});\n  }, [admin, isOpen]);\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {};\n\n    // Email validation\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.email)) {\n      newErrors.email = 'Please enter a valid email address';\n    }\n\n    // Password validation (only for new admins) - match AdminRegister validation\n    if (!isEditing) {\n      if (!password.trim()) {\n        newErrors.password = 'Password is required';\n      } else if (password.length < 8) {\n        newErrors.password = 'Password must be at least 8 characters long';\n      } else {\n        // Check for uppercase, lowercase, and number\n        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/;\n        if (!passwordRegex.test(password)) {\n          newErrors.password = 'Password must contain at least one uppercase letter, one lowercase letter, and one number';\n        }\n      }\n\n      if (password !== confirmPassword) {\n        newErrors.confirmPassword = 'Passwords do not match';\n      }\n    }\n\n    // Name validation\n    if (!formData.profile.first_name.trim()) {\n      newErrors.first_name = 'First name is required';\n    }\n    if (!formData.profile.last_name.trim()) {\n      newErrors.last_name = 'Last name is required';\n    }\n\n    // Grade level validation for professors\n    if (formData.profile.position === 'professor' && !formData.profile.grade_level) {\n      newErrors.grade_level = 'Grade level is required for professors';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    try {\n      const adminData = { ...formData };\n\n      // Generate full_name from individual name components\n      const fullNameParts = [\n        adminData.profile.first_name,\n        adminData.profile.middle_name,\n        adminData.profile.last_name,\n        adminData.profile.suffix\n      ].filter(Boolean);\n\n      adminData.profile.full_name = fullNameParts.join(' ');\n\n      if (!isEditing) {\n        // Add password for new admins\n        (adminData as any).password = password;\n      }\n\n      await onSave(adminData);\n      onClose();\n    } catch (error) {\n      console.error('Error saving admin:', error);\n    }\n  };\n\n  const handleInputChange = (field: string, value: any) => {\n    if (field.startsWith('profile.')) {\n      const profileField = field.replace('profile.', '');\n      setFormData(prev => ({\n        ...prev,\n        profile: {\n          ...prev.profile,\n          [profileField]: value\n        }\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [field]: value\n      }));\n    }\n\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: '' }));\n    }\n  };\n\n  // Profile picture handlers\n  const handleProfilePictureUpload = async (file: File) => {\n    if (!isEditing || !admin?.admin_id) {\n      throw new Error('Cannot upload profile picture for new admin. Please save the admin first.');\n    }\n\n    setIsUploadingPicture(true);\n    try {\n      const result = await adminManagementService.uploadProfilePicture(admin.admin_id, file);\n      if (result.success) {\n        // Update the form data with the new profile picture\n        const profilePicture = (result.data as any)?.admin?.profile_picture || (result.data as any)?.profile_picture;\n        setFormData(prev => ({\n          ...prev,\n          profile: {\n            ...prev.profile,\n            profile_picture: profilePicture\n          }\n        }));\n      } else {\n        throw new Error(result.message || 'Failed to upload profile picture');\n      }\n    } catch (error: any) {\n      throw new Error(error.message || 'Failed to upload profile picture');\n    } finally {\n      setIsUploadingPicture(false);\n    }\n  };\n\n  const handleProfilePictureRemove = async () => {\n    if (!isEditing || !admin?.admin_id) {\n      throw new Error('Cannot remove profile picture for new admin.');\n    }\n\n    setIsUploadingPicture(true);\n    try {\n      const result = await adminManagementService.removeProfilePicture(admin.admin_id);\n      if (result.success) {\n        // Update the form data to remove the profile picture\n        setFormData(prev => ({\n          ...prev,\n          profile: {\n            ...prev.profile,\n            profile_picture: undefined\n          }\n        }));\n      } else {\n        throw new Error(result.message || 'Failed to remove profile picture');\n      }\n    } catch (error: any) {\n      throw new Error(error.message || 'Failed to remove profile picture');\n    } finally {\n      setIsUploadingPicture(false);\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div style={{\n      position: 'fixed',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      background: 'rgba(0, 0, 0, 0.5)',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      zIndex: 1000,\n      padding: '1rem'\n    }}>\n      <div style={{\n        background: 'white',\n        borderRadius: '12px',\n        width: '100%',\n        maxWidth: '600px',\n        maxHeight: '90vh',\n        overflow: 'hidden',\n        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)'\n      }}>\n        {/* Header */}\n        <div style={{\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          padding: '1.5rem',\n          borderBottom: '1px solid #e5e7eb'\n        }}>\n          <h2 style={{\n            margin: 0,\n            fontSize: '1.5rem',\n            fontWeight: '600',\n            color: '#1f2937'\n          }}>\n            {isEditing ? 'Edit Admin Account' : 'Add New Admin'}\n          </h2>\n          \n          <button\n            onClick={onClose}\n            style={{\n              padding: '0.5rem',\n              background: 'transparent',\n              border: 'none',\n              borderRadius: '6px',\n              cursor: 'pointer',\n              color: '#6b7280'\n            }}\n          >\n            <X size={20} />\n          </button>\n        </div>\n\n        {/* Form */}\n        <form onSubmit={handleSubmit} style={{ padding: '1.5rem', overflowY: 'auto', maxHeight: 'calc(90vh - 140px)' }}>\n          {/* Profile Picture Upload (only for editing) */}\n          {isEditing && (\n            <div style={{ marginBottom: '2rem', textAlign: 'center' }}>\n              <h3 style={{\n                margin: '0 0 1rem 0',\n                fontSize: '1rem',\n                fontWeight: '600',\n                color: '#374151'\n              }}>\n                Profile Picture\n              </h3>\n              <ProfilePictureUpload\n                currentPicture={formData.profile.profile_picture ? `http://localhost:5000${formData.profile.profile_picture}` : undefined}\n                userInitials={`${formData.profile.first_name?.charAt(0) || ''}${formData.profile.last_name?.charAt(0) || ''}`}\n                onUpload={handleProfilePictureUpload}\n                onRemove={handleProfilePictureRemove}\n                isLoading={isUploadingPicture}\n                size={120}\n              />\n              <p style={{\n                margin: '0.5rem 0 0 0',\n                fontSize: '0.75rem',\n                color: '#6b7280'\n              }}>\n                Upload a profile picture for this admin account\n              </p>\n            </div>\n          )}\n\n          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1.5rem' }}>\n            {/* Email */}\n            <div>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                <Mail size={16} style={{ display: 'inline', marginRight: '0.5rem' }} />\n                Email Address *\n              </label>\n              <input\n                type=\"email\"\n                value={formData.email}\n                onChange={(e) => handleInputChange('email', e.target.value)}\n                style={{\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: `1px solid ${errors.email ? '#ef4444' : '#d1d5db'}`,\n                  borderRadius: '6px',\n                  fontSize: '0.875rem'\n                }}\n                placeholder=\"<EMAIL>\"\n              />\n              {errors.email && (\n                <p style={{ margin: '0.25rem 0 0', fontSize: '0.75rem', color: '#ef4444' }}>\n                  {errors.email}\n                </p>\n              )}\n            </div>\n\n            {/* Position */}\n            <div>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                <User size={16} style={{ display: 'inline', marginRight: '0.5rem' }} />\n                Position *\n              </label>\n              <select\n                value={formData.profile.position}\n                onChange={(e) => handleInputChange('profile.position', e.target.value)}\n                style={{\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '6px',\n                  fontSize: '0.875rem'\n                }}\n              >\n                <option value=\"professor\">Professor</option>\n                <option value=\"super_admin\">Super Admin</option>\n              </select>\n            </div>\n\n            {/* First Name */}\n            <div>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                First Name *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.profile.first_name}\n                onChange={(e) => handleInputChange('profile.first_name', e.target.value)}\n                style={{\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: `1px solid ${errors.first_name ? '#ef4444' : '#d1d5db'}`,\n                  borderRadius: '6px',\n                  fontSize: '0.875rem'\n                }}\n                placeholder=\"John\"\n              />\n              {errors.first_name && (\n                <p style={{ margin: '0.25rem 0 0', fontSize: '0.75rem', color: '#ef4444' }}>\n                  {errors.first_name}\n                </p>\n              )}\n            </div>\n\n            {/* Middle Name */}\n            <div>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                Middle Name\n              </label>\n              <input\n                type=\"text\"\n                value={formData.profile.middle_name || ''}\n                onChange={(e) => handleInputChange('profile.middle_name', e.target.value)}\n                style={{\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '6px',\n                  fontSize: '0.875rem'\n                }}\n                placeholder=\"Michael\"\n              />\n            </div>\n\n            {/* Last Name */}\n            <div>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                Last Name *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.profile.last_name}\n                onChange={(e) => handleInputChange('profile.last_name', e.target.value)}\n                style={{\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: `1px solid ${errors.last_name ? '#ef4444' : '#d1d5db'}`,\n                  borderRadius: '6px',\n                  fontSize: '0.875rem'\n                }}\n                placeholder=\"Doe\"\n              />\n              {errors.last_name && (\n                <p style={{ margin: '0.25rem 0 0', fontSize: '0.75rem', color: '#ef4444' }}>\n                  {errors.last_name}\n                </p>\n              )}\n            </div>\n\n            {/* Suffix */}\n            <div>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                Suffix\n              </label>\n              <input\n                type=\"text\"\n                value={formData.profile.suffix || ''}\n                onChange={(e) => handleInputChange('profile.suffix', e.target.value)}\n                style={{\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '6px',\n                  fontSize: '0.875rem'\n                }}\n                placeholder=\"Jr., Sr., III\"\n              />\n            </div>\n\n            {/* Phone Number */}\n            <div>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                <Phone size={16} style={{ display: 'inline', marginRight: '0.5rem' }} />\n                Phone Number\n              </label>\n              <input\n                type=\"tel\"\n                value={formData.profile.phone_number || ''}\n                onChange={(e) => handleInputChange('profile.phone_number', e.target.value)}\n                style={{\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '6px',\n                  fontSize: '0.875rem'\n                }}\n                placeholder=\"+639123456789\"\n              />\n            </div>\n\n            {/* Department */}\n            <div>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                <Building size={16} style={{ display: 'inline', marginRight: '0.5rem' }} />\n                Department\n              </label>\n              <input\n                type=\"text\"\n                value={formData.profile.department || ''}\n                onChange={(e) => handleInputChange('profile.department', e.target.value)}\n                style={{\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '6px',\n                  fontSize: '0.875rem'\n                }}\n                placeholder=\"Mathematics, Science, etc.\"\n              />\n            </div>\n\n            {/* Grade Level (for professors) */}\n            {formData.profile.position === 'professor' && (\n              <div>\n                <label style={{\n                  display: 'block',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  color: '#374151',\n                  marginBottom: '0.5rem'\n                }}>\n                  <GraduationCap size={16} style={{ display: 'inline', marginRight: '0.5rem' }} />\n                  Grade Level *\n                </label>\n                <select\n                  value={formData.profile.grade_level || ''}\n                  onChange={(e) => handleInputChange('profile.grade_level', e.target.value ? Number(e.target.value) : undefined)}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: `1px solid ${errors.grade_level ? '#ef4444' : '#d1d5db'}`,\n                    borderRadius: '6px',\n                    fontSize: '0.875rem'\n                  }}\n                >\n                  <option value=\"\">Select Grade Level</option>\n                  <option value=\"11\">Grade 11</option>\n                  <option value=\"12\">Grade 12</option>\n                </select>\n                {errors.grade_level && (\n                  <p style={{ margin: '0.25rem 0 0', fontSize: '0.75rem', color: '#ef4444' }}>\n                    {errors.grade_level}\n                  </p>\n                )}\n              </div>\n            )}\n\n            {/* Password (only for new admins) */}\n            {!isEditing && (\n              <>\n                <div>\n                  <label style={{\n                    display: 'block',\n                    fontSize: '0.875rem',\n                    fontWeight: '600',\n                    color: '#374151',\n                    marginBottom: '0.5rem'\n                  }}>\n                    Password *\n                  </label>\n                  <div style={{ position: 'relative' }}>\n                    <input\n                      type={showPassword ? 'text' : 'password'}\n                      value={password}\n                      onChange={(e) => setPassword(e.target.value)}\n                      style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        paddingRight: '2.5rem',\n                        border: `1px solid ${errors.password ? '#ef4444' : '#d1d5db'}`,\n                        borderRadius: '6px',\n                        fontSize: '0.875rem'\n                      }}\n                      placeholder=\"Enter password\"\n                    />\n                    <button\n                      type=\"button\"\n                      onClick={() => setShowPassword(!showPassword)}\n                      style={{\n                        position: 'absolute',\n                        right: '0.75rem',\n                        top: '50%',\n                        transform: 'translateY(-50%)',\n                        background: 'transparent',\n                        border: 'none',\n                        cursor: 'pointer',\n                        color: '#6b7280'\n                      }}\n                    >\n                      {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}\n                    </button>\n                  </div>\n                  {errors.password && (\n                    <p style={{ margin: '0.25rem 0 0', fontSize: '0.75rem', color: '#ef4444' }}>\n                      {errors.password}\n                    </p>\n                  )}\n                  <p style={{ margin: '0.25rem 0 0', fontSize: '0.75rem', color: '#6b7280' }}>\n                    Password must be at least 8 characters and contain at least one uppercase letter, one lowercase letter, and one number.\n                  </p>\n                </div>\n\n                <div>\n                  <label style={{\n                    display: 'block',\n                    fontSize: '0.875rem',\n                    fontWeight: '600',\n                    color: '#374151',\n                    marginBottom: '0.5rem'\n                  }}>\n                    Confirm Password *\n                  </label>\n                  <input\n                    type={showPassword ? 'text' : 'password'}\n                    value={confirmPassword}\n                    onChange={(e) => setConfirmPassword(e.target.value)}\n                    style={{\n                      width: '100%',\n                      padding: '0.75rem',\n                      border: `1px solid ${errors.confirmPassword ? '#ef4444' : '#d1d5db'}`,\n                      borderRadius: '6px',\n                      fontSize: '0.875rem'\n                    }}\n                    placeholder=\"Confirm password\"\n                  />\n                  {errors.confirmPassword && (\n                    <p style={{ margin: '0.25rem 0 0', fontSize: '0.75rem', color: '#ef4444' }}>\n                      {errors.confirmPassword}\n                    </p>\n                  )}\n                </div>\n              </>\n            )}\n          </div>\n\n          {/* Bio */}\n          <div style={{ marginTop: '1.5rem' }}>\n            <label style={{\n              display: 'block',\n              fontSize: '0.875rem',\n              fontWeight: '600',\n              color: '#374151',\n              marginBottom: '0.5rem'\n            }}>\n              <FileText size={16} style={{ display: 'inline', marginRight: '0.5rem' }} />\n              Bio\n            </label>\n            <textarea\n              value={formData.profile.bio || ''}\n              onChange={(e) => handleInputChange('profile.bio', e.target.value)}\n              rows={3}\n              style={{\n                width: '100%',\n                padding: '0.75rem',\n                border: '1px solid #d1d5db',\n                borderRadius: '6px',\n                fontSize: '0.875rem',\n                resize: 'vertical'\n              }}\n              placeholder=\"Brief description about the admin...\"\n            />\n          </div>\n\n          {/* Form Actions */}\n          <div style={{\n            display: 'flex',\n            gap: '1rem',\n            justifyContent: 'flex-end',\n            marginTop: '2rem',\n            paddingTop: '1.5rem',\n            borderTop: '1px solid #e5e7eb'\n          }}>\n            <button\n              type=\"button\"\n              onClick={onClose}\n              disabled={loading}\n              style={{\n                padding: '0.75rem 1.5rem',\n                background: '#f3f4f6',\n                color: '#6b7280',\n                border: 'none',\n                borderRadius: '6px',\n                cursor: loading ? 'not-allowed' : 'pointer',\n                fontSize: '0.875rem',\n                fontWeight: '500'\n              }}\n            >\n              Cancel\n            </button>\n            \n            <button\n              type=\"submit\"\n              disabled={loading}\n              style={{\n                padding: '0.75rem 1.5rem',\n                background: loading ? '#9ca3af' : '#3b82f6',\n                color: 'white',\n                border: 'none',\n                borderRadius: '6px',\n                cursor: loading ? 'not-allowed' : 'pointer',\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              }}\n            >\n              {loading && (\n                <div style={{\n                  width: '16px',\n                  height: '16px',\n                  border: '2px solid transparent',\n                  borderTop: '2px solid white',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }} />\n              )}\n              {loading ? 'Saving...' : (isEditing ? 'Update Admin' : 'Create Admin')}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminAccountModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,CAAC,EAAEC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,MAAM,QAAQ,cAAc;AACnG,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,SAASC,sBAAsB,QAAQ,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AA+B/E,MAAMC,iBAAmD,GAAGA,CAAC;EAC3DC,MAAM;EACNC,OAAO;EACPC,MAAM;EACNC,KAAK;EACLC,OAAO,GAAG;AACZ,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAe;IACrD4B,KAAK,EAAE,EAAE;IACTC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE;MACPC,UAAU,EAAE,EAAE;MACdC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbC,MAAM,EAAE,EAAE;MACVC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,EAAE;MAChBC,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAE,WAAW;MACrBC,WAAW,EAAEC,SAAS;MACtBC,GAAG,EAAE;IACP;EACF,CAAC,CAAC;EAEF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC4C,eAAe,EAAEC,kBAAkB,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC8C,YAAY,EAAEC,eAAe,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACgD,MAAM,EAAEC,SAAS,CAAC,GAAGjD,QAAQ,CAAyB,CAAC,CAAC,CAAC;EAChE,MAAM,CAACkD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EAEnE,MAAMoD,SAAS,GAAG,CAAC,EAAC/B,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEgC,QAAQ;EAEnCpD,SAAS,CAAC,MAAM;IACd,IAAIoB,KAAK,EAAE;MACTM,WAAW,CAACN,KAAK,CAAC;IACpB,CAAC,MAAM;MACL;MACAM,WAAW,CAAC;QACVC,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE;UACPC,UAAU,EAAE,EAAE;UACdC,WAAW,EAAE,EAAE;UACfC,SAAS,EAAE,EAAE;UACbC,MAAM,EAAE,EAAE;UACVC,SAAS,EAAE,EAAE;UACbC,YAAY,EAAE,EAAE;UAChBC,UAAU,EAAE,EAAE;UACdC,QAAQ,EAAE,WAAW;UACrBC,WAAW,EAAEC,SAAS;UACtBC,GAAG,EAAE;QACP;MACF,CAAC,CAAC;IACJ;IACAE,WAAW,CAAC,EAAE,CAAC;IACfE,kBAAkB,CAAC,EAAE,CAAC;IACtBI,SAAS,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,EAAE,CAAC5B,KAAK,EAAEH,MAAM,CAAC,CAAC;EAEnB,MAAMoC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAiC,GAAG,CAAC,CAAC;;IAE5C;IACA,IAAI,CAAC7B,QAAQ,CAACE,KAAK,CAAC4B,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAAC3B,KAAK,GAAG,mBAAmB;IACvC,CAAC,MAAM,IAAI,CAAC,4BAA4B,CAAC6B,IAAI,CAAC/B,QAAQ,CAACE,KAAK,CAAC,EAAE;MAC7D2B,SAAS,CAAC3B,KAAK,GAAG,oCAAoC;IACxD;;IAEA;IACA,IAAI,CAACwB,SAAS,EAAE;MACd,IAAI,CAACV,QAAQ,CAACc,IAAI,CAAC,CAAC,EAAE;QACpBD,SAAS,CAACb,QAAQ,GAAG,sBAAsB;MAC7C,CAAC,MAAM,IAAIA,QAAQ,CAACgB,MAAM,GAAG,CAAC,EAAE;QAC9BH,SAAS,CAACb,QAAQ,GAAG,6CAA6C;MACpE,CAAC,MAAM;QACL;QACA,MAAMiB,aAAa,GAAG,iCAAiC;QACvD,IAAI,CAACA,aAAa,CAACF,IAAI,CAACf,QAAQ,CAAC,EAAE;UACjCa,SAAS,CAACb,QAAQ,GAAG,2FAA2F;QAClH;MACF;MAEA,IAAIA,QAAQ,KAAKE,eAAe,EAAE;QAChCW,SAAS,CAACX,eAAe,GAAG,wBAAwB;MACtD;IACF;;IAEA;IACA,IAAI,CAAClB,QAAQ,CAACI,OAAO,CAACC,UAAU,CAACyB,IAAI,CAAC,CAAC,EAAE;MACvCD,SAAS,CAACxB,UAAU,GAAG,wBAAwB;IACjD;IACA,IAAI,CAACL,QAAQ,CAACI,OAAO,CAACG,SAAS,CAACuB,IAAI,CAAC,CAAC,EAAE;MACtCD,SAAS,CAACtB,SAAS,GAAG,uBAAuB;IAC/C;;IAEA;IACA,IAAIP,QAAQ,CAACI,OAAO,CAACQ,QAAQ,KAAK,WAAW,IAAI,CAACZ,QAAQ,CAACI,OAAO,CAACS,WAAW,EAAE;MAC9EgB,SAAS,CAAChB,WAAW,GAAG,wCAAwC;IAClE;IAEAU,SAAS,CAACM,SAAS,CAAC;IACpB,OAAOK,MAAM,CAACC,IAAI,CAACN,SAAS,CAAC,CAACG,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMI,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACV,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEA,IAAI;MACF,MAAMW,SAAS,GAAG;QAAE,GAAGvC;MAAS,CAAC;;MAEjC;MACA,MAAMwC,aAAa,GAAG,CACpBD,SAAS,CAACnC,OAAO,CAACC,UAAU,EAC5BkC,SAAS,CAACnC,OAAO,CAACE,WAAW,EAC7BiC,SAAS,CAACnC,OAAO,CAACG,SAAS,EAC3BgC,SAAS,CAACnC,OAAO,CAACI,MAAM,CACzB,CAACiC,MAAM,CAACC,OAAO,CAAC;MAEjBH,SAAS,CAACnC,OAAO,CAACK,SAAS,GAAG+B,aAAa,CAACG,IAAI,CAAC,GAAG,CAAC;MAErD,IAAI,CAACjB,SAAS,EAAE;QACd;QACCa,SAAS,CAASvB,QAAQ,GAAGA,QAAQ;MACxC;MAEA,MAAMtB,MAAM,CAAC6C,SAAS,CAAC;MACvB9C,OAAO,CAAC,CAAC;IACX,CAAC,CAAC,OAAOmD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;IAC7C;EACF,CAAC;EAED,MAAME,iBAAiB,GAAGA,CAACC,KAAa,EAAEC,KAAU,KAAK;IACvD,IAAID,KAAK,CAACE,UAAU,CAAC,UAAU,CAAC,EAAE;MAChC,MAAMC,YAAY,GAAGH,KAAK,CAACI,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;MAClDlD,WAAW,CAACmD,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPhD,OAAO,EAAE;UACP,GAAGgD,IAAI,CAAChD,OAAO;UACf,CAAC8C,YAAY,GAAGF;QAClB;MACF,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACL/C,WAAW,CAACmD,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP,CAACL,KAAK,GAAGC;MACX,CAAC,CAAC,CAAC;IACL;;IAEA;IACA,IAAI1B,MAAM,CAACyB,KAAK,CAAC,EAAE;MACjBxB,SAAS,CAAC6B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACL,KAAK,GAAG;MAAG,CAAC,CAAC,CAAC;IAC/C;EACF,CAAC;;EAED;EACA,MAAMM,0BAA0B,GAAG,MAAOC,IAAU,IAAK;IACvD,IAAI,CAAC5B,SAAS,IAAI,EAAC/B,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEgC,QAAQ,GAAE;MAClC,MAAM,IAAI4B,KAAK,CAAC,2EAA2E,CAAC;IAC9F;IAEA9B,qBAAqB,CAAC,IAAI,CAAC;IAC3B,IAAI;MACF,MAAM+B,MAAM,GAAG,MAAMtE,sBAAsB,CAACuE,oBAAoB,CAAC9D,KAAK,CAACgC,QAAQ,EAAE2B,IAAI,CAAC;MACtF,IAAIE,MAAM,CAACE,OAAO,EAAE;QAAA,IAAAC,YAAA,EAAAC,kBAAA,EAAAC,aAAA;QAClB;QACA,MAAMC,cAAc,GAAG,EAAAH,YAAA,GAACH,MAAM,CAACO,IAAI,cAAAJ,YAAA,wBAAAC,kBAAA,GAAZD,YAAA,CAAsBhE,KAAK,cAAAiE,kBAAA,uBAA3BA,kBAAA,CAA6BI,eAAe,OAAAH,aAAA,GAAKL,MAAM,CAACO,IAAI,cAAAF,aAAA,uBAAZA,aAAA,CAAsBG,eAAe;QAC5G/D,WAAW,CAACmD,IAAI,KAAK;UACnB,GAAGA,IAAI;UACPhD,OAAO,EAAE;YACP,GAAGgD,IAAI,CAAChD,OAAO;YACf4D,eAAe,EAAEF;UACnB;QACF,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL,MAAM,IAAIP,KAAK,CAACC,MAAM,CAACS,OAAO,IAAI,kCAAkC,CAAC;MACvE;IACF,CAAC,CAAC,OAAOrB,KAAU,EAAE;MACnB,MAAM,IAAIW,KAAK,CAACX,KAAK,CAACqB,OAAO,IAAI,kCAAkC,CAAC;IACtE,CAAC,SAAS;MACRxC,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;EAED,MAAMyC,0BAA0B,GAAG,MAAAA,CAAA,KAAY;IAC7C,IAAI,CAACxC,SAAS,IAAI,EAAC/B,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEgC,QAAQ,GAAE;MAClC,MAAM,IAAI4B,KAAK,CAAC,8CAA8C,CAAC;IACjE;IAEA9B,qBAAqB,CAAC,IAAI,CAAC;IAC3B,IAAI;MACF,MAAM+B,MAAM,GAAG,MAAMtE,sBAAsB,CAACiF,oBAAoB,CAACxE,KAAK,CAACgC,QAAQ,CAAC;MAChF,IAAI6B,MAAM,CAACE,OAAO,EAAE;QAClB;QACAzD,WAAW,CAACmD,IAAI,KAAK;UACnB,GAAGA,IAAI;UACPhD,OAAO,EAAE;YACP,GAAGgD,IAAI,CAAChD,OAAO;YACf4D,eAAe,EAAElD;UACnB;QACF,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL,MAAM,IAAIyC,KAAK,CAACC,MAAM,CAACS,OAAO,IAAI,kCAAkC,CAAC;MACvE;IACF,CAAC,CAAC,OAAOrB,KAAU,EAAE;MACnB,MAAM,IAAIW,KAAK,CAACX,KAAK,CAACqB,OAAO,IAAI,kCAAkC,CAAC;IACtE,CAAC,SAAS;MACRxC,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;EAED,IAAI,CAACjC,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEJ,OAAA;IAAKgF,KAAK,EAAE;MACVxD,QAAQ,EAAE,OAAO;MACjByD,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,UAAU,EAAE,oBAAoB;MAChCC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE;IACX,CAAE;IAAAC,QAAA,eACA3F,OAAA;MAAKgF,KAAK,EAAE;QACVK,UAAU,EAAE,OAAO;QACnBO,YAAY,EAAE,MAAM;QACpBC,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE,OAAO;QACjBC,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE,QAAQ;QAClBC,SAAS,EAAE;MACb,CAAE;MAAAN,QAAA,gBAEA3F,OAAA;QAAKgF,KAAK,EAAE;UACVM,OAAO,EAAE,MAAM;UACfE,cAAc,EAAE,eAAe;UAC/BD,UAAU,EAAE,QAAQ;UACpBG,OAAO,EAAE,QAAQ;UACjBQ,YAAY,EAAE;QAChB,CAAE;QAAAP,QAAA,gBACA3F,OAAA;UAAIgF,KAAK,EAAE;YACTmB,MAAM,EAAE,CAAC;YACTC,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE,KAAK;YACjBC,KAAK,EAAE;UACT,CAAE;UAAAX,QAAA,EACCrD,SAAS,GAAG,oBAAoB,GAAG;QAAe;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eAEL1G,OAAA;UACE2G,OAAO,EAAEtG,OAAQ;UACjB2E,KAAK,EAAE;YACLU,OAAO,EAAE,QAAQ;YACjBL,UAAU,EAAE,aAAa;YACzBuB,MAAM,EAAE,MAAM;YACdhB,YAAY,EAAE,KAAK;YACnBiB,MAAM,EAAE,SAAS;YACjBP,KAAK,EAAE;UACT,CAAE;UAAAX,QAAA,eAEF3F,OAAA,CAACZ,CAAC;YAAC0H,IAAI,EAAE;UAAG;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGN1G,OAAA;QAAM+G,QAAQ,EAAE/D,YAAa;QAACgC,KAAK,EAAE;UAAEU,OAAO,EAAE,QAAQ;UAAEsB,SAAS,EAAE,MAAM;UAAEjB,SAAS,EAAE;QAAqB,CAAE;QAAAJ,QAAA,GAE5GrD,SAAS,iBACRtC,OAAA;UAAKgF,KAAK,EAAE;YAAEiC,YAAY,EAAE,MAAM;YAAEC,SAAS,EAAE;UAAS,CAAE;UAAAvB,QAAA,gBACxD3F,OAAA;YAAIgF,KAAK,EAAE;cACTmB,MAAM,EAAE,YAAY;cACpBC,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE,KAAK;cACjBC,KAAK,EAAE;YACT,CAAE;YAAAX,QAAA,EAAC;UAEH;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL1G,OAAA,CAACH,oBAAoB;YACnBsH,cAAc,EAAEvG,QAAQ,CAACI,OAAO,CAAC4D,eAAe,GAAG,wBAAwBhE,QAAQ,CAACI,OAAO,CAAC4D,eAAe,EAAE,GAAGlD,SAAU;YAC1H0F,YAAY,EAAE,GAAG,EAAA1G,qBAAA,GAAAE,QAAQ,CAACI,OAAO,CAACC,UAAU,cAAAP,qBAAA,uBAA3BA,qBAAA,CAA6B2G,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE,GAAG,EAAA1G,qBAAA,GAAAC,QAAQ,CAACI,OAAO,CAACG,SAAS,cAAAR,qBAAA,uBAA1BA,qBAAA,CAA4B0G,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE,EAAG;YAC9GC,QAAQ,EAAErD,0BAA2B;YACrCsD,QAAQ,EAAEzC,0BAA2B;YACrC0C,SAAS,EAAEpF,kBAAmB;YAC9B0E,IAAI,EAAE;UAAI;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACF1G,OAAA;YAAGgF,KAAK,EAAE;cACRmB,MAAM,EAAE,cAAc;cACtBC,QAAQ,EAAE,SAAS;cACnBE,KAAK,EAAE;YACT,CAAE;YAAAX,QAAA,EAAC;UAEH;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN,eAED1G,OAAA;UAAKgF,KAAK,EAAE;YAAEM,OAAO,EAAE,MAAM;YAAEmC,mBAAmB,EAAE,sCAAsC;YAAEC,GAAG,EAAE;UAAS,CAAE;UAAA/B,QAAA,gBAE1G3F,OAAA;YAAA2F,QAAA,gBACE3F,OAAA;cAAOgF,KAAK,EAAE;gBACZM,OAAO,EAAE,OAAO;gBAChBc,QAAQ,EAAE,UAAU;gBACpBC,UAAU,EAAE,KAAK;gBACjBC,KAAK,EAAE,SAAS;gBAChBW,YAAY,EAAE;cAChB,CAAE;cAAAtB,QAAA,gBACA3F,OAAA,CAACV,IAAI;gBAACwH,IAAI,EAAE,EAAG;gBAAC9B,KAAK,EAAE;kBAAEM,OAAO,EAAE,QAAQ;kBAAEqC,WAAW,EAAE;gBAAS;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAEzE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR1G,OAAA;cACE4H,IAAI,EAAC,OAAO;cACZhE,KAAK,EAAEhD,QAAQ,CAACE,KAAM;cACtB+G,QAAQ,EAAG5E,CAAC,IAAKS,iBAAiB,CAAC,OAAO,EAAET,CAAC,CAAC6E,MAAM,CAAClE,KAAK,CAAE;cAC5DoB,KAAK,EAAE;gBACLa,KAAK,EAAE,MAAM;gBACbH,OAAO,EAAE,SAAS;gBAClBkB,MAAM,EAAE,aAAa1E,MAAM,CAACpB,KAAK,GAAG,SAAS,GAAG,SAAS,EAAE;gBAC3D8E,YAAY,EAAE,KAAK;gBACnBQ,QAAQ,EAAE;cACZ,CAAE;cACF2B,WAAW,EAAC;YAAmB;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,EACDxE,MAAM,CAACpB,KAAK,iBACXd,OAAA;cAAGgF,KAAK,EAAE;gBAAEmB,MAAM,EAAE,aAAa;gBAAEC,QAAQ,EAAE,SAAS;gBAAEE,KAAK,EAAE;cAAU,CAAE;cAAAX,QAAA,EACxEzD,MAAM,CAACpB;YAAK;cAAAyF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CACJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN1G,OAAA;YAAA2F,QAAA,gBACE3F,OAAA;cAAOgF,KAAK,EAAE;gBACZM,OAAO,EAAE,OAAO;gBAChBc,QAAQ,EAAE,UAAU;gBACpBC,UAAU,EAAE,KAAK;gBACjBC,KAAK,EAAE,SAAS;gBAChBW,YAAY,EAAE;cAChB,CAAE;cAAAtB,QAAA,gBACA3F,OAAA,CAACX,IAAI;gBAACyH,IAAI,EAAE,EAAG;gBAAC9B,KAAK,EAAE;kBAAEM,OAAO,EAAE,QAAQ;kBAAEqC,WAAW,EAAE;gBAAS;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,cAEzE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR1G,OAAA;cACE4D,KAAK,EAAEhD,QAAQ,CAACI,OAAO,CAACQ,QAAS;cACjCqG,QAAQ,EAAG5E,CAAC,IAAKS,iBAAiB,CAAC,kBAAkB,EAAET,CAAC,CAAC6E,MAAM,CAAClE,KAAK,CAAE;cACvEoB,KAAK,EAAE;gBACLa,KAAK,EAAE,MAAM;gBACbH,OAAO,EAAE,SAAS;gBAClBkB,MAAM,EAAE,mBAAmB;gBAC3BhB,YAAY,EAAE,KAAK;gBACnBQ,QAAQ,EAAE;cACZ,CAAE;cAAAT,QAAA,gBAEF3F,OAAA;gBAAQ4D,KAAK,EAAC,WAAW;gBAAA+B,QAAA,EAAC;cAAS;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5C1G,OAAA;gBAAQ4D,KAAK,EAAC,aAAa;gBAAA+B,QAAA,EAAC;cAAW;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGN1G,OAAA;YAAA2F,QAAA,gBACE3F,OAAA;cAAOgF,KAAK,EAAE;gBACZM,OAAO,EAAE,OAAO;gBAChBc,QAAQ,EAAE,UAAU;gBACpBC,UAAU,EAAE,KAAK;gBACjBC,KAAK,EAAE,SAAS;gBAChBW,YAAY,EAAE;cAChB,CAAE;cAAAtB,QAAA,EAAC;YAEH;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR1G,OAAA;cACE4H,IAAI,EAAC,MAAM;cACXhE,KAAK,EAAEhD,QAAQ,CAACI,OAAO,CAACC,UAAW;cACnC4G,QAAQ,EAAG5E,CAAC,IAAKS,iBAAiB,CAAC,oBAAoB,EAAET,CAAC,CAAC6E,MAAM,CAAClE,KAAK,CAAE;cACzEoB,KAAK,EAAE;gBACLa,KAAK,EAAE,MAAM;gBACbH,OAAO,EAAE,SAAS;gBAClBkB,MAAM,EAAE,aAAa1E,MAAM,CAACjB,UAAU,GAAG,SAAS,GAAG,SAAS,EAAE;gBAChE2E,YAAY,EAAE,KAAK;gBACnBQ,QAAQ,EAAE;cACZ,CAAE;cACF2B,WAAW,EAAC;YAAM;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,EACDxE,MAAM,CAACjB,UAAU,iBAChBjB,OAAA;cAAGgF,KAAK,EAAE;gBAAEmB,MAAM,EAAE,aAAa;gBAAEC,QAAQ,EAAE,SAAS;gBAAEE,KAAK,EAAE;cAAU,CAAE;cAAAX,QAAA,EACxEzD,MAAM,CAACjB;YAAU;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CACJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN1G,OAAA;YAAA2F,QAAA,gBACE3F,OAAA;cAAOgF,KAAK,EAAE;gBACZM,OAAO,EAAE,OAAO;gBAChBc,QAAQ,EAAE,UAAU;gBACpBC,UAAU,EAAE,KAAK;gBACjBC,KAAK,EAAE,SAAS;gBAChBW,YAAY,EAAE;cAChB,CAAE;cAAAtB,QAAA,EAAC;YAEH;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR1G,OAAA;cACE4H,IAAI,EAAC,MAAM;cACXhE,KAAK,EAAEhD,QAAQ,CAACI,OAAO,CAACE,WAAW,IAAI,EAAG;cAC1C2G,QAAQ,EAAG5E,CAAC,IAAKS,iBAAiB,CAAC,qBAAqB,EAAET,CAAC,CAAC6E,MAAM,CAAClE,KAAK,CAAE;cAC1EoB,KAAK,EAAE;gBACLa,KAAK,EAAE,MAAM;gBACbH,OAAO,EAAE,SAAS;gBAClBkB,MAAM,EAAE,mBAAmB;gBAC3BhB,YAAY,EAAE,KAAK;gBACnBQ,QAAQ,EAAE;cACZ,CAAE;cACF2B,WAAW,EAAC;YAAS;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN1G,OAAA;YAAA2F,QAAA,gBACE3F,OAAA;cAAOgF,KAAK,EAAE;gBACZM,OAAO,EAAE,OAAO;gBAChBc,QAAQ,EAAE,UAAU;gBACpBC,UAAU,EAAE,KAAK;gBACjBC,KAAK,EAAE,SAAS;gBAChBW,YAAY,EAAE;cAChB,CAAE;cAAAtB,QAAA,EAAC;YAEH;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR1G,OAAA;cACE4H,IAAI,EAAC,MAAM;cACXhE,KAAK,EAAEhD,QAAQ,CAACI,OAAO,CAACG,SAAU;cAClC0G,QAAQ,EAAG5E,CAAC,IAAKS,iBAAiB,CAAC,mBAAmB,EAAET,CAAC,CAAC6E,MAAM,CAAClE,KAAK,CAAE;cACxEoB,KAAK,EAAE;gBACLa,KAAK,EAAE,MAAM;gBACbH,OAAO,EAAE,SAAS;gBAClBkB,MAAM,EAAE,aAAa1E,MAAM,CAACf,SAAS,GAAG,SAAS,GAAG,SAAS,EAAE;gBAC/DyE,YAAY,EAAE,KAAK;gBACnBQ,QAAQ,EAAE;cACZ,CAAE;cACF2B,WAAW,EAAC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,EACDxE,MAAM,CAACf,SAAS,iBACfnB,OAAA;cAAGgF,KAAK,EAAE;gBAAEmB,MAAM,EAAE,aAAa;gBAAEC,QAAQ,EAAE,SAAS;gBAAEE,KAAK,EAAE;cAAU,CAAE;cAAAX,QAAA,EACxEzD,MAAM,CAACf;YAAS;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CACJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN1G,OAAA;YAAA2F,QAAA,gBACE3F,OAAA;cAAOgF,KAAK,EAAE;gBACZM,OAAO,EAAE,OAAO;gBAChBc,QAAQ,EAAE,UAAU;gBACpBC,UAAU,EAAE,KAAK;gBACjBC,KAAK,EAAE,SAAS;gBAChBW,YAAY,EAAE;cAChB,CAAE;cAAAtB,QAAA,EAAC;YAEH;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR1G,OAAA;cACE4H,IAAI,EAAC,MAAM;cACXhE,KAAK,EAAEhD,QAAQ,CAACI,OAAO,CAACI,MAAM,IAAI,EAAG;cACrCyG,QAAQ,EAAG5E,CAAC,IAAKS,iBAAiB,CAAC,gBAAgB,EAAET,CAAC,CAAC6E,MAAM,CAAClE,KAAK,CAAE;cACrEoB,KAAK,EAAE;gBACLa,KAAK,EAAE,MAAM;gBACbH,OAAO,EAAE,SAAS;gBAClBkB,MAAM,EAAE,mBAAmB;gBAC3BhB,YAAY,EAAE,KAAK;gBACnBQ,QAAQ,EAAE;cACZ,CAAE;cACF2B,WAAW,EAAC;YAAe;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN1G,OAAA;YAAA2F,QAAA,gBACE3F,OAAA;cAAOgF,KAAK,EAAE;gBACZM,OAAO,EAAE,OAAO;gBAChBc,QAAQ,EAAE,UAAU;gBACpBC,UAAU,EAAE,KAAK;gBACjBC,KAAK,EAAE,SAAS;gBAChBW,YAAY,EAAE;cAChB,CAAE;cAAAtB,QAAA,gBACA3F,OAAA,CAACT,KAAK;gBAACuH,IAAI,EAAE,EAAG;gBAAC9B,KAAK,EAAE;kBAAEM,OAAO,EAAE,QAAQ;kBAAEqC,WAAW,EAAE;gBAAS;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE1E;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR1G,OAAA;cACE4H,IAAI,EAAC,KAAK;cACVhE,KAAK,EAAEhD,QAAQ,CAACI,OAAO,CAACM,YAAY,IAAI,EAAG;cAC3CuG,QAAQ,EAAG5E,CAAC,IAAKS,iBAAiB,CAAC,sBAAsB,EAAET,CAAC,CAAC6E,MAAM,CAAClE,KAAK,CAAE;cAC3EoB,KAAK,EAAE;gBACLa,KAAK,EAAE,MAAM;gBACbH,OAAO,EAAE,SAAS;gBAClBkB,MAAM,EAAE,mBAAmB;gBAC3BhB,YAAY,EAAE,KAAK;gBACnBQ,QAAQ,EAAE;cACZ,CAAE;cACF2B,WAAW,EAAC;YAAe;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN1G,OAAA;YAAA2F,QAAA,gBACE3F,OAAA;cAAOgF,KAAK,EAAE;gBACZM,OAAO,EAAE,OAAO;gBAChBc,QAAQ,EAAE,UAAU;gBACpBC,UAAU,EAAE,KAAK;gBACjBC,KAAK,EAAE,SAAS;gBAChBW,YAAY,EAAE;cAChB,CAAE;cAAAtB,QAAA,gBACA3F,OAAA,CAACR,QAAQ;gBAACsH,IAAI,EAAE,EAAG;gBAAC9B,KAAK,EAAE;kBAAEM,OAAO,EAAE,QAAQ;kBAAEqC,WAAW,EAAE;gBAAS;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,cAE7E;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR1G,OAAA;cACE4H,IAAI,EAAC,MAAM;cACXhE,KAAK,EAAEhD,QAAQ,CAACI,OAAO,CAACO,UAAU,IAAI,EAAG;cACzCsG,QAAQ,EAAG5E,CAAC,IAAKS,iBAAiB,CAAC,oBAAoB,EAAET,CAAC,CAAC6E,MAAM,CAAClE,KAAK,CAAE;cACzEoB,KAAK,EAAE;gBACLa,KAAK,EAAE,MAAM;gBACbH,OAAO,EAAE,SAAS;gBAClBkB,MAAM,EAAE,mBAAmB;gBAC3BhB,YAAY,EAAE,KAAK;gBACnBQ,QAAQ,EAAE;cACZ,CAAE;cACF2B,WAAW,EAAC;YAA4B;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EAGL9F,QAAQ,CAACI,OAAO,CAACQ,QAAQ,KAAK,WAAW,iBACxCxB,OAAA;YAAA2F,QAAA,gBACE3F,OAAA;cAAOgF,KAAK,EAAE;gBACZM,OAAO,EAAE,OAAO;gBAChBc,QAAQ,EAAE,UAAU;gBACpBC,UAAU,EAAE,KAAK;gBACjBC,KAAK,EAAE,SAAS;gBAChBW,YAAY,EAAE;cAChB,CAAE;cAAAtB,QAAA,gBACA3F,OAAA,CAACP,aAAa;gBAACqH,IAAI,EAAE,EAAG;gBAAC9B,KAAK,EAAE;kBAAEM,OAAO,EAAE,QAAQ;kBAAEqC,WAAW,EAAE;gBAAS;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,iBAElF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR1G,OAAA;cACE4D,KAAK,EAAEhD,QAAQ,CAACI,OAAO,CAACS,WAAW,IAAI,EAAG;cAC1CoG,QAAQ,EAAG5E,CAAC,IAAKS,iBAAiB,CAAC,qBAAqB,EAAET,CAAC,CAAC6E,MAAM,CAAClE,KAAK,GAAGoE,MAAM,CAAC/E,CAAC,CAAC6E,MAAM,CAAClE,KAAK,CAAC,GAAGlC,SAAS,CAAE;cAC/GsD,KAAK,EAAE;gBACLa,KAAK,EAAE,MAAM;gBACbH,OAAO,EAAE,SAAS;gBAClBkB,MAAM,EAAE,aAAa1E,MAAM,CAACT,WAAW,GAAG,SAAS,GAAG,SAAS,EAAE;gBACjEmE,YAAY,EAAE,KAAK;gBACnBQ,QAAQ,EAAE;cACZ,CAAE;cAAAT,QAAA,gBAEF3F,OAAA;gBAAQ4D,KAAK,EAAC,EAAE;gBAAA+B,QAAA,EAAC;cAAkB;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5C1G,OAAA;gBAAQ4D,KAAK,EAAC,IAAI;gBAAA+B,QAAA,EAAC;cAAQ;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpC1G,OAAA;gBAAQ4D,KAAK,EAAC,IAAI;gBAAA+B,QAAA,EAAC;cAAQ;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,EACRxE,MAAM,CAACT,WAAW,iBACjBzB,OAAA;cAAGgF,KAAK,EAAE;gBAAEmB,MAAM,EAAE,aAAa;gBAAEC,QAAQ,EAAE,SAAS;gBAAEE,KAAK,EAAE;cAAU,CAAE;cAAAX,QAAA,EACxEzD,MAAM,CAACT;YAAW;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CACJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAGA,CAACpE,SAAS,iBACTtC,OAAA,CAAAE,SAAA;YAAAyF,QAAA,gBACE3F,OAAA;cAAA2F,QAAA,gBACE3F,OAAA;gBAAOgF,KAAK,EAAE;kBACZM,OAAO,EAAE,OAAO;kBAChBc,QAAQ,EAAE,UAAU;kBACpBC,UAAU,EAAE,KAAK;kBACjBC,KAAK,EAAE,SAAS;kBAChBW,YAAY,EAAE;gBAChB,CAAE;gBAAAtB,QAAA,EAAC;cAEH;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR1G,OAAA;gBAAKgF,KAAK,EAAE;kBAAExD,QAAQ,EAAE;gBAAW,CAAE;gBAAAmE,QAAA,gBACnC3F,OAAA;kBACE4H,IAAI,EAAE5F,YAAY,GAAG,MAAM,GAAG,UAAW;kBACzC4B,KAAK,EAAEhC,QAAS;kBAChBiG,QAAQ,EAAG5E,CAAC,IAAKpB,WAAW,CAACoB,CAAC,CAAC6E,MAAM,CAAClE,KAAK,CAAE;kBAC7CoB,KAAK,EAAE;oBACLa,KAAK,EAAE,MAAM;oBACbH,OAAO,EAAE,SAAS;oBAClBuC,YAAY,EAAE,QAAQ;oBACtBrB,MAAM,EAAE,aAAa1E,MAAM,CAACN,QAAQ,GAAG,SAAS,GAAG,SAAS,EAAE;oBAC9DgE,YAAY,EAAE,KAAK;oBACnBQ,QAAQ,EAAE;kBACZ,CAAE;kBACF2B,WAAW,EAAC;gBAAgB;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC,eACF1G,OAAA;kBACE4H,IAAI,EAAC,QAAQ;kBACbjB,OAAO,EAAEA,CAAA,KAAM1E,eAAe,CAAC,CAACD,YAAY,CAAE;kBAC9CgD,KAAK,EAAE;oBACLxD,QAAQ,EAAE,UAAU;oBACpB2D,KAAK,EAAE,SAAS;oBAChBF,GAAG,EAAE,KAAK;oBACViD,SAAS,EAAE,kBAAkB;oBAC7B7C,UAAU,EAAE,aAAa;oBACzBuB,MAAM,EAAE,MAAM;oBACdC,MAAM,EAAE,SAAS;oBACjBP,KAAK,EAAE;kBACT,CAAE;kBAAAX,QAAA,EAED3D,YAAY,gBAAGhC,OAAA,CAACJ,MAAM;oBAACkH,IAAI,EAAE;kBAAG;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAG1G,OAAA,CAACL,GAAG;oBAACmH,IAAI,EAAE;kBAAG;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,EACLxE,MAAM,CAACN,QAAQ,iBACd5B,OAAA;gBAAGgF,KAAK,EAAE;kBAAEmB,MAAM,EAAE,aAAa;kBAAEC,QAAQ,EAAE,SAAS;kBAAEE,KAAK,EAAE;gBAAU,CAAE;gBAAAX,QAAA,EACxEzD,MAAM,CAACN;cAAQ;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CACJ,eACD1G,OAAA;gBAAGgF,KAAK,EAAE;kBAAEmB,MAAM,EAAE,aAAa;kBAAEC,QAAQ,EAAE,SAAS;kBAAEE,KAAK,EAAE;gBAAU,CAAE;gBAAAX,QAAA,EAAC;cAE5E;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAEN1G,OAAA;cAAA2F,QAAA,gBACE3F,OAAA;gBAAOgF,KAAK,EAAE;kBACZM,OAAO,EAAE,OAAO;kBAChBc,QAAQ,EAAE,UAAU;kBACpBC,UAAU,EAAE,KAAK;kBACjBC,KAAK,EAAE,SAAS;kBAChBW,YAAY,EAAE;gBAChB,CAAE;gBAAAtB,QAAA,EAAC;cAEH;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR1G,OAAA;gBACE4H,IAAI,EAAE5F,YAAY,GAAG,MAAM,GAAG,UAAW;gBACzC4B,KAAK,EAAE9B,eAAgB;gBACvB+F,QAAQ,EAAG5E,CAAC,IAAKlB,kBAAkB,CAACkB,CAAC,CAAC6E,MAAM,CAAClE,KAAK,CAAE;gBACpDoB,KAAK,EAAE;kBACLa,KAAK,EAAE,MAAM;kBACbH,OAAO,EAAE,SAAS;kBAClBkB,MAAM,EAAE,aAAa1E,MAAM,CAACJ,eAAe,GAAG,SAAS,GAAG,SAAS,EAAE;kBACrE8D,YAAY,EAAE,KAAK;kBACnBQ,QAAQ,EAAE;gBACZ,CAAE;gBACF2B,WAAW,EAAC;cAAkB;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,EACDxE,MAAM,CAACJ,eAAe,iBACrB9B,OAAA;gBAAGgF,KAAK,EAAE;kBAAEmB,MAAM,EAAE,aAAa;kBAAEC,QAAQ,EAAE,SAAS;kBAAEE,KAAK,EAAE;gBAAU,CAAE;gBAAAX,QAAA,EACxEzD,MAAM,CAACJ;cAAe;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,eACN,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN1G,OAAA;UAAKgF,KAAK,EAAE;YAAEmD,SAAS,EAAE;UAAS,CAAE;UAAAxC,QAAA,gBAClC3F,OAAA;YAAOgF,KAAK,EAAE;cACZM,OAAO,EAAE,OAAO;cAChBc,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBC,KAAK,EAAE,SAAS;cAChBW,YAAY,EAAE;YAChB,CAAE;YAAAtB,QAAA,gBACA3F,OAAA,CAACN,QAAQ;cAACoH,IAAI,EAAE,EAAG;cAAC9B,KAAK,EAAE;gBAAEM,OAAO,EAAE,QAAQ;gBAAEqC,WAAW,EAAE;cAAS;YAAE;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,OAE7E;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR1G,OAAA;YACE4D,KAAK,EAAEhD,QAAQ,CAACI,OAAO,CAACW,GAAG,IAAI,EAAG;YAClCkG,QAAQ,EAAG5E,CAAC,IAAKS,iBAAiB,CAAC,aAAa,EAAET,CAAC,CAAC6E,MAAM,CAAClE,KAAK,CAAE;YAClEwE,IAAI,EAAE,CAAE;YACRpD,KAAK,EAAE;cACLa,KAAK,EAAE,MAAM;cACbH,OAAO,EAAE,SAAS;cAClBkB,MAAM,EAAE,mBAAmB;cAC3BhB,YAAY,EAAE,KAAK;cACnBQ,QAAQ,EAAE,UAAU;cACpBiC,MAAM,EAAE;YACV,CAAE;YACFN,WAAW,EAAC;UAAsC;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN1G,OAAA;UAAKgF,KAAK,EAAE;YACVM,OAAO,EAAE,MAAM;YACfoC,GAAG,EAAE,MAAM;YACXlC,cAAc,EAAE,UAAU;YAC1B2C,SAAS,EAAE,MAAM;YACjBG,UAAU,EAAE,QAAQ;YACpBC,SAAS,EAAE;UACb,CAAE;UAAA5C,QAAA,gBACA3F,OAAA;YACE4H,IAAI,EAAC,QAAQ;YACbjB,OAAO,EAAEtG,OAAQ;YACjBmI,QAAQ,EAAEhI,OAAQ;YAClBwE,KAAK,EAAE;cACLU,OAAO,EAAE,gBAAgB;cACzBL,UAAU,EAAE,SAAS;cACrBiB,KAAK,EAAE,SAAS;cAChBM,MAAM,EAAE,MAAM;cACdhB,YAAY,EAAE,KAAK;cACnBiB,MAAM,EAAErG,OAAO,GAAG,aAAa,GAAG,SAAS;cAC3C4F,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE;YACd,CAAE;YAAAV,QAAA,EACH;UAED;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET1G,OAAA;YACE4H,IAAI,EAAC,QAAQ;YACbY,QAAQ,EAAEhI,OAAQ;YAClBwE,KAAK,EAAE;cACLU,OAAO,EAAE,gBAAgB;cACzBL,UAAU,EAAE7E,OAAO,GAAG,SAAS,GAAG,SAAS;cAC3C8F,KAAK,EAAE,OAAO;cACdM,MAAM,EAAE,MAAM;cACdhB,YAAY,EAAE,KAAK;cACnBiB,MAAM,EAAErG,OAAO,GAAG,aAAa,GAAG,SAAS;cAC3C4F,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBf,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBmC,GAAG,EAAE;YACP,CAAE;YAAA/B,QAAA,GAEDnF,OAAO,iBACNR,OAAA;cAAKgF,KAAK,EAAE;gBACVa,KAAK,EAAE,MAAM;gBACb4C,MAAM,EAAE,MAAM;gBACd7B,MAAM,EAAE,uBAAuB;gBAC/B2B,SAAS,EAAE,iBAAiB;gBAC5B3C,YAAY,EAAE,KAAK;gBACnB8C,SAAS,EAAE;cACb;YAAE;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CACL,EACAlG,OAAO,GAAG,WAAW,GAAI8B,SAAS,GAAG,cAAc,GAAG,cAAe;UAAA;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjG,EAAA,CA1uBIN,iBAAmD;AAAAwI,EAAA,GAAnDxI,iBAAmD;AA4uBzD,eAAeA,iBAAiB;AAAC,IAAAwI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}