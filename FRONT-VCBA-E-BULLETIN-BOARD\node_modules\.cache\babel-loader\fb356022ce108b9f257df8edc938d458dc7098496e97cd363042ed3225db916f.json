{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\admin\\\\AdminManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminManagement = () => {\n  _s();\n  const {\n    user\n  } = useAdminAuth();\n  const permissions = usePermissions(user);\n\n  // State management\n  const [admins, setAdmins] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n\n  // Modal state\n  const [showModal, setShowModal] = useState(false);\n  const [editingAdmin, setEditingAdmin] = useState(null);\n  const [modalLoading, setModalLoading] = useState(false);\n\n  // Search and filter state\n  const [searchTerm, setSearchTerm] = useState('');\n  const [positionFilter, setPositionFilter] = useState('');\n  const [statusFilter, setStatusFilter] = useState('');\n\n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(10);\n  const [totalItems, setTotalItems] = useState(0);\n  useEffect(() => {\n    // Check if user has permission to manage admins\n    if (!permissions.canManageAdmins) {\n      setError('You do not have permission to manage admin accounts');\n      setLoading(false);\n      return;\n    }\n    loadAdmins();\n  }, [permissions.canManageAdmins]);\n  const loadAdmins = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // TODO: Implement API call to fetch admins\n      // const response = await adminManagementService.getAdmins();\n      // setAdmins(response.data.admins);\n\n      // Mock data for now\n      setAdmins([{\n        admin_id: 1,\n        email: '<EMAIL>',\n        first_name: 'Von Christian',\n        last_name: 'Admin',\n        phone_number: '+************',\n        department: 'IT Department',\n        position: 'super_admin',\n        is_active: true,\n        last_login: '2025-08-05T10:30:00Z',\n        created_at: '2025-01-01T00:00:00Z'\n      }, {\n        admin_id: 2,\n        email: '<EMAIL>',\n        first_name: 'Zaira',\n        last_name: 'Professor',\n        phone_number: '+************',\n        department: 'Academic Department',\n        position: 'professor',\n        grade_level: 11,\n        is_active: true,\n        last_login: '2025-08-05T09:15:00Z',\n        created_at: '2025-02-01T00:00:00Z'\n      }]);\n    } catch (err) {\n      setError('Failed to load admin accounts');\n      console.error('Error loading admins:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getPositionIcon = position => {\n    return position === 'super_admin' ? Shield : User;\n  };\n  const getPositionColor = position => {\n    return position === 'super_admin' ? '#dc2626' : '#2563eb';\n  };\n  const getPositionLabel = position => {\n    return position === 'super_admin' ? 'Super Administrator' : 'Professor';\n  };\n  if (!permissions.canManageAdmins) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      },\n      children: [/*#__PURE__*/_jsxDEV(UserCog, {\n        size: 64,\n        style: {\n          marginBottom: '1rem',\n          opacity: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          margin: '0 0 0.5rem',\n          fontSize: '1.5rem',\n          fontWeight: '600'\n        },\n        children: \"Access Denied\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: 0,\n          fontSize: '1rem'\n        },\n        children: \"You do not have permission to manage admin accounts.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        },\n        children: [\"Current Role: \", permissions.getPositionDisplayName()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '40px',\n          height: '40px',\n          border: '4px solid #f3f4f6',\n          borderTop: '4px solid #3b82f6',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '2rem',\n        textAlign: 'center',\n        color: '#ef4444'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Error\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: loadAdmins,\n        style: {\n          padding: '0.5rem 1rem',\n          background: '#3b82f6',\n          color: 'white',\n          border: 'none',\n          borderRadius: '6px',\n          cursor: 'pointer'\n        },\n        children: \"Retry\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      maxWidth: '1200px',\n      margin: '0 auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            margin: '0 0 0.5rem',\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          },\n          children: \"Admin Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          },\n          children: \"Manage administrator accounts and permissions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          padding: '0.75rem 1.5rem',\n          background: '#3b82f6',\n          color: 'white',\n          border: 'none',\n          borderRadius: '8px',\n          fontSize: '0.875rem',\n          fontWeight: '600',\n          cursor: 'pointer',\n          transition: 'background-color 0.2s'\n        },\n        onMouseEnter: e => e.currentTarget.style.background = '#2563eb',\n        onMouseLeave: e => e.currentTarget.style.background = '#3b82f6',\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this), \"Add Admin\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fill, minmax(400px, 1fr))',\n        gap: '1.5rem'\n      },\n      children: admins.map(admin => {\n        const PositionIcon = getPositionIcon(admin.position);\n        const positionColor = getPositionColor(admin.position);\n        const positionLabel = getPositionLabel(admin.position);\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'white',\n            borderRadius: '12px',\n            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n            padding: '1.5rem',\n            border: admin.admin_id === (user === null || user === void 0 ? void 0 : user.id) ? '2px solid #facc15' : '1px solid #e5e7eb'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'flex-start',\n              marginBottom: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '48px',\n                  height: '48px',\n                  background: `linear-gradient(135deg, ${positionColor}20, ${positionColor}10)`,\n                  borderRadius: '12px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                },\n                children: /*#__PURE__*/_jsxDEV(PositionIcon, {\n                  size: 24,\n                  color: positionColor\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  style: {\n                    margin: '0 0 0.25rem',\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: '#1f2937'\n                  },\n                  children: [admin.first_name, \" \", admin.last_name, admin.admin_id === (user === null || user === void 0 ? void 0 : user.id) && /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      marginLeft: '0.5rem',\n                      padding: '0.125rem 0.375rem',\n                      background: '#facc15',\n                      color: '#1f2937',\n                      borderRadius: '4px',\n                      fontSize: '0.625rem',\n                      fontWeight: '700'\n                    },\n                    children: \"YOU\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 302,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    padding: '0.25rem 0.5rem',\n                    background: positionColor,\n                    color: 'white',\n                    borderRadius: '4px',\n                    fontSize: '0.75rem',\n                    fontWeight: '600',\n                    display: 'inline-block'\n                  },\n                  children: positionLabel\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '0.25rem 0.5rem',\n                background: admin.is_active ? '#dcfce7' : '#fef2f2',\n                color: admin.is_active ? '#166534' : '#dc2626',\n                borderRadius: '4px',\n                fontSize: '0.75rem',\n                fontWeight: '600'\n              },\n              children: admin.is_active ? 'Active' : 'Inactive'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                marginBottom: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Mail, {\n                size: 16,\n                color: \"#6b7280\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                },\n                children: admin.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 17\n            }, this), admin.phone_number && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                marginBottom: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Phone, {\n                size: 16,\n                color: \"#6b7280\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                },\n                children: admin.phone_number\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 19\n            }, this), admin.department && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                color: '#6b7280',\n                marginTop: '0.5rem'\n              },\n              children: [\"Department: \", admin.department]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 19\n            }, this), admin.grade_level && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                color: '#6b7280',\n                marginTop: '0.25rem'\n              },\n              children: [\"Grade Level: \", admin.grade_level]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 15\n          }, this), admin.last_login && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.75rem',\n              color: '#9ca3af',\n              marginBottom: '1rem'\n            },\n            children: [\"Last login: \", new Date(admin.last_login).toLocaleDateString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '0.5rem',\n              paddingTop: '1rem',\n              borderTop: '1px solid #f3f4f6'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                flex: 1,\n                padding: '0.5rem',\n                background: 'transparent',\n                border: '1px solid #d1d5db',\n                borderRadius: '6px',\n                cursor: 'pointer',\n                color: '#6b7280',\n                fontSize: '0.875rem',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                gap: '0.25rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Edit, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 19\n              }, this), \"Edit\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 17\n            }, this), admin.admin_id !== (user === null || user === void 0 ? void 0 : user.id) && /*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                flex: 1,\n                padding: '0.5rem',\n                background: 'transparent',\n                border: '1px solid #fca5a5',\n                borderRadius: '6px',\n                cursor: 'pointer',\n                color: '#dc2626',\n                fontSize: '0.875rem',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                gap: '0.25rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Trash2, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 21\n              }, this), \"Delete\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 15\n          }, this)]\n        }, admin.admin_id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 203,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminManagement, \"1ta6jFr/aZK9eOUImOuE16b5P8E=\", false, function () {\n  return [useAdminAuth, usePermissions];\n});\n_c = AdminManagement;\nexport default AdminManagement;\nvar _c;\n$RefreshReg$(_c, \"AdminManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAdminAuth", "usePermissions", "jsxDEV", "_jsxDEV", "AdminManagement", "_s", "user", "permissions", "admins", "setAdmins", "loading", "setLoading", "error", "setError", "success", "setSuccess", "showModal", "setShowModal", "editingAdmin", "setEditingAdmin", "modalLoading", "setModalLoading", "searchTerm", "setSearchTerm", "positionFilter", "setPositionFilter", "statusFilter", "setStatus<PERSON>ilter", "currentPage", "setCurrentPage", "itemsPerPage", "setItemsPerPage", "totalItems", "setTotalItems", "canManageAdmins", "loadAdmins", "admin_id", "email", "first_name", "last_name", "phone_number", "department", "position", "is_active", "last_login", "created_at", "grade_level", "err", "console", "getPositionIcon", "Shield", "User", "getPositionColor", "getPositionLabel", "style", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "textAlign", "color", "children", "UserCog", "size", "marginBottom", "opacity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "margin", "fontSize", "fontWeight", "marginTop", "padding", "background", "getPositionBadgeColor", "borderRadius", "getPositionDisplayName", "width", "height", "border", "borderTop", "animation", "onClick", "cursor", "max<PERSON><PERSON><PERSON>", "gap", "transition", "onMouseEnter", "e", "currentTarget", "onMouseLeave", "Plus", "gridTemplateColumns", "map", "admin", "PositionIcon", "positionColor", "position<PERSON><PERSON><PERSON>", "boxShadow", "id", "marginLeft", "Mail", "Phone", "Date", "toLocaleDateString", "paddingTop", "flex", "Edit", "Trash2", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/admin/AdminManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { UserPlus, Search, Filter, Users, AlertTriangle, CheckCircle } from 'lucide-react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\nimport { adminManagementService } from '../../services/adminManagementService';\nimport AdminAccountList from '../../components/admin/AdminAccountList';\nimport AdminAccountModal from '../../components/admin/AdminAccountModal';\n\ninterface AdminAccount {\n  admin_id: number;\n  email: string;\n  is_active: boolean;\n  last_login: string | null;\n  created_at: string;\n  profile: {\n    first_name: string;\n    middle_name?: string;\n    last_name: string;\n    suffix?: string;\n    full_name: string;\n    phone_number?: string;\n    department?: string;\n    position: 'super_admin' | 'professor';\n    grade_level?: number;\n    bio?: string;\n    profile_picture?: string;\n  };\n}\n\nconst AdminManagement: React.FC = () => {\n  const { user } = useAdminAuth();\n  const permissions = usePermissions(user);\n\n  // State management\n  const [admins, setAdmins] = useState<AdminAccount[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  // Modal state\n  const [showModal, setShowModal] = useState(false);\n  const [editingAdmin, setEditingAdmin] = useState<AdminAccount | null>(null);\n  const [modalLoading, setModalLoading] = useState(false);\n\n  // Search and filter state\n  const [searchTerm, setSearchTerm] = useState('');\n  const [positionFilter, setPositionFilter] = useState<string>('');\n  const [statusFilter, setStatusFilter] = useState<string>('');\n\n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(10);\n  const [totalItems, setTotalItems] = useState(0);\n\n  useEffect(() => {\n    // Check if user has permission to manage admins\n    if (!permissions.canManageAdmins) {\n      setError('You do not have permission to manage admin accounts');\n      setLoading(false);\n      return;\n    }\n\n    loadAdmins();\n  }, [permissions.canManageAdmins]);\n\n  const loadAdmins = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      // TODO: Implement API call to fetch admins\n      // const response = await adminManagementService.getAdmins();\n      // setAdmins(response.data.admins);\n      \n      // Mock data for now\n      setAdmins([\n        {\n          admin_id: 1,\n          email: '<EMAIL>',\n          first_name: 'Von Christian',\n          last_name: 'Admin',\n          phone_number: '+************',\n          department: 'IT Department',\n          position: 'super_admin',\n          is_active: true,\n          last_login: '2025-08-05T10:30:00Z',\n          created_at: '2025-01-01T00:00:00Z'\n        },\n        {\n          admin_id: 2,\n          email: '<EMAIL>',\n          first_name: 'Zaira',\n          last_name: 'Professor',\n          phone_number: '+************',\n          department: 'Academic Department',\n          position: 'professor',\n          grade_level: 11,\n          is_active: true,\n          last_login: '2025-08-05T09:15:00Z',\n          created_at: '2025-02-01T00:00:00Z'\n        }\n      ]);\n    } catch (err) {\n      setError('Failed to load admin accounts');\n      console.error('Error loading admins:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getPositionIcon = (position: string) => {\n    return position === 'super_admin' ? Shield : User;\n  };\n\n  const getPositionColor = (position: string) => {\n    return position === 'super_admin' ? '#dc2626' : '#2563eb';\n  };\n\n  const getPositionLabel = (position: string) => {\n    return position === 'super_admin' ? 'Super Administrator' : 'Professor';\n  };\n\n  if (!permissions.canManageAdmins) {\n    return (\n      <div style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      }}>\n        <UserCog size={64} style={{ marginBottom: '1rem', opacity: 0.5 }} />\n        <h2 style={{ margin: '0 0 0.5rem', fontSize: '1.5rem', fontWeight: '600' }}>\n          Access Denied\n        </h2>\n        <p style={{ margin: 0, fontSize: '1rem' }}>\n          You do not have permission to manage admin accounts.\n        </p>\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        }}>\n          Current Role: {permissions.getPositionDisplayName()}\n        </div>\n      </div>\n    );\n  }\n\n  if (loading) {\n    return (\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px'\n      }}>\n        <div style={{\n          width: '40px',\n          height: '40px',\n          border: '4px solid #f3f4f6',\n          borderTop: '4px solid #3b82f6',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }} />\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div style={{\n        padding: '2rem',\n        textAlign: 'center',\n        color: '#ef4444'\n      }}>\n        <h2>Error</h2>\n        <p>{error}</p>\n        <button\n          onClick={loadAdmins}\n          style={{\n            padding: '0.5rem 1rem',\n            background: '#3b82f6',\n            color: 'white',\n            border: 'none',\n            borderRadius: '6px',\n            cursor: 'pointer'\n          }}\n        >\n          Retry\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ maxWidth: '1200px', margin: '0 auto' }}>\n      {/* Header */}\n      <div style={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      }}>\n        <div>\n          <h1 style={{\n            margin: '0 0 0.5rem',\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          }}>\n            Admin Management\n          </h1>\n          <p style={{\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          }}>\n            Manage administrator accounts and permissions\n          </p>\n        </div>\n        \n        <button\n          style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            padding: '0.75rem 1.5rem',\n            background: '#3b82f6',\n            color: 'white',\n            border: 'none',\n            borderRadius: '8px',\n            fontSize: '0.875rem',\n            fontWeight: '600',\n            cursor: 'pointer',\n            transition: 'background-color 0.2s'\n          }}\n          onMouseEnter={(e) => e.currentTarget.style.background = '#2563eb'}\n          onMouseLeave={(e) => e.currentTarget.style.background = '#3b82f6'}\n        >\n          <Plus size={16} />\n          Add Admin\n        </button>\n      </div>\n\n      {/* Admin Cards */}\n      <div style={{\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fill, minmax(400px, 1fr))',\n        gap: '1.5rem'\n      }}>\n        {admins.map((admin) => {\n          const PositionIcon = getPositionIcon(admin.position);\n          const positionColor = getPositionColor(admin.position);\n          const positionLabel = getPositionLabel(admin.position);\n          \n          return (\n            <div\n              key={admin.admin_id}\n              style={{\n                background: 'white',\n                borderRadius: '12px',\n                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n                padding: '1.5rem',\n                border: admin.admin_id === user?.id ? '2px solid #facc15' : '1px solid #e5e7eb'\n              }}\n            >\n              {/* Header */}\n              <div style={{\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'flex-start',\n                marginBottom: '1rem'\n              }}>\n                <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>\n                  <div style={{\n                    width: '48px',\n                    height: '48px',\n                    background: `linear-gradient(135deg, ${positionColor}20, ${positionColor}10)`,\n                    borderRadius: '12px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  }}>\n                    <PositionIcon size={24} color={positionColor} />\n                  </div>\n                  <div>\n                    <h3 style={{\n                      margin: '0 0 0.25rem',\n                      fontSize: '1.125rem',\n                      fontWeight: '600',\n                      color: '#1f2937'\n                    }}>\n                      {admin.first_name} {admin.last_name}\n                      {admin.admin_id === user?.id && (\n                        <span style={{\n                          marginLeft: '0.5rem',\n                          padding: '0.125rem 0.375rem',\n                          background: '#facc15',\n                          color: '#1f2937',\n                          borderRadius: '4px',\n                          fontSize: '0.625rem',\n                          fontWeight: '700'\n                        }}>\n                          YOU\n                        </span>\n                      )}\n                    </h3>\n                    <div style={{\n                      padding: '0.25rem 0.5rem',\n                      background: positionColor,\n                      color: 'white',\n                      borderRadius: '4px',\n                      fontSize: '0.75rem',\n                      fontWeight: '600',\n                      display: 'inline-block'\n                    }}>\n                      {positionLabel}\n                    </div>\n                  </div>\n                </div>\n                \n                <div style={{\n                  padding: '0.25rem 0.5rem',\n                  background: admin.is_active ? '#dcfce7' : '#fef2f2',\n                  color: admin.is_active ? '#166534' : '#dc2626',\n                  borderRadius: '4px',\n                  fontSize: '0.75rem',\n                  fontWeight: '600'\n                }}>\n                  {admin.is_active ? 'Active' : 'Inactive'}\n                </div>\n              </div>\n\n              {/* Contact Info */}\n              <div style={{ marginBottom: '1rem' }}>\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  marginBottom: '0.5rem'\n                }}>\n                  <Mail size={16} color=\"#6b7280\" />\n                  <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>\n                    {admin.email}\n                  </span>\n                </div>\n                \n                {admin.phone_number && (\n                  <div style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem',\n                    marginBottom: '0.5rem'\n                  }}>\n                    <Phone size={16} color=\"#6b7280\" />\n                    <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>\n                      {admin.phone_number}\n                    </span>\n                  </div>\n                )}\n                \n                {admin.department && (\n                  <div style={{\n                    fontSize: '0.875rem',\n                    color: '#6b7280',\n                    marginTop: '0.5rem'\n                  }}>\n                    Department: {admin.department}\n                  </div>\n                )}\n                \n                {admin.grade_level && (\n                  <div style={{\n                    fontSize: '0.875rem',\n                    color: '#6b7280',\n                    marginTop: '0.25rem'\n                  }}>\n                    Grade Level: {admin.grade_level}\n                  </div>\n                )}\n              </div>\n\n              {/* Last Login */}\n              {admin.last_login && (\n                <div style={{\n                  fontSize: '0.75rem',\n                  color: '#9ca3af',\n                  marginBottom: '1rem'\n                }}>\n                  Last login: {new Date(admin.last_login).toLocaleDateString()}\n                </div>\n              )}\n\n              {/* Actions */}\n              <div style={{\n                display: 'flex',\n                gap: '0.5rem',\n                paddingTop: '1rem',\n                borderTop: '1px solid #f3f4f6'\n              }}>\n                <button\n                  style={{\n                    flex: 1,\n                    padding: '0.5rem',\n                    background: 'transparent',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '6px',\n                    cursor: 'pointer',\n                    color: '#6b7280',\n                    fontSize: '0.875rem',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    gap: '0.25rem'\n                  }}\n                >\n                  <Edit size={14} />\n                  Edit\n                </button>\n                \n                {admin.admin_id !== user?.id && (\n                  <button\n                    style={{\n                      flex: 1,\n                      padding: '0.5rem',\n                      background: 'transparent',\n                      border: '1px solid #fca5a5',\n                      borderRadius: '6px',\n                      cursor: 'pointer',\n                      color: '#dc2626',\n                      fontSize: '0.875rem',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      gap: '0.25rem'\n                    }}\n                  >\n                    <Trash2 size={14} />\n                    Delete\n                  </button>\n                )}\n              </div>\n            </div>\n          );\n        })}\n      </div>\n    </div>\n  );\n};\n\nexport default AdminManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAElD,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA0BzD,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IAAEC;EAAK,CAAC,GAAGN,YAAY,CAAC,CAAC;EAC/B,MAAMO,WAAW,GAAGN,cAAc,CAACK,IAAI,CAAC;;EAExC;EACA,MAAM,CAACE,MAAM,EAAEC,SAAS,CAAC,GAAGX,QAAQ,CAAiB,EAAE,CAAC;EACxD,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAgB,IAAI,CAAC;;EAE3D;EACA,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAsB,IAAI,CAAC;EAC3E,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0B,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAAS,EAAE,CAAC;EAChE,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAS,EAAE,CAAC;;EAE5D;EACA,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACkC,UAAU,EAAEC,aAAa,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;EAE/CC,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACQ,WAAW,CAAC2B,eAAe,EAAE;MAChCrB,QAAQ,CAAC,qDAAqD,CAAC;MAC/DF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEAwB,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAC5B,WAAW,CAAC2B,eAAe,CAAC,CAAC;EAEjC,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFxB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA;MACA;;MAEA;MACAJ,SAAS,CAAC,CACR;QACE2B,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE,0BAA0B;QACjCC,UAAU,EAAE,eAAe;QAC3BC,SAAS,EAAE,OAAO;QAClBC,YAAY,EAAE,eAAe;QAC7BC,UAAU,EAAE,eAAe;QAC3BC,QAAQ,EAAE,aAAa;QACvBC,SAAS,EAAE,IAAI;QACfC,UAAU,EAAE,sBAAsB;QAClCC,UAAU,EAAE;MACd,CAAC,EACD;QACET,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE,oBAAoB;QAC3BC,UAAU,EAAE,OAAO;QACnBC,SAAS,EAAE,WAAW;QACtBC,YAAY,EAAE,eAAe;QAC7BC,UAAU,EAAE,qBAAqB;QACjCC,QAAQ,EAAE,WAAW;QACrBI,WAAW,EAAE,EAAE;QACfH,SAAS,EAAE,IAAI;QACfC,UAAU,EAAE,sBAAsB;QAClCC,UAAU,EAAE;MACd,CAAC,CACF,CAAC;IACJ,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZlC,QAAQ,CAAC,+BAA+B,CAAC;MACzCmC,OAAO,CAACpC,KAAK,CAAC,uBAAuB,EAAEmC,GAAG,CAAC;IAC7C,CAAC,SAAS;MACRpC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsC,eAAe,GAAIP,QAAgB,IAAK;IAC5C,OAAOA,QAAQ,KAAK,aAAa,GAAGQ,MAAM,GAAGC,IAAI;EACnD,CAAC;EAED,MAAMC,gBAAgB,GAAIV,QAAgB,IAAK;IAC7C,OAAOA,QAAQ,KAAK,aAAa,GAAG,SAAS,GAAG,SAAS;EAC3D,CAAC;EAED,MAAMW,gBAAgB,GAAIX,QAAgB,IAAK;IAC7C,OAAOA,QAAQ,KAAK,aAAa,GAAG,qBAAqB,GAAG,WAAW;EACzE,CAAC;EAED,IAAI,CAACnC,WAAW,CAAC2B,eAAe,EAAE;IAChC,oBACE/B,OAAA;MAAKmD,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE,OAAO;QAClBC,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACA3D,OAAA,CAAC4D,OAAO;QAACC,IAAI,EAAE,EAAG;QAACV,KAAK,EAAE;UAAEW,YAAY,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAI;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpEnE,OAAA;QAAImD,KAAK,EAAE;UAAEiB,MAAM,EAAE,YAAY;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAM,CAAE;QAAAX,QAAA,EAAC;MAE5E;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLnE,OAAA;QAAGmD,KAAK,EAAE;UAAEiB,MAAM,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAV,QAAA,EAAC;MAE3C;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJnE,OAAA;QAAKmD,KAAK,EAAE;UACVoB,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAErE,WAAW,CAACsE,qBAAqB,CAAC,CAAC;UAC/CC,YAAY,EAAE,KAAK;UACnBjB,KAAK,EAAE,OAAO;UACdW,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE;QACd,CAAE;QAAAX,QAAA,GAAC,gBACa,EAACvD,WAAW,CAACwE,sBAAsB,CAAC,CAAC;MAAA;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI5D,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKmD,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE;MACb,CAAE;MAAAG,QAAA,eACA3D,OAAA;QAAKmD,KAAK,EAAE;UACV0B,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,mBAAmB;UAC3BC,SAAS,EAAE,mBAAmB;UAC9BL,YAAY,EAAE,KAAK;UACnBM,SAAS,EAAE;QACb;MAAE;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEV;EAEA,IAAI1D,KAAK,EAAE;IACT,oBACET,OAAA;MAAKmD,KAAK,EAAE;QACVqB,OAAO,EAAE,MAAM;QACff,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACA3D,OAAA;QAAA2D,QAAA,EAAI;MAAK;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdnE,OAAA;QAAA2D,QAAA,EAAIlD;MAAK;QAAAuD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdnE,OAAA;QACEkF,OAAO,EAAElD,UAAW;QACpBmB,KAAK,EAAE;UACLqB,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAE,SAAS;UACrBf,KAAK,EAAE,OAAO;UACdqB,MAAM,EAAE,MAAM;UACdJ,YAAY,EAAE,KAAK;UACnBQ,MAAM,EAAE;QACV,CAAE;QAAAxB,QAAA,EACH;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACEnE,OAAA;IAAKmD,KAAK,EAAE;MAAEiC,QAAQ,EAAE,QAAQ;MAAEhB,MAAM,EAAE;IAAS,CAAE;IAAAT,QAAA,gBAEnD3D,OAAA;MAAKmD,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfG,cAAc,EAAE,eAAe;QAC/BD,UAAU,EAAE,QAAQ;QACpBQ,YAAY,EAAE;MAChB,CAAE;MAAAH,QAAA,gBACA3D,OAAA;QAAA2D,QAAA,gBACE3D,OAAA;UAAImD,KAAK,EAAE;YACTiB,MAAM,EAAE,YAAY;YACpBC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjBZ,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,EAAC;QAEH;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLnE,OAAA;UAAGmD,KAAK,EAAE;YACRiB,MAAM,EAAE,CAAC;YACTV,KAAK,EAAE,SAAS;YAChBW,QAAQ,EAAE;UACZ,CAAE;UAAAV,QAAA,EAAC;QAEH;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENnE,OAAA;QACEmD,KAAK,EAAE;UACLC,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpB+B,GAAG,EAAE,QAAQ;UACbb,OAAO,EAAE,gBAAgB;UACzBC,UAAU,EAAE,SAAS;UACrBf,KAAK,EAAE,OAAO;UACdqB,MAAM,EAAE,MAAM;UACdJ,YAAY,EAAE,KAAK;UACnBN,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE,KAAK;UACjBa,MAAM,EAAE,SAAS;UACjBG,UAAU,EAAE;QACd,CAAE;QACFC,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACtC,KAAK,CAACsB,UAAU,GAAG,SAAU;QAClEiB,YAAY,EAAGF,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACtC,KAAK,CAACsB,UAAU,GAAG,SAAU;QAAAd,QAAA,gBAElE3D,OAAA,CAAC2F,IAAI;UAAC9B,IAAI,EAAE;QAAG;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,aAEpB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNnE,OAAA;MAAKmD,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfwC,mBAAmB,EAAE,uCAAuC;QAC5DP,GAAG,EAAE;MACP,CAAE;MAAA1B,QAAA,EACCtD,MAAM,CAACwF,GAAG,CAAEC,KAAK,IAAK;QACrB,MAAMC,YAAY,GAAGjD,eAAe,CAACgD,KAAK,CAACvD,QAAQ,CAAC;QACpD,MAAMyD,aAAa,GAAG/C,gBAAgB,CAAC6C,KAAK,CAACvD,QAAQ,CAAC;QACtD,MAAM0D,aAAa,GAAG/C,gBAAgB,CAAC4C,KAAK,CAACvD,QAAQ,CAAC;QAEtD,oBACEvC,OAAA;UAEEmD,KAAK,EAAE;YACLsB,UAAU,EAAE,OAAO;YACnBE,YAAY,EAAE,MAAM;YACpBuB,SAAS,EAAE,8BAA8B;YACzC1B,OAAO,EAAE,QAAQ;YACjBO,MAAM,EAAEe,KAAK,CAAC7D,QAAQ,MAAK9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgG,EAAE,IAAG,mBAAmB,GAAG;UAC9D,CAAE;UAAAxC,QAAA,gBAGF3D,OAAA;YAAKmD,KAAK,EAAE;cACVC,OAAO,EAAE,MAAM;cACfG,cAAc,EAAE,eAAe;cAC/BD,UAAU,EAAE,YAAY;cACxBQ,YAAY,EAAE;YAChB,CAAE;YAAAH,QAAA,gBACA3D,OAAA;cAAKmD,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAE+B,GAAG,EAAE;cAAU,CAAE;cAAA1B,QAAA,gBACpE3D,OAAA;gBAAKmD,KAAK,EAAE;kBACV0B,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdL,UAAU,EAAE,2BAA2BuB,aAAa,OAAOA,aAAa,KAAK;kBAC7ErB,YAAY,EAAE,MAAM;kBACpBvB,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBC,cAAc,EAAE;gBAClB,CAAE;gBAAAI,QAAA,eACA3D,OAAA,CAAC+F,YAAY;kBAAClC,IAAI,EAAE,EAAG;kBAACH,KAAK,EAAEsC;gBAAc;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACNnE,OAAA;gBAAA2D,QAAA,gBACE3D,OAAA;kBAAImD,KAAK,EAAE;oBACTiB,MAAM,EAAE,aAAa;oBACrBC,QAAQ,EAAE,UAAU;oBACpBC,UAAU,EAAE,KAAK;oBACjBZ,KAAK,EAAE;kBACT,CAAE;kBAAAC,QAAA,GACCmC,KAAK,CAAC3D,UAAU,EAAC,GAAC,EAAC2D,KAAK,CAAC1D,SAAS,EAClC0D,KAAK,CAAC7D,QAAQ,MAAK9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgG,EAAE,kBAC1BnG,OAAA;oBAAMmD,KAAK,EAAE;sBACXiD,UAAU,EAAE,QAAQ;sBACpB5B,OAAO,EAAE,mBAAmB;sBAC5BC,UAAU,EAAE,SAAS;sBACrBf,KAAK,EAAE,SAAS;sBAChBiB,YAAY,EAAE,KAAK;sBACnBN,QAAQ,EAAE,UAAU;sBACpBC,UAAU,EAAE;oBACd,CAAE;oBAAAX,QAAA,EAAC;kBAEH;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACLnE,OAAA;kBAAKmD,KAAK,EAAE;oBACVqB,OAAO,EAAE,gBAAgB;oBACzBC,UAAU,EAAEuB,aAAa;oBACzBtC,KAAK,EAAE,OAAO;oBACdiB,YAAY,EAAE,KAAK;oBACnBN,QAAQ,EAAE,SAAS;oBACnBC,UAAU,EAAE,KAAK;oBACjBlB,OAAO,EAAE;kBACX,CAAE;kBAAAO,QAAA,EACCsC;gBAAa;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENnE,OAAA;cAAKmD,KAAK,EAAE;gBACVqB,OAAO,EAAE,gBAAgB;gBACzBC,UAAU,EAAEqB,KAAK,CAACtD,SAAS,GAAG,SAAS,GAAG,SAAS;gBACnDkB,KAAK,EAAEoC,KAAK,CAACtD,SAAS,GAAG,SAAS,GAAG,SAAS;gBAC9CmC,YAAY,EAAE,KAAK;gBACnBN,QAAQ,EAAE,SAAS;gBACnBC,UAAU,EAAE;cACd,CAAE;cAAAX,QAAA,EACCmC,KAAK,CAACtD,SAAS,GAAG,QAAQ,GAAG;YAAU;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNnE,OAAA;YAAKmD,KAAK,EAAE;cAAEW,YAAY,EAAE;YAAO,CAAE;YAAAH,QAAA,gBACnC3D,OAAA;cAAKmD,KAAK,EAAE;gBACVC,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE,QAAQ;gBACpB+B,GAAG,EAAE,QAAQ;gBACbvB,YAAY,EAAE;cAChB,CAAE;cAAAH,QAAA,gBACA3D,OAAA,CAACqG,IAAI;gBAACxC,IAAI,EAAE,EAAG;gBAACH,KAAK,EAAC;cAAS;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClCnE,OAAA;gBAAMmD,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,UAAU;kBAAEX,KAAK,EAAE;gBAAU,CAAE;gBAAAC,QAAA,EACrDmC,KAAK,CAAC5D;cAAK;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EAEL2B,KAAK,CAACzD,YAAY,iBACjBrC,OAAA;cAAKmD,KAAK,EAAE;gBACVC,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE,QAAQ;gBACpB+B,GAAG,EAAE,QAAQ;gBACbvB,YAAY,EAAE;cAChB,CAAE;cAAAH,QAAA,gBACA3D,OAAA,CAACsG,KAAK;gBAACzC,IAAI,EAAE,EAAG;gBAACH,KAAK,EAAC;cAAS;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnCnE,OAAA;gBAAMmD,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,UAAU;kBAAEX,KAAK,EAAE;gBAAU,CAAE;gBAAAC,QAAA,EACrDmC,KAAK,CAACzD;cAAY;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN,EAEA2B,KAAK,CAACxD,UAAU,iBACftC,OAAA;cAAKmD,KAAK,EAAE;gBACVkB,QAAQ,EAAE,UAAU;gBACpBX,KAAK,EAAE,SAAS;gBAChBa,SAAS,EAAE;cACb,CAAE;cAAAZ,QAAA,GAAC,cACW,EAACmC,KAAK,CAACxD,UAAU;YAAA;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CACN,EAEA2B,KAAK,CAACnD,WAAW,iBAChB3C,OAAA;cAAKmD,KAAK,EAAE;gBACVkB,QAAQ,EAAE,UAAU;gBACpBX,KAAK,EAAE,SAAS;gBAChBa,SAAS,EAAE;cACb,CAAE;cAAAZ,QAAA,GAAC,eACY,EAACmC,KAAK,CAACnD,WAAW;YAAA;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGL2B,KAAK,CAACrD,UAAU,iBACfzC,OAAA;YAAKmD,KAAK,EAAE;cACVkB,QAAQ,EAAE,SAAS;cACnBX,KAAK,EAAE,SAAS;cAChBI,YAAY,EAAE;YAChB,CAAE;YAAAH,QAAA,GAAC,cACW,EAAC,IAAI4C,IAAI,CAACT,KAAK,CAACrD,UAAU,CAAC,CAAC+D,kBAAkB,CAAC,CAAC;UAAA;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CACN,eAGDnE,OAAA;YAAKmD,KAAK,EAAE;cACVC,OAAO,EAAE,MAAM;cACfiC,GAAG,EAAE,QAAQ;cACboB,UAAU,EAAE,MAAM;cAClBzB,SAAS,EAAE;YACb,CAAE;YAAArB,QAAA,gBACA3D,OAAA;cACEmD,KAAK,EAAE;gBACLuD,IAAI,EAAE,CAAC;gBACPlC,OAAO,EAAE,QAAQ;gBACjBC,UAAU,EAAE,aAAa;gBACzBM,MAAM,EAAE,mBAAmB;gBAC3BJ,YAAY,EAAE,KAAK;gBACnBQ,MAAM,EAAE,SAAS;gBACjBzB,KAAK,EAAE,SAAS;gBAChBW,QAAQ,EAAE,UAAU;gBACpBjB,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB8B,GAAG,EAAE;cACP,CAAE;cAAA1B,QAAA,gBAEF3D,OAAA,CAAC2G,IAAI;gBAAC9C,IAAI,EAAE;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,QAEpB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAER2B,KAAK,CAAC7D,QAAQ,MAAK9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgG,EAAE,kBAC1BnG,OAAA;cACEmD,KAAK,EAAE;gBACLuD,IAAI,EAAE,CAAC;gBACPlC,OAAO,EAAE,QAAQ;gBACjBC,UAAU,EAAE,aAAa;gBACzBM,MAAM,EAAE,mBAAmB;gBAC3BJ,YAAY,EAAE,KAAK;gBACnBQ,MAAM,EAAE,SAAS;gBACjBzB,KAAK,EAAE,SAAS;gBAChBW,QAAQ,EAAE,UAAU;gBACpBjB,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB8B,GAAG,EAAE;cACP,CAAE;cAAA1B,QAAA,gBAEF3D,OAAA,CAAC4G,MAAM;gBAAC/C,IAAI,EAAE;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAEtB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,GAxLD2B,KAAK,CAAC7D,QAAQ;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAyLhB,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjE,EAAA,CA1aID,eAAyB;EAAA,QACZJ,YAAY,EACTC,cAAc;AAAA;AAAA+G,EAAA,GAF9B5G,eAAyB;AA4a/B,eAAeA,eAAe;AAAC,IAAA4G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}