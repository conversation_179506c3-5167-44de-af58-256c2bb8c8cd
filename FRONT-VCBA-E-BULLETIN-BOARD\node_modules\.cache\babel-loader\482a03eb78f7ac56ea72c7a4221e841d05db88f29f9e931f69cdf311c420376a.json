{"ast": null, "code": "/**\n * Frontend Position-Based Permissions System\n * Mirrors the backend permission system for consistent UI behavior\n */\n\n// Define admin positions\nexport const POSITIONS = {\n  SUPER_ADMIN: 'super_admin',\n  PROFESSOR: 'professor'\n};\n// Define permission categories\nexport const PERMISSIONS = {\n  // Category & Subcategory Management\n  MANAGE_CATEGORIES: 'manage_categories',\n  MANAGE_SUBCATEGORIES: 'manage_subcategories',\n  // Admin Management\n  MANAGE_ADMIN_ACCOUNTS: 'manage_admin_accounts',\n  MANAGE_ADMIN_PROFILES: 'manage_admin_profiles',\n  // System Settings\n  MANAGE_SMS_SETTINGS: 'manage_sms_settings',\n  MANAGE_SYSTEM_SETTINGS: 'manage_system_settings',\n  // Student Management\n  MANAGE_STUDENTS: 'manage_students',\n  VIEW_STUDENTS: 'view_students',\n  // Content Management\n  CREATE_ANNOUNCEMENTS: 'create_announcements',\n  MANAGE_ANNOUNCEMENTS: 'manage_announcements',\n  CREATE_CALENDAR_EVENTS: 'create_calendar_events',\n  MANAGE_CALENDAR_EVENTS: 'manage_calendar_events',\n  CREATE_NEWSFEED_POSTS: 'create_newsfeed_posts',\n  // Archive Management\n  VIEW_ARCHIVE: 'view_archive',\n  MANAGE_ARCHIVE: 'manage_archive',\n  // TV Display Management\n  MANAGE_TV_DISPLAY: 'manage_tv_display'\n};\n// Define position-based permissions mapping (mirrors backend)\nconst POSITION_PERMISSIONS = {\n  [POSITIONS.SUPER_ADMIN]: [\n  // Full system access\n  PERMISSIONS.MANAGE_CATEGORIES, PERMISSIONS.MANAGE_SUBCATEGORIES, PERMISSIONS.MANAGE_ADMIN_ACCOUNTS, PERMISSIONS.MANAGE_ADMIN_PROFILES, PERMISSIONS.MANAGE_SMS_SETTINGS, PERMISSIONS.MANAGE_SYSTEM_SETTINGS, PERMISSIONS.MANAGE_STUDENTS, PERMISSIONS.VIEW_STUDENTS, PERMISSIONS.CREATE_ANNOUNCEMENTS, PERMISSIONS.MANAGE_ANNOUNCEMENTS, PERMISSIONS.CREATE_CALENDAR_EVENTS, PERMISSIONS.MANAGE_CALENDAR_EVENTS, PERMISSIONS.CREATE_NEWSFEED_POSTS, PERMISSIONS.VIEW_ARCHIVE, PERMISSIONS.MANAGE_ARCHIVE, PERMISSIONS.MANAGE_TV_DISPLAY],\n  [POSITIONS.PROFESSOR]: [\n  // Content creation and basic management\n  PERMISSIONS.VIEW_STUDENTS,\n  // Read-only access to students\n  PERMISSIONS.CREATE_ANNOUNCEMENTS, PERMISSIONS.MANAGE_ANNOUNCEMENTS,\n  // Can manage their own announcements\n  PERMISSIONS.CREATE_CALENDAR_EVENTS, PERMISSIONS.MANAGE_CALENDAR_EVENTS,\n  // Can manage their own calendar events\n  PERMISSIONS.CREATE_NEWSFEED_POSTS, PERMISSIONS.VIEW_ARCHIVE // Read-only archive access\n  ]\n};\n\n/**\n * Permission checking utilities\n */\nexport class PermissionChecker {\n  /**\n   * Check if a position has a specific permission\n   */\n  static hasPermission(position, permission) {\n    if (!position || !this.isValidPosition(position)) {\n      return false;\n    }\n    const positionPermissions = POSITION_PERMISSIONS[position] || [];\n    return positionPermissions.includes(permission);\n  }\n\n  /**\n   * Check if a user has a specific permission\n   */\n  static userHasPermission(user, permission) {\n    if (!user || !user.position) {\n      return false;\n    }\n    return this.hasPermission(user.position, permission);\n  }\n\n  /**\n   * Check if a position can access admin management features\n   */\n  static canManageAdmins(position) {\n    return this.hasPermission(position, PERMISSIONS.MANAGE_ADMIN_ACCOUNTS);\n  }\n\n  /**\n   * Check if a position can manage categories/subcategories\n   */\n  static canManageCategories(position) {\n    return this.hasPermission(position, PERMISSIONS.MANAGE_CATEGORIES);\n  }\n\n  /**\n   * Check if a position can manage system settings\n   */\n  static canManageSystemSettings(position) {\n    return this.hasPermission(position, PERMISSIONS.MANAGE_SYSTEM_SETTINGS);\n  }\n\n  /**\n   * Check if a position can fully manage students (create, update, delete)\n   */\n  static canManageStudents(position) {\n    return this.hasPermission(position, PERMISSIONS.MANAGE_STUDENTS);\n  }\n\n  /**\n   * Check if a position can view students (read-only)\n   */\n  static canViewStudents(position) {\n    return this.hasPermission(position, PERMISSIONS.VIEW_STUDENTS);\n  }\n\n  /**\n   * Check if a position can manage SMS settings\n   */\n  static canManageSMSSettings(position) {\n    return this.hasPermission(position, PERMISSIONS.MANAGE_SMS_SETTINGS);\n  }\n\n  /**\n   * Check if a position can create announcements\n   */\n  static canCreateAnnouncements(position) {\n    return this.hasPermission(position, PERMISSIONS.CREATE_ANNOUNCEMENTS);\n  }\n\n  /**\n   * Check if a position can create calendar events\n   */\n  static canCreateCalendarEvents(position) {\n    return this.hasPermission(position, PERMISSIONS.CREATE_CALENDAR_EVENTS);\n  }\n\n  /**\n   * Check if a position can manage TV display\n   */\n  static canManageTVDisplay(position) {\n    return this.hasPermission(position, PERMISSIONS.MANAGE_TV_DISPLAY);\n  }\n\n  /**\n   * Check if a position can view archive\n   */\n  static canViewArchive(position) {\n    return this.hasPermission(position, PERMISSIONS.VIEW_ARCHIVE);\n  }\n\n  /**\n   * Get all permissions for a position\n   */\n  static getPositionPermissions(position) {\n    if (!position || !this.isValidPosition(position)) {\n      return [];\n    }\n    return POSITION_PERMISSIONS[position] || [];\n  }\n\n  /**\n   * Validate if a position is valid\n   */\n  static isValidPosition(position) {\n    return Object.values(POSITIONS).includes(position);\n  }\n\n  /**\n   * Check if user is super admin\n   */\n  static isSuperAdmin(user) {\n    return (user === null || user === void 0 ? void 0 : user.position) === POSITIONS.SUPER_ADMIN;\n  }\n\n  /**\n   * Check if user is professor\n   */\n  static isProfessor(user) {\n    return (user === null || user === void 0 ? void 0 : user.position) === POSITIONS.PROFESSOR;\n  }\n\n  /**\n   * Get user position display name\n   */\n  static getPositionDisplayName(position) {\n    switch (position) {\n      case POSITIONS.SUPER_ADMIN:\n        return 'Super Administrator';\n      case POSITIONS.PROFESSOR:\n        return 'Professor';\n      default:\n        return 'Unknown';\n    }\n  }\n\n  /**\n   * Get position badge color for UI\n   */\n  static getPositionBadgeColor(position) {\n    switch (position) {\n      case POSITIONS.SUPER_ADMIN:\n        return '#dc2626';\n      // Red for super admin\n      case POSITIONS.PROFESSOR:\n        return '#2563eb';\n      // Blue for professor\n      default:\n        return '#6b7280';\n      // Gray for unknown\n    }\n  }\n}\n\n/**\n * Hook for using permissions in React components\n */\nexport const usePermissions = user => {\n  return {\n    // Position checks\n    isSuperAdmin: PermissionChecker.isSuperAdmin(user),\n    isProfessor: PermissionChecker.isProfessor(user),\n    // Permission checks\n    canManageAdmins: PermissionChecker.canManageAdmins(user === null || user === void 0 ? void 0 : user.position),\n    canManageCategories: PermissionChecker.canManageCategories(user === null || user === void 0 ? void 0 : user.position),\n    canManageSystemSettings: PermissionChecker.canManageSystemSettings(user === null || user === void 0 ? void 0 : user.position),\n    canManageStudents: PermissionChecker.canManageStudents(user === null || user === void 0 ? void 0 : user.position),\n    canViewStudents: PermissionChecker.canViewStudents(user === null || user === void 0 ? void 0 : user.position),\n    canManageSMSSettings: PermissionChecker.canManageSMSSettings(user === null || user === void 0 ? void 0 : user.position),\n    canCreateAnnouncements: PermissionChecker.canCreateAnnouncements(user === null || user === void 0 ? void 0 : user.position),\n    canCreateCalendarEvents: PermissionChecker.canCreateCalendarEvents(user === null || user === void 0 ? void 0 : user.position),\n    canManageTVDisplay: PermissionChecker.canManageTVDisplay(user === null || user === void 0 ? void 0 : user.position),\n    canViewArchive: PermissionChecker.canViewArchive(user === null || user === void 0 ? void 0 : user.position),\n    // Utility functions\n    hasPermission: permission => PermissionChecker.userHasPermission(user, permission),\n    getPositionDisplayName: () => PermissionChecker.getPositionDisplayName(user === null || user === void 0 ? void 0 : user.position),\n    getPositionBadgeColor: () => PermissionChecker.getPositionBadgeColor(user === null || user === void 0 ? void 0 : user.position)\n  };\n};\nexport default PermissionChecker;", "map": {"version": 3, "names": ["POSITIONS", "SUPER_ADMIN", "PROFESSOR", "PERMISSIONS", "MANAGE_CATEGORIES", "MANAGE_SUBCATEGORIES", "MANAGE_ADMIN_ACCOUNTS", "MANAGE_ADMIN_PROFILES", "MANAGE_SMS_SETTINGS", "MANAGE_SYSTEM_SETTINGS", "MANAGE_STUDENTS", "VIEW_STUDENTS", "CREATE_ANNOUNCEMENTS", "MANAGE_ANNOUNCEMENTS", "CREATE_CALENDAR_EVENTS", "MANAGE_CALENDAR_EVENTS", "CREATE_NEWSFEED_POSTS", "VIEW_ARCHIVE", "MANAGE_ARCHIVE", "MANAGE_TV_DISPLAY", "POSITION_PERMISSIONS", "PermissionChecker", "hasPermission", "position", "permission", "isValidPosition", "positionPermissions", "includes", "userHasPermission", "user", "canManageAdmins", "canManageCategories", "canManageSystemSettings", "canManageStudents", "canViewStudents", "canManageSMSSettings", "canCreateAnnouncements", "canCreateCalendarEvents", "canManageTVDisplay", "canViewArchive", "getPositionPermissions", "Object", "values", "isSuperAdmin", "isProfessor", "getPositionDisplayName", "getPositionBadgeColor", "usePermissions"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/utils/permissions.ts"], "sourcesContent": ["/**\n * Frontend Position-Based Permissions System\n * Mirrors the backend permission system for consistent UI behavior\n */\n\nimport { User } from '../types/auth.types';\n\n// Define admin positions\nexport const POSITIONS = {\n  SUPER_ADMIN: 'super_admin',\n  PROFESSOR: 'professor'\n} as const;\n\nexport type Position = typeof POSITIONS[keyof typeof POSITIONS];\n\n// Define permission categories\nexport const PERMISSIONS = {\n  // Category & Subcategory Management\n  MANAGE_CATEGORIES: 'manage_categories',\n  MANAGE_SUBCATEGORIES: 'manage_subcategories',\n  \n  // Admin Management\n  MANAGE_ADMIN_ACCOUNTS: 'manage_admin_accounts',\n  MANAGE_ADMIN_PROFILES: 'manage_admin_profiles',\n  \n  // System Settings\n  MANAGE_SMS_SETTINGS: 'manage_sms_settings',\n  MANAGE_SYSTEM_SETTINGS: 'manage_system_settings',\n  \n  // Student Management\n  MANAGE_STUDENTS: 'manage_students',\n  VIEW_STUDENTS: 'view_students',\n  \n  // Content Management\n  CREATE_ANNOUNCEMENTS: 'create_announcements',\n  <PERSON><PERSON><PERSON>_ANNOUNCEMENTS: 'manage_announcements',\n  CREATE_CALENDAR_EVENTS: 'create_calendar_events',\n  MANAGE_CALENDAR_EVENTS: 'manage_calendar_events',\n  CREATE_NEWSFEED_POSTS: 'create_newsfeed_posts',\n  \n  // Archive Management\n  VIEW_ARCHIVE: 'view_archive',\n  MANAGE_ARCHIVE: 'manage_archive',\n  \n  // TV Display Management\n  MANAGE_TV_DISPLAY: 'manage_tv_display'\n} as const;\n\nexport type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS];\n\n// Define position-based permissions mapping (mirrors backend)\nconst POSITION_PERMISSIONS: Record<Position, Permission[]> = {\n  [POSITIONS.SUPER_ADMIN]: [\n    // Full system access\n    PERMISSIONS.MANAGE_CATEGORIES,\n    PERMISSIONS.MANAGE_SUBCATEGORIES,\n    PERMISSIONS.MANAGE_ADMIN_ACCOUNTS,\n    PERMISSIONS.MANAGE_ADMIN_PROFILES,\n    PERMISSIONS.MANAGE_SMS_SETTINGS,\n    PERMISSIONS.MANAGE_SYSTEM_SETTINGS,\n    PERMISSIONS.MANAGE_STUDENTS,\n    PERMISSIONS.VIEW_STUDENTS,\n    PERMISSIONS.CREATE_ANNOUNCEMENTS,\n    PERMISSIONS.MANAGE_ANNOUNCEMENTS,\n    PERMISSIONS.CREATE_CALENDAR_EVENTS,\n    PERMISSIONS.MANAGE_CALENDAR_EVENTS,\n    PERMISSIONS.CREATE_NEWSFEED_POSTS,\n    PERMISSIONS.VIEW_ARCHIVE,\n    PERMISSIONS.MANAGE_ARCHIVE,\n    PERMISSIONS.MANAGE_TV_DISPLAY\n  ],\n  \n  [POSITIONS.PROFESSOR]: [\n    // Content creation and basic management\n    PERMISSIONS.VIEW_STUDENTS, // Read-only access to students\n    PERMISSIONS.CREATE_ANNOUNCEMENTS,\n    PERMISSIONS.MANAGE_ANNOUNCEMENTS, // Can manage their own announcements\n    PERMISSIONS.CREATE_CALENDAR_EVENTS,\n    PERMISSIONS.MANAGE_CALENDAR_EVENTS, // Can manage their own calendar events\n    PERMISSIONS.CREATE_NEWSFEED_POSTS,\n    PERMISSIONS.VIEW_ARCHIVE // Read-only archive access\n  ]\n};\n\n/**\n * Permission checking utilities\n */\nexport class PermissionChecker {\n  /**\n   * Check if a position has a specific permission\n   */\n  static hasPermission(position: string | undefined, permission: Permission): boolean {\n    if (!position || !this.isValidPosition(position)) {\n      return false;\n    }\n    \n    const positionPermissions = POSITION_PERMISSIONS[position as Position] || [];\n    return positionPermissions.includes(permission);\n  }\n  \n  /**\n   * Check if a user has a specific permission\n   */\n  static userHasPermission(user: User | null, permission: Permission): boolean {\n    if (!user || !user.position) {\n      return false;\n    }\n    \n    return this.hasPermission(user.position, permission);\n  }\n  \n  /**\n   * Check if a position can access admin management features\n   */\n  static canManageAdmins(position: string | undefined): boolean {\n    return this.hasPermission(position, PERMISSIONS.MANAGE_ADMIN_ACCOUNTS);\n  }\n  \n  /**\n   * Check if a position can manage categories/subcategories\n   */\n  static canManageCategories(position: string | undefined): boolean {\n    return this.hasPermission(position, PERMISSIONS.MANAGE_CATEGORIES);\n  }\n  \n  /**\n   * Check if a position can manage system settings\n   */\n  static canManageSystemSettings(position: string | undefined): boolean {\n    return this.hasPermission(position, PERMISSIONS.MANAGE_SYSTEM_SETTINGS);\n  }\n  \n  /**\n   * Check if a position can fully manage students (create, update, delete)\n   */\n  static canManageStudents(position: string | undefined): boolean {\n    return this.hasPermission(position, PERMISSIONS.MANAGE_STUDENTS);\n  }\n  \n  /**\n   * Check if a position can view students (read-only)\n   */\n  static canViewStudents(position: string | undefined): boolean {\n    return this.hasPermission(position, PERMISSIONS.VIEW_STUDENTS);\n  }\n  \n  /**\n   * Check if a position can manage SMS settings\n   */\n  static canManageSMSSettings(position: string | undefined): boolean {\n    return this.hasPermission(position, PERMISSIONS.MANAGE_SMS_SETTINGS);\n  }\n  \n  /**\n   * Check if a position can create announcements\n   */\n  static canCreateAnnouncements(position: string | undefined): boolean {\n    return this.hasPermission(position, PERMISSIONS.CREATE_ANNOUNCEMENTS);\n  }\n  \n  /**\n   * Check if a position can create calendar events\n   */\n  static canCreateCalendarEvents(position: string | undefined): boolean {\n    return this.hasPermission(position, PERMISSIONS.CREATE_CALENDAR_EVENTS);\n  }\n  \n  /**\n   * Check if a position can manage TV display\n   */\n  static canManageTVDisplay(position: string | undefined): boolean {\n    return this.hasPermission(position, PERMISSIONS.MANAGE_TV_DISPLAY);\n  }\n  \n  /**\n   * Check if a position can view archive\n   */\n  static canViewArchive(position: string | undefined): boolean {\n    return this.hasPermission(position, PERMISSIONS.VIEW_ARCHIVE);\n  }\n  \n  /**\n   * Get all permissions for a position\n   */\n  static getPositionPermissions(position: string | undefined): Permission[] {\n    if (!position || !this.isValidPosition(position)) {\n      return [];\n    }\n    \n    return POSITION_PERMISSIONS[position as Position] || [];\n  }\n  \n  /**\n   * Validate if a position is valid\n   */\n  static isValidPosition(position: string): position is Position {\n    return Object.values(POSITIONS).includes(position as Position);\n  }\n  \n  /**\n   * Check if user is super admin\n   */\n  static isSuperAdmin(user: User | null): boolean {\n    return user?.position === POSITIONS.SUPER_ADMIN;\n  }\n  \n  /**\n   * Check if user is professor\n   */\n  static isProfessor(user: User | null): boolean {\n    return user?.position === POSITIONS.PROFESSOR;\n  }\n  \n  /**\n   * Get user position display name\n   */\n  static getPositionDisplayName(position: string | undefined): string {\n    switch (position) {\n      case POSITIONS.SUPER_ADMIN:\n        return 'Super Administrator';\n      case POSITIONS.PROFESSOR:\n        return 'Professor';\n      default:\n        return 'Unknown';\n    }\n  }\n  \n  /**\n   * Get position badge color for UI\n   */\n  static getPositionBadgeColor(position: string | undefined): string {\n    switch (position) {\n      case POSITIONS.SUPER_ADMIN:\n        return '#dc2626'; // Red for super admin\n      case POSITIONS.PROFESSOR:\n        return '#2563eb'; // Blue for professor\n      default:\n        return '#6b7280'; // Gray for unknown\n    }\n  }\n}\n\n/**\n * Hook for using permissions in React components\n */\nexport const usePermissions = (user: User | null) => {\n  return {\n    // Position checks\n    isSuperAdmin: PermissionChecker.isSuperAdmin(user),\n    isProfessor: PermissionChecker.isProfessor(user),\n    \n    // Permission checks\n    canManageAdmins: PermissionChecker.canManageAdmins(user?.position),\n    canManageCategories: PermissionChecker.canManageCategories(user?.position),\n    canManageSystemSettings: PermissionChecker.canManageSystemSettings(user?.position),\n    canManageStudents: PermissionChecker.canManageStudents(user?.position),\n    canViewStudents: PermissionChecker.canViewStudents(user?.position),\n    canManageSMSSettings: PermissionChecker.canManageSMSSettings(user?.position),\n    canCreateAnnouncements: PermissionChecker.canCreateAnnouncements(user?.position),\n    canCreateCalendarEvents: PermissionChecker.canCreateCalendarEvents(user?.position),\n    canManageTVDisplay: PermissionChecker.canManageTVDisplay(user?.position),\n    canViewArchive: PermissionChecker.canViewArchive(user?.position),\n    \n    // Utility functions\n    hasPermission: (permission: Permission) => PermissionChecker.userHasPermission(user, permission),\n    getPositionDisplayName: () => PermissionChecker.getPositionDisplayName(user?.position),\n    getPositionBadgeColor: () => PermissionChecker.getPositionBadgeColor(user?.position),\n  };\n};\n\nexport default PermissionChecker;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAIA;AACA,OAAO,MAAMA,SAAS,GAAG;EACvBC,WAAW,EAAE,aAAa;EAC1BC,SAAS,EAAE;AACb,CAAU;AAIV;AACA,OAAO,MAAMC,WAAW,GAAG;EACzB;EACAC,iBAAiB,EAAE,mBAAmB;EACtCC,oBAAoB,EAAE,sBAAsB;EAE5C;EACAC,qBAAqB,EAAE,uBAAuB;EAC9CC,qBAAqB,EAAE,uBAAuB;EAE9C;EACAC,mBAAmB,EAAE,qBAAqB;EAC1CC,sBAAsB,EAAE,wBAAwB;EAEhD;EACAC,eAAe,EAAE,iBAAiB;EAClCC,aAAa,EAAE,eAAe;EAE9B;EACAC,oBAAoB,EAAE,sBAAsB;EAC5CC,oBAAoB,EAAE,sBAAsB;EAC5CC,sBAAsB,EAAE,wBAAwB;EAChDC,sBAAsB,EAAE,wBAAwB;EAChDC,qBAAqB,EAAE,uBAAuB;EAE9C;EACAC,YAAY,EAAE,cAAc;EAC5BC,cAAc,EAAE,gBAAgB;EAEhC;EACAC,iBAAiB,EAAE;AACrB,CAAU;AAIV;AACA,MAAMC,oBAAoD,GAAG;EAC3D,CAACpB,SAAS,CAACC,WAAW,GAAG;EACvB;EACAE,WAAW,CAACC,iBAAiB,EAC7BD,WAAW,CAACE,oBAAoB,EAChCF,WAAW,CAACG,qBAAqB,EACjCH,WAAW,CAACI,qBAAqB,EACjCJ,WAAW,CAACK,mBAAmB,EAC/BL,WAAW,CAACM,sBAAsB,EAClCN,WAAW,CAACO,eAAe,EAC3BP,WAAW,CAACQ,aAAa,EACzBR,WAAW,CAACS,oBAAoB,EAChCT,WAAW,CAACU,oBAAoB,EAChCV,WAAW,CAACW,sBAAsB,EAClCX,WAAW,CAACY,sBAAsB,EAClCZ,WAAW,CAACa,qBAAqB,EACjCb,WAAW,CAACc,YAAY,EACxBd,WAAW,CAACe,cAAc,EAC1Bf,WAAW,CAACgB,iBAAiB,CAC9B;EAED,CAACnB,SAAS,CAACE,SAAS,GAAG;EACrB;EACAC,WAAW,CAACQ,aAAa;EAAE;EAC3BR,WAAW,CAACS,oBAAoB,EAChCT,WAAW,CAACU,oBAAoB;EAAE;EAClCV,WAAW,CAACW,sBAAsB,EAClCX,WAAW,CAACY,sBAAsB;EAAE;EACpCZ,WAAW,CAACa,qBAAqB,EACjCb,WAAW,CAACc,YAAY,CAAC;EAAA;AAE7B,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMI,iBAAiB,CAAC;EAC7B;AACF;AACA;EACE,OAAOC,aAAaA,CAACC,QAA4B,EAAEC,UAAsB,EAAW;IAClF,IAAI,CAACD,QAAQ,IAAI,CAAC,IAAI,CAACE,eAAe,CAACF,QAAQ,CAAC,EAAE;MAChD,OAAO,KAAK;IACd;IAEA,MAAMG,mBAAmB,GAAGN,oBAAoB,CAACG,QAAQ,CAAa,IAAI,EAAE;IAC5E,OAAOG,mBAAmB,CAACC,QAAQ,CAACH,UAAU,CAAC;EACjD;;EAEA;AACF;AACA;EACE,OAAOI,iBAAiBA,CAACC,IAAiB,EAAEL,UAAsB,EAAW;IAC3E,IAAI,CAACK,IAAI,IAAI,CAACA,IAAI,CAACN,QAAQ,EAAE;MAC3B,OAAO,KAAK;IACd;IAEA,OAAO,IAAI,CAACD,aAAa,CAACO,IAAI,CAACN,QAAQ,EAAEC,UAAU,CAAC;EACtD;;EAEA;AACF;AACA;EACE,OAAOM,eAAeA,CAACP,QAA4B,EAAW;IAC5D,OAAO,IAAI,CAACD,aAAa,CAACC,QAAQ,EAAEpB,WAAW,CAACG,qBAAqB,CAAC;EACxE;;EAEA;AACF;AACA;EACE,OAAOyB,mBAAmBA,CAACR,QAA4B,EAAW;IAChE,OAAO,IAAI,CAACD,aAAa,CAACC,QAAQ,EAAEpB,WAAW,CAACC,iBAAiB,CAAC;EACpE;;EAEA;AACF;AACA;EACE,OAAO4B,uBAAuBA,CAACT,QAA4B,EAAW;IACpE,OAAO,IAAI,CAACD,aAAa,CAACC,QAAQ,EAAEpB,WAAW,CAACM,sBAAsB,CAAC;EACzE;;EAEA;AACF;AACA;EACE,OAAOwB,iBAAiBA,CAACV,QAA4B,EAAW;IAC9D,OAAO,IAAI,CAACD,aAAa,CAACC,QAAQ,EAAEpB,WAAW,CAACO,eAAe,CAAC;EAClE;;EAEA;AACF;AACA;EACE,OAAOwB,eAAeA,CAACX,QAA4B,EAAW;IAC5D,OAAO,IAAI,CAACD,aAAa,CAACC,QAAQ,EAAEpB,WAAW,CAACQ,aAAa,CAAC;EAChE;;EAEA;AACF;AACA;EACE,OAAOwB,oBAAoBA,CAACZ,QAA4B,EAAW;IACjE,OAAO,IAAI,CAACD,aAAa,CAACC,QAAQ,EAAEpB,WAAW,CAACK,mBAAmB,CAAC;EACtE;;EAEA;AACF;AACA;EACE,OAAO4B,sBAAsBA,CAACb,QAA4B,EAAW;IACnE,OAAO,IAAI,CAACD,aAAa,CAACC,QAAQ,EAAEpB,WAAW,CAACS,oBAAoB,CAAC;EACvE;;EAEA;AACF;AACA;EACE,OAAOyB,uBAAuBA,CAACd,QAA4B,EAAW;IACpE,OAAO,IAAI,CAACD,aAAa,CAACC,QAAQ,EAAEpB,WAAW,CAACW,sBAAsB,CAAC;EACzE;;EAEA;AACF;AACA;EACE,OAAOwB,kBAAkBA,CAACf,QAA4B,EAAW;IAC/D,OAAO,IAAI,CAACD,aAAa,CAACC,QAAQ,EAAEpB,WAAW,CAACgB,iBAAiB,CAAC;EACpE;;EAEA;AACF;AACA;EACE,OAAOoB,cAAcA,CAAChB,QAA4B,EAAW;IAC3D,OAAO,IAAI,CAACD,aAAa,CAACC,QAAQ,EAAEpB,WAAW,CAACc,YAAY,CAAC;EAC/D;;EAEA;AACF;AACA;EACE,OAAOuB,sBAAsBA,CAACjB,QAA4B,EAAgB;IACxE,IAAI,CAACA,QAAQ,IAAI,CAAC,IAAI,CAACE,eAAe,CAACF,QAAQ,CAAC,EAAE;MAChD,OAAO,EAAE;IACX;IAEA,OAAOH,oBAAoB,CAACG,QAAQ,CAAa,IAAI,EAAE;EACzD;;EAEA;AACF;AACA;EACE,OAAOE,eAAeA,CAACF,QAAgB,EAAwB;IAC7D,OAAOkB,MAAM,CAACC,MAAM,CAAC1C,SAAS,CAAC,CAAC2B,QAAQ,CAACJ,QAAoB,CAAC;EAChE;;EAEA;AACF;AACA;EACE,OAAOoB,YAAYA,CAACd,IAAiB,EAAW;IAC9C,OAAO,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEN,QAAQ,MAAKvB,SAAS,CAACC,WAAW;EACjD;;EAEA;AACF;AACA;EACE,OAAO2C,WAAWA,CAACf,IAAiB,EAAW;IAC7C,OAAO,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEN,QAAQ,MAAKvB,SAAS,CAACE,SAAS;EAC/C;;EAEA;AACF;AACA;EACE,OAAO2C,sBAAsBA,CAACtB,QAA4B,EAAU;IAClE,QAAQA,QAAQ;MACd,KAAKvB,SAAS,CAACC,WAAW;QACxB,OAAO,qBAAqB;MAC9B,KAAKD,SAAS,CAACE,SAAS;QACtB,OAAO,WAAW;MACpB;QACE,OAAO,SAAS;IACpB;EACF;;EAEA;AACF;AACA;EACE,OAAO4C,qBAAqBA,CAACvB,QAA4B,EAAU;IACjE,QAAQA,QAAQ;MACd,KAAKvB,SAAS,CAACC,WAAW;QACxB,OAAO,SAAS;MAAE;MACpB,KAAKD,SAAS,CAACE,SAAS;QACtB,OAAO,SAAS;MAAE;MACpB;QACE,OAAO,SAAS;MAAE;IACtB;EACF;AACF;;AAEA;AACA;AACA;AACA,OAAO,MAAM6C,cAAc,GAAIlB,IAAiB,IAAK;EACnD,OAAO;IACL;IACAc,YAAY,EAAEtB,iBAAiB,CAACsB,YAAY,CAACd,IAAI,CAAC;IAClDe,WAAW,EAAEvB,iBAAiB,CAACuB,WAAW,CAACf,IAAI,CAAC;IAEhD;IACAC,eAAe,EAAET,iBAAiB,CAACS,eAAe,CAACD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEN,QAAQ,CAAC;IAClEQ,mBAAmB,EAAEV,iBAAiB,CAACU,mBAAmB,CAACF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEN,QAAQ,CAAC;IAC1ES,uBAAuB,EAAEX,iBAAiB,CAACW,uBAAuB,CAACH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEN,QAAQ,CAAC;IAClFU,iBAAiB,EAAEZ,iBAAiB,CAACY,iBAAiB,CAACJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEN,QAAQ,CAAC;IACtEW,eAAe,EAAEb,iBAAiB,CAACa,eAAe,CAACL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEN,QAAQ,CAAC;IAClEY,oBAAoB,EAAEd,iBAAiB,CAACc,oBAAoB,CAACN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEN,QAAQ,CAAC;IAC5Ea,sBAAsB,EAAEf,iBAAiB,CAACe,sBAAsB,CAACP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEN,QAAQ,CAAC;IAChFc,uBAAuB,EAAEhB,iBAAiB,CAACgB,uBAAuB,CAACR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEN,QAAQ,CAAC;IAClFe,kBAAkB,EAAEjB,iBAAiB,CAACiB,kBAAkB,CAACT,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEN,QAAQ,CAAC;IACxEgB,cAAc,EAAElB,iBAAiB,CAACkB,cAAc,CAACV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEN,QAAQ,CAAC;IAEhE;IACAD,aAAa,EAAGE,UAAsB,IAAKH,iBAAiB,CAACO,iBAAiB,CAACC,IAAI,EAAEL,UAAU,CAAC;IAChGqB,sBAAsB,EAAEA,CAAA,KAAMxB,iBAAiB,CAACwB,sBAAsB,CAAChB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEN,QAAQ,CAAC;IACtFuB,qBAAqB,EAAEA,CAAA,KAAMzB,iBAAiB,CAACyB,qBAAqB,CAACjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEN,QAAQ;EACrF,CAAC;AACH,CAAC;AAED,eAAeF,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}