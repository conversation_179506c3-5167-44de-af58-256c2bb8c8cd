import React, { useState, useEffect, useCallback } from 'react';
import { studentService, CreateStudentRequest, Student, StudentsResponse } from '../../services/studentService';
import { AlertTriangle, RefreshCw, Edit, Key, Trash2, Info, User, Users } from 'lucide-react';
import { useAdminAuth } from '../../contexts/AdminAuthContext';
import { usePermissions } from '../../utils/permissions';
import { getImageUrl } from '../../config/constants';

const StudentManagement: React.FC = () => {
  // Auth context
  const { user } = useAdminAuth();
  const permissions = usePermissions(user);

  // State management
  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive'>('active');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalStudents, setTotalStudents] = useState(0);

  // Get available grade levels based on admin's position and assigned grade
  const getAvailableGradeLevels = () => {
    if (permissions.isSuperAdmin) {
      // Super admin can manage all grades
      return [11, 12];
    } else if (user?.grade_level) {
      // Grade-specific professor can only manage their assigned grade
      return [user.grade_level];
    } else {
      // Default to grades 11 and 12
      return [11, 12];
    }
  };

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);

  // Form states for creating/editing student
  const [formData, setFormData] = useState({
    studentNumber: '',
    email: '',
    firstName: '',
    middleName: '',
    lastName: '',
    suffix: '',
    phoneNumber: '',
    gradeLevel: permissions.isProfessor && user?.grade_level ? user.grade_level : 11,
    section: '',
    parentGuardianName: '',
    parentGuardianPhone: '',
    address: ''
  });
  const [profilePictureFile, setProfilePictureFile] = useState<File | null>(null);
  const [profilePicturePreview, setProfilePicturePreview] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // State for debounced search term
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');

  // Load students data - SIMPLIFIED WITHOUT AUTH GUARDS
  const loadStudents = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Build filter parameters
      const params: any = {
        page: currentPage,
        limit: 10,
        search: debouncedSearchTerm || undefined,
      };

      // Add status filter if not 'all'
      if (filterStatus === 'active') {
        params.is_active = true;
      } else if (filterStatus === 'inactive') {
        params.is_active = false;
      }
      // If filterStatus === 'all', don't add is_active parameter

      // Add grade level filter based on position and assigned grade
      if (permissions.isSuperAdmin) {
        // Super admin can see ALL students regardless of grade level
        // Don't add grade_level filter parameter
      } else if (user?.grade_level) {
        // Professor with assigned grade level can only see their grade
        params.grade_level = user.grade_level;
      }
      // If no grade_level specified, show all students (fallback)

      console.log('Loading students with params:', params);
      console.log('Filter status:', filterStatus, 'is_active param:', params.is_active);

      const response = await studentService.getStudents(params);
      console.log('API Response received:', response);
      console.log('Students loaded:', response.students.map(s => ({
        name: s.profile?.full_name || 'No name',
        email: s.email,
        is_active: s.is_active,
        status: s.is_active ? 'Active' : 'Inactive'
      })));

      // Additional debug: Check what we're about to set in state
      console.log('About to set students state with:', response.students.length, 'students');

      setStudents(response.students);
      setTotalPages(response.pagination.totalPages);
      setTotalStudents(response.pagination.total);
    } catch (error: any) {
      console.error('Error loading students:', error);
      setError('Failed to load students. Please check if the backend is running.');
      setStudents([]);
    } finally {
      setLoading(false);
    }
  }, [currentPage, debouncedSearchTerm, filterStatus, user]);

  // Load students when dependencies change
  useEffect(() => {
    // Check if user has permission to view students
    if (!permissions.canViewStudents) {
      setError('You do not have permission to view student information');
      setLoading(false);
      return;
    }

    loadStudents();
  }, [loadStudents, permissions.canViewStudents]);

  // Debounced search - update debounced term after delay
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
      setCurrentPage(1); // Reset to first page when searching
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  // Utility functions
  const getStatusColor = (isActive: boolean) => {
    return isActive ? '#22c55e' : '#f59e0b';
  };

  const getStatusText = (isActive: boolean) => {
    return isActive ? 'Active' : 'Inactive';
  };

  // Search handler
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Email generation function
  const generateEmail = (studentNumber: string, gradeLevel: string, section: string, lastName: string, firstName: string, middleName: string) => {
    if (!studentNumber || !gradeLevel || !section || !lastName || !firstName) {
      return '';
    }

    const firstLetter = firstName.charAt(0).toUpperCase();
    const middleInitial = middleName ? middleName.charAt(0).toUpperCase() : '';
    const cleanLastName = lastName.replace(/\s+/g, '').toLowerCase();
    const cleanSection = section.replace(/\s+/g, '').toUpperCase();

    return `${studentNumber}_${gradeLevel}_${cleanSection}_${cleanLastName}_${firstLetter}_${middleInitial}@gmail.com`;
  };

  // Form handlers
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    const newFormData = {
      ...formData,
      [name]: name === 'gradeLevel' ? parseInt(value) : value
    };

    // Auto-generate email when required fields are filled
    if (['studentNumber', 'gradeLevel', 'section', 'lastName', 'firstName', 'middleName'].includes(name)) {
      const generatedEmail = generateEmail(
        newFormData.studentNumber,
        newFormData.gradeLevel.toString(),
        newFormData.section,
        newFormData.lastName,
        newFormData.firstName,
        newFormData.middleName
      );
      if (generatedEmail) {
        newFormData.email = generatedEmail;
      }
    }

    setFormData(newFormData);
  };

  const resetForm = () => {
    setFormData({
      studentNumber: '',
      email: '',
      firstName: '',
      middleName: '',
      lastName: '',
      suffix: '',
      phoneNumber: '',
      gradeLevel: permissions.isProfessor && user?.grade_level ? user.grade_level : 11,
      section: '',
      parentGuardianName: '',
      parentGuardianPhone: '',
      address: ''
    });
    setProfilePictureFile(null);
    setProfilePicturePreview(null);
  };

  // Profile picture handling functions
  const handleProfilePictureChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        alert('Please select an image file');
        return;
      }

      // Validate file size (2MB limit)
      if (file.size > 2 * 1024 * 1024) {
        alert('File size must be less than 2MB');
        return;
      }

      setProfilePictureFile(file);

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setProfilePicturePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Remove profile picture for create modal (local only)
  const removeProfilePictureLocal = () => {
    setProfilePictureFile(null);
    setProfilePicturePreview(null);
  };

  // Remove profile picture for edit modal (calls API for existing students)
  const removeProfilePicture = async () => {
    try {
      // If we're editing an existing student and they have a profile picture, remove it from the server
      if (selectedStudent && selectedStudent.profile.profile_picture) {
        // Confirm before removing
        const confirmed = window.confirm(
          `Are you sure you want to remove ${selectedStudent.profile.full_name}'s profile picture? This action cannot be undone.`
        );

        if (!confirmed) {
          return;
        }

        setLoading(true);
        const updatedStudent = await studentService.removeStudentProfilePicture(selectedStudent.student_id.toString());

        // Update the selected student data immediately
        setSelectedStudent(updatedStudent);

        // Refresh the students list to show the updated data
        await loadStudents();

        alert('Profile picture removed successfully!');
      }

      // Clear local state regardless
      setProfilePictureFile(null);
      setProfilePicturePreview(null);
    } catch (error: any) {
      console.error('Error removing profile picture:', error);
      alert(`Failed to remove profile picture: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // CRUD Operations
  const handleCreateStudent = async () => {
    setIsSubmitting(true);
    setError(null);

    try {
      // Validate required fields
      if (!formData.studentNumber || !formData.email || !formData.firstName || !formData.lastName || !formData.phoneNumber || !formData.section || !formData.gradeLevel) {
        throw new Error('Please fill in all required fields (First Name, Last Name, Student Number, Email, Phone, Grade Level, Section)');
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) {
        throw new Error('Please enter a valid email address');
      }

      // Prepare student data for API
      const studentData: CreateStudentRequest = {
        // Account data
        student_number: formData.studentNumber,
        email: formData.email,
        password: 'Student123', // Default password
        is_active: true,
        created_by: user?.id || 1, // Current admin ID

        // Profile data
        first_name: formData.firstName,
        middle_name: formData.middleName || undefined,
        last_name: formData.lastName,
        suffix: formData.suffix || undefined,
        phone_number: formData.phoneNumber,
        grade_level: formData.gradeLevel,
        section: formData.section,
        parent_guardian_name: formData.parentGuardianName || undefined,
        parent_guardian_phone: formData.parentGuardianPhone || undefined,
        address: formData.address || undefined
      };

      // Debug: Log the data being sent
      console.log('Sending student data:', studentData);

      // Call API to create student
      const createdStudent = await studentService.createStudent(studentData);

      // Upload profile picture if provided
      if (profilePictureFile) {
        try {
          await studentService.uploadStudentProfilePicture(createdStudent.student_id.toString(), profilePictureFile);
        } catch (profileError: any) {
          console.error('Error uploading profile picture:', profileError);
          // Don't fail the entire creation process for profile picture upload failure
          alert(`Student account created successfully, but profile picture upload failed: ${profileError.message}`);
        }
      }

      alert(`Student account created successfully!\n\nStudent Details:\nName: ${createdStudent.profile.full_name}\nStudent Number: ${createdStudent.student_number}\nEmail: ${createdStudent.email}\n\nLogin Credentials:\nEmail: ${createdStudent.email}\nPassword: Student123\n\nPlease share these credentials with the student and ask them to change the password on first login.`);

      resetForm();
      setShowCreateModal(false);

      // Refresh the students list
      await loadStudents();

    } catch (error: any) {
      console.error('Error creating student:', error);
      setError(error.message || 'Failed to create student account. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditStudent = (student: Student) => {
    setSelectedStudent(student);
    setFormData({
      studentNumber: student.student_number,
      email: student.email,
      firstName: student.profile.first_name,
      middleName: student.profile.middle_name || '',
      lastName: student.profile.last_name,
      suffix: student.profile.suffix || '',
      phoneNumber: student.profile.phone_number,
      gradeLevel: student.profile.grade_level,
      section: student.profile.section,
      parentGuardianName: student.profile.parent_guardian_name || '',
      parentGuardianPhone: student.profile.parent_guardian_phone || '',
      address: student.profile.address || ''
    });

    // Set existing profile picture preview
    setProfilePictureFile(null);
    setProfilePicturePreview(student.profile.profile_picture ? getImageUrl(student.profile.profile_picture) : null);

    setShowEditModal(true);
  };

  const handleUpdateStudent = async () => {
    if (!selectedStudent) return;

    setIsSubmitting(true);
    setError(null);

    try {
      // Validate required fields
      if (!formData.studentNumber || !formData.email || !formData.firstName || !formData.lastName || !formData.phoneNumber || !formData.section) {
        throw new Error('Please fill in all required fields (First Name, Last Name, Student Number, Email, Phone, Section)');
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) {
        throw new Error('Please enter a valid email address');
      }

      // Prepare update data
      const updateData: Partial<CreateStudentRequest> = {
        student_number: formData.studentNumber,
        email: formData.email,
        first_name: formData.firstName,
        middle_name: formData.middleName || undefined,
        last_name: formData.lastName,
        suffix: formData.suffix || undefined,
        phone_number: formData.phoneNumber,
        grade_level: formData.gradeLevel,
        section: formData.section,
        parent_guardian_name: formData.parentGuardianName || undefined,
        parent_guardian_phone: formData.parentGuardianPhone || undefined,
        address: formData.address || undefined
      };

      // Call API to update student
      await studentService.updateStudent(selectedStudent.student_id.toString(), updateData);

      // Handle profile picture upload if a new file was selected
      if (profilePictureFile) {
        try {
          await studentService.uploadStudentProfilePicture(selectedStudent.student_id.toString(), profilePictureFile);
        } catch (profileError: any) {
          console.error('Error uploading profile picture:', profileError);
          alert(`Student information updated successfully, but profile picture upload failed: ${profileError.message}`);
        }
      }

      alert('Student information updated successfully!');

      resetForm();
      setShowEditModal(false);
      setSelectedStudent(null);

      // Refresh the students list
      await loadStudents();

    } catch (error: any) {
      console.error('Error updating student:', error);
      setError(error.message || 'Failed to update student information. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteStudent = (student: Student) => {
    setSelectedStudent(student);
    setShowDeleteModal(true);
  };

  const handleResetPassword = async (student: Student) => {
    if (!window.confirm(`Are you sure you want to reset the password for ${student.profile.full_name}?\n\nThe password will be reset to: Student123`)) {
      return;
    }

    try {
      setIsSubmitting(true);
      await studentService.resetStudentPassword(student.student_id.toString());

      // Show success message
      alert(`Password reset successfully for ${student.profile.full_name}!\n\nNew password: Student123`);

    } catch (error: any) {
      console.error('Error resetting password:', error);
      alert(`Failed to reset password: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const confirmDeleteStudent = async () => {
    if (!selectedStudent) return;

    setIsSubmitting(true);
    setError(null);

    try {
      // Call API to soft delete student (deactivate)
      await studentService.deleteStudent(selectedStudent.student_id.toString());

      alert('Student account has been deactivated successfully!');

      setShowDeleteModal(false);
      setSelectedStudent(null);

      // Refresh the students list
      await loadStudents();

    } catch (error: any) {
      console.error('Error deleting student:', error);
      setError(error.message || 'Failed to deactivate student account. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleStatusFilterChange = (value: 'all' | 'active' | 'inactive') => {
    setFilterStatus(value);
    setCurrentPage(1);
  };

  // Check permissions first
  if (!permissions.canViewStudents) {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '400px',
        textAlign: 'center',
        color: '#6b7280'
      }}>
        <Users size={64} style={{ marginBottom: '1rem', opacity: 0.5 }} />
        <h2 style={{ margin: '0 0 0.5rem', fontSize: '1.5rem', fontWeight: '600' }}>
          Access Denied
        </h2>
        <p style={{ margin: 0, fontSize: '1rem' }}>
          You do not have permission to view student information.
        </p>
        <div style={{
          marginTop: '1rem',
          padding: '0.5rem 1rem',
          background: permissions.getPositionBadgeColor(),
          borderRadius: '6px',
          color: 'white',
          fontSize: '0.875rem',
          fontWeight: '600'
        }}>
          Current Role: {permissions.getPositionDisplayName()}
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '1.5rem'
      }}>
        <div>
          <h1 style={{
            fontSize: '2rem',
            fontWeight: '700',
            color: '#2d5016',
            margin: '0 0 0.5rem 0'
          }}>
            <span style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
              <User size={20} color="#1e40af" />
              Student Management
            </span>
          </h1>
          <p style={{
            color: '#6b7280',
            margin: 0,
            fontSize: '1.1rem'
          }}>
            {permissions.isSuperAdmin
              ? 'Create, edit, and manage student accounts across all grades'
              : permissions.isProfessor
                ? 'Create, edit, and manage student accounts in your assigned grade'
                : 'View student information (read-only)'
            }
            {permissions.isSuperAdmin && (
              <span style={{
                display: 'inline-block',
                marginLeft: '0.5rem',
                padding: '0.25rem 0.5rem',
                backgroundColor: '#fef3c7',
                color: '#92400e',
                borderRadius: '0.375rem',
                fontSize: '0.875rem',
                fontWeight: '500'
              }}>
                All Grades
              </span>
            )}
            {permissions.isProfessor && user?.grade_level && (
              <span style={{
                display: 'inline-block',
                marginLeft: '0.5rem',
                padding: '0.25rem 0.5rem',
                backgroundColor: '#dbeafe',
                color: '#1e40af',
                borderRadius: '0.375rem',
                fontSize: '0.875rem',
                fontWeight: '500'
              }}>
                Grade {user.grade_level} Only
              </span>
            )}
          </p>
        </div>
      </div>

      {/* Filters and Search */}
      <div style={{
        background: 'white',
        borderRadius: '16px',
        padding: '1.5rem',
        marginBottom: '2rem',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
        border: '1px solid #e8f5e8'
      }}>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', flexWrap: 'wrap' }}>
          {/* Create Student Account Button - Only for super_admin */}
          {permissions.canManageStudents && (
            <button
              onClick={() => setShowCreateModal(true)}
              disabled={loading}
              style={{
                background: 'linear-gradient(135deg, #2d5016 0%, #4ade80 100%)',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                padding: '0.5rem 1rem',
                fontSize: '0.9rem',
                fontWeight: '600',
                cursor: loading ? 'not-allowed' : 'pointer',
                opacity: loading ? 0.6 : 1,
                transition: 'all 0.2s ease',
                display: 'flex',
                alignItems: 'center',
                gap: '0.4rem',
                whiteSpace: 'nowrap'
              }}
            >
              <span style={{ fontSize: '1rem' }}>+</span>
              Create Student Account
            </button>
          )}

          {/* Search */}
          <div style={{ flex: 1, minWidth: '300px' }}>
            <input
              type="text"
              placeholder="Search students"
              value={searchTerm}
              onChange={handleSearchChange}
              style={{
                width: '100%',
                padding: '0.75rem 1rem',
                border: '1px solid #e8f5e8',
                borderRadius: '8px',
                fontSize: '1rem',
                outline: 'none'
              }}
            />
          </div>

          {/* Status Filter Buttons */}
          <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>
            <span style={{ fontSize: '0.9rem', color: '#666', marginRight: '0.5rem' }}>Filter:</span>
            <button
              onClick={() => handleStatusFilterChange('all')}
              style={{
                padding: '0.5rem 1rem',
                border: filterStatus === 'all' ? '2px solid #4CAF50' : '1px solid #ddd',
                borderRadius: '6px',
                fontSize: '0.85rem',
                backgroundColor: filterStatus === 'all' ? '#4CAF50' : '#fff',
                color: filterStatus === 'all' ? '#fff' : '#333',
                cursor: 'pointer',
                fontWeight: filterStatus === 'all' ? 'bold' : 'normal',
                transition: 'all 0.2s ease'
              }}
            >
              All
            </button>
            <button
              onClick={() => handleStatusFilterChange('active')}
              style={{
                padding: '0.5rem 1rem',
                border: filterStatus === 'active' ? '2px solid #4CAF50' : '1px solid #ddd',
                borderRadius: '6px',
                fontSize: '0.85rem',
                backgroundColor: filterStatus === 'active' ? '#4CAF50' : '#fff',
                color: filterStatus === 'active' ? '#fff' : '#333',
                cursor: 'pointer',
                fontWeight: filterStatus === 'active' ? 'bold' : 'normal',
                transition: 'all 0.2s ease'
              }}
            >
              Active
            </button>
            <button
              onClick={() => handleStatusFilterChange('inactive')}
              style={{
                padding: '0.5rem 1rem',
                border: filterStatus === 'inactive' ? '2px solid #f44336' : '1px solid #ddd',
                borderRadius: '6px',
                fontSize: '0.85rem',
                backgroundColor: filterStatus === 'inactive' ? '#f44336' : '#fff',
                color: filterStatus === 'inactive' ? '#fff' : '#333',
                cursor: 'pointer',
                fontWeight: filterStatus === 'inactive' ? 'bold' : 'normal',
                transition: 'all 0.2s ease'
              }}
            >
              Inactive
            </button>
          </div>

          {/* Refresh Button */}
          <button
            onClick={loadStudents}
            disabled={loading}
            style={{
              background: '#f8fdf8',
              border: '1px solid #e8f5e8',
              borderRadius: '8px',
              padding: '0.75rem 1rem',
              fontSize: '1rem',
              cursor: loading ? 'not-allowed' : 'pointer',
              opacity: loading ? 0.6 : 1,
              transition: 'all 0.2s ease'
            }}
          >
            <span style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
              <RefreshCw size={16} />
              Refresh
            </span>
          </button>
        </div>
      </div>

      {/* Students Table */}
      <div style={{
        background: 'white',
        borderRadius: '16px',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
        border: '1px solid #e8f5e8',
        overflow: 'hidden'
      }}>
        {/* Table Header */}
        <div style={{
          background: '#f8fdf8',
          padding: '1rem 1.5rem',
          borderBottom: '1px solid #e8f5e8',
          display: 'grid',
          gridTemplateColumns: '60px 1fr 2fr 2fr 1fr 1fr 1fr 150px',
          gap: '1rem',
          fontWeight: '600',
          color: '#2d5016',
          fontSize: '0.875rem'
        }}>
          <div>Photo</div>
          <div>Student #</div>
          <div>Name</div>
          <div>Email</div>
          <div>Grade & Section</div>
          <div>Phone</div>
          <div>Status</div>
          <div>Actions</div>
        </div>

        {/* Table Body */}
        {loading ? (
          <div style={{ padding: '2rem', textAlign: 'center', color: '#6b7280' }}>
            Loading students...
          </div>
        ) : students.length === 0 ? (
          <div style={{ padding: '2rem', textAlign: 'center', color: '#6b7280' }}>
            No students found. {searchTerm && 'Try adjusting your search criteria.'}
          </div>
        ) : (
          students.map(student => (
            <div
              key={student.student_id}
              style={{
                padding: '1rem 1.5rem',
                borderBottom: '1px solid #f3f4f6',
                display: 'grid',
                gridTemplateColumns: '60px 1fr 2fr 2fr 1fr 1fr 1fr 150px',
                gap: '1rem',
                alignItems: 'center',
                fontSize: '0.875rem'
              }}
            >
              <div style={{ display: 'flex', justifyContent: 'center' }}>
                {student.profile.profile_picture ? (
                  <img
                    src={getImageUrl(student.profile.profile_picture) || ''}
                    alt={`${student.profile.full_name} profile`}
                    style={{
                      width: '40px',
                      height: '40px',
                      borderRadius: '50%',
                      objectFit: 'cover',
                      border: '2px solid #e8f5e8'
                    }}
                    onError={(e) => {
                      // Fallback to initials if image fails to load
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      const parent = target.parentElement;
                      if (parent) {
                        parent.innerHTML = `
                          <div style="
                            width: 40px;
                            height: 40px;
                            border-radius: 50%;
                            background-color: #f3f4f6;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: #6b7280;
                            font-size: 0.75rem;
                            font-weight: 600;
                          ">
                            ${student.profile.first_name.charAt(0)}${student.profile.last_name.charAt(0)}
                          </div>
                        `;
                      }
                    }}
                  />
                ) : (
                  <div style={{
                    width: '40px',
                    height: '40px',
                    borderRadius: '50%',
                    backgroundColor: '#f3f4f6',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: '#6b7280',
                    fontSize: '0.75rem',
                    fontWeight: '600'
                  }}>
                    {student.profile.first_name.charAt(0)}{student.profile.last_name.charAt(0)}
                  </div>
                )}
              </div>
              <div style={{ fontWeight: '600', color: '#2d5016' }}>
                {student.student_number}
              </div>
              <div style={{ color: '#374151' }}>
                {student.profile.full_name}
              </div>
              <div style={{ color: '#6b7280' }}>
                {student.email}
              </div>
              <div style={{ color: '#374151' }}>
                Grade {student.profile.grade_level} - {student.profile.section}
              </div>
              <div style={{ color: '#374151' }}>
                {student.profile.phone_number}
              </div>
              <div>
                <span style={{
                  background: getStatusColor(student.is_active),
                  color: 'white',
                  padding: '0.25rem 0.75rem',
                  borderRadius: '12px',
                  fontSize: '0.75rem',
                  fontWeight: '600'
                }}>
                  {getStatusText(student.is_active)}
                </span>
              </div>
              <div style={{ display: 'flex', gap: '0.25rem' }}>
                {permissions.canManageStudents ? (
                  <>
                    <button
                      onClick={() => handleEditStudent(student)}
                      title="Edit Student"
                      style={{
                        background: '#3b82f6',
                        color: 'white',
                        border: 'none',
                        borderRadius: '6px',
                        padding: '0.5rem',
                        cursor: 'pointer',
                        fontSize: '0.75rem'
                      }}
                    >
                      <Edit size={16} />
                    </button>
                    <button
                      onClick={() => handleResetPassword(student)}
                      title="Reset Password to Default (Student123)"
                      style={{
                        background: '#f59e0b',
                        color: 'white',
                        border: 'none',
                        borderRadius: '6px',
                        padding: '0.5rem',
                        cursor: 'pointer',
                        fontSize: '0.75rem'
                      }}
                    >
                      <Key size={16} />
                    </button>
                    {permissions.isSuperAdmin && (
                      <button
                        onClick={() => handleDeleteStudent(student)}
                        title="Deactivate Student"
                        style={{
                          background: '#ef4444',
                          color: 'white',
                          border: 'none',
                          borderRadius: '6px',
                          padding: '0.5rem',
                          cursor: 'pointer',
                          fontSize: '0.75rem'
                        }}
                      >
                        <Trash2 size={16} />
                      </button>
                    )}
                    {permissions.isProfessor && (
                      <div style={{
                        padding: '0.5rem',
                        background: '#f3f4f6',
                        color: '#6b7280',
                        borderRadius: '6px',
                        fontSize: '0.65rem',
                        fontWeight: '500',
                        display: 'flex',
                        alignItems: 'center'
                      }}>
                        Edit Only
                      </div>
                    )}
                  </>
                ) : (
                  <div style={{
                    padding: '0.5rem 1rem',
                    background: '#f3f4f6',
                    color: '#6b7280',
                    borderRadius: '6px',
                    fontSize: '0.75rem',
                    fontWeight: '500'
                  }}>
                    View Only
                  </div>
                )}
              </div>
            </div>
          ))
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div style={{
            padding: '1rem 1.5rem',
            borderTop: '1px solid #f3f4f6',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>
              Page {currentPage} of {totalPages}
            </div>
            <div style={{ display: 'flex', gap: '0.5rem' }}>
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                style={{
                  background: currentPage === 1 ? '#f3f4f6' : '#2d5016',
                  color: currentPage === 1 ? '#9ca3af' : 'white',
                  border: 'none',
                  borderRadius: '6px',
                  padding: '0.5rem 1rem',
                  cursor: currentPage === 1 ? 'not-allowed' : 'pointer',
                  fontSize: '0.875rem'
                }}
              >
                Previous
              </button>
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                style={{
                  background: currentPage === totalPages ? '#f3f4f6' : '#2d5016',
                  color: currentPage === totalPages ? '#9ca3af' : 'white',
                  border: 'none',
                  borderRadius: '6px',
                  padding: '0.5rem 1rem',
                  cursor: currentPage === totalPages ? 'not-allowed' : 'pointer',
                  fontSize: '0.875rem'
                }}
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Create Student Modal */}
      {showCreateModal && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000,
          padding: '1rem'
        }}>
          <div style={{
            background: 'white',
            borderRadius: '16px',
            padding: '2rem',
            width: '95%',
            maxWidth: '1200px',
            maxHeight: '95vh',
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '2rem'
            }}>
              <h2 style={{
                margin: 0,
                color: '#2d5016',
                fontSize: '1.5rem',
                fontWeight: '700'
              }}>
                Create New Student Account
              </h2>
              <button
                onClick={() => {
                  setShowCreateModal(false);
                  resetForm();
                }}
                style={{
                  background: 'none',
                  border: 'none',
                  fontSize: '1.5rem',
                  cursor: 'pointer',
                  color: '#6b7280'
                }}
              >
                ×
              </button>
            </div>

            {error && (
              <div style={{
                background: '#fef2f2',
                border: '1px solid #fecaca',
                borderRadius: '8px',
                padding: '1rem',
                marginBottom: '1rem',
                color: '#dc2626'
              }}>
                {error}
              </div>
            )}

            <div style={{ flex: 1, overflow: 'auto', paddingRight: '0.5rem' }}>
              <form onSubmit={(e) => { e.preventDefault(); handleCreateStudent(); }} style={{
                display: 'grid',
                gridTemplateColumns: '1fr 1fr',
                gap: '2rem',
                height: 'fit-content'
              }}>
                {/* Left Column */}
                <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                  {/* Profile Picture Upload */}
                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>
                      Profile Picture
                    </label>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                      {profilePicturePreview ? (
                        <img
                          src={profilePicturePreview}
                          alt="Profile preview"
                          style={{
                            width: '80px',
                            height: '80px',
                            borderRadius: '50%',
                            objectFit: 'cover',
                            border: '2px solid #e8f5e8'
                          }}
                          onError={(e) => {
                            console.error('Profile picture preview failed to load:', profilePicturePreview);
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                            const parent = target.parentElement;
                            if (parent) {
                              parent.innerHTML = `
                                <div style="
                                  width: 80px;
                                  height: 80px;
                                  border-radius: 50%;
                                  background-color: #f3f4f6;
                                  display: flex;
                                  align-items: center;
                                  justify-content: center;
                                  color: #6b7280;
                                  font-size: 0.875rem;
                                ">
                                  No Photo
                                </div>
                              `;
                            }
                          }}
                        />
                      ) : (
                        <div style={{
                          width: '80px',
                          height: '80px',
                          borderRadius: '50%',
                          backgroundColor: '#f3f4f6',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          color: '#6b7280',
                          fontSize: '0.875rem'
                        }}>
                          No Photo
                        </div>
                      )}
                      <div style={{ flex: 1 }}>
                        <input
                          type="file"
                          accept="image/*"
                          onChange={handleProfilePictureChange}
                          style={{
                            width: '100%',
                            padding: '0.5rem',
                            border: '1px solid #d1d5db',
                            borderRadius: '8px',
                            fontSize: '0.875rem'
                          }}
                        />
                        {profilePicturePreview && (
                          <button
                            type="button"
                            onClick={removeProfilePictureLocal}
                            style={{
                              marginTop: '0.5rem',
                              padding: '0.25rem 0.5rem',
                              backgroundColor: '#ef4444',
                              color: 'white',
                              border: 'none',
                              borderRadius: '4px',
                              fontSize: '0.75rem',
                              cursor: 'pointer'
                            }}
                          >
                            Remove
                          </button>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Student Number */}
                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>
                      Student Number *
                    </label>
                    <input
                      type="text"
                      name="studentNumber"
                      value={formData.studentNumber}
                      onChange={handleInputChange}
                      required
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '1rem'
                      }}
                      placeholder="e.g., 2025-0001"
                    />
                  </div>

                  {/* Email */}
                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>
                      Email Address * (Auto-generated)
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      readOnly
                      required
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '1rem',
                        backgroundColor: '#f9fafb',
                        color: '#6b7280'
                      }}
                      placeholder="Email will be auto-generated based on student details"
                    />
                  </div>

                {/* Name Fields */}
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
                  {/* First Name */}
                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>
                      First Name *
                    </label>
                    <input
                      type="text"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      required
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '1rem'
                      }}
                      placeholder="Juan"
                    />
                  </div>

                  {/* Last Name */}
                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>
                      Last Name *
                    </label>
                    <input
                      type="text"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      required
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '1rem'
                      }}
                      placeholder="Cruz"
                    />
                  </div>
                </div>

                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
                  {/* Middle Name */}
                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>
                      Middle Name
                    </label>
                    <input
                      type="text"
                      name="middleName"
                      value={formData.middleName}
                      onChange={handleInputChange}
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '1rem'
                      }}
                      placeholder="Dela"
                    />
                  </div>

                  {/* Suffix */}
                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>
                      Suffix
                    </label>
                    <input
                      type="text"
                      name="suffix"
                      value={formData.suffix}
                      onChange={handleInputChange}
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '1rem'
                      }}
                      placeholder="Jr., Sr., III"
                    />
                  </div>
                  </div>
                </div>

                {/* Right Column */}
                <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                  {/* Phone Number */}
                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>
                      Phone Number *
                    </label>
                    <input
                      type="tel"
                      name="phoneNumber"
                      value={formData.phoneNumber}
                      onChange={handleInputChange}
                      onInput={(e) => {
                        // Allow only numbers
                        e.currentTarget.value = e.currentTarget.value.replace(/[^0-9]/g, '');
                      }}
                      required
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '1rem'
                      }}
                      placeholder="09123456789"
                      maxLength={11}
                    />
                  </div>

                  {/* Grade Level */}
                  <div>
                    <label
                      style={{
                        display: 'block',
                        marginBottom: '0.5rem',
                        fontWeight: '600',
                        color: '#374151'
                      }}
                    >
                      Grade Level *
                    </label>
                    <select
                      name="gradeLevel"
                      value={formData.gradeLevel}
                      onChange={handleInputChange}
                      required
                      disabled={permissions.isProfessor && getAvailableGradeLevels().length === 1} // Disable for professor if only one option
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '1rem',
                        backgroundColor: 'white'
                      }}
                    >
                      {getAvailableGradeLevels().map((grade) => (
                        <option key={grade} value={grade}>
                          Grade {grade}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Section */}
                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>
                      Section *
                    </label>
                    <input
                      type="text"
                      name="section"
                      value={formData.section}
                      onChange={handleInputChange}
                      required
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '1rem'
                      }}
                      placeholder="A, B, C, etc."
                    />
                  </div>

                  {/* Parent/Guardian Name */}
                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>
                      Parent/Guardian Name
                    </label>
                    <input
                      type="text"
                      name="parentGuardianName"
                      value={formData.parentGuardianName}
                      onChange={handleInputChange}
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '1rem'
                      }}
                      placeholder="Parent/Guardian Name"
                    />
                  </div>

                  {/* Parent/Guardian Phone */}
                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>
                      Parent/Guardian Phone
                    </label>
                    <input
                      type="tel"
                      name="parentGuardianPhone"
                      value={formData.parentGuardianPhone}
                      onChange={handleInputChange}
                      onInput={(e) => {
                        // Allow only numbers
                        e.currentTarget.value = e.currentTarget.value.replace(/[^0-9]/g, '');
                      }}
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '1rem'
                      }}
                      placeholder="09123456789"
                      maxLength={11}
                    />
                  </div>

                  {/* Address */}
                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>
                      Address
                    </label>
                    <textarea
                      name="address"
                      value={formData.address}
                      onChange={handleInputChange}
                      rows={4}
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '1rem',
                        resize: 'vertical'
                      }}
                      placeholder="Complete address"
                    />
                  </div>

                  {/* Default Password Info */}
                  <div style={{
                    background: '#f0f9ff',
                    border: '1px solid #bae6fd',
                    borderRadius: '8px',
                    padding: '1rem'
                  }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
                      <Info size={20} color="#0369a1" />
                      <span style={{ fontWeight: '600', color: '#0369a1' }}>Default Login Credentials</span>
                    </div>
                    <p style={{ margin: 0, color: '#0369a1', fontSize: '0.875rem' }}>
                      The student account will be created with the default password: <strong>Student123</strong>
                      <br />
                      Please share these credentials with the student and ask them to change the password on first login.
                    </p>
                  </div>
                </div>

                {/* Form Actions - Spans both columns */}
                <div style={{
                  gridColumn: '1 / -1',
                  display: 'flex',
                  justifyContent: 'flex-end',
                  gap: '1rem',
                  marginTop: '1rem',
                  paddingTop: '1rem',
                  borderTop: '1px solid #e5e7eb'
                }}>

                  <button
                    type="button"
                    onClick={() => {
                      setShowCreateModal(false);
                      resetForm();
                    }}
                    disabled={isSubmitting}
                    style={{
                      background: '#f3f4f6',
                      color: '#374151',
                      border: 'none',
                      borderRadius: '8px',
                      padding: '0.75rem 1.5rem',
                      fontSize: '1rem',
                      cursor: isSubmitting ? 'not-allowed' : 'pointer',
                      opacity: isSubmitting ? 0.6 : 1
                    }}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    style={{
                      background: isSubmitting ? '#9ca3af' : 'linear-gradient(135deg, #2d5016 0%, #4ade80 100%)',
                      color: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      padding: '0.75rem 1.5rem',
                      fontSize: '1rem',
                      cursor: isSubmitting ? 'not-allowed' : 'pointer',
                      fontWeight: '600'
                    }}
                  >
                    {isSubmitting ? 'Creating...' : 'Create Student Account'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Edit Student Modal */}
      {showEditModal && selectedStudent && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            borderRadius: '16px',
            padding: '2rem',
            width: '95%',
            maxWidth: '1200px',
            maxHeight: '95vh',
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '2rem'
            }}>
              <h2 style={{
                margin: 0,
                color: '#2d5016',
                fontSize: '1.5rem',
                fontWeight: '700'
              }}>
                Edit Student: {selectedStudent.profile.full_name}
              </h2>
              <button
                onClick={() => {
                  setShowEditModal(false);
                  setSelectedStudent(null);
                  resetForm();
                }}
                style={{
                  background: 'none',
                  border: 'none',
                  fontSize: '1.5rem',
                  cursor: 'pointer',
                  color: '#6b7280'
                }}
              >
                ×
              </button>
            </div>

            {error && (
              <div style={{
                background: '#fef2f2',
                border: '1px solid #fecaca',
                borderRadius: '8px',
                padding: '1rem',
                marginBottom: '1rem',
                color: '#dc2626'
              }}>
                {error}
              </div>
            )}

            <div style={{ flex: 1, overflow: 'auto', paddingRight: '0.5rem' }}>
              <form onSubmit={(e) => { e.preventDefault(); handleUpdateStudent(); }} style={{
                display: 'grid',
                gridTemplateColumns: '1fr 1fr',
                gap: '2rem',
                height: 'fit-content'
              }}>
                {/* Left Column */}
                <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                  {/* Profile Picture Upload */}
                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>
                      Profile Picture
                    </label>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                      {profilePicturePreview ? (
                        <img
                          src={profilePicturePreview}
                          alt="Profile preview"
                          style={{
                            width: '80px',
                            height: '80px',
                            borderRadius: '50%',
                            objectFit: 'cover',
                            border: '2px solid #e8f5e8'
                          }}
                          onError={(e) => {
                            console.error('Edit modal profile picture preview failed to load:', profilePicturePreview);
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                            const parent = target.parentElement;
                            if (parent) {
                              parent.innerHTML = `
                                <div style="
                                  width: 80px;
                                  height: 80px;
                                  border-radius: 50%;
                                  background-color: #f3f4f6;
                                  display: flex;
                                  align-items: center;
                                  justify-content: center;
                                  color: #6b7280;
                                  font-size: 0.875rem;
                                ">
                                  No Photo
                                </div>
                              `;
                            }
                          }}
                        />
                      ) : (
                        <div style={{
                          width: '80px',
                          height: '80px',
                          borderRadius: '50%',
                          backgroundColor: '#f3f4f6',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          color: '#6b7280',
                          fontSize: '0.875rem'
                        }}>
                          No Photo
                        </div>
                      )}
                      <div style={{ flex: 1 }}>
                        <input
                          type="file"
                          accept="image/*"
                          onChange={handleProfilePictureChange}
                          style={{
                            width: '100%',
                            padding: '0.5rem',
                            border: '1px solid #d1d5db',
                            borderRadius: '8px',
                            fontSize: '0.875rem'
                          }}
                        />
                        <div style={{ display: 'flex', gap: '0.5rem', marginTop: '0.5rem' }}>
                          {profilePicturePreview && (
                            <button
                              type="button"
                              onClick={removeProfilePicture}
                              style={{
                                padding: '0.25rem 0.5rem',
                                backgroundColor: '#ef4444',
                                color: 'white',
                                border: 'none',
                                borderRadius: '4px',
                                fontSize: '0.75rem',
                                cursor: 'pointer'
                              }}
                            >
                              Remove Picture
                            </button>
                          )}
                          {/* {selectedStudent?.profile.profile_picture && !profilePictureFile && (
                            <button
                              type="button"
                              onClick={removeProfilePicture}
                              style={{
                                padding: '0.25rem 0.5rem',
                                backgroundColor: '#dc2626',
                                color: 'white',
                                border: 'none',
                                borderRadius: '4px',
                                fontSize: '0.75rem',
                                cursor: 'pointer'
                              }}
                            >
                              Remove Current Picture
                            </button>
                          )} */}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Student Number */}
                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>
                      Student Number *
                    </label>
                    <input
                      type="text"
                      name="studentNumber"
                      value={formData.studentNumber}
                      onChange={handleInputChange}
                      required
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '1rem'
                      }}
                      placeholder="e.g., 2025-0001"
                    />
                  </div>

                  {/* Email */}
                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>
                      Email Address * (Auto-generated)
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      readOnly
                      required
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '1rem',
                        backgroundColor: '#f9fafb',
                        color: '#6b7280'
                      }}
                    />
                  </div>

                  {/* First Name */}
                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>
                      First Name *
                    </label>
                    <input
                      type="text"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      required
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '1rem'
                      }}
                      placeholder="Juan"
                    />
                  </div>

                  {/* Last Name */}
                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>
                      Last Name *
                    </label>
                    <input
                      type="text"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      required
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '1rem'
                      }}
                      placeholder="Cruz"
                    />
                  </div>

                  {/* Middle Name */}
                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>
                      Middle Name
                    </label>
                    <input
                      type="text"
                      name="middleName"
                      value={formData.middleName}
                      onChange={handleInputChange}
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '1rem'
                      }}
                      placeholder="Dela"
                    />
                  </div>

                  {/* Suffix */}
                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>
                      Suffix
                    </label>
                    <input
                      type="text"
                      name="suffix"
                      value={formData.suffix}
                      onChange={handleInputChange}
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '1rem'
                      }}
                      placeholder="Jr., Sr., III"
                    />
                  </div>
                </div>

                {/* Right Column */}
                <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                  {/* Phone Number */}
                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>
                      Phone Number *
                    </label>
                    <input
                      type="tel"
                      name="phoneNumber"
                      value={formData.phoneNumber}
                      onChange={handleInputChange}
                      onInput={(e) => {
                        // Allow only numbers
                        e.currentTarget.value = e.currentTarget.value.replace(/[^0-9]/g, '');
                      }}
                      required
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '1rem'
                      }}
                      maxLength={11}
                    />
                  </div>

                  {/* Grade Level */}
                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>
                      Grade Level *
                    </label>
                    <select
                      name="gradeLevel"
                      value={formData.gradeLevel}
                      onChange={handleInputChange}
                      required
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '1rem',
                        backgroundColor: 'white'
                      }}
                    >
                      {getAvailableGradeLevels().map(grade => (
                        <option key={grade} value={grade}>Grade {grade}</option>
                      ))}
                    </select>
                  </div>

                  {/* Section */}
                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>
                      Section *
                    </label>
                    <input
                      type="text"
                      name="section"
                      value={formData.section}
                      onChange={handleInputChange}
                      required
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '1rem'
                      }}
                    />
                  </div>

                  {/* Parent/Guardian Name */}
                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>
                      Parent/Guardian Name
                    </label>
                    <input
                      type="text"
                      name="parentGuardianName"
                      value={formData.parentGuardianName}
                      onChange={handleInputChange}
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '1rem'
                      }}
                    />
                  </div>

                  {/* Parent/Guardian Phone */}
                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>
                      Parent/Guardian Phone
                    </label>
                    <input
                      type="tel"
                      name="parentGuardianPhone"
                      value={formData.parentGuardianPhone}
                      onChange={handleInputChange}
                      onInput={(e) => {
                        // Allow only numbers
                        e.currentTarget.value = e.currentTarget.value.replace(/[^0-9]/g, '');
                      }}
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '1rem'
                      }}
                      maxLength={11}
                    />
                  </div>

                  {/* Address */}
                  <div>
                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>
                      Address
                    </label>
                    <textarea
                      name="address"
                      value={formData.address}
                      onChange={handleInputChange}
                      rows={4}
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        fontSize: '1rem',
                        resize: 'vertical'
                      }}
                    />
                  </div>
                </div>

                {/* Form Actions - Spans both columns */}
                <div style={{
                  gridColumn: '1 / -1',
                  display: 'flex',
                  justifyContent: 'flex-end',
                  gap: '1rem',
                  marginTop: '1rem',
                  paddingTop: '1rem',
                  borderTop: '1px solid #e5e7eb'
                }}>
                  <button
                    type="button"
                    onClick={() => {
                      setShowEditModal(false);
                      setSelectedStudent(null);
                      resetForm();
                    }}
                    disabled={isSubmitting}
                    style={{
                      background: '#f3f4f6',
                      color: '#374151',
                      border: 'none',
                      borderRadius: '8px',
                      padding: '0.75rem 1.5rem',
                      fontSize: '1rem',
                      cursor: isSubmitting ? 'not-allowed' : 'pointer',
                      opacity: isSubmitting ? 0.6 : 1
                    }}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    style={{
                      background: isSubmitting ? '#9ca3af' : 'linear-gradient(135deg, #2d5016 0%, #4ade80 100%)',
                      color: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      padding: '0.75rem 1.5rem',
                      fontSize: '1rem',
                      cursor: isSubmitting ? 'not-allowed' : 'pointer',
                      fontWeight: '600'
                    }}
                  >
                    {isSubmitting ? 'Updating...' : 'Update Student'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && selectedStudent && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            borderRadius: '16px',
            padding: '2rem',
            width: '90%',
            maxWidth: '500px'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '1.5rem'
            }}>
              <h2 style={{
                margin: 0,
                color: '#dc2626',
                fontSize: '1.5rem',
                fontWeight: '700'
              }}>
                Deactivate Student Account
              </h2>
              <button
                onClick={() => {
                  setShowDeleteModal(false);
                  setSelectedStudent(null);
                }}
                style={{
                  background: 'none',
                  border: 'none',
                  fontSize: '1.5rem',
                  cursor: 'pointer',
                  color: '#6b7280'
                }}
              >
                ×
              </button>
            </div>

            <div style={{ marginBottom: '2rem' }}>
              <p style={{ margin: '0 0 1rem 0', color: '#374151' }}>
                Are you sure you want to deactivate the account for:
              </p>
              <div style={{
                background: '#f9fafb',
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                padding: '1rem'
              }}>
                <p style={{ margin: 0, fontWeight: '600', color: '#2d5016' }}>
                  {selectedStudent.profile.full_name}
                </p>
                <p style={{ margin: '0.25rem 0 0 0', color: '#6b7280', fontSize: '0.875rem' }}>
                  Student Number: {selectedStudent.student_number}
                </p>
                <p style={{ margin: '0.25rem 0 0 0', color: '#6b7280', fontSize: '0.875rem' }}>
                  Email: {selectedStudent.email}
                </p>
              </div>
              <p style={{ margin: '1rem 0 0 0', color: '#dc2626', fontSize: '0.875rem' }}>
                <span style={{ display: 'flex', alignItems: 'flex-start', gap: '0.5rem' }}>
                  <AlertTriangle size={16} color="#dc2626" style={{ marginTop: '0.125rem', flexShrink: 0 }} />
                  This action will deactivate the student's account. They will not be able to log in until reactivated.
                </span>
              </p>
            </div>

            <div style={{
              display: 'flex',
              justifyContent: 'flex-end',
              gap: '1rem'
            }}>
              <button
                onClick={() => {
                  setShowDeleteModal(false);
                  setSelectedStudent(null);
                }}
                disabled={isSubmitting}
                style={{
                  background: '#f3f4f6',
                  color: '#374151',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '0.75rem 1.5rem',
                  fontSize: '1rem',
                  cursor: isSubmitting ? 'not-allowed' : 'pointer',
                  opacity: isSubmitting ? 0.6 : 1
                }}
              >
                Cancel
              </button>
              <button
                onClick={confirmDeleteStudent}
                disabled={isSubmitting}
                style={{
                  background: isSubmitting ? '#9ca3af' : '#dc2626',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '0.75rem 1.5rem',
                  fontSize: '1rem',
                  cursor: isSubmitting ? 'not-allowed' : 'pointer',
                  opacity: isSubmitting ? 0.6 : 1
                }}
              >
                {isSubmitting ? 'Deactivating...' : 'Deactivate Account'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StudentManagement;