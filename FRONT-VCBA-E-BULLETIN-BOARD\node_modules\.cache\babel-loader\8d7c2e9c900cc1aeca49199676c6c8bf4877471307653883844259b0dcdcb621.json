{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\admin\\\\layout\\\\AdminSidebar.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { NavLink, useLocation } from 'react-router-dom';\nimport { BarChart3, Calendar, Newspaper, Users, Settings, Rss, Archive, Monitor, UserCog, FolderTree, MessageSquare, Upload, Shield } from 'lucide-react';\nimport { useAdminAuth } from '../../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../../utils/permissions';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst navItems = [{\n  path: '/admin/dashboard',\n  label: 'Dashboard',\n  icon: BarChart3,\n  description: 'Overview & Analytics',\n  requiresPermission: permissions => permissions.isProfessor // Hide from super_admin, show to professor\n}, {\n  path: '/admin/newsfeed',\n  label: 'Newsfeed',\n  icon: Rss,\n  description: 'Monitor Announcements & Events',\n  requiresPermission: permissions => permissions.isProfessor // Hide from super_admin, show to professor\n}, {\n  path: '/admin/calendar',\n  label: 'Calendar',\n  icon: Calendar,\n  description: 'Events & Schedule',\n  requiresPermission: permissions => permissions.isProfessor // Hide from super_admin, show to professor\n}, {\n  path: '/admin/posts',\n  label: 'Post',\n  icon: Newspaper,\n  description: 'Create & Manage Announcements',\n  requiresPermission: permissions => permissions.isProfessor // Hide from super_admin, show to professor\n}, {\n  path: '/admin/student-management',\n  label: 'Student',\n  icon: Users,\n  description: 'Manage Students',\n  requiresPermission: permissions => permissions.canViewStudents\n}, {\n  path: '/admin/categories',\n  label: 'Categories',\n  icon: FolderTree,\n  description: 'Manage Categories & Subcategories',\n  requiresPermission: permissions => permissions.isSuperAdmin // Show only to super_admin\n}, {\n  path: '/admin/admin-management',\n  label: 'Admin Management',\n  icon: UserCog,\n  description: 'Manage Admin Accounts',\n  requiresPermission: permissions => permissions.isSuperAdmin // Show only to super_admin\n}, {\n  path: '/admin/archive',\n  label: 'Archive',\n  icon: Archive,\n  description: 'View Archived Records',\n  requiresPermission: permissions => permissions.canViewArchive\n}, {\n  path: '/admin/tv-control',\n  label: 'TV Display',\n  icon: Monitor,\n  description: 'Manage TV Display & Signage',\n  requiresPermission: permissions => permissions.isSuperAdmin || permissions.isProfessor // Show to both roles\n}, {\n  path: '/admin/bulk-operations',\n  label: 'Bulk Operations',\n  icon: Upload,\n  description: 'Import, Export & Bulk Actions',\n  requiresPermission: permissions => permissions.isSuperAdmin // Show only to super_admin\n}, {\n  path: '/admin/audit-logs',\n  label: 'Audit Logs',\n  icon: Shield,\n  description: 'System Activity Monitoring',\n  requiresPermission: permissions => permissions.isSuperAdmin // Show only to super_admin\n}, {\n  path: '/admin/sms-settings',\n  label: 'SMS Settings',\n  icon: MessageSquare,\n  description: 'Configure SMS Notifications',\n  requiresPermission: permissions => permissions.isSuperAdmin // Show only to super_admin\n}, {\n  path: '/admin/settings',\n  label: 'Settings',\n  icon: Settings,\n  description: 'Profile & System Settings',\n  requiresPermission: permissions => permissions.isSuperAdmin || permissions.isProfessor // Show to both roles\n}];\nconst AdminSidebar = ({\n  isOpen,\n  onToggle\n}) => {\n  _s();\n  const location = useLocation();\n  const {\n    user\n  } = useAdminAuth();\n  const permissions = usePermissions(user);\n  const isActive = path => {\n    return location.pathname === path || path === '/admin/dashboard' && location.pathname === '/admin';\n  };\n\n  // Filter navigation items based on permissions\n  const visibleNavItems = navItems.filter(item => {\n    if (!item.requiresPermission) {\n      return true; // Always show items without permission requirements\n    }\n    return item.requiresPermission(permissions);\n  });\n  return /*#__PURE__*/_jsxDEV(\"aside\", {\n    style: {\n      position: 'fixed',\n      left: 0,\n      top: 0,\n      height: '100vh',\n      width: isOpen ? '280px' : '80px',\n      background: 'linear-gradient(180deg, #2d5016 0%, #1a3009 100%)',\n      transition: 'width 0.3s ease',\n      zIndex: 1000,\n      boxShadow: '4px 0 20px rgba(0, 0, 0, 0.1)',\n      overflow: 'hidden',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: isOpen ? '1rem' : '0.75rem',\n        // Reduced padding\n        borderBottom: '1px solid rgba(255, 255, 255, 0.1)',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.75rem',\n        // Reduced gap\n        minHeight: '60px',\n        // Reduced height\n        flexShrink: 0 // Prevent shrinking\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"/logo/vcba1.png\",\n        alt: \"VCBA Logo\",\n        style: {\n          width: '48px',\n          height: '48px',\n          objectFit: 'contain',\n          flexShrink: 0\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this), isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            color: 'white',\n            margin: 0,\n            fontSize: '1.1rem',\n            fontWeight: '700',\n            lineHeight: '1.2'\n          },\n          children: \"VCBA Admin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: 'rgba(255, 255, 255, 0.7)',\n            margin: 0,\n            fontSize: '0.75rem',\n            lineHeight: '1.2'\n          },\n          children: \"E-Bulletin Board\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this), (user === null || user === void 0 ? void 0 : user.position) && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '0.5rem',\n            padding: '0.25rem 0.5rem',\n            background: permissions.getPositionBadgeColor(),\n            borderRadius: '4px',\n            fontSize: '0.65rem',\n            fontWeight: '600',\n            color: 'white',\n            textAlign: 'center',\n            textTransform: 'uppercase',\n            letterSpacing: '0.5px'\n          },\n          children: permissions.getPositionDisplayName()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      style: {\n        flex: 1,\n        overflowY: 'auto',\n        overflowX: 'hidden',\n        padding: '0.5rem 0',\n        scrollbarWidth: 'thin',\n        scrollbarColor: 'rgba(255, 255, 255, 0.3) transparent'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"style\", {\n        children: `\n            nav::-webkit-scrollbar {\n              width: 4px;\n            }\n            nav::-webkit-scrollbar-track {\n              background: transparent;\n            }\n            nav::-webkit-scrollbar-thumb {\n              background: rgba(255, 255, 255, 0.3);\n              border-radius: 2px;\n            }\n            nav::-webkit-scrollbar-thumb:hover {\n              background: rgba(255, 255, 255, 0.5);\n            }\n          `\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this), visibleNavItems.map(item => /*#__PURE__*/_jsxDEV(NavLink, {\n        to: item.path,\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: isOpen ? '0.75rem' : '0',\n          padding: isOpen ? '0.625rem 1rem' : '0.625rem 0.5rem',\n          // More compact padding\n          margin: '0.125rem 0.5rem',\n          // Reduced margins\n          borderRadius: '6px',\n          // Smaller border radius\n          color: isActive(item.path) ? '#facc15' : 'rgba(255, 255, 255, 0.8)',\n          textDecoration: 'none',\n          background: isActive(item.path) ? 'linear-gradient(90deg, rgba(250, 204, 21, 0.2) 0%, transparent 100%)' : 'transparent',\n          borderLeft: isActive(item.path) ? '3px solid #facc15' : '3px solid transparent',\n          transition: 'all 0.2s ease',\n          position: 'relative',\n          overflow: 'hidden',\n          minHeight: '36px',\n          // Consistent minimum height\n          justifyContent: isOpen ? 'flex-start' : 'center'\n        },\n        onMouseEnter: e => {\n          if (!isActive(item.path)) {\n            e.currentTarget.style.background = 'rgba(255, 255, 255, 0.05)';\n            e.currentTarget.style.color = 'white';\n          }\n        },\n        onMouseLeave: e => {\n          if (!isActive(item.path)) {\n            e.currentTarget.style.background = 'transparent';\n            e.currentTarget.style.color = 'rgba(255, 255, 255, 0.8)';\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            flexShrink: 0,\n            width: '20px',\n            // Smaller icon container\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(item.icon, {\n            size: 18,\n            color: isActive(item.path) ? '#facc15' : 'rgba(255, 255, 255, 0.8)'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 13\n        }, this), isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            minWidth: 0\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: isActive(item.path) ? '600' : '500',\n              fontSize: '0.8125rem',\n              // Slightly smaller font\n              marginBottom: '0.125rem',\n              // Reduced margin\n              whiteSpace: 'nowrap',\n              overflow: 'hidden',\n              textOverflow: 'ellipsis'\n            },\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.6875rem',\n              // Smaller description font\n              color: isActive(item.path) ? 'rgba(250, 204, 21, 0.8)' : 'rgba(255, 255, 255, 0.6)',\n              lineHeight: '1.1',\n              // Tighter line height\n              whiteSpace: 'nowrap',\n              overflow: 'hidden',\n              textOverflow: 'ellipsis'\n            },\n            children: item.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 15\n        }, this)]\n      }, item.path, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 11\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this), isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        bottom: '1rem',\n        left: '1.5rem',\n        right: '1.5rem',\n        padding: '1rem',\n        background: 'rgba(255, 255, 255, 0.05)',\n        borderRadius: '12px',\n        border: '1px solid rgba(255, 255, 255, 0.1)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: 'rgba(255, 255, 255, 0.9)',\n          fontSize: '0.8rem',\n          fontWeight: '600',\n          marginBottom: '0.25rem'\n        },\n        children: \"Villamor College\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: 'rgba(255, 255, 255, 0.6)',\n          fontSize: '0.7rem',\n          lineHeight: '1.3'\n        },\n        children: \"Business and Arts, Inc.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 133,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminSidebar, \"NY5dx5Nj8LjAYtEAOd0U3KKRz1E=\", false, function () {\n  return [useLocation, useAdminAuth, usePermissions];\n});\n_c = AdminSidebar;\nexport default AdminSidebar;\nvar _c;\n$RefreshReg$(_c, \"AdminSidebar\");", "map": {"version": 3, "names": ["React", "NavLink", "useLocation", "BarChart3", "Calendar", "Newspaper", "Users", "Settings", "Rss", "Archive", "Monitor", "UserCog", "FolderTree", "MessageSquare", "Upload", "Shield", "useAdminAuth", "usePermissions", "jsxDEV", "_jsxDEV", "navItems", "path", "label", "icon", "description", "requiresPermission", "permissions", "isProfessor", "canViewStudents", "isSuperAdmin", "canViewArchive", "AdminSidebar", "isOpen", "onToggle", "_s", "location", "user", "isActive", "pathname", "visibleNavItems", "filter", "item", "style", "position", "left", "top", "height", "width", "background", "transition", "zIndex", "boxShadow", "overflow", "display", "flexDirection", "children", "padding", "borderBottom", "alignItems", "gap", "minHeight", "flexShrink", "src", "alt", "objectFit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "margin", "fontSize", "fontWeight", "lineHeight", "marginTop", "getPositionBadgeColor", "borderRadius", "textAlign", "textTransform", "letterSpacing", "getPositionDisplayName", "flex", "overflowY", "overflowX", "scrollbarWidth", "scrollbarColor", "map", "to", "textDecoration", "borderLeft", "justifyContent", "onMouseEnter", "e", "currentTarget", "onMouseLeave", "size", "min<PERSON><PERSON><PERSON>", "marginBottom", "whiteSpace", "textOverflow", "bottom", "right", "border", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/admin/layout/AdminSidebar.tsx"], "sourcesContent": ["import React from 'react';\nimport { NavLink, useLocation } from 'react-router-dom';\nimport { BarChart3, Calendar, Newspaper, Users, Settings, Rss, Archive, Monitor, UserCog, FolderTree, ChevronLeft, ChevronRight, MessageSquare, Upload, Shield } from 'lucide-react';\nimport { useAdminAuth } from '../../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../../utils/permissions';\n\ninterface AdminSidebarProps {\n  isOpen: boolean;\n  onToggle: () => void;\n}\n\ninterface NavItem {\n  path: string;\n  label: string;\n  icon: React.ComponentType<{ size?: number; color?: string }>;\n  description: string;\n  requiresPermission?: (permissions: ReturnType<typeof usePermissions>) => boolean;\n}\n\nconst navItems: NavItem[] = [\n  {\n    path: '/admin/dashboard',\n    label: 'Dashboard',\n    icon: BarChart3,\n    description: 'Overview & Analytics',\n    requiresPermission: (permissions) => permissions.isProfessor // Hide from super_admin, show to professor\n  },\n  {\n    path: '/admin/newsfeed',\n    label: 'Newsfeed',\n    icon: Rss,\n    description: 'Monitor Announcements & Events',\n    requiresPermission: (permissions) => permissions.isProfessor // Hide from super_admin, show to professor\n  },\n  {\n    path: '/admin/calendar',\n    label: 'Calendar',\n    icon: Calendar,\n    description: 'Events & Schedule',\n    requiresPermission: (permissions) => permissions.isProfessor // Hide from super_admin, show to professor\n  },\n  {\n    path: '/admin/posts',\n    label: 'Post',\n    icon: Newspaper,\n    description: 'Create & Manage Announcements',\n    requiresPermission: (permissions) => permissions.isProfessor // Hide from super_admin, show to professor\n  },\n  {\n    path: '/admin/student-management',\n    label: 'Student',\n    icon: Users,\n    description: 'Manage Students',\n    requiresPermission: (permissions) => permissions.canViewStudents\n  },\n  {\n    path: '/admin/categories',\n    label: 'Categories',\n    icon: FolderTree,\n    description: 'Manage Categories & Subcategories',\n    requiresPermission: (permissions) => permissions.isSuperAdmin // Show only to super_admin\n  },\n  {\n    path: '/admin/admin-management',\n    label: 'Admin Management',\n    icon: UserCog,\n    description: 'Manage Admin Accounts',\n    requiresPermission: (permissions) => permissions.isSuperAdmin // Show only to super_admin\n  },\n  {\n    path: '/admin/archive',\n    label: 'Archive',\n    icon: Archive,\n    description: 'View Archived Records',\n    requiresPermission: (permissions) => permissions.canViewArchive\n  },\n  {\n    path: '/admin/tv-control',\n    label: 'TV Display',\n    icon: Monitor,\n    description: 'Manage TV Display & Signage',\n    requiresPermission: (permissions) => permissions.isSuperAdmin || permissions.isProfessor // Show to both roles\n  },\n  {\n    path: '/admin/bulk-operations',\n    label: 'Bulk Operations',\n    icon: Upload,\n    description: 'Import, Export & Bulk Actions',\n    requiresPermission: (permissions) => permissions.isSuperAdmin // Show only to super_admin\n  },\n  {\n    path: '/admin/audit-logs',\n    label: 'Audit Logs',\n    icon: Shield,\n    description: 'System Activity Monitoring',\n    requiresPermission: (permissions) => permissions.isSuperAdmin // Show only to super_admin\n  },\n  {\n    path: '/admin/sms-settings',\n    label: 'SMS Settings',\n    icon: MessageSquare,\n    description: 'Configure SMS Notifications',\n    requiresPermission: (permissions) => permissions.isSuperAdmin // Show only to super_admin\n  },\n  {\n    path: '/admin/settings',\n    label: 'Settings',\n    icon: Settings,\n    description: 'Profile & System Settings',\n    requiresPermission: (permissions) => permissions.isSuperAdmin || permissions.isProfessor // Show to both roles\n  }\n];\n\nconst AdminSidebar: React.FC<AdminSidebarProps> = ({ isOpen, onToggle }) => {\n  const location = useLocation();\n  const { user } = useAdminAuth();\n  const permissions = usePermissions(user);\n\n  const isActive = (path: string) => {\n    return location.pathname === path ||\n           (path === '/admin/dashboard' && location.pathname === '/admin');\n  };\n\n  // Filter navigation items based on permissions\n  const visibleNavItems = navItems.filter(item => {\n    if (!item.requiresPermission) {\n      return true; // Always show items without permission requirements\n    }\n    return item.requiresPermission(permissions);\n  });\n\n  return (\n    <aside style={{\n      position: 'fixed',\n      left: 0,\n      top: 0,\n      height: '100vh',\n      width: isOpen ? '280px' : '80px',\n      background: 'linear-gradient(180deg, #2d5016 0%, #1a3009 100%)',\n      transition: 'width 0.3s ease',\n      zIndex: 1000,\n      boxShadow: '4px 0 20px rgba(0, 0, 0, 0.1)',\n      overflow: 'hidden',\n      display: 'flex',\n      flexDirection: 'column'\n    }}>\n      {/* Logo Section - More Compact */}\n      <div style={{\n        padding: isOpen ? '1rem' : '0.75rem', // Reduced padding\n        borderBottom: '1px solid rgba(255, 255, 255, 0.1)',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.75rem', // Reduced gap\n        minHeight: '60px', // Reduced height\n        flexShrink: 0 // Prevent shrinking\n      }}>\n        <img\n          src=\"/logo/vcba1.png\"\n          alt=\"VCBA Logo\"\n          style={{\n            width: '48px',\n            height: '48px',\n            objectFit: 'contain',\n            flexShrink: 0\n          }}\n        />\n        {isOpen && (\n          <div>\n            <h2 style={{\n              color: 'white',\n              margin: 0,\n              fontSize: '1.1rem',\n              fontWeight: '700',\n              lineHeight: '1.2'\n            }}>\n              VCBA Admin\n            </h2>\n            <p style={{\n              color: 'rgba(255, 255, 255, 0.7)',\n              margin: 0,\n              fontSize: '0.75rem',\n              lineHeight: '1.2'\n            }}>\n              E-Bulletin Board\n            </p>\n            {/* Position Badge */}\n            {user?.position && (\n              <div style={{\n                marginTop: '0.5rem',\n                padding: '0.25rem 0.5rem',\n                background: permissions.getPositionBadgeColor(),\n                borderRadius: '4px',\n                fontSize: '0.65rem',\n                fontWeight: '600',\n                color: 'white',\n                textAlign: 'center',\n                textTransform: 'uppercase',\n                letterSpacing: '0.5px'\n              }}>\n                {permissions.getPositionDisplayName()}\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n\n      {/* Navigation - Scrollable */}\n      <nav style={{\n        flex: 1,\n        overflowY: 'auto',\n        overflowX: 'hidden',\n        padding: '0.5rem 0',\n        scrollbarWidth: 'thin',\n        scrollbarColor: 'rgba(255, 255, 255, 0.3) transparent'\n      }}>\n        <style>\n          {`\n            nav::-webkit-scrollbar {\n              width: 4px;\n            }\n            nav::-webkit-scrollbar-track {\n              background: transparent;\n            }\n            nav::-webkit-scrollbar-thumb {\n              background: rgba(255, 255, 255, 0.3);\n              border-radius: 2px;\n            }\n            nav::-webkit-scrollbar-thumb:hover {\n              background: rgba(255, 255, 255, 0.5);\n            }\n          `}\n        </style>\n        {visibleNavItems.map((item) => (\n          <NavLink\n            key={item.path}\n            to={item.path}\n            style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: isOpen ? '0.75rem' : '0',\n              padding: isOpen ? '0.625rem 1rem' : '0.625rem 0.5rem', // More compact padding\n              margin: '0.125rem 0.5rem', // Reduced margins\n              borderRadius: '6px', // Smaller border radius\n              color: isActive(item.path) ? '#facc15' : 'rgba(255, 255, 255, 0.8)',\n              textDecoration: 'none',\n              background: isActive(item.path)\n                ? 'linear-gradient(90deg, rgba(250, 204, 21, 0.2) 0%, transparent 100%)'\n                : 'transparent',\n              borderLeft: isActive(item.path) ? '3px solid #facc15' : '3px solid transparent',\n              transition: 'all 0.2s ease',\n              position: 'relative',\n              overflow: 'hidden',\n              minHeight: '36px', // Consistent minimum height\n              justifyContent: isOpen ? 'flex-start' : 'center'\n            }}\n            onMouseEnter={(e) => {\n              if (!isActive(item.path)) {\n                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.05)';\n                e.currentTarget.style.color = 'white';\n              }\n            }}\n            onMouseLeave={(e) => {\n              if (!isActive(item.path)) {\n                e.currentTarget.style.background = 'transparent';\n                e.currentTarget.style.color = 'rgba(255, 255, 255, 0.8)';\n              }\n            }}\n          >\n            <span style={{\n              flexShrink: 0,\n              width: '20px', // Smaller icon container\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            }}>\n              <item.icon size={18} color={isActive(item.path) ? '#facc15' : 'rgba(255, 255, 255, 0.8)'} />\n            </span>\n            {isOpen && (\n              <div style={{ flex: 1, minWidth: 0 }}>\n                <div style={{\n                  fontWeight: isActive(item.path) ? '600' : '500',\n                  fontSize: '0.8125rem', // Slightly smaller font\n                  marginBottom: '0.125rem', // Reduced margin\n                  whiteSpace: 'nowrap',\n                  overflow: 'hidden',\n                  textOverflow: 'ellipsis'\n                }}>\n                  {item.label}\n                </div>\n                <div style={{\n                  fontSize: '0.6875rem', // Smaller description font\n                  color: isActive(item.path)\n                    ? 'rgba(250, 204, 21, 0.8)'\n                    : 'rgba(255, 255, 255, 0.6)',\n                  lineHeight: '1.1', // Tighter line height\n                  whiteSpace: 'nowrap',\n                  overflow: 'hidden',\n                  textOverflow: 'ellipsis'\n                }}>\n                  {item.description}\n                </div>\n              </div>\n            )}\n          </NavLink>\n        ))}\n      </nav>\n\n      {/* Footer */}\n      {isOpen && (\n        <div style={{\n          position: 'absolute',\n          bottom: '1rem',\n          left: '1.5rem',\n          right: '1.5rem',\n          padding: '1rem',\n          background: 'rgba(255, 255, 255, 0.05)',\n          borderRadius: '12px',\n          border: '1px solid rgba(255, 255, 255, 0.1)'\n        }}>\n          <div style={{\n            color: 'rgba(255, 255, 255, 0.9)',\n            fontSize: '0.8rem',\n            fontWeight: '600',\n            marginBottom: '0.25rem'\n          }}>\n            Villamor College\n          </div>\n          <div style={{\n            color: 'rgba(255, 255, 255, 0.6)',\n            fontSize: '0.7rem',\n            lineHeight: '1.3'\n          }}>\n            Business and Arts, Inc.\n          </div>\n        </div>\n      )}\n    </aside>\n  );\n};\n\nexport default AdminSidebar;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,EAAEC,WAAW,QAAQ,kBAAkB;AACvD,SAASC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,UAAU,EAA6BC,aAAa,EAAEC,MAAM,EAAEC,MAAM,QAAQ,cAAc;AACpL,SAASC,YAAY,QAAQ,oCAAoC;AACjE,SAASC,cAAc,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAe5D,MAAMC,QAAmB,GAAG,CAC1B;EACEC,IAAI,EAAE,kBAAkB;EACxBC,KAAK,EAAE,WAAW;EAClBC,IAAI,EAAEpB,SAAS;EACfqB,WAAW,EAAE,sBAAsB;EACnCC,kBAAkB,EAAGC,WAAW,IAAKA,WAAW,CAACC,WAAW,CAAC;AAC/D,CAAC,EACD;EACEN,IAAI,EAAE,iBAAiB;EACvBC,KAAK,EAAE,UAAU;EACjBC,IAAI,EAAEf,GAAG;EACTgB,WAAW,EAAE,gCAAgC;EAC7CC,kBAAkB,EAAGC,WAAW,IAAKA,WAAW,CAACC,WAAW,CAAC;AAC/D,CAAC,EACD;EACEN,IAAI,EAAE,iBAAiB;EACvBC,KAAK,EAAE,UAAU;EACjBC,IAAI,EAAEnB,QAAQ;EACdoB,WAAW,EAAE,mBAAmB;EAChCC,kBAAkB,EAAGC,WAAW,IAAKA,WAAW,CAACC,WAAW,CAAC;AAC/D,CAAC,EACD;EACEN,IAAI,EAAE,cAAc;EACpBC,KAAK,EAAE,MAAM;EACbC,IAAI,EAAElB,SAAS;EACfmB,WAAW,EAAE,+BAA+B;EAC5CC,kBAAkB,EAAGC,WAAW,IAAKA,WAAW,CAACC,WAAW,CAAC;AAC/D,CAAC,EACD;EACEN,IAAI,EAAE,2BAA2B;EACjCC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAEjB,KAAK;EACXkB,WAAW,EAAE,iBAAiB;EAC9BC,kBAAkB,EAAGC,WAAW,IAAKA,WAAW,CAACE;AACnD,CAAC,EACD;EACEP,IAAI,EAAE,mBAAmB;EACzBC,KAAK,EAAE,YAAY;EACnBC,IAAI,EAAEX,UAAU;EAChBY,WAAW,EAAE,mCAAmC;EAChDC,kBAAkB,EAAGC,WAAW,IAAKA,WAAW,CAACG,YAAY,CAAC;AAChE,CAAC,EACD;EACER,IAAI,EAAE,yBAAyB;EAC/BC,KAAK,EAAE,kBAAkB;EACzBC,IAAI,EAAEZ,OAAO;EACba,WAAW,EAAE,uBAAuB;EACpCC,kBAAkB,EAAGC,WAAW,IAAKA,WAAW,CAACG,YAAY,CAAC;AAChE,CAAC,EACD;EACER,IAAI,EAAE,gBAAgB;EACtBC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAEd,OAAO;EACbe,WAAW,EAAE,uBAAuB;EACpCC,kBAAkB,EAAGC,WAAW,IAAKA,WAAW,CAACI;AACnD,CAAC,EACD;EACET,IAAI,EAAE,mBAAmB;EACzBC,KAAK,EAAE,YAAY;EACnBC,IAAI,EAAEb,OAAO;EACbc,WAAW,EAAE,6BAA6B;EAC1CC,kBAAkB,EAAGC,WAAW,IAAKA,WAAW,CAACG,YAAY,IAAIH,WAAW,CAACC,WAAW,CAAC;AAC3F,CAAC,EACD;EACEN,IAAI,EAAE,wBAAwB;EAC9BC,KAAK,EAAE,iBAAiB;EACxBC,IAAI,EAAET,MAAM;EACZU,WAAW,EAAE,+BAA+B;EAC5CC,kBAAkB,EAAGC,WAAW,IAAKA,WAAW,CAACG,YAAY,CAAC;AAChE,CAAC,EACD;EACER,IAAI,EAAE,mBAAmB;EACzBC,KAAK,EAAE,YAAY;EACnBC,IAAI,EAAER,MAAM;EACZS,WAAW,EAAE,4BAA4B;EACzCC,kBAAkB,EAAGC,WAAW,IAAKA,WAAW,CAACG,YAAY,CAAC;AAChE,CAAC,EACD;EACER,IAAI,EAAE,qBAAqB;EAC3BC,KAAK,EAAE,cAAc;EACrBC,IAAI,EAAEV,aAAa;EACnBW,WAAW,EAAE,6BAA6B;EAC1CC,kBAAkB,EAAGC,WAAW,IAAKA,WAAW,CAACG,YAAY,CAAC;AAChE,CAAC,EACD;EACER,IAAI,EAAE,iBAAiB;EACvBC,KAAK,EAAE,UAAU;EACjBC,IAAI,EAAEhB,QAAQ;EACdiB,WAAW,EAAE,2BAA2B;EACxCC,kBAAkB,EAAGC,WAAW,IAAKA,WAAW,CAACG,YAAY,IAAIH,WAAW,CAACC,WAAW,CAAC;AAC3F,CAAC,CACF;AAED,MAAMI,YAAyC,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC1E,MAAMC,QAAQ,GAAGjC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkC;EAAK,CAAC,GAAGpB,YAAY,CAAC,CAAC;EAC/B,MAAMU,WAAW,GAAGT,cAAc,CAACmB,IAAI,CAAC;EAExC,MAAMC,QAAQ,GAAIhB,IAAY,IAAK;IACjC,OAAOc,QAAQ,CAACG,QAAQ,KAAKjB,IAAI,IACzBA,IAAI,KAAK,kBAAkB,IAAIc,QAAQ,CAACG,QAAQ,KAAK,QAAS;EACxE,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGnB,QAAQ,CAACoB,MAAM,CAACC,IAAI,IAAI;IAC9C,IAAI,CAACA,IAAI,CAAChB,kBAAkB,EAAE;MAC5B,OAAO,IAAI,CAAC,CAAC;IACf;IACA,OAAOgB,IAAI,CAAChB,kBAAkB,CAACC,WAAW,CAAC;EAC7C,CAAC,CAAC;EAEF,oBACEP,OAAA;IAAOuB,KAAK,EAAE;MACZC,QAAQ,EAAE,OAAO;MACjBC,IAAI,EAAE,CAAC;MACPC,GAAG,EAAE,CAAC;MACNC,MAAM,EAAE,OAAO;MACfC,KAAK,EAAEf,MAAM,GAAG,OAAO,GAAG,MAAM;MAChCgB,UAAU,EAAE,mDAAmD;MAC/DC,UAAU,EAAE,iBAAiB;MAC7BC,MAAM,EAAE,IAAI;MACZC,SAAS,EAAE,+BAA+B;MAC1CC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE;IACjB,CAAE;IAAAC,QAAA,gBAEApC,OAAA;MAAKuB,KAAK,EAAE;QACVc,OAAO,EAAExB,MAAM,GAAG,MAAM,GAAG,SAAS;QAAE;QACtCyB,YAAY,EAAE,oCAAoC;QAClDJ,OAAO,EAAE,MAAM;QACfK,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE,SAAS;QAAE;QAChBC,SAAS,EAAE,MAAM;QAAE;QACnBC,UAAU,EAAE,CAAC,CAAC;MAChB,CAAE;MAAAN,QAAA,gBACApC,OAAA;QACE2C,GAAG,EAAC,iBAAiB;QACrBC,GAAG,EAAC,WAAW;QACfrB,KAAK,EAAE;UACLK,KAAK,EAAE,MAAM;UACbD,MAAM,EAAE,MAAM;UACdkB,SAAS,EAAE,SAAS;UACpBH,UAAU,EAAE;QACd;MAAE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACDpC,MAAM,iBACLb,OAAA;QAAAoC,QAAA,gBACEpC,OAAA;UAAIuB,KAAK,EAAE;YACT2B,KAAK,EAAE,OAAO;YACdC,MAAM,EAAE,CAAC;YACTC,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE,KAAK;YACjBC,UAAU,EAAE;UACd,CAAE;UAAAlB,QAAA,EAAC;QAEH;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLjD,OAAA;UAAGuB,KAAK,EAAE;YACR2B,KAAK,EAAE,0BAA0B;YACjCC,MAAM,EAAE,CAAC;YACTC,QAAQ,EAAE,SAAS;YACnBE,UAAU,EAAE;UACd,CAAE;UAAAlB,QAAA,EAAC;QAEH;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAEH,CAAAhC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,QAAQ,kBACbxB,OAAA;UAAKuB,KAAK,EAAE;YACVgC,SAAS,EAAE,QAAQ;YACnBlB,OAAO,EAAE,gBAAgB;YACzBR,UAAU,EAAEtB,WAAW,CAACiD,qBAAqB,CAAC,CAAC;YAC/CC,YAAY,EAAE,KAAK;YACnBL,QAAQ,EAAE,SAAS;YACnBC,UAAU,EAAE,KAAK;YACjBH,KAAK,EAAE,OAAO;YACdQ,SAAS,EAAE,QAAQ;YACnBC,aAAa,EAAE,WAAW;YAC1BC,aAAa,EAAE;UACjB,CAAE;UAAAxB,QAAA,EACC7B,WAAW,CAACsD,sBAAsB,CAAC;QAAC;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNjD,OAAA;MAAKuB,KAAK,EAAE;QACVuC,IAAI,EAAE,CAAC;QACPC,SAAS,EAAE,MAAM;QACjBC,SAAS,EAAE,QAAQ;QACnB3B,OAAO,EAAE,UAAU;QACnB4B,cAAc,EAAE,MAAM;QACtBC,cAAc,EAAE;MAClB,CAAE;MAAA9B,QAAA,gBACApC,OAAA;QAAAoC,QAAA,EACG;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MAAW;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,EACP7B,eAAe,CAAC+C,GAAG,CAAE7C,IAAI,iBACxBtB,OAAA,CAAClB,OAAO;QAENsF,EAAE,EAAE9C,IAAI,CAACpB,IAAK;QACdqB,KAAK,EAAE;UACLW,OAAO,EAAE,MAAM;UACfK,UAAU,EAAE,QAAQ;UACpBC,GAAG,EAAE3B,MAAM,GAAG,SAAS,GAAG,GAAG;UAC7BwB,OAAO,EAAExB,MAAM,GAAG,eAAe,GAAG,iBAAiB;UAAE;UACvDsC,MAAM,EAAE,iBAAiB;UAAE;UAC3BM,YAAY,EAAE,KAAK;UAAE;UACrBP,KAAK,EAAEhC,QAAQ,CAACI,IAAI,CAACpB,IAAI,CAAC,GAAG,SAAS,GAAG,0BAA0B;UACnEmE,cAAc,EAAE,MAAM;UACtBxC,UAAU,EAAEX,QAAQ,CAACI,IAAI,CAACpB,IAAI,CAAC,GAC3B,sEAAsE,GACtE,aAAa;UACjBoE,UAAU,EAAEpD,QAAQ,CAACI,IAAI,CAACpB,IAAI,CAAC,GAAG,mBAAmB,GAAG,uBAAuB;UAC/E4B,UAAU,EAAE,eAAe;UAC3BN,QAAQ,EAAE,UAAU;UACpBS,QAAQ,EAAE,QAAQ;UAClBQ,SAAS,EAAE,MAAM;UAAE;UACnB8B,cAAc,EAAE1D,MAAM,GAAG,YAAY,GAAG;QAC1C,CAAE;QACF2D,YAAY,EAAGC,CAAC,IAAK;UACnB,IAAI,CAACvD,QAAQ,CAACI,IAAI,CAACpB,IAAI,CAAC,EAAE;YACxBuE,CAAC,CAACC,aAAa,CAACnD,KAAK,CAACM,UAAU,GAAG,2BAA2B;YAC9D4C,CAAC,CAACC,aAAa,CAACnD,KAAK,CAAC2B,KAAK,GAAG,OAAO;UACvC;QACF,CAAE;QACFyB,YAAY,EAAGF,CAAC,IAAK;UACnB,IAAI,CAACvD,QAAQ,CAACI,IAAI,CAACpB,IAAI,CAAC,EAAE;YACxBuE,CAAC,CAACC,aAAa,CAACnD,KAAK,CAACM,UAAU,GAAG,aAAa;YAChD4C,CAAC,CAACC,aAAa,CAACnD,KAAK,CAAC2B,KAAK,GAAG,0BAA0B;UAC1D;QACF,CAAE;QAAAd,QAAA,gBAEFpC,OAAA;UAAMuB,KAAK,EAAE;YACXmB,UAAU,EAAE,CAAC;YACbd,KAAK,EAAE,MAAM;YAAE;YACfM,OAAO,EAAE,MAAM;YACfK,UAAU,EAAE,QAAQ;YACpBgC,cAAc,EAAE;UAClB,CAAE;UAAAnC,QAAA,eACApC,OAAA,CAACsB,IAAI,CAAClB,IAAI;YAACwE,IAAI,EAAE,EAAG;YAAC1B,KAAK,EAAEhC,QAAQ,CAACI,IAAI,CAACpB,IAAI,CAAC,GAAG,SAAS,GAAG;UAA2B;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxF,CAAC,EACNpC,MAAM,iBACLb,OAAA;UAAKuB,KAAK,EAAE;YAAEuC,IAAI,EAAE,CAAC;YAAEe,QAAQ,EAAE;UAAE,CAAE;UAAAzC,QAAA,gBACnCpC,OAAA;YAAKuB,KAAK,EAAE;cACV8B,UAAU,EAAEnC,QAAQ,CAACI,IAAI,CAACpB,IAAI,CAAC,GAAG,KAAK,GAAG,KAAK;cAC/CkD,QAAQ,EAAE,WAAW;cAAE;cACvB0B,YAAY,EAAE,UAAU;cAAE;cAC1BC,UAAU,EAAE,QAAQ;cACpB9C,QAAQ,EAAE,QAAQ;cAClB+C,YAAY,EAAE;YAChB,CAAE;YAAA5C,QAAA,EACCd,IAAI,CAACnB;UAAK;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACNjD,OAAA;YAAKuB,KAAK,EAAE;cACV6B,QAAQ,EAAE,WAAW;cAAE;cACvBF,KAAK,EAAEhC,QAAQ,CAACI,IAAI,CAACpB,IAAI,CAAC,GACtB,yBAAyB,GACzB,0BAA0B;cAC9BoD,UAAU,EAAE,KAAK;cAAE;cACnByB,UAAU,EAAE,QAAQ;cACpB9C,QAAQ,EAAE,QAAQ;cAClB+C,YAAY,EAAE;YAChB,CAAE;YAAA5C,QAAA,EACCd,IAAI,CAACjB;UAAW;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA,GApEI3B,IAAI,CAACpB,IAAI;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqEP,CACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGLpC,MAAM,iBACLb,OAAA;MAAKuB,KAAK,EAAE;QACVC,QAAQ,EAAE,UAAU;QACpByD,MAAM,EAAE,MAAM;QACdxD,IAAI,EAAE,QAAQ;QACdyD,KAAK,EAAE,QAAQ;QACf7C,OAAO,EAAE,MAAM;QACfR,UAAU,EAAE,2BAA2B;QACvC4B,YAAY,EAAE,MAAM;QACpB0B,MAAM,EAAE;MACV,CAAE;MAAA/C,QAAA,gBACApC,OAAA;QAAKuB,KAAK,EAAE;UACV2B,KAAK,EAAE,0BAA0B;UACjCE,QAAQ,EAAE,QAAQ;UAClBC,UAAU,EAAE,KAAK;UACjByB,YAAY,EAAE;QAChB,CAAE;QAAA1C,QAAA,EAAC;MAEH;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNjD,OAAA;QAAKuB,KAAK,EAAE;UACV2B,KAAK,EAAE,0BAA0B;UACjCE,QAAQ,EAAE,QAAQ;UAClBE,UAAU,EAAE;QACd,CAAE;QAAAlB,QAAA,EAAC;MAEH;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEZ,CAAC;AAAClC,EAAA,CAjOIH,YAAyC;EAAA,QAC5B7B,WAAW,EACXc,YAAY,EACTC,cAAc;AAAA;AAAAsF,EAAA,GAH9BxE,YAAyC;AAmO/C,eAAeA,YAAY;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}