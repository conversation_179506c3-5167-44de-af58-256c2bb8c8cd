{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\admin\\\\AdminManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminManagement = () => {\n  _s();\n  const {\n    user\n  } = useAdminAuth();\n  const permissions = usePermissions(user);\n  const [admins, setAdmins] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    // Check if user has permission to manage admins\n    if (!permissions.canManageAdmins) {\n      setError('You do not have permission to manage admin accounts');\n      setLoading(false);\n      return;\n    }\n    loadAdmins();\n  }, [permissions.canManageAdmins]);\n  const loadAdmins = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // TODO: Implement API call to fetch admins\n      // const response = await adminManagementService.getAdmins();\n      // setAdmins(response.data.admins);\n\n      // Mock data for now\n      setAdmins([{\n        admin_id: 1,\n        email: '<EMAIL>',\n        first_name: 'Von Christian',\n        last_name: 'Admin',\n        phone_number: '+************',\n        department: 'IT Department',\n        position: 'super_admin',\n        is_active: true,\n        last_login: '2025-08-05T10:30:00Z',\n        created_at: '2025-01-01T00:00:00Z'\n      }, {\n        admin_id: 2,\n        email: '<EMAIL>',\n        first_name: 'Zaira',\n        last_name: 'Professor',\n        phone_number: '+************',\n        department: 'Academic Department',\n        position: 'professor',\n        grade_level: 11,\n        is_active: true,\n        last_login: '2025-08-05T09:15:00Z',\n        created_at: '2025-02-01T00:00:00Z'\n      }]);\n    } catch (err) {\n      setError('Failed to load admin accounts');\n      console.error('Error loading admins:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getPositionIcon = position => {\n    return position === 'super_admin' ? Shield : User;\n  };\n  const getPositionColor = position => {\n    return position === 'super_admin' ? '#dc2626' : '#2563eb';\n  };\n  const getPositionLabel = position => {\n    return position === 'super_admin' ? 'Super Administrator' : 'Professor';\n  };\n  if (!permissions.canManageAdmins) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      },\n      children: [/*#__PURE__*/_jsxDEV(UserCog, {\n        size: 64,\n        style: {\n          marginBottom: '1rem',\n          opacity: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          margin: '0 0 0.5rem',\n          fontSize: '1.5rem',\n          fontWeight: '600'\n        },\n        children: \"Access Denied\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: 0,\n          fontSize: '1rem'\n        },\n        children: \"You do not have permission to manage admin accounts.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        },\n        children: [\"Current Role: \", permissions.getPositionDisplayName()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '40px',\n          height: '40px',\n          border: '4px solid #f3f4f6',\n          borderTop: '4px solid #3b82f6',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '2rem',\n        textAlign: 'center',\n        color: '#ef4444'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Error\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: loadAdmins,\n        style: {\n          padding: '0.5rem 1rem',\n          background: '#3b82f6',\n          color: 'white',\n          border: 'none',\n          borderRadius: '6px',\n          cursor: 'pointer'\n        },\n        children: \"Retry\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      maxWidth: '1200px',\n      margin: '0 auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            margin: '0 0 0.5rem',\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          },\n          children: \"Admin Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          },\n          children: \"Manage administrator accounts and permissions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          padding: '0.75rem 1.5rem',\n          background: '#3b82f6',\n          color: 'white',\n          border: 'none',\n          borderRadius: '8px',\n          fontSize: '0.875rem',\n          fontWeight: '600',\n          cursor: 'pointer',\n          transition: 'background-color 0.2s'\n        },\n        onMouseEnter: e => e.currentTarget.style.background = '#2563eb',\n        onMouseLeave: e => e.currentTarget.style.background = '#3b82f6',\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this), \"Add Admin\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fill, minmax(400px, 1fr))',\n        gap: '1.5rem'\n      },\n      children: admins.map(admin => {\n        const PositionIcon = getPositionIcon(admin.position);\n        const positionColor = getPositionColor(admin.position);\n        const positionLabel = getPositionLabel(admin.position);\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'white',\n            borderRadius: '12px',\n            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n            padding: '1.5rem',\n            border: admin.admin_id === (user === null || user === void 0 ? void 0 : user.id) ? '2px solid #facc15' : '1px solid #e5e7eb'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'flex-start',\n              marginBottom: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '48px',\n                  height: '48px',\n                  background: `linear-gradient(135deg, ${positionColor}20, ${positionColor}10)`,\n                  borderRadius: '12px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                },\n                children: /*#__PURE__*/_jsxDEV(PositionIcon, {\n                  size: 24,\n                  color: positionColor\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  style: {\n                    margin: '0 0 0.25rem',\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: '#1f2937'\n                  },\n                  children: [admin.first_name, \" \", admin.last_name, admin.admin_id === (user === null || user === void 0 ? void 0 : user.id) && /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      marginLeft: '0.5rem',\n                      padding: '0.125rem 0.375rem',\n                      background: '#facc15',\n                      color: '#1f2937',\n                      borderRadius: '4px',\n                      fontSize: '0.625rem',\n                      fontWeight: '700'\n                    },\n                    children: \"YOU\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 284,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    padding: '0.25rem 0.5rem',\n                    background: positionColor,\n                    color: 'white',\n                    borderRadius: '4px',\n                    fontSize: '0.75rem',\n                    fontWeight: '600',\n                    display: 'inline-block'\n                  },\n                  children: positionLabel\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '0.25rem 0.5rem',\n                background: admin.is_active ? '#dcfce7' : '#fef2f2',\n                color: admin.is_active ? '#166534' : '#dc2626',\n                borderRadius: '4px',\n                fontSize: '0.75rem',\n                fontWeight: '600'\n              },\n              children: admin.is_active ? 'Active' : 'Inactive'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                marginBottom: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Mail, {\n                size: 16,\n                color: \"#6b7280\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                },\n                children: admin.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 17\n            }, this), admin.phone_number && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                marginBottom: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Phone, {\n                size: 16,\n                color: \"#6b7280\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                },\n                children: admin.phone_number\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 19\n            }, this), admin.department && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                color: '#6b7280',\n                marginTop: '0.5rem'\n              },\n              children: [\"Department: \", admin.department]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 19\n            }, this), admin.grade_level && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                color: '#6b7280',\n                marginTop: '0.25rem'\n              },\n              children: [\"Grade Level: \", admin.grade_level]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 15\n          }, this), admin.last_login && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.75rem',\n              color: '#9ca3af',\n              marginBottom: '1rem'\n            },\n            children: [\"Last login: \", new Date(admin.last_login).toLocaleDateString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '0.5rem',\n              paddingTop: '1rem',\n              borderTop: '1px solid #f3f4f6'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                flex: 1,\n                padding: '0.5rem',\n                background: 'transparent',\n                border: '1px solid #d1d5db',\n                borderRadius: '6px',\n                cursor: 'pointer',\n                color: '#6b7280',\n                fontSize: '0.875rem',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                gap: '0.25rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Edit, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 19\n              }, this), \"Edit\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 17\n            }, this), admin.admin_id !== (user === null || user === void 0 ? void 0 : user.id) && /*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                flex: 1,\n                padding: '0.5rem',\n                background: 'transparent',\n                border: '1px solid #fca5a5',\n                borderRadius: '6px',\n                cursor: 'pointer',\n                color: '#dc2626',\n                fontSize: '0.875rem',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                gap: '0.25rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Trash2, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 21\n              }, this), \"Delete\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 15\n          }, this)]\n        }, admin.admin_id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 185,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminManagement, \"kvELdNBrE9+GC+2e7p+yaJg9kPQ=\", false, function () {\n  return [useAdminAuth, usePermissions];\n});\n_c = AdminManagement;\nexport default AdminManagement;\nvar _c;\n$RefreshReg$(_c, \"AdminManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAdminAuth", "usePermissions", "jsxDEV", "_jsxDEV", "AdminManagement", "_s", "user", "permissions", "admins", "setAdmins", "loading", "setLoading", "error", "setError", "canManageAdmins", "loadAdmins", "admin_id", "email", "first_name", "last_name", "phone_number", "department", "position", "is_active", "last_login", "created_at", "grade_level", "err", "console", "getPositionIcon", "Shield", "User", "getPositionColor", "getPositionLabel", "style", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "textAlign", "color", "children", "UserCog", "size", "marginBottom", "opacity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "margin", "fontSize", "fontWeight", "marginTop", "padding", "background", "getPositionBadgeColor", "borderRadius", "getPositionDisplayName", "width", "height", "border", "borderTop", "animation", "onClick", "cursor", "max<PERSON><PERSON><PERSON>", "gap", "transition", "onMouseEnter", "e", "currentTarget", "onMouseLeave", "Plus", "gridTemplateColumns", "map", "admin", "PositionIcon", "positionColor", "position<PERSON><PERSON><PERSON>", "boxShadow", "id", "marginLeft", "Mail", "Phone", "Date", "toLocaleDateString", "paddingTop", "flex", "Edit", "Trash2", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/admin/AdminManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { UserPlus, Search, Filter, Users, AlertTriangle, CheckCircle } from 'lucide-react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\nimport { adminManagementService } from '../../services/adminManagementService';\nimport AdminAccountList from '../../components/admin/AdminAccountList';\nimport AdminAccountModal from '../../components/admin/AdminAccountModal';\n\ninterface AdminAccount {\n  admin_id: number;\n  email: string;\n  is_active: boolean;\n  last_login: string | null;\n  created_at: string;\n  profile: {\n    first_name: string;\n    middle_name?: string;\n    last_name: string;\n    suffix?: string;\n    full_name: string;\n    phone_number?: string;\n    department?: string;\n    position: 'super_admin' | 'professor';\n    grade_level?: number;\n    bio?: string;\n    profile_picture?: string;\n  };\n}\n\nconst AdminManagement: React.FC = () => {\n  const { user } = useAdminAuth();\n  const permissions = usePermissions(user);\n  const [admins, setAdmins] = useState<AdminUser[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    // Check if user has permission to manage admins\n    if (!permissions.canManageAdmins) {\n      setError('You do not have permission to manage admin accounts');\n      setLoading(false);\n      return;\n    }\n\n    loadAdmins();\n  }, [permissions.canManageAdmins]);\n\n  const loadAdmins = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      // TODO: Implement API call to fetch admins\n      // const response = await adminManagementService.getAdmins();\n      // setAdmins(response.data.admins);\n      \n      // Mock data for now\n      setAdmins([\n        {\n          admin_id: 1,\n          email: '<EMAIL>',\n          first_name: 'Von Christian',\n          last_name: 'Admin',\n          phone_number: '+************',\n          department: 'IT Department',\n          position: 'super_admin',\n          is_active: true,\n          last_login: '2025-08-05T10:30:00Z',\n          created_at: '2025-01-01T00:00:00Z'\n        },\n        {\n          admin_id: 2,\n          email: '<EMAIL>',\n          first_name: 'Zaira',\n          last_name: 'Professor',\n          phone_number: '+************',\n          department: 'Academic Department',\n          position: 'professor',\n          grade_level: 11,\n          is_active: true,\n          last_login: '2025-08-05T09:15:00Z',\n          created_at: '2025-02-01T00:00:00Z'\n        }\n      ]);\n    } catch (err) {\n      setError('Failed to load admin accounts');\n      console.error('Error loading admins:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getPositionIcon = (position: string) => {\n    return position === 'super_admin' ? Shield : User;\n  };\n\n  const getPositionColor = (position: string) => {\n    return position === 'super_admin' ? '#dc2626' : '#2563eb';\n  };\n\n  const getPositionLabel = (position: string) => {\n    return position === 'super_admin' ? 'Super Administrator' : 'Professor';\n  };\n\n  if (!permissions.canManageAdmins) {\n    return (\n      <div style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      }}>\n        <UserCog size={64} style={{ marginBottom: '1rem', opacity: 0.5 }} />\n        <h2 style={{ margin: '0 0 0.5rem', fontSize: '1.5rem', fontWeight: '600' }}>\n          Access Denied\n        </h2>\n        <p style={{ margin: 0, fontSize: '1rem' }}>\n          You do not have permission to manage admin accounts.\n        </p>\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        }}>\n          Current Role: {permissions.getPositionDisplayName()}\n        </div>\n      </div>\n    );\n  }\n\n  if (loading) {\n    return (\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px'\n      }}>\n        <div style={{\n          width: '40px',\n          height: '40px',\n          border: '4px solid #f3f4f6',\n          borderTop: '4px solid #3b82f6',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }} />\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div style={{\n        padding: '2rem',\n        textAlign: 'center',\n        color: '#ef4444'\n      }}>\n        <h2>Error</h2>\n        <p>{error}</p>\n        <button\n          onClick={loadAdmins}\n          style={{\n            padding: '0.5rem 1rem',\n            background: '#3b82f6',\n            color: 'white',\n            border: 'none',\n            borderRadius: '6px',\n            cursor: 'pointer'\n          }}\n        >\n          Retry\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ maxWidth: '1200px', margin: '0 auto' }}>\n      {/* Header */}\n      <div style={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      }}>\n        <div>\n          <h1 style={{\n            margin: '0 0 0.5rem',\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          }}>\n            Admin Management\n          </h1>\n          <p style={{\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          }}>\n            Manage administrator accounts and permissions\n          </p>\n        </div>\n        \n        <button\n          style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            padding: '0.75rem 1.5rem',\n            background: '#3b82f6',\n            color: 'white',\n            border: 'none',\n            borderRadius: '8px',\n            fontSize: '0.875rem',\n            fontWeight: '600',\n            cursor: 'pointer',\n            transition: 'background-color 0.2s'\n          }}\n          onMouseEnter={(e) => e.currentTarget.style.background = '#2563eb'}\n          onMouseLeave={(e) => e.currentTarget.style.background = '#3b82f6'}\n        >\n          <Plus size={16} />\n          Add Admin\n        </button>\n      </div>\n\n      {/* Admin Cards */}\n      <div style={{\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fill, minmax(400px, 1fr))',\n        gap: '1.5rem'\n      }}>\n        {admins.map((admin) => {\n          const PositionIcon = getPositionIcon(admin.position);\n          const positionColor = getPositionColor(admin.position);\n          const positionLabel = getPositionLabel(admin.position);\n          \n          return (\n            <div\n              key={admin.admin_id}\n              style={{\n                background: 'white',\n                borderRadius: '12px',\n                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n                padding: '1.5rem',\n                border: admin.admin_id === user?.id ? '2px solid #facc15' : '1px solid #e5e7eb'\n              }}\n            >\n              {/* Header */}\n              <div style={{\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'flex-start',\n                marginBottom: '1rem'\n              }}>\n                <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>\n                  <div style={{\n                    width: '48px',\n                    height: '48px',\n                    background: `linear-gradient(135deg, ${positionColor}20, ${positionColor}10)`,\n                    borderRadius: '12px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  }}>\n                    <PositionIcon size={24} color={positionColor} />\n                  </div>\n                  <div>\n                    <h3 style={{\n                      margin: '0 0 0.25rem',\n                      fontSize: '1.125rem',\n                      fontWeight: '600',\n                      color: '#1f2937'\n                    }}>\n                      {admin.first_name} {admin.last_name}\n                      {admin.admin_id === user?.id && (\n                        <span style={{\n                          marginLeft: '0.5rem',\n                          padding: '0.125rem 0.375rem',\n                          background: '#facc15',\n                          color: '#1f2937',\n                          borderRadius: '4px',\n                          fontSize: '0.625rem',\n                          fontWeight: '700'\n                        }}>\n                          YOU\n                        </span>\n                      )}\n                    </h3>\n                    <div style={{\n                      padding: '0.25rem 0.5rem',\n                      background: positionColor,\n                      color: 'white',\n                      borderRadius: '4px',\n                      fontSize: '0.75rem',\n                      fontWeight: '600',\n                      display: 'inline-block'\n                    }}>\n                      {positionLabel}\n                    </div>\n                  </div>\n                </div>\n                \n                <div style={{\n                  padding: '0.25rem 0.5rem',\n                  background: admin.is_active ? '#dcfce7' : '#fef2f2',\n                  color: admin.is_active ? '#166534' : '#dc2626',\n                  borderRadius: '4px',\n                  fontSize: '0.75rem',\n                  fontWeight: '600'\n                }}>\n                  {admin.is_active ? 'Active' : 'Inactive'}\n                </div>\n              </div>\n\n              {/* Contact Info */}\n              <div style={{ marginBottom: '1rem' }}>\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  marginBottom: '0.5rem'\n                }}>\n                  <Mail size={16} color=\"#6b7280\" />\n                  <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>\n                    {admin.email}\n                  </span>\n                </div>\n                \n                {admin.phone_number && (\n                  <div style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem',\n                    marginBottom: '0.5rem'\n                  }}>\n                    <Phone size={16} color=\"#6b7280\" />\n                    <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>\n                      {admin.phone_number}\n                    </span>\n                  </div>\n                )}\n                \n                {admin.department && (\n                  <div style={{\n                    fontSize: '0.875rem',\n                    color: '#6b7280',\n                    marginTop: '0.5rem'\n                  }}>\n                    Department: {admin.department}\n                  </div>\n                )}\n                \n                {admin.grade_level && (\n                  <div style={{\n                    fontSize: '0.875rem',\n                    color: '#6b7280',\n                    marginTop: '0.25rem'\n                  }}>\n                    Grade Level: {admin.grade_level}\n                  </div>\n                )}\n              </div>\n\n              {/* Last Login */}\n              {admin.last_login && (\n                <div style={{\n                  fontSize: '0.75rem',\n                  color: '#9ca3af',\n                  marginBottom: '1rem'\n                }}>\n                  Last login: {new Date(admin.last_login).toLocaleDateString()}\n                </div>\n              )}\n\n              {/* Actions */}\n              <div style={{\n                display: 'flex',\n                gap: '0.5rem',\n                paddingTop: '1rem',\n                borderTop: '1px solid #f3f4f6'\n              }}>\n                <button\n                  style={{\n                    flex: 1,\n                    padding: '0.5rem',\n                    background: 'transparent',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '6px',\n                    cursor: 'pointer',\n                    color: '#6b7280',\n                    fontSize: '0.875rem',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    gap: '0.25rem'\n                  }}\n                >\n                  <Edit size={14} />\n                  Edit\n                </button>\n                \n                {admin.admin_id !== user?.id && (\n                  <button\n                    style={{\n                      flex: 1,\n                      padding: '0.5rem',\n                      background: 'transparent',\n                      border: '1px solid #fca5a5',\n                      borderRadius: '6px',\n                      cursor: 'pointer',\n                      color: '#dc2626',\n                      fontSize: '0.875rem',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      gap: '0.25rem'\n                    }}\n                  >\n                    <Trash2 size={14} />\n                    Delete\n                  </button>\n                )}\n              </div>\n            </div>\n          );\n        })}\n      </div>\n    </div>\n  );\n};\n\nexport default AdminManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAElD,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA0BzD,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IAAEC;EAAK,CAAC,GAAGN,YAAY,CAAC,CAAC;EAC/B,MAAMO,WAAW,GAAGN,cAAc,CAACK,IAAI,CAAC;EACxC,MAAM,CAACE,MAAM,EAAEC,SAAS,CAAC,GAAGX,QAAQ,CAAc,EAAE,CAAC;EACrD,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAgB,IAAI,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACQ,WAAW,CAACO,eAAe,EAAE;MAChCD,QAAQ,CAAC,qDAAqD,CAAC;MAC/DF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEAI,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACR,WAAW,CAACO,eAAe,CAAC,CAAC;EAEjC,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFJ,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA;MACA;;MAEA;MACAJ,SAAS,CAAC,CACR;QACEO,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE,0BAA0B;QACjCC,UAAU,EAAE,eAAe;QAC3BC,SAAS,EAAE,OAAO;QAClBC,YAAY,EAAE,eAAe;QAC7BC,UAAU,EAAE,eAAe;QAC3BC,QAAQ,EAAE,aAAa;QACvBC,SAAS,EAAE,IAAI;QACfC,UAAU,EAAE,sBAAsB;QAClCC,UAAU,EAAE;MACd,CAAC,EACD;QACET,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE,oBAAoB;QAC3BC,UAAU,EAAE,OAAO;QACnBC,SAAS,EAAE,WAAW;QACtBC,YAAY,EAAE,eAAe;QAC7BC,UAAU,EAAE,qBAAqB;QACjCC,QAAQ,EAAE,WAAW;QACrBI,WAAW,EAAE,EAAE;QACfH,SAAS,EAAE,IAAI;QACfC,UAAU,EAAE,sBAAsB;QAClCC,UAAU,EAAE;MACd,CAAC,CACF,CAAC;IACJ,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZd,QAAQ,CAAC,+BAA+B,CAAC;MACzCe,OAAO,CAAChB,KAAK,CAAC,uBAAuB,EAAEe,GAAG,CAAC;IAC7C,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkB,eAAe,GAAIP,QAAgB,IAAK;IAC5C,OAAOA,QAAQ,KAAK,aAAa,GAAGQ,MAAM,GAAGC,IAAI;EACnD,CAAC;EAED,MAAMC,gBAAgB,GAAIV,QAAgB,IAAK;IAC7C,OAAOA,QAAQ,KAAK,aAAa,GAAG,SAAS,GAAG,SAAS;EAC3D,CAAC;EAED,MAAMW,gBAAgB,GAAIX,QAAgB,IAAK;IAC7C,OAAOA,QAAQ,KAAK,aAAa,GAAG,qBAAqB,GAAG,WAAW;EACzE,CAAC;EAED,IAAI,CAACf,WAAW,CAACO,eAAe,EAAE;IAChC,oBACEX,OAAA;MAAK+B,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE,OAAO;QAClBC,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACAvC,OAAA,CAACwC,OAAO;QAACC,IAAI,EAAE,EAAG;QAACV,KAAK,EAAE;UAAEW,YAAY,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAI;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpE/C,OAAA;QAAI+B,KAAK,EAAE;UAAEiB,MAAM,EAAE,YAAY;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAM,CAAE;QAAAX,QAAA,EAAC;MAE5E;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL/C,OAAA;QAAG+B,KAAK,EAAE;UAAEiB,MAAM,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAV,QAAA,EAAC;MAE3C;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJ/C,OAAA;QAAK+B,KAAK,EAAE;UACVoB,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAEjD,WAAW,CAACkD,qBAAqB,CAAC,CAAC;UAC/CC,YAAY,EAAE,KAAK;UACnBjB,KAAK,EAAE,OAAO;UACdW,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE;QACd,CAAE;QAAAX,QAAA,GAAC,gBACa,EAACnC,WAAW,CAACoD,sBAAsB,CAAC,CAAC;MAAA;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIxC,OAAO,EAAE;IACX,oBACEP,OAAA;MAAK+B,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE;MACb,CAAE;MAAAG,QAAA,eACAvC,OAAA;QAAK+B,KAAK,EAAE;UACV0B,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,mBAAmB;UAC3BC,SAAS,EAAE,mBAAmB;UAC9BL,YAAY,EAAE,KAAK;UACnBM,SAAS,EAAE;QACb;MAAE;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEV;EAEA,IAAItC,KAAK,EAAE;IACT,oBACET,OAAA;MAAK+B,KAAK,EAAE;QACVqB,OAAO,EAAE,MAAM;QACff,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACAvC,OAAA;QAAAuC,QAAA,EAAI;MAAK;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACd/C,OAAA;QAAAuC,QAAA,EAAI9B;MAAK;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACd/C,OAAA;QACE8D,OAAO,EAAElD,UAAW;QACpBmB,KAAK,EAAE;UACLqB,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAE,SAAS;UACrBf,KAAK,EAAE,OAAO;UACdqB,MAAM,EAAE,MAAM;UACdJ,YAAY,EAAE,KAAK;UACnBQ,MAAM,EAAE;QACV,CAAE;QAAAxB,QAAA,EACH;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACE/C,OAAA;IAAK+B,KAAK,EAAE;MAAEiC,QAAQ,EAAE,QAAQ;MAAEhB,MAAM,EAAE;IAAS,CAAE;IAAAT,QAAA,gBAEnDvC,OAAA;MAAK+B,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfG,cAAc,EAAE,eAAe;QAC/BD,UAAU,EAAE,QAAQ;QACpBQ,YAAY,EAAE;MAChB,CAAE;MAAAH,QAAA,gBACAvC,OAAA;QAAAuC,QAAA,gBACEvC,OAAA;UAAI+B,KAAK,EAAE;YACTiB,MAAM,EAAE,YAAY;YACpBC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjBZ,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,EAAC;QAEH;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL/C,OAAA;UAAG+B,KAAK,EAAE;YACRiB,MAAM,EAAE,CAAC;YACTV,KAAK,EAAE,SAAS;YAChBW,QAAQ,EAAE;UACZ,CAAE;UAAAV,QAAA,EAAC;QAEH;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN/C,OAAA;QACE+B,KAAK,EAAE;UACLC,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpB+B,GAAG,EAAE,QAAQ;UACbb,OAAO,EAAE,gBAAgB;UACzBC,UAAU,EAAE,SAAS;UACrBf,KAAK,EAAE,OAAO;UACdqB,MAAM,EAAE,MAAM;UACdJ,YAAY,EAAE,KAAK;UACnBN,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE,KAAK;UACjBa,MAAM,EAAE,SAAS;UACjBG,UAAU,EAAE;QACd,CAAE;QACFC,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACtC,KAAK,CAACsB,UAAU,GAAG,SAAU;QAClEiB,YAAY,EAAGF,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACtC,KAAK,CAACsB,UAAU,GAAG,SAAU;QAAAd,QAAA,gBAElEvC,OAAA,CAACuE,IAAI;UAAC9B,IAAI,EAAE;QAAG;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,aAEpB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN/C,OAAA;MAAK+B,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfwC,mBAAmB,EAAE,uCAAuC;QAC5DP,GAAG,EAAE;MACP,CAAE;MAAA1B,QAAA,EACClC,MAAM,CAACoE,GAAG,CAAEC,KAAK,IAAK;QACrB,MAAMC,YAAY,GAAGjD,eAAe,CAACgD,KAAK,CAACvD,QAAQ,CAAC;QACpD,MAAMyD,aAAa,GAAG/C,gBAAgB,CAAC6C,KAAK,CAACvD,QAAQ,CAAC;QACtD,MAAM0D,aAAa,GAAG/C,gBAAgB,CAAC4C,KAAK,CAACvD,QAAQ,CAAC;QAEtD,oBACEnB,OAAA;UAEE+B,KAAK,EAAE;YACLsB,UAAU,EAAE,OAAO;YACnBE,YAAY,EAAE,MAAM;YACpBuB,SAAS,EAAE,8BAA8B;YACzC1B,OAAO,EAAE,QAAQ;YACjBO,MAAM,EAAEe,KAAK,CAAC7D,QAAQ,MAAKV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4E,EAAE,IAAG,mBAAmB,GAAG;UAC9D,CAAE;UAAAxC,QAAA,gBAGFvC,OAAA;YAAK+B,KAAK,EAAE;cACVC,OAAO,EAAE,MAAM;cACfG,cAAc,EAAE,eAAe;cAC/BD,UAAU,EAAE,YAAY;cACxBQ,YAAY,EAAE;YAChB,CAAE;YAAAH,QAAA,gBACAvC,OAAA;cAAK+B,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAE+B,GAAG,EAAE;cAAU,CAAE;cAAA1B,QAAA,gBACpEvC,OAAA;gBAAK+B,KAAK,EAAE;kBACV0B,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdL,UAAU,EAAE,2BAA2BuB,aAAa,OAAOA,aAAa,KAAK;kBAC7ErB,YAAY,EAAE,MAAM;kBACpBvB,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBC,cAAc,EAAE;gBAClB,CAAE;gBAAAI,QAAA,eACAvC,OAAA,CAAC2E,YAAY;kBAAClC,IAAI,EAAE,EAAG;kBAACH,KAAK,EAAEsC;gBAAc;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACN/C,OAAA;gBAAAuC,QAAA,gBACEvC,OAAA;kBAAI+B,KAAK,EAAE;oBACTiB,MAAM,EAAE,aAAa;oBACrBC,QAAQ,EAAE,UAAU;oBACpBC,UAAU,EAAE,KAAK;oBACjBZ,KAAK,EAAE;kBACT,CAAE;kBAAAC,QAAA,GACCmC,KAAK,CAAC3D,UAAU,EAAC,GAAC,EAAC2D,KAAK,CAAC1D,SAAS,EAClC0D,KAAK,CAAC7D,QAAQ,MAAKV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4E,EAAE,kBAC1B/E,OAAA;oBAAM+B,KAAK,EAAE;sBACXiD,UAAU,EAAE,QAAQ;sBACpB5B,OAAO,EAAE,mBAAmB;sBAC5BC,UAAU,EAAE,SAAS;sBACrBf,KAAK,EAAE,SAAS;sBAChBiB,YAAY,EAAE,KAAK;sBACnBN,QAAQ,EAAE,UAAU;sBACpBC,UAAU,EAAE;oBACd,CAAE;oBAAAX,QAAA,EAAC;kBAEH;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACL/C,OAAA;kBAAK+B,KAAK,EAAE;oBACVqB,OAAO,EAAE,gBAAgB;oBACzBC,UAAU,EAAEuB,aAAa;oBACzBtC,KAAK,EAAE,OAAO;oBACdiB,YAAY,EAAE,KAAK;oBACnBN,QAAQ,EAAE,SAAS;oBACnBC,UAAU,EAAE,KAAK;oBACjBlB,OAAO,EAAE;kBACX,CAAE;kBAAAO,QAAA,EACCsC;gBAAa;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN/C,OAAA;cAAK+B,KAAK,EAAE;gBACVqB,OAAO,EAAE,gBAAgB;gBACzBC,UAAU,EAAEqB,KAAK,CAACtD,SAAS,GAAG,SAAS,GAAG,SAAS;gBACnDkB,KAAK,EAAEoC,KAAK,CAACtD,SAAS,GAAG,SAAS,GAAG,SAAS;gBAC9CmC,YAAY,EAAE,KAAK;gBACnBN,QAAQ,EAAE,SAAS;gBACnBC,UAAU,EAAE;cACd,CAAE;cAAAX,QAAA,EACCmC,KAAK,CAACtD,SAAS,GAAG,QAAQ,GAAG;YAAU;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN/C,OAAA;YAAK+B,KAAK,EAAE;cAAEW,YAAY,EAAE;YAAO,CAAE;YAAAH,QAAA,gBACnCvC,OAAA;cAAK+B,KAAK,EAAE;gBACVC,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE,QAAQ;gBACpB+B,GAAG,EAAE,QAAQ;gBACbvB,YAAY,EAAE;cAChB,CAAE;cAAAH,QAAA,gBACAvC,OAAA,CAACiF,IAAI;gBAACxC,IAAI,EAAE,EAAG;gBAACH,KAAK,EAAC;cAAS;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClC/C,OAAA;gBAAM+B,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,UAAU;kBAAEX,KAAK,EAAE;gBAAU,CAAE;gBAAAC,QAAA,EACrDmC,KAAK,CAAC5D;cAAK;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EAEL2B,KAAK,CAACzD,YAAY,iBACjBjB,OAAA;cAAK+B,KAAK,EAAE;gBACVC,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE,QAAQ;gBACpB+B,GAAG,EAAE,QAAQ;gBACbvB,YAAY,EAAE;cAChB,CAAE;cAAAH,QAAA,gBACAvC,OAAA,CAACkF,KAAK;gBAACzC,IAAI,EAAE,EAAG;gBAACH,KAAK,EAAC;cAAS;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnC/C,OAAA;gBAAM+B,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,UAAU;kBAAEX,KAAK,EAAE;gBAAU,CAAE;gBAAAC,QAAA,EACrDmC,KAAK,CAACzD;cAAY;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN,EAEA2B,KAAK,CAACxD,UAAU,iBACflB,OAAA;cAAK+B,KAAK,EAAE;gBACVkB,QAAQ,EAAE,UAAU;gBACpBX,KAAK,EAAE,SAAS;gBAChBa,SAAS,EAAE;cACb,CAAE;cAAAZ,QAAA,GAAC,cACW,EAACmC,KAAK,CAACxD,UAAU;YAAA;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CACN,EAEA2B,KAAK,CAACnD,WAAW,iBAChBvB,OAAA;cAAK+B,KAAK,EAAE;gBACVkB,QAAQ,EAAE,UAAU;gBACpBX,KAAK,EAAE,SAAS;gBAChBa,SAAS,EAAE;cACb,CAAE;cAAAZ,QAAA,GAAC,eACY,EAACmC,KAAK,CAACnD,WAAW;YAAA;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGL2B,KAAK,CAACrD,UAAU,iBACfrB,OAAA;YAAK+B,KAAK,EAAE;cACVkB,QAAQ,EAAE,SAAS;cACnBX,KAAK,EAAE,SAAS;cAChBI,YAAY,EAAE;YAChB,CAAE;YAAAH,QAAA,GAAC,cACW,EAAC,IAAI4C,IAAI,CAACT,KAAK,CAACrD,UAAU,CAAC,CAAC+D,kBAAkB,CAAC,CAAC;UAAA;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CACN,eAGD/C,OAAA;YAAK+B,KAAK,EAAE;cACVC,OAAO,EAAE,MAAM;cACfiC,GAAG,EAAE,QAAQ;cACboB,UAAU,EAAE,MAAM;cAClBzB,SAAS,EAAE;YACb,CAAE;YAAArB,QAAA,gBACAvC,OAAA;cACE+B,KAAK,EAAE;gBACLuD,IAAI,EAAE,CAAC;gBACPlC,OAAO,EAAE,QAAQ;gBACjBC,UAAU,EAAE,aAAa;gBACzBM,MAAM,EAAE,mBAAmB;gBAC3BJ,YAAY,EAAE,KAAK;gBACnBQ,MAAM,EAAE,SAAS;gBACjBzB,KAAK,EAAE,SAAS;gBAChBW,QAAQ,EAAE,UAAU;gBACpBjB,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB8B,GAAG,EAAE;cACP,CAAE;cAAA1B,QAAA,gBAEFvC,OAAA,CAACuF,IAAI;gBAAC9C,IAAI,EAAE;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,QAEpB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAER2B,KAAK,CAAC7D,QAAQ,MAAKV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4E,EAAE,kBAC1B/E,OAAA;cACE+B,KAAK,EAAE;gBACLuD,IAAI,EAAE,CAAC;gBACPlC,OAAO,EAAE,QAAQ;gBACjBC,UAAU,EAAE,aAAa;gBACzBM,MAAM,EAAE,mBAAmB;gBAC3BJ,YAAY,EAAE,KAAK;gBACnBQ,MAAM,EAAE,SAAS;gBACjBzB,KAAK,EAAE,SAAS;gBAChBW,QAAQ,EAAE,UAAU;gBACpBjB,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB8B,GAAG,EAAE;cACP,CAAE;cAAA1B,QAAA,gBAEFvC,OAAA,CAACwF,MAAM;gBAAC/C,IAAI,EAAE;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAEtB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,GAxLD2B,KAAK,CAAC7D,QAAQ;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAyLhB,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7C,EAAA,CAxZID,eAAyB;EAAA,QACZJ,YAAY,EACTC,cAAc;AAAA;AAAA2F,EAAA,GAF9BxF,eAAyB;AA0Z/B,eAAeA,eAAe;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}