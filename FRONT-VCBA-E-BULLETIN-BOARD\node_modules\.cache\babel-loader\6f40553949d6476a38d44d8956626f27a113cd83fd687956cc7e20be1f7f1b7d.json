{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\admin\\\\CategoryManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { FolderTree, Plus, AlertTriangle, CheckCircle } from 'lucide-react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\nimport { categoryService } from '../../services/categoryService';\nimport CategoryList from '../../components/admin/CategoryList';\nimport CategoryModal from '../../components/admin/CategoryModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CategoryManagement = () => {\n  _s();\n  const {\n    user\n  } = useAdminAuth();\n  const permissions = usePermissions(user);\n\n  // State management\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n\n  // Modal state\n  const [showModal, setShowModal] = useState(false);\n  const [modalMode, setModalMode] = useState('add_category');\n  const [editingCategory, setEditingCategory] = useState(null);\n  const [editingSubcategory, setEditingSubcategory] = useState(null);\n  const [parentCategory, setParentCategory] = useState(null);\n  const [modalLoading, setModalLoading] = useState(false);\n  useEffect(() => {\n    // Check if user has permission to manage categories\n    if (!permissions.canManageCategories) {\n      setError('You do not have permission to manage categories');\n      setLoading(false);\n      return;\n    }\n    loadCategories();\n  }, [permissions.canManageCategories]);\n  const loadCategories = async () => {\n    try {\n      var _response$data;\n      setLoading(true);\n      setError(null);\n      console.log('🔍 CategoryManagement - Loading categories from API...');\n      const response = await categoryService.getCategoriesWithSubcategories();\n      console.log('🔍 CategoryManagement - API response:', response);\n      if (response.success && (_response$data = response.data) !== null && _response$data !== void 0 && _response$data.categories) {\n        setCategories(response.data.categories);\n        console.log('✅ CategoryManagement - Categories loaded successfully:', response.data.categories.length);\n      } else {\n        throw new Error(response.message || 'Failed to load categories');\n      }\n    } catch (err) {\n      console.error('❌ CategoryManagement - Error loading categories:', err);\n      setError(err.message || 'Failed to load categories');\n      // Set empty array on error to show \"no categories\" state\n      setCategories([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handler functions\n  const handleAddCategory = () => {\n    setModalMode('add_category');\n    setEditingCategory(null);\n    setEditingSubcategory(null);\n    setParentCategory(null);\n    setShowModal(true);\n  };\n  const handleEditCategory = category => {\n    setModalMode('edit_category');\n    setEditingCategory(category);\n    setEditingSubcategory(null);\n    setParentCategory(null);\n    setShowModal(true);\n  };\n  const handleDeleteCategory = async category => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await categoryService.deleteCategory(category.category_id!);\n\n      setSuccess(`Category \"${category.name}\" has been deleted successfully`);\n      loadCategories();\n    } catch (err) {\n      setError(err.message || 'Failed to delete category');\n    }\n  };\n  const handleToggleCategoryStatus = async category => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await categoryService.toggleCategoryStatus(category.category_id!, !category.is_active);\n\n      const action = category.is_active ? 'deactivated' : 'activated';\n      setSuccess(`Category \"${category.name}\" has been ${action} successfully`);\n      loadCategories();\n    } catch (err) {\n      setError(err.message || 'Failed to update category status');\n    }\n  };\n  const handleAddSubcategory = category => {\n    setModalMode('add_subcategory');\n    setEditingCategory(null);\n    setEditingSubcategory(null);\n    setParentCategory(category);\n    setShowModal(true);\n  };\n  const handleEditSubcategory = (category, subcategory) => {\n    setModalMode('edit_subcategory');\n    setEditingCategory(null);\n    setEditingSubcategory(subcategory);\n    setParentCategory(category);\n    setShowModal(true);\n  };\n  const handleDeleteSubcategory = async (category, subcategory) => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await categoryService.deleteSubcategory(category.category_id!, subcategory.subcategory_id!);\n\n      setSuccess(`Subcategory \"${subcategory.name}\" has been deleted successfully`);\n      loadCategories();\n    } catch (err) {\n      setError(err.message || 'Failed to delete subcategory');\n    }\n  };\n  const handleToggleSubcategoryStatus = async (category, subcategory) => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await categoryService.toggleSubcategoryStatus(category.category_id!, subcategory.subcategory_id!, !subcategory.is_active);\n\n      const action = subcategory.is_active ? 'deactivated' : 'activated';\n      setSuccess(`Subcategory \"${subcategory.name}\" has been ${action} successfully`);\n      loadCategories();\n    } catch (err) {\n      setError(err.message || 'Failed to update subcategory status');\n    }\n  };\n  const handleSave = async (data, parentCat) => {\n    try {\n      setModalLoading(true);\n      setError(null);\n      setSuccess(null);\n      if (modalMode === 'add_category') {\n        // TODO: Implement actual API call\n        // await categoryService.createCategory(data as Category);\n        setSuccess(`Category \"${data.name}\" has been created successfully`);\n      } else if (modalMode === 'edit_category') {\n        // TODO: Implement actual API call\n        // await categoryService.updateCategory(editingCategory!.category_id!, data as Category);\n        setSuccess(`Category \"${data.name}\" has been updated successfully`);\n      } else if (modalMode === 'add_subcategory') {\n        // TODO: Implement actual API call\n        // await categoryService.createSubcategory(parentCat!.category_id!, data as Subcategory);\n        setSuccess(`Subcategory \"${data.name}\" has been created successfully`);\n      } else if (modalMode === 'edit_subcategory') {\n        // TODO: Implement actual API call\n        // await categoryService.updateSubcategory(parentCat!.category_id!, editingSubcategory!.subcategory_id!, data as Subcategory);\n        setSuccess(`Subcategory \"${data.name}\" has been updated successfully`);\n      }\n      loadCategories();\n    } catch (err) {\n      setError(err.message || 'Failed to save changes');\n      throw err; // Re-throw to prevent modal from closing\n    } finally {\n      setModalLoading(false);\n    }\n  };\n  const clearMessages = () => {\n    setError(null);\n    setSuccess(null);\n  };\n  if (!permissions.canManageCategories) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      },\n      children: [/*#__PURE__*/_jsxDEV(FolderTree, {\n        size: 64,\n        style: {\n          marginBottom: '1rem',\n          opacity: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          margin: '0 0 0.5rem',\n          fontSize: '1.5rem',\n          fontWeight: '600'\n        },\n        children: \"Access Denied\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: 0,\n          fontSize: '1rem'\n        },\n        children: \"You do not have permission to manage categories and subcategories.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        },\n        children: [\"Current Role: \", permissions.getPositionDisplayName()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '40px',\n          height: '40px',\n          border: '4px solid #f3f4f6',\n          borderTop: '4px solid #3b82f6',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '2rem',\n        textAlign: 'center',\n        color: '#ef4444'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Error\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: loadCategories,\n        style: {\n          padding: '0.5rem 1rem',\n          background: '#3b82f6',\n          color: 'white',\n          border: 'none',\n          borderRadius: '6px',\n          cursor: 'pointer'\n        },\n        children: \"Retry\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      maxWidth: '1400px',\n      margin: '0 auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            margin: '0 0 0.5rem',\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          },\n          children: \"Category Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          },\n          children: \"Manage categories and subcategories for announcements and events\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleAddCategory,\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          padding: '0.75rem 1.5rem',\n          background: '#3b82f6',\n          color: 'white',\n          border: 'none',\n          borderRadius: '8px',\n          fontSize: '0.875rem',\n          fontWeight: '600',\n          cursor: 'pointer',\n          transition: 'background-color 0.2s'\n        },\n        onMouseEnter: e => e.currentTarget.style.background = '#2563eb',\n        onMouseLeave: e => e.currentTarget.style.background = '#3b82f6',\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this), \"Add Category\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 302,\n      columnNumber: 7\n    }, this), (error || success) && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '1.5rem'\n      },\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '1rem',\n          background: '#fef2f2',\n          border: '1px solid #fecaca',\n          borderRadius: '8px',\n          color: '#dc2626',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 17\n          }, this), error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: clearMessages,\n          style: {\n            background: 'transparent',\n            border: 'none',\n            color: '#dc2626',\n            cursor: 'pointer',\n            padding: '0.25rem'\n          },\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 13\n      }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '1rem',\n          background: '#f0fdf4',\n          border: '1px solid #bbf7d0',\n          borderRadius: '8px',\n          color: '#166534',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 17\n          }, this), success]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: clearMessages,\n          style: {\n            background: 'transparent',\n            border: 'none',\n            color: '#166534',\n            cursor: 'pointer',\n            padding: '0.25rem'\n          },\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 352,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(CategoryList, {\n      categories: categories,\n      loading: loading,\n      onEditCategory: handleEditCategory,\n      onDeleteCategory: handleDeleteCategory,\n      onToggleCategoryStatus: handleToggleCategoryStatus,\n      onAddSubcategory: handleAddSubcategory,\n      onEditSubcategory: handleEditSubcategory,\n      onDeleteSubcategory: handleDeleteSubcategory,\n      onToggleSubcategoryStatus: handleToggleSubcategoryStatus\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 416,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CategoryModal, {\n      isOpen: showModal,\n      onClose: () => setShowModal(false),\n      onSave: handleSave,\n      category: editingCategory,\n      subcategory: editingSubcategory,\n      parentCategory: parentCategory,\n      mode: modalMode,\n      loading: modalLoading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 429,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 300,\n    columnNumber: 5\n  }, this);\n};\n_s(CategoryManagement, \"Fi2eW2xY3Tqt2uCjsGG8TTkc8Lc=\", false, function () {\n  return [useAdminAuth, usePermissions];\n});\n_c = CategoryManagement;\nexport default CategoryManagement;\nvar _c;\n$RefreshReg$(_c, \"CategoryManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FolderTree", "Plus", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CheckCircle", "useAdminAuth", "usePermissions", "categoryService", "CategoryList", "CategoryModal", "jsxDEV", "_jsxDEV", "CategoryManagement", "_s", "user", "permissions", "categories", "setCategories", "loading", "setLoading", "error", "setError", "success", "setSuccess", "showModal", "setShowModal", "modalMode", "setModalMode", "editingCategory", "setEditingCategory", "editingSubcategory", "setEditingSubcategory", "parentCategory", "setParentCategory", "modalLoading", "setModalLoading", "canManageCategories", "loadCategories", "_response$data", "console", "log", "response", "getCategoriesWithSubcategories", "data", "length", "Error", "message", "err", "handleAddCategory", "handleEditCategory", "category", "handleDeleteCategory", "name", "handleToggleCategoryStatus", "action", "is_active", "handleAddSubcategory", "handleEditSubcategory", "subcategory", "handleDeleteSubcategory", "handleToggleSubcategoryStatus", "handleSave", "parentCat", "clearMessages", "style", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "textAlign", "color", "children", "size", "marginBottom", "opacity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "margin", "fontSize", "fontWeight", "marginTop", "padding", "background", "getPositionBadgeColor", "borderRadius", "getPositionDisplayName", "width", "height", "border", "borderTop", "animation", "onClick", "cursor", "max<PERSON><PERSON><PERSON>", "gap", "transition", "onMouseEnter", "e", "currentTarget", "onMouseLeave", "onEditCategory", "onDeleteCategory", "onToggleCategoryStatus", "onAddSubcategory", "onEditSubcategory", "onDeleteSubcategory", "onToggleSubcategoryStatus", "isOpen", "onClose", "onSave", "mode", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/admin/CategoryManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { FolderTree, Plus, AlertTriangle, CheckCircle } from 'lucide-react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\nimport { categoryService } from '../../services/categoryService';\nimport CategoryList from '../../components/admin/CategoryList';\nimport CategoryModal from '../../components/admin/CategoryModal';\n\ninterface Category {\n  category_id?: number;\n  name: string;\n  description?: string;\n  color_code: string;\n  is_active: boolean;\n  subcategories?: Subcategory[];\n}\n\ninterface Subcategory {\n  subcategory_id?: number;\n  name: string;\n  description?: string;\n  color_code: string;\n  display_order: number;\n  is_active: boolean;\n}\n\nconst CategoryManagement: React.FC = () => {\n  const { user } = useAdminAuth();\n  const permissions = usePermissions(user);\n\n  // State management\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  // Modal state\n  const [showModal, setShowModal] = useState(false);\n  const [modalMode, setModalMode] = useState<'add_category' | 'edit_category' | 'add_subcategory' | 'edit_subcategory'>('add_category');\n  const [editingCategory, setEditingCategory] = useState<Category | null>(null);\n  const [editingSubcategory, setEditingSubcategory] = useState<Subcategory | null>(null);\n  const [parentCategory, setParentCategory] = useState<Category | null>(null);\n  const [modalLoading, setModalLoading] = useState(false);\n\n  useEffect(() => {\n    // Check if user has permission to manage categories\n    if (!permissions.canManageCategories) {\n      setError('You do not have permission to manage categories');\n      setLoading(false);\n      return;\n    }\n\n    loadCategories();\n  }, [permissions.canManageCategories]);\n\n  const loadCategories = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log('🔍 CategoryManagement - Loading categories from API...');\n      const response = await categoryService.getCategoriesWithSubcategories();\n      console.log('🔍 CategoryManagement - API response:', response);\n\n      if (response.success && response.data?.categories) {\n        setCategories(response.data.categories);\n        console.log('✅ CategoryManagement - Categories loaded successfully:', response.data.categories.length);\n      } else {\n        throw new Error(response.message || 'Failed to load categories');\n      }\n\n    } catch (err: any) {\n      console.error('❌ CategoryManagement - Error loading categories:', err);\n      setError(err.message || 'Failed to load categories');\n      // Set empty array on error to show \"no categories\" state\n      setCategories([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handler functions\n  const handleAddCategory = () => {\n    setModalMode('add_category');\n    setEditingCategory(null);\n    setEditingSubcategory(null);\n    setParentCategory(null);\n    setShowModal(true);\n  };\n\n  const handleEditCategory = (category: Category) => {\n    setModalMode('edit_category');\n    setEditingCategory(category);\n    setEditingSubcategory(null);\n    setParentCategory(null);\n    setShowModal(true);\n  };\n\n  const handleDeleteCategory = async (category: Category) => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await categoryService.deleteCategory(category.category_id!);\n\n      setSuccess(`Category \"${category.name}\" has been deleted successfully`);\n      loadCategories();\n\n    } catch (err: any) {\n      setError(err.message || 'Failed to delete category');\n    }\n  };\n\n  const handleToggleCategoryStatus = async (category: Category) => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await categoryService.toggleCategoryStatus(category.category_id!, !category.is_active);\n\n      const action = category.is_active ? 'deactivated' : 'activated';\n      setSuccess(`Category \"${category.name}\" has been ${action} successfully`);\n      loadCategories();\n\n    } catch (err: any) {\n      setError(err.message || 'Failed to update category status');\n    }\n  };\n\n  const handleAddSubcategory = (category: Category) => {\n    setModalMode('add_subcategory');\n    setEditingCategory(null);\n    setEditingSubcategory(null);\n    setParentCategory(category);\n    setShowModal(true);\n  };\n\n  const handleEditSubcategory = (category: Category, subcategory: Subcategory) => {\n    setModalMode('edit_subcategory');\n    setEditingCategory(null);\n    setEditingSubcategory(subcategory);\n    setParentCategory(category);\n    setShowModal(true);\n  };\n\n  const handleDeleteSubcategory = async (category: Category, subcategory: Subcategory) => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await categoryService.deleteSubcategory(category.category_id!, subcategory.subcategory_id!);\n\n      setSuccess(`Subcategory \"${subcategory.name}\" has been deleted successfully`);\n      loadCategories();\n\n    } catch (err: any) {\n      setError(err.message || 'Failed to delete subcategory');\n    }\n  };\n\n  const handleToggleSubcategoryStatus = async (category: Category, subcategory: Subcategory) => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await categoryService.toggleSubcategoryStatus(category.category_id!, subcategory.subcategory_id!, !subcategory.is_active);\n\n      const action = subcategory.is_active ? 'deactivated' : 'activated';\n      setSuccess(`Subcategory \"${subcategory.name}\" has been ${action} successfully`);\n      loadCategories();\n\n    } catch (err: any) {\n      setError(err.message || 'Failed to update subcategory status');\n    }\n  };\n\n  const handleSave = async (data: Category | Subcategory, parentCat?: Category) => {\n    try {\n      setModalLoading(true);\n      setError(null);\n      setSuccess(null);\n\n      if (modalMode === 'add_category') {\n        // TODO: Implement actual API call\n        // await categoryService.createCategory(data as Category);\n        setSuccess(`Category \"${data.name}\" has been created successfully`);\n      } else if (modalMode === 'edit_category') {\n        // TODO: Implement actual API call\n        // await categoryService.updateCategory(editingCategory!.category_id!, data as Category);\n        setSuccess(`Category \"${data.name}\" has been updated successfully`);\n      } else if (modalMode === 'add_subcategory') {\n        // TODO: Implement actual API call\n        // await categoryService.createSubcategory(parentCat!.category_id!, data as Subcategory);\n        setSuccess(`Subcategory \"${data.name}\" has been created successfully`);\n      } else if (modalMode === 'edit_subcategory') {\n        // TODO: Implement actual API call\n        // await categoryService.updateSubcategory(parentCat!.category_id!, editingSubcategory!.subcategory_id!, data as Subcategory);\n        setSuccess(`Subcategory \"${data.name}\" has been updated successfully`);\n      }\n\n      loadCategories();\n\n    } catch (err: any) {\n      setError(err.message || 'Failed to save changes');\n      throw err; // Re-throw to prevent modal from closing\n    } finally {\n      setModalLoading(false);\n    }\n  };\n\n  const clearMessages = () => {\n    setError(null);\n    setSuccess(null);\n  };\n\n  if (!permissions.canManageCategories) {\n    return (\n      <div style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      }}>\n        <FolderTree size={64} style={{ marginBottom: '1rem', opacity: 0.5 }} />\n        <h2 style={{ margin: '0 0 0.5rem', fontSize: '1.5rem', fontWeight: '600' }}>\n          Access Denied\n        </h2>\n        <p style={{ margin: 0, fontSize: '1rem' }}>\n          You do not have permission to manage categories and subcategories.\n        </p>\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        }}>\n          Current Role: {permissions.getPositionDisplayName()}\n        </div>\n      </div>\n    );\n  }\n\n  if (loading) {\n    return (\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px'\n      }}>\n        <div style={{\n          width: '40px',\n          height: '40px',\n          border: '4px solid #f3f4f6',\n          borderTop: '4px solid #3b82f6',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }} />\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div style={{\n        padding: '2rem',\n        textAlign: 'center',\n        color: '#ef4444'\n      }}>\n        <h2>Error</h2>\n        <p>{error}</p>\n        <button\n          onClick={loadCategories}\n          style={{\n            padding: '0.5rem 1rem',\n            background: '#3b82f6',\n            color: 'white',\n            border: 'none',\n            borderRadius: '6px',\n            cursor: 'pointer'\n          }}\n        >\n          Retry\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ maxWidth: '1400px', margin: '0 auto' }}>\n      {/* Header */}\n      <div style={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      }}>\n        <div>\n          <h1 style={{\n            margin: '0 0 0.5rem',\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          }}>\n            Category Management\n          </h1>\n          <p style={{\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          }}>\n            Manage categories and subcategories for announcements and events\n          </p>\n        </div>\n\n        <button\n          onClick={handleAddCategory}\n          style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            padding: '0.75rem 1.5rem',\n            background: '#3b82f6',\n            color: 'white',\n            border: 'none',\n            borderRadius: '8px',\n            fontSize: '0.875rem',\n            fontWeight: '600',\n            cursor: 'pointer',\n            transition: 'background-color 0.2s'\n          }}\n          onMouseEnter={(e) => e.currentTarget.style.background = '#2563eb'}\n          onMouseLeave={(e) => e.currentTarget.style.background = '#3b82f6'}\n        >\n          <Plus size={16} />\n          Add Category\n        </button>\n      </div>\n\n      {/* Success/Error Messages */}\n      {(error || success) && (\n        <div style={{ marginBottom: '1.5rem' }}>\n          {error && (\n            <div style={{\n              padding: '1rem',\n              background: '#fef2f2',\n              border: '1px solid #fecaca',\n              borderRadius: '8px',\n              color: '#dc2626',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            }}>\n              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                <AlertTriangle size={16} />\n                {error}\n              </div>\n              <button\n                onClick={clearMessages}\n                style={{\n                  background: 'transparent',\n                  border: 'none',\n                  color: '#dc2626',\n                  cursor: 'pointer',\n                  padding: '0.25rem'\n                }}\n              >\n                ×\n              </button>\n            </div>\n          )}\n\n          {success && (\n            <div style={{\n              padding: '1rem',\n              background: '#f0fdf4',\n              border: '1px solid #bbf7d0',\n              borderRadius: '8px',\n              color: '#166534',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            }}>\n              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                <CheckCircle size={16} />\n                {success}\n              </div>\n              <button\n                onClick={clearMessages}\n                style={{\n                  background: 'transparent',\n                  border: 'none',\n                  color: '#166534',\n                  cursor: 'pointer',\n                  padding: '0.25rem'\n                }}\n              >\n                ×\n              </button>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Category List */}\n      <CategoryList\n        categories={categories}\n        loading={loading}\n        onEditCategory={handleEditCategory}\n        onDeleteCategory={handleDeleteCategory}\n        onToggleCategoryStatus={handleToggleCategoryStatus}\n        onAddSubcategory={handleAddSubcategory}\n        onEditSubcategory={handleEditSubcategory}\n        onDeleteSubcategory={handleDeleteSubcategory}\n        onToggleSubcategoryStatus={handleToggleSubcategoryStatus}\n      />\n\n      {/* Category Modal */}\n      <CategoryModal\n        isOpen={showModal}\n        onClose={() => setShowModal(false)}\n        onSave={handleSave}\n        category={editingCategory}\n        subcategory={editingSubcategory}\n        parentCategory={parentCategory}\n        mode={modalMode}\n        loading={modalLoading}\n      />\n    </div>\n  );\n};\n\nexport default CategoryManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,UAAU,EAAEC,IAAI,EAAEC,aAAa,EAAEC,WAAW,QAAQ,cAAc;AAC3E,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,gCAAgC;AAChE,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,OAAOC,aAAa,MAAM,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAoBjE,MAAMC,kBAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM;IAAEC;EAAK,CAAC,GAAGT,YAAY,CAAC,CAAC;EAC/B,MAAMU,WAAW,GAAGT,cAAc,CAACQ,IAAI,CAAC;;EAExC;EACA,MAAM,CAACE,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAa,EAAE,CAAC;EAC5D,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAgB,IAAI,CAAC;;EAE3D;EACA,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAA4E,cAAc,CAAC;EACrI,MAAM,CAAC6B,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAkB,IAAI,CAAC;EAC7E,MAAM,CAAC+B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhC,QAAQ,CAAqB,IAAI,CAAC;EACtF,MAAM,CAACiC,cAAc,EAAEC,iBAAiB,CAAC,GAAGlC,QAAQ,CAAkB,IAAI,CAAC;EAC3E,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACe,WAAW,CAACqB,mBAAmB,EAAE;MACpCf,QAAQ,CAAC,iDAAiD,CAAC;MAC3DF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEAkB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACtB,WAAW,CAACqB,mBAAmB,CAAC,CAAC;EAErC,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MAAA,IAAAC,cAAA;MACFnB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEdkB,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;MACrE,MAAMC,QAAQ,GAAG,MAAMlC,eAAe,CAACmC,8BAA8B,CAAC,CAAC;MACvEH,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEC,QAAQ,CAAC;MAE9D,IAAIA,QAAQ,CAACnB,OAAO,KAAAgB,cAAA,GAAIG,QAAQ,CAACE,IAAI,cAAAL,cAAA,eAAbA,cAAA,CAAetB,UAAU,EAAE;QACjDC,aAAa,CAACwB,QAAQ,CAACE,IAAI,CAAC3B,UAAU,CAAC;QACvCuB,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAEC,QAAQ,CAACE,IAAI,CAAC3B,UAAU,CAAC4B,MAAM,CAAC;MACxG,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAACJ,QAAQ,CAACK,OAAO,IAAI,2BAA2B,CAAC;MAClE;IAEF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBR,OAAO,CAACnB,KAAK,CAAC,kDAAkD,EAAE2B,GAAG,CAAC;MACtE1B,QAAQ,CAAC0B,GAAG,CAACD,OAAO,IAAI,2BAA2B,CAAC;MACpD;MACA7B,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM6B,iBAAiB,GAAGA,CAAA,KAAM;IAC9BrB,YAAY,CAAC,cAAc,CAAC;IAC5BE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,qBAAqB,CAAC,IAAI,CAAC;IAC3BE,iBAAiB,CAAC,IAAI,CAAC;IACvBR,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMwB,kBAAkB,GAAIC,QAAkB,IAAK;IACjDvB,YAAY,CAAC,eAAe,CAAC;IAC7BE,kBAAkB,CAACqB,QAAQ,CAAC;IAC5BnB,qBAAqB,CAAC,IAAI,CAAC;IAC3BE,iBAAiB,CAAC,IAAI,CAAC;IACvBR,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAM0B,oBAAoB,GAAG,MAAOD,QAAkB,IAAK;IACzD,IAAI;MACF7B,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA;;MAEAA,UAAU,CAAC,aAAa2B,QAAQ,CAACE,IAAI,iCAAiC,CAAC;MACvEf,cAAc,CAAC,CAAC;IAElB,CAAC,CAAC,OAAOU,GAAQ,EAAE;MACjB1B,QAAQ,CAAC0B,GAAG,CAACD,OAAO,IAAI,2BAA2B,CAAC;IACtD;EACF,CAAC;EAED,MAAMO,0BAA0B,GAAG,MAAOH,QAAkB,IAAK;IAC/D,IAAI;MACF7B,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA;;MAEA,MAAM+B,MAAM,GAAGJ,QAAQ,CAACK,SAAS,GAAG,aAAa,GAAG,WAAW;MAC/DhC,UAAU,CAAC,aAAa2B,QAAQ,CAACE,IAAI,cAAcE,MAAM,eAAe,CAAC;MACzEjB,cAAc,CAAC,CAAC;IAElB,CAAC,CAAC,OAAOU,GAAQ,EAAE;MACjB1B,QAAQ,CAAC0B,GAAG,CAACD,OAAO,IAAI,kCAAkC,CAAC;IAC7D;EACF,CAAC;EAED,MAAMU,oBAAoB,GAAIN,QAAkB,IAAK;IACnDvB,YAAY,CAAC,iBAAiB,CAAC;IAC/BE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,qBAAqB,CAAC,IAAI,CAAC;IAC3BE,iBAAiB,CAACiB,QAAQ,CAAC;IAC3BzB,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMgC,qBAAqB,GAAGA,CAACP,QAAkB,EAAEQ,WAAwB,KAAK;IAC9E/B,YAAY,CAAC,kBAAkB,CAAC;IAChCE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,qBAAqB,CAAC2B,WAAW,CAAC;IAClCzB,iBAAiB,CAACiB,QAAQ,CAAC;IAC3BzB,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMkC,uBAAuB,GAAG,MAAAA,CAAOT,QAAkB,EAAEQ,WAAwB,KAAK;IACtF,IAAI;MACFrC,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA;;MAEAA,UAAU,CAAC,gBAAgBmC,WAAW,CAACN,IAAI,iCAAiC,CAAC;MAC7Ef,cAAc,CAAC,CAAC;IAElB,CAAC,CAAC,OAAOU,GAAQ,EAAE;MACjB1B,QAAQ,CAAC0B,GAAG,CAACD,OAAO,IAAI,8BAA8B,CAAC;IACzD;EACF,CAAC;EAED,MAAMc,6BAA6B,GAAG,MAAAA,CAAOV,QAAkB,EAAEQ,WAAwB,KAAK;IAC5F,IAAI;MACFrC,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA;;MAEA,MAAM+B,MAAM,GAAGI,WAAW,CAACH,SAAS,GAAG,aAAa,GAAG,WAAW;MAClEhC,UAAU,CAAC,gBAAgBmC,WAAW,CAACN,IAAI,cAAcE,MAAM,eAAe,CAAC;MAC/EjB,cAAc,CAAC,CAAC;IAElB,CAAC,CAAC,OAAOU,GAAQ,EAAE;MACjB1B,QAAQ,CAAC0B,GAAG,CAACD,OAAO,IAAI,qCAAqC,CAAC;IAChE;EACF,CAAC;EAED,MAAMe,UAAU,GAAG,MAAAA,CAAOlB,IAA4B,EAAEmB,SAAoB,KAAK;IAC/E,IAAI;MACF3B,eAAe,CAAC,IAAI,CAAC;MACrBd,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIG,SAAS,KAAK,cAAc,EAAE;QAChC;QACA;QACAH,UAAU,CAAC,aAAaoB,IAAI,CAACS,IAAI,iCAAiC,CAAC;MACrE,CAAC,MAAM,IAAI1B,SAAS,KAAK,eAAe,EAAE;QACxC;QACA;QACAH,UAAU,CAAC,aAAaoB,IAAI,CAACS,IAAI,iCAAiC,CAAC;MACrE,CAAC,MAAM,IAAI1B,SAAS,KAAK,iBAAiB,EAAE;QAC1C;QACA;QACAH,UAAU,CAAC,gBAAgBoB,IAAI,CAACS,IAAI,iCAAiC,CAAC;MACxE,CAAC,MAAM,IAAI1B,SAAS,KAAK,kBAAkB,EAAE;QAC3C;QACA;QACAH,UAAU,CAAC,gBAAgBoB,IAAI,CAACS,IAAI,iCAAiC,CAAC;MACxE;MAEAf,cAAc,CAAC,CAAC;IAElB,CAAC,CAAC,OAAOU,GAAQ,EAAE;MACjB1B,QAAQ,CAAC0B,GAAG,CAACD,OAAO,IAAI,wBAAwB,CAAC;MACjD,MAAMC,GAAG,CAAC,CAAC;IACb,CAAC,SAAS;MACRZ,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAM4B,aAAa,GAAGA,CAAA,KAAM;IAC1B1C,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;EAClB,CAAC;EAED,IAAI,CAACR,WAAW,CAACqB,mBAAmB,EAAE;IACpC,oBACEzB,OAAA;MAAKqD,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE,OAAO;QAClBC,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACA7D,OAAA,CAACV,UAAU;QAACwE,IAAI,EAAE,EAAG;QAACT,KAAK,EAAE;UAAEU,YAAY,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAI;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvEpE,OAAA;QAAIqD,KAAK,EAAE;UAAEgB,MAAM,EAAE,YAAY;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAM,CAAE;QAAAV,QAAA,EAAC;MAE5E;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLpE,OAAA;QAAGqD,KAAK,EAAE;UAAEgB,MAAM,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAT,QAAA,EAAC;MAE3C;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJpE,OAAA;QAAKqD,KAAK,EAAE;UACVmB,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAEtE,WAAW,CAACuE,qBAAqB,CAAC,CAAC;UAC/CC,YAAY,EAAE,KAAK;UACnBhB,KAAK,EAAE,OAAO;UACdU,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE;QACd,CAAE;QAAAV,QAAA,GAAC,gBACa,EAACzD,WAAW,CAACyE,sBAAsB,CAAC,CAAC;MAAA;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI7D,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKqD,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE;MACb,CAAE;MAAAG,QAAA,eACA7D,OAAA;QAAKqD,KAAK,EAAE;UACVyB,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,mBAAmB;UAC3BC,SAAS,EAAE,mBAAmB;UAC9BL,YAAY,EAAE,KAAK;UACnBM,SAAS,EAAE;QACb;MAAE;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEV;EAEA,IAAI3D,KAAK,EAAE;IACT,oBACET,OAAA;MAAKqD,KAAK,EAAE;QACVoB,OAAO,EAAE,MAAM;QACfd,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACA7D,OAAA;QAAA6D,QAAA,EAAI;MAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdpE,OAAA;QAAA6D,QAAA,EAAIpD;MAAK;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdpE,OAAA;QACEmF,OAAO,EAAEzD,cAAe;QACxB2B,KAAK,EAAE;UACLoB,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAE,SAAS;UACrBd,KAAK,EAAE,OAAO;UACdoB,MAAM,EAAE,MAAM;UACdJ,YAAY,EAAE,KAAK;UACnBQ,MAAM,EAAE;QACV,CAAE;QAAAvB,QAAA,EACH;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACEpE,OAAA;IAAKqD,KAAK,EAAE;MAAEgC,QAAQ,EAAE,QAAQ;MAAEhB,MAAM,EAAE;IAAS,CAAE;IAAAR,QAAA,gBAEnD7D,OAAA;MAAKqD,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfG,cAAc,EAAE,eAAe;QAC/BD,UAAU,EAAE,QAAQ;QACpBO,YAAY,EAAE;MAChB,CAAE;MAAAF,QAAA,gBACA7D,OAAA;QAAA6D,QAAA,gBACE7D,OAAA;UAAIqD,KAAK,EAAE;YACTgB,MAAM,EAAE,YAAY;YACpBC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjBX,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,EAAC;QAEH;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLpE,OAAA;UAAGqD,KAAK,EAAE;YACRgB,MAAM,EAAE,CAAC;YACTT,KAAK,EAAE,SAAS;YAChBU,QAAQ,EAAE;UACZ,CAAE;UAAAT,QAAA,EAAC;QAEH;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENpE,OAAA;QACEmF,OAAO,EAAE9C,iBAAkB;QAC3BgB,KAAK,EAAE;UACLC,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpB8B,GAAG,EAAE,QAAQ;UACbb,OAAO,EAAE,gBAAgB;UACzBC,UAAU,EAAE,SAAS;UACrBd,KAAK,EAAE,OAAO;UACdoB,MAAM,EAAE,MAAM;UACdJ,YAAY,EAAE,KAAK;UACnBN,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE,KAAK;UACjBa,MAAM,EAAE,SAAS;UACjBG,UAAU,EAAE;QACd,CAAE;QACFC,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACrC,KAAK,CAACqB,UAAU,GAAG,SAAU;QAClEiB,YAAY,EAAGF,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACrC,KAAK,CAACqB,UAAU,GAAG,SAAU;QAAAb,QAAA,gBAElE7D,OAAA,CAACT,IAAI;UAACuE,IAAI,EAAE;QAAG;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEpB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGL,CAAC3D,KAAK,IAAIE,OAAO,kBAChBX,OAAA;MAAKqD,KAAK,EAAE;QAAEU,YAAY,EAAE;MAAS,CAAE;MAAAF,QAAA,GACpCpD,KAAK,iBACJT,OAAA;QAAKqD,KAAK,EAAE;UACVoB,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,SAAS;UACrBM,MAAM,EAAE,mBAAmB;UAC3BJ,YAAY,EAAE,KAAK;UACnBhB,KAAK,EAAE,SAAS;UAChBN,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE;QAClB,CAAE;QAAAI,QAAA,gBACA7D,OAAA;UAAKqD,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAE8B,GAAG,EAAE;UAAS,CAAE;UAAAzB,QAAA,gBACnE7D,OAAA,CAACR,aAAa;YAACsE,IAAI,EAAE;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAC1B3D,KAAK;QAAA;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpE,OAAA;UACEmF,OAAO,EAAE/B,aAAc;UACvBC,KAAK,EAAE;YACLqB,UAAU,EAAE,aAAa;YACzBM,MAAM,EAAE,MAAM;YACdpB,KAAK,EAAE,SAAS;YAChBwB,MAAM,EAAE,SAAS;YACjBX,OAAO,EAAE;UACX,CAAE;UAAAZ,QAAA,EACH;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAEAzD,OAAO,iBACNX,OAAA;QAAKqD,KAAK,EAAE;UACVoB,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,SAAS;UACrBM,MAAM,EAAE,mBAAmB;UAC3BJ,YAAY,EAAE,KAAK;UACnBhB,KAAK,EAAE,SAAS;UAChBN,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE;QAClB,CAAE;QAAAI,QAAA,gBACA7D,OAAA;UAAKqD,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAE8B,GAAG,EAAE;UAAS,CAAE;UAAAzB,QAAA,gBACnE7D,OAAA,CAACP,WAAW;YAACqE,IAAI,EAAE;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACxBzD,OAAO;QAAA;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNpE,OAAA;UACEmF,OAAO,EAAE/B,aAAc;UACvBC,KAAK,EAAE;YACLqB,UAAU,EAAE,aAAa;YACzBM,MAAM,EAAE,MAAM;YACdpB,KAAK,EAAE,SAAS;YAChBwB,MAAM,EAAE,SAAS;YACjBX,OAAO,EAAE;UACX,CAAE;UAAAZ,QAAA,EACH;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAGDpE,OAAA,CAACH,YAAY;MACXQ,UAAU,EAAEA,UAAW;MACvBE,OAAO,EAAEA,OAAQ;MACjBqF,cAAc,EAAEtD,kBAAmB;MACnCuD,gBAAgB,EAAErD,oBAAqB;MACvCsD,sBAAsB,EAAEpD,0BAA2B;MACnDqD,gBAAgB,EAAElD,oBAAqB;MACvCmD,iBAAiB,EAAElD,qBAAsB;MACzCmD,mBAAmB,EAAEjD,uBAAwB;MAC7CkD,yBAAyB,EAAEjD;IAA8B;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC,eAGFpE,OAAA,CAACF,aAAa;MACZqG,MAAM,EAAEtF,SAAU;MAClBuF,OAAO,EAAEA,CAAA,KAAMtF,YAAY,CAAC,KAAK,CAAE;MACnCuF,MAAM,EAAEnD,UAAW;MACnBX,QAAQ,EAAEtB,eAAgB;MAC1B8B,WAAW,EAAE5B,kBAAmB;MAChCE,cAAc,EAAEA,cAAe;MAC/BiF,IAAI,EAAEvF,SAAU;MAChBR,OAAO,EAAEgB;IAAa;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAClE,EAAA,CA9ZID,kBAA4B;EAAA,QACfP,YAAY,EACTC,cAAc;AAAA;AAAA4G,EAAA,GAF9BtG,kBAA4B;AAgalC,eAAeA,kBAAkB;AAAC,IAAAsG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}