{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\admin\\\\StudentManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { studentService } from '../../services/studentService';\nimport { AlertTriangle, RefreshCw, Edit, Key, Trash2, Info, User, Users } from 'lucide-react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\nimport { getImageUrl } from '../../config/constants';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst StudentManagement = () => {\n  _s();\n  // Auth context\n  const {\n    user\n  } = useAdminAuth();\n  const permissions = usePermissions(user);\n\n  // State management\n  const [students, setStudents] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterStatus, setFilterStatus] = useState('active');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [totalStudents, setTotalStudents] = useState(0);\n\n  // Get available grade levels based on admin's position and assigned grade\n  const getAvailableGradeLevels = () => {\n    if (permissions.isSuperAdmin) {\n      // Super admin can manage all grades\n      return [11, 12];\n    } else if (user !== null && user !== void 0 && user.grade_level) {\n      // Grade-specific professor can only manage their assigned grade\n      return [user.grade_level];\n    } else {\n      // Default to grades 11 and 12\n      return [11, 12];\n    }\n  };\n\n  // Modal states\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\n  const [selectedStudent, setSelectedStudent] = useState(null);\n\n  // Form states for creating/editing student\n  const [formData, setFormData] = useState({\n    studentNumber: '',\n    email: '',\n    firstName: '',\n    middleName: '',\n    lastName: '',\n    suffix: '',\n    phoneNumber: '',\n    gradeLevel: permissions.isProfessor && user !== null && user !== void 0 && user.grade_level ? user.grade_level : 11,\n    section: '',\n    parentGuardianName: '',\n    parentGuardianPhone: '',\n    address: ''\n  });\n  const [profilePictureFile, setProfilePictureFile] = useState(null);\n  const [profilePicturePreview, setProfilePicturePreview] = useState(null);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [error, setError] = useState(null);\n\n  // State for debounced search term\n  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');\n\n  // Load students data - SIMPLIFIED WITHOUT AUTH GUARDS\n  const loadStudents = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Build filter parameters\n      const params = {\n        page: currentPage,\n        limit: 10,\n        search: debouncedSearchTerm || undefined\n      };\n\n      // Add status filter if not 'all'\n      if (filterStatus === 'active') {\n        params.is_active = true;\n      } else if (filterStatus === 'inactive') {\n        params.is_active = false;\n      }\n      // If filterStatus === 'all', don't add is_active parameter\n\n      // Add grade level filter based on position and assigned grade\n      if (permissions.isSuperAdmin) {\n        // Super admin can see ALL students regardless of grade level\n        // Don't add grade_level filter parameter\n      } else if (user !== null && user !== void 0 && user.grade_level) {\n        // Professor with assigned grade level can only see their grade\n        params.grade_level = user.grade_level;\n      }\n      // If no grade_level specified, show all students (fallback)\n\n      console.log('Loading students with params:', params);\n      console.log('Filter status:', filterStatus, 'is_active param:', params.is_active);\n      const response = await studentService.getStudents(params);\n      console.log('API Response received:', response);\n      console.log('Students loaded:', response.students.map(s => {\n        var _s$profile;\n        return {\n          name: ((_s$profile = s.profile) === null || _s$profile === void 0 ? void 0 : _s$profile.full_name) || 'No name',\n          email: s.email,\n          is_active: s.is_active,\n          status: s.is_active ? 'Active' : 'Inactive'\n        };\n      }));\n\n      // Additional debug: Check what we're about to set in state\n      console.log('About to set students state with:', response.students.length, 'students');\n      setStudents(response.students);\n      setTotalPages(response.pagination.totalPages);\n      setTotalStudents(response.pagination.total);\n    } catch (error) {\n      console.error('Error loading students:', error);\n      setError('Failed to load students. Please check if the backend is running.');\n      setStudents([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [currentPage, debouncedSearchTerm, filterStatus, user]);\n\n  // Load students when dependencies change\n  useEffect(() => {\n    // Check if user has permission to view students\n    if (!permissions.canViewStudents) {\n      setError('You do not have permission to view student information');\n      setLoading(false);\n      return;\n    }\n    loadStudents();\n  }, [loadStudents, permissions.canViewStudents]);\n\n  // Debounced search - update debounced term after delay\n  useEffect(() => {\n    const timeoutId = setTimeout(() => {\n      setDebouncedSearchTerm(searchTerm);\n      setCurrentPage(1); // Reset to first page when searching\n    }, 500);\n    return () => clearTimeout(timeoutId);\n  }, [searchTerm]);\n\n  // Utility functions\n  const getStatusColor = isActive => {\n    return isActive ? '#22c55e' : '#f59e0b';\n  };\n  const getStatusText = isActive => {\n    return isActive ? 'Active' : 'Inactive';\n  };\n\n  // Search handler\n  const handleSearchChange = e => {\n    setSearchTerm(e.target.value);\n  };\n\n  // Email generation function\n  const generateEmail = (studentNumber, gradeLevel, section, lastName, firstName, middleName) => {\n    if (!studentNumber || !gradeLevel || !section || !lastName || !firstName) {\n      return '';\n    }\n    const firstLetter = firstName.charAt(0).toUpperCase();\n    const middleInitial = middleName ? middleName.charAt(0).toUpperCase() : '';\n    const cleanLastName = lastName.replace(/\\s+/g, '').toLowerCase();\n    const cleanSection = section.replace(/\\s+/g, '').toUpperCase();\n    return `${studentNumber}_${gradeLevel}_${cleanSection}_${cleanLastName}_${firstLetter}_${middleInitial}@gmail.com`;\n  };\n\n  // Form handlers\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    const newFormData = {\n      ...formData,\n      [name]: name === 'gradeLevel' ? parseInt(value) : value\n    };\n\n    // Auto-generate email when required fields are filled\n    if (['studentNumber', 'gradeLevel', 'section', 'lastName', 'firstName', 'middleName'].includes(name)) {\n      const generatedEmail = generateEmail(newFormData.studentNumber, newFormData.gradeLevel.toString(), newFormData.section, newFormData.lastName, newFormData.firstName, newFormData.middleName);\n      if (generatedEmail) {\n        newFormData.email = generatedEmail;\n      }\n    }\n    setFormData(newFormData);\n  };\n  const resetForm = () => {\n    setFormData({\n      studentNumber: '',\n      email: '',\n      firstName: '',\n      middleName: '',\n      lastName: '',\n      suffix: '',\n      phoneNumber: '',\n      gradeLevel: permissions.isProfessor && user !== null && user !== void 0 && user.grade_level ? user.grade_level : 11,\n      section: '',\n      parentGuardianName: '',\n      parentGuardianPhone: '',\n      address: ''\n    });\n    setProfilePictureFile(null);\n    setProfilePicturePreview(null);\n  };\n\n  // Profile picture handling functions\n  const handleProfilePictureChange = e => {\n    var _e$target$files;\n    const file = (_e$target$files = e.target.files) === null || _e$target$files === void 0 ? void 0 : _e$target$files[0];\n    if (file) {\n      // Validate file type\n      if (!file.type.startsWith('image/')) {\n        alert('Please select an image file');\n        return;\n      }\n\n      // Validate file size (2MB limit)\n      if (file.size > 2 * 1024 * 1024) {\n        alert('File size must be less than 2MB');\n        return;\n      }\n      setProfilePictureFile(file);\n\n      // Create preview\n      const reader = new FileReader();\n      reader.onload = e => {\n        var _e$target;\n        setProfilePicturePreview((_e$target = e.target) === null || _e$target === void 0 ? void 0 : _e$target.result);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  // Remove profile picture for create modal (local only)\n  const removeProfilePictureLocal = () => {\n    setProfilePictureFile(null);\n    setProfilePicturePreview(null);\n  };\n\n  // Remove profile picture for edit modal (calls API for existing students)\n  const removeProfilePicture = async () => {\n    try {\n      // If we're editing an existing student and they have a profile picture, remove it from the server\n      if (selectedStudent && selectedStudent.profile.profile_picture) {\n        // Confirm before removing\n        const confirmed = window.confirm(`Are you sure you want to remove ${selectedStudent.profile.full_name}'s profile picture? This action cannot be undone.`);\n        if (!confirmed) {\n          return;\n        }\n        setLoading(true);\n        const updatedStudent = await studentService.removeStudentProfilePicture(selectedStudent.student_id.toString());\n\n        // Update the selected student data immediately\n        setSelectedStudent(updatedStudent);\n\n        // Refresh the students list to show the updated data\n        await loadStudents();\n        alert('Profile picture removed successfully!');\n      }\n\n      // Clear local state regardless\n      setProfilePictureFile(null);\n      setProfilePicturePreview(null);\n    } catch (error) {\n      console.error('Error removing profile picture:', error);\n      alert(`Failed to remove profile picture: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // CRUD Operations\n  const handleCreateStudent = async () => {\n    setIsSubmitting(true);\n    setError(null);\n    try {\n      // Validate required fields\n      if (!formData.studentNumber || !formData.email || !formData.firstName || !formData.lastName || !formData.phoneNumber || !formData.section || !formData.gradeLevel) {\n        throw new Error('Please fill in all required fields (First Name, Last Name, Student Number, Email, Phone, Grade Level, Section)');\n      }\n\n      // Validate email format\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      if (!emailRegex.test(formData.email)) {\n        throw new Error('Please enter a valid email address');\n      }\n\n      // Prepare student data for API\n      const studentData = {\n        // Account data\n        student_number: formData.studentNumber,\n        email: formData.email,\n        password: 'Student123',\n        // Default password\n        is_active: true,\n        created_by: (user === null || user === void 0 ? void 0 : user.id) || 1,\n        // Current admin ID\n\n        // Profile data\n        first_name: formData.firstName,\n        middle_name: formData.middleName || undefined,\n        last_name: formData.lastName,\n        suffix: formData.suffix || undefined,\n        phone_number: formData.phoneNumber,\n        grade_level: formData.gradeLevel,\n        section: formData.section,\n        parent_guardian_name: formData.parentGuardianName || undefined,\n        parent_guardian_phone: formData.parentGuardianPhone || undefined,\n        address: formData.address || undefined\n      };\n\n      // Debug: Log the data being sent\n      console.log('Sending student data:', studentData);\n\n      // Call API to create student\n      const createdStudent = await studentService.createStudent(studentData);\n\n      // Upload profile picture if provided\n      if (profilePictureFile) {\n        try {\n          await studentService.uploadStudentProfilePicture(createdStudent.student_id.toString(), profilePictureFile);\n        } catch (profileError) {\n          console.error('Error uploading profile picture:', profileError);\n          // Don't fail the entire creation process for profile picture upload failure\n          alert(`Student account created successfully, but profile picture upload failed: ${profileError.message}`);\n        }\n      }\n      alert(`Student account created successfully!\\n\\nStudent Details:\\nName: ${createdStudent.profile.full_name}\\nStudent Number: ${createdStudent.student_number}\\nEmail: ${createdStudent.email}\\n\\nLogin Credentials:\\nEmail: ${createdStudent.email}\\nPassword: Student123\\n\\nPlease share these credentials with the student and ask them to change the password on first login.`);\n      resetForm();\n      setShowCreateModal(false);\n\n      // Refresh the students list\n      await loadStudents();\n    } catch (error) {\n      console.error('Error creating student:', error);\n      setError(error.message || 'Failed to create student account. Please try again.');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const handleEditStudent = student => {\n    setSelectedStudent(student);\n    setFormData({\n      studentNumber: student.student_number,\n      email: student.email,\n      firstName: student.profile.first_name,\n      middleName: student.profile.middle_name || '',\n      lastName: student.profile.last_name,\n      suffix: student.profile.suffix || '',\n      phoneNumber: student.profile.phone_number,\n      gradeLevel: student.profile.grade_level,\n      section: student.profile.section,\n      parentGuardianName: student.profile.parent_guardian_name || '',\n      parentGuardianPhone: student.profile.parent_guardian_phone || '',\n      address: student.profile.address || ''\n    });\n\n    // Set existing profile picture preview\n    setProfilePictureFile(null);\n    setProfilePicturePreview(student.profile.profile_picture ? getImageUrl(student.profile.profile_picture) : null);\n    setShowEditModal(true);\n  };\n  const handleUpdateStudent = async () => {\n    if (!selectedStudent) return;\n    setIsSubmitting(true);\n    setError(null);\n    try {\n      // Validate required fields\n      if (!formData.studentNumber || !formData.email || !formData.firstName || !formData.lastName || !formData.phoneNumber || !formData.section) {\n        throw new Error('Please fill in all required fields (First Name, Last Name, Student Number, Email, Phone, Section)');\n      }\n\n      // Validate email format\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      if (!emailRegex.test(formData.email)) {\n        throw new Error('Please enter a valid email address');\n      }\n\n      // Prepare update data\n      const updateData = {\n        student_number: formData.studentNumber,\n        email: formData.email,\n        first_name: formData.firstName,\n        middle_name: formData.middleName || undefined,\n        last_name: formData.lastName,\n        suffix: formData.suffix || undefined,\n        phone_number: formData.phoneNumber,\n        grade_level: formData.gradeLevel,\n        section: formData.section,\n        parent_guardian_name: formData.parentGuardianName || undefined,\n        parent_guardian_phone: formData.parentGuardianPhone || undefined,\n        address: formData.address || undefined\n      };\n\n      // Call API to update student\n      await studentService.updateStudent(selectedStudent.student_id.toString(), updateData);\n\n      // Handle profile picture upload if a new file was selected\n      if (profilePictureFile) {\n        try {\n          await studentService.uploadStudentProfilePicture(selectedStudent.student_id.toString(), profilePictureFile);\n        } catch (profileError) {\n          console.error('Error uploading profile picture:', profileError);\n          alert(`Student information updated successfully, but profile picture upload failed: ${profileError.message}`);\n        }\n      }\n      alert('Student information updated successfully!');\n      resetForm();\n      setShowEditModal(false);\n      setSelectedStudent(null);\n\n      // Refresh the students list\n      await loadStudents();\n    } catch (error) {\n      console.error('Error updating student:', error);\n      setError(error.message || 'Failed to update student information. Please try again.');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const handleDeleteStudent = student => {\n    setSelectedStudent(student);\n    setShowDeleteModal(true);\n  };\n  const handleResetPassword = async student => {\n    if (!window.confirm(`Are you sure you want to reset the password for ${student.profile.full_name}?\\n\\nThe password will be reset to: Student123`)) {\n      return;\n    }\n    try {\n      setIsSubmitting(true);\n      await studentService.resetStudentPassword(student.student_id.toString());\n\n      // Show success message\n      alert(`Password reset successfully for ${student.profile.full_name}!\\n\\nNew password: Student123`);\n    } catch (error) {\n      console.error('Error resetting password:', error);\n      alert(`Failed to reset password: ${error.message}`);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const confirmDeleteStudent = async () => {\n    if (!selectedStudent) return;\n    setIsSubmitting(true);\n    setError(null);\n    try {\n      // Call API to soft delete student (deactivate)\n      await studentService.deleteStudent(selectedStudent.student_id.toString());\n      alert('Student account has been deactivated successfully!');\n      setShowDeleteModal(false);\n      setSelectedStudent(null);\n\n      // Refresh the students list\n      await loadStudents();\n    } catch (error) {\n      console.error('Error deleting student:', error);\n      setError(error.message || 'Failed to deactivate student account. Please try again.');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const handlePageChange = page => {\n    setCurrentPage(page);\n  };\n  const handleStatusFilterChange = value => {\n    setFilterStatus(value);\n    setCurrentPage(1);\n  };\n\n  // Check permissions first\n  if (!permissions.canViewStudents) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Users, {\n        size: 64,\n        style: {\n          marginBottom: '1rem',\n          opacity: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 520,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          margin: '0 0 0.5rem',\n          fontSize: '1.5rem',\n          fontWeight: '600'\n        },\n        children: \"Access Denied\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 521,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: 0,\n          fontSize: '1rem'\n        },\n        children: \"You do not have permission to view student information.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 524,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        },\n        children: [\"Current Role: \", permissions.getPositionDisplayName()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 527,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 511,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '1.5rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#2d5016',\n            margin: '0 0 0.5rem 0'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(User, {\n              size: 20,\n              color: \"#1e40af\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 15\n            }, this), \"Student Management\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#6b7280',\n            margin: 0,\n            fontSize: '1.1rem'\n          },\n          children: [permissions.isSuperAdmin ? 'Create, edit, and manage student accounts across all grades' : permissions.isProfessor ? 'Create, edit, and manage student accounts in your assigned grade' : 'View student information (read-only)', permissions.isSuperAdmin && /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: 'inline-block',\n              marginLeft: '0.5rem',\n              padding: '0.25rem 0.5rem',\n              backgroundColor: '#fef3c7',\n              color: '#92400e',\n              borderRadius: '0.375rem',\n              fontSize: '0.875rem',\n              fontWeight: '500'\n            },\n            children: \"All Grades\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 15\n          }, this), permissions.isProfessor && (user === null || user === void 0 ? void 0 : user.grade_level) && /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: 'inline-block',\n              marginLeft: '0.5rem',\n              padding: '0.25rem 0.5rem',\n              backgroundColor: '#dbeafe',\n              color: '#1e40af',\n              borderRadius: '0.375rem',\n              fontSize: '0.875rem',\n              fontWeight: '500'\n            },\n            children: [\"Grade \", user.grade_level, \" Only\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 589,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 563,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 551,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 545,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '16px',\n        padding: '1.5rem',\n        marginBottom: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '1rem',\n          alignItems: 'center',\n          flexWrap: 'wrap'\n        },\n        children: [permissions.canManageStudents && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowCreateModal(true),\n          disabled: loading,\n          style: {\n            background: 'linear-gradient(135deg, #2d5016 0%, #4ade80 100%)',\n            color: 'white',\n            border: 'none',\n            borderRadius: '8px',\n            padding: '0.5rem 1rem',\n            fontSize: '0.9rem',\n            fontWeight: '600',\n            cursor: loading ? 'not-allowed' : 'pointer',\n            opacity: loading ? 0.6 : 1,\n            transition: 'all 0.2s ease',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.4rem',\n            whiteSpace: 'nowrap'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '1rem'\n            },\n            children: \"+\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 638,\n            columnNumber: 15\n          }, this), \"Create Student Account\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 618,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            minWidth: '300px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search students\",\n            value: searchTerm,\n            onChange: handleSearchChange,\n            style: {\n              width: '100%',\n              padding: '0.75rem 1rem',\n              border: '1px solid #e8f5e8',\n              borderRadius: '8px',\n              fontSize: '1rem',\n              outline: 'none'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 645,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 644,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '0.5rem',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '0.9rem',\n              color: '#666',\n              marginRight: '0.5rem'\n            },\n            children: \"Filter:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 663,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleStatusFilterChange('all'),\n            style: {\n              padding: '0.5rem 1rem',\n              border: filterStatus === 'all' ? '2px solid #4CAF50' : '1px solid #ddd',\n              borderRadius: '6px',\n              fontSize: '0.85rem',\n              backgroundColor: filterStatus === 'all' ? '#4CAF50' : '#fff',\n              color: filterStatus === 'all' ? '#fff' : '#333',\n              cursor: 'pointer',\n              fontWeight: filterStatus === 'all' ? 'bold' : 'normal',\n              transition: 'all 0.2s ease'\n            },\n            children: \"All\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 664,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleStatusFilterChange('active'),\n            style: {\n              padding: '0.5rem 1rem',\n              border: filterStatus === 'active' ? '2px solid #4CAF50' : '1px solid #ddd',\n              borderRadius: '6px',\n              fontSize: '0.85rem',\n              backgroundColor: filterStatus === 'active' ? '#4CAF50' : '#fff',\n              color: filterStatus === 'active' ? '#fff' : '#333',\n              cursor: 'pointer',\n              fontWeight: filterStatus === 'active' ? 'bold' : 'normal',\n              transition: 'all 0.2s ease'\n            },\n            children: \"Active\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 680,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleStatusFilterChange('inactive'),\n            style: {\n              padding: '0.5rem 1rem',\n              border: filterStatus === 'inactive' ? '2px solid #f44336' : '1px solid #ddd',\n              borderRadius: '6px',\n              fontSize: '0.85rem',\n              backgroundColor: filterStatus === 'inactive' ? '#f44336' : '#fff',\n              color: filterStatus === 'inactive' ? '#fff' : '#333',\n              cursor: 'pointer',\n              fontWeight: filterStatus === 'inactive' ? 'bold' : 'normal',\n              transition: 'all 0.2s ease'\n            },\n            children: \"Inactive\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 696,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 662,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: loadStudents,\n          disabled: loading,\n          style: {\n            background: '#f8fdf8',\n            border: '1px solid #e8f5e8',\n            borderRadius: '8px',\n            padding: '0.75rem 1rem',\n            fontSize: '1rem',\n            cursor: loading ? 'not-allowed' : 'pointer',\n            opacity: loading ? 0.6 : 1,\n            transition: 'all 0.2s ease'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 730,\n              columnNumber: 15\n            }, this), \"Refresh\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 729,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 715,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 615,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 607,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '16px',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8',\n        overflow: 'hidden'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: '#f8fdf8',\n          padding: '1rem 1.5rem',\n          borderBottom: '1px solid #e8f5e8',\n          display: 'grid',\n          gridTemplateColumns: '60px 1fr 2fr 2fr 1fr 1fr 1fr 150px',\n          gap: '1rem',\n          fontWeight: '600',\n          color: '#2d5016',\n          fontSize: '0.875rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Photo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 757,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Student #\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 758,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 759,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 760,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Grade & Section\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 761,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Phone\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 762,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 763,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 764,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 746,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '2rem',\n          textAlign: 'center',\n          color: '#6b7280'\n        },\n        children: \"Loading students...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 769,\n        columnNumber: 11\n      }, this) : students.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '2rem',\n          textAlign: 'center',\n          color: '#6b7280'\n        },\n        children: [\"No students found. \", searchTerm && 'Try adjusting your search criteria.']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 773,\n        columnNumber: 11\n      }, this) : students.map(student => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '1rem 1.5rem',\n          borderBottom: '1px solid #f3f4f6',\n          display: 'grid',\n          gridTemplateColumns: '60px 1fr 2fr 2fr 1fr 1fr 1fr 150px',\n          gap: '1rem',\n          alignItems: 'center',\n          fontSize: '0.875rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'center'\n          },\n          children: student.profile.profile_picture ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: getImageUrl(student.profile.profile_picture) || '',\n            alt: `${student.profile.full_name} profile`,\n            style: {\n              width: '40px',\n              height: '40px',\n              borderRadius: '50%',\n              objectFit: 'cover',\n              border: '2px solid #e8f5e8'\n            },\n            onError: e => {\n              // Fallback to initials if image fails to load\n              const target = e.target;\n              target.style.display = 'none';\n              const parent = target.parentElement;\n              if (parent) {\n                parent.innerHTML = `\n                          <div style=\"\n                            width: 40px;\n                            height: 40px;\n                            border-radius: 50%;\n                            background-color: #f3f4f6;\n                            display: flex;\n                            align-items: center;\n                            justify-content: center;\n                            color: #6b7280;\n                            font-size: 0.75rem;\n                            font-weight: 600;\n                          \">\n                            ${student.profile.first_name.charAt(0)}${student.profile.last_name.charAt(0)}\n                          </div>\n                        `;\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 792,\n            columnNumber: 19\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '40px',\n              height: '40px',\n              borderRadius: '50%',\n              backgroundColor: '#f3f4f6',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: '#6b7280',\n              fontSize: '0.75rem',\n              fontWeight: '600'\n            },\n            children: [student.profile.first_name.charAt(0), student.profile.last_name.charAt(0)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 828,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 790,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: '600',\n            color: '#2d5016'\n          },\n          children: student.student_number\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 844,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: '#374151'\n          },\n          children: student.profile.full_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 847,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: '#6b7280'\n          },\n          children: student.email\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 850,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: '#374151'\n          },\n          children: [\"Grade \", student.profile.grade_level, \" - \", student.profile.section]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 853,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: '#374151'\n          },\n          children: student.profile.phone_number\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 856,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              background: getStatusColor(student.is_active),\n              color: 'white',\n              padding: '0.25rem 0.75rem',\n              borderRadius: '12px',\n              fontSize: '0.75rem',\n              fontWeight: '600'\n            },\n            children: getStatusText(student.is_active)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 860,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 859,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '0.25rem'\n          },\n          children: permissions.canManageStudents ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleEditStudent(student),\n              title: \"Edit Student\",\n              style: {\n                background: '#3b82f6',\n                color: 'white',\n                border: 'none',\n                borderRadius: '6px',\n                padding: '0.5rem',\n                cursor: 'pointer',\n                fontSize: '0.75rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(Edit, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 887,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 874,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleResetPassword(student),\n              title: \"Reset Password to Default (Student123)\",\n              style: {\n                background: '#f59e0b',\n                color: 'white',\n                border: 'none',\n                borderRadius: '6px',\n                padding: '0.5rem',\n                cursor: 'pointer',\n                fontSize: '0.75rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(Key, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 902,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 889,\n              columnNumber: 21\n            }, this), permissions.isSuperAdmin && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleDeleteStudent(student),\n              title: \"Deactivate Student\",\n              style: {\n                background: '#ef4444',\n                color: 'white',\n                border: 'none',\n                borderRadius: '6px',\n                padding: '0.5rem',\n                cursor: 'pointer',\n                fontSize: '0.75rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(Trash2, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 918,\n                columnNumber: 25\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 905,\n              columnNumber: 23\n            }, this), permissions.isProfessor && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '0.5rem',\n                background: '#f3f4f6',\n                color: '#6b7280',\n                borderRadius: '6px',\n                fontSize: '0.65rem',\n                fontWeight: '500',\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: \"Edit Only\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 922,\n              columnNumber: 23\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '0.5rem 1rem',\n              background: '#f3f4f6',\n              color: '#6b7280',\n              borderRadius: '6px',\n              fontSize: '0.75rem',\n              fontWeight: '500'\n            },\n            children: \"View Only\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 937,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 871,\n          columnNumber: 15\n        }, this)]\n      }, student.student_id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 778,\n        columnNumber: 13\n      }, this)), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '1rem 1.5rem',\n          borderTop: '1px solid #f3f4f6',\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: '#6b7280',\n            fontSize: '0.875rem'\n          },\n          children: [\"Page \", currentPage, \" of \", totalPages]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 962,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handlePageChange(currentPage - 1),\n            disabled: currentPage === 1,\n            style: {\n              background: currentPage === 1 ? '#f3f4f6' : '#2d5016',\n              color: currentPage === 1 ? '#9ca3af' : 'white',\n              border: 'none',\n              borderRadius: '6px',\n              padding: '0.5rem 1rem',\n              cursor: currentPage === 1 ? 'not-allowed' : 'pointer',\n              fontSize: '0.875rem'\n            },\n            children: \"Previous\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 966,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handlePageChange(currentPage + 1),\n            disabled: currentPage === totalPages,\n            style: {\n              background: currentPage === totalPages ? '#f3f4f6' : '#2d5016',\n              color: currentPage === totalPages ? '#9ca3af' : 'white',\n              border: 'none',\n              borderRadius: '6px',\n              padding: '0.5rem 1rem',\n              cursor: currentPage === totalPages ? 'not-allowed' : 'pointer',\n              fontSize: '0.875rem'\n            },\n            children: \"Next\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 981,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 965,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 955,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 738,\n      columnNumber: 7\n    }, this), showCreateModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000,\n        padding: '1rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '16px',\n          padding: '2rem',\n          width: '95%',\n          maxWidth: '1200px',\n          maxHeight: '95vh',\n          overflow: 'hidden',\n          display: 'flex',\n          flexDirection: 'column'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              margin: 0,\n              color: '#2d5016',\n              fontSize: '1.5rem',\n              fontWeight: '700'\n            },\n            children: \"Create New Student Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1033,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setShowCreateModal(false);\n              resetForm();\n            },\n            style: {\n              background: 'none',\n              border: 'none',\n              fontSize: '1.5rem',\n              cursor: 'pointer',\n              color: '#6b7280'\n            },\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1041,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1027,\n          columnNumber: 13\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#fef2f2',\n            border: '1px solid #fecaca',\n            borderRadius: '8px',\n            padding: '1rem',\n            marginBottom: '1rem',\n            color: '#dc2626'\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1059,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            overflow: 'auto',\n            paddingRight: '0.5rem'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: e => {\n              e.preventDefault();\n              handleCreateStudent();\n            },\n            style: {\n              display: 'grid',\n              gridTemplateColumns: '1fr 1fr',\n              gap: '2rem',\n              height: 'fit-content'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: '0.5rem',\n                    fontWeight: '600',\n                    color: '#374151'\n                  },\n                  children: \"Profile Picture\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1082,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '1rem'\n                  },\n                  children: [profilePicturePreview ? /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: profilePicturePreview,\n                    alt: \"Profile preview\",\n                    style: {\n                      width: '80px',\n                      height: '80px',\n                      borderRadius: '50%',\n                      objectFit: 'cover',\n                      border: '2px solid #e8f5e8'\n                    },\n                    onError: e => {\n                      console.error('Profile picture preview failed to load:', profilePicturePreview);\n                      const target = e.target;\n                      target.style.display = 'none';\n                      const parent = target.parentElement;\n                      if (parent) {\n                        parent.innerHTML = `\n                                <div style=\"\n                                  width: 80px;\n                                  height: 80px;\n                                  border-radius: 50%;\n                                  background-color: #f3f4f6;\n                                  display: flex;\n                                  align-items: center;\n                                  justify-content: center;\n                                  color: #6b7280;\n                                  font-size: 0.875rem;\n                                \">\n                                  No Photo\n                                </div>\n                              `;\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1087,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '80px',\n                      height: '80px',\n                      borderRadius: '50%',\n                      backgroundColor: '#f3f4f6',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      color: '#6b7280',\n                      fontSize: '0.875rem'\n                    },\n                    children: \"No Photo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1122,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"file\",\n                      accept: \"image/*\",\n                      onChange: handleProfilePictureChange,\n                      style: {\n                        width: '100%',\n                        padding: '0.5rem',\n                        border: '1px solid #d1d5db',\n                        borderRadius: '8px',\n                        fontSize: '0.875rem'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1137,\n                      columnNumber: 25\n                    }, this), profilePicturePreview && /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      onClick: removeProfilePictureLocal,\n                      style: {\n                        marginTop: '0.5rem',\n                        padding: '0.25rem 0.5rem',\n                        backgroundColor: '#ef4444',\n                        color: 'white',\n                        border: 'none',\n                        borderRadius: '4px',\n                        fontSize: '0.75rem',\n                        cursor: 'pointer'\n                      },\n                      children: \"Remove\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1150,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1136,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1085,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1081,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: '0.5rem',\n                    fontWeight: '600',\n                    color: '#374151'\n                  },\n                  children: \"Student Number *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1173,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"studentNumber\",\n                  value: formData.studentNumber,\n                  onChange: handleInputChange,\n                  required: true,\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '8px',\n                    fontSize: '1rem'\n                  },\n                  placeholder: \"e.g., 2025-0001\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1176,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1172,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: '0.5rem',\n                    fontWeight: '600',\n                    color: '#374151'\n                  },\n                  children: \"Email Address * (Auto-generated)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1195,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  name: \"email\",\n                  value: formData.email,\n                  readOnly: true,\n                  required: true,\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '8px',\n                    fontSize: '1rem',\n                    backgroundColor: '#f9fafb',\n                    color: '#6b7280'\n                  },\n                  placeholder: \"Email will be auto-generated based on student details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1198,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1194,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'grid',\n                  gridTemplateColumns: '1fr 1fr',\n                  gap: '1rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    style: {\n                      display: 'block',\n                      marginBottom: '0.5rem',\n                      fontWeight: '600',\n                      color: '#374151'\n                    },\n                    children: \"First Name *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1221,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"firstName\",\n                    value: formData.firstName,\n                    onChange: handleInputChange,\n                    required: true,\n                    style: {\n                      width: '100%',\n                      padding: '0.75rem',\n                      border: '1px solid #d1d5db',\n                      borderRadius: '8px',\n                      fontSize: '1rem'\n                    },\n                    placeholder: \"Juan\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1224,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1220,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    style: {\n                      display: 'block',\n                      marginBottom: '0.5rem',\n                      fontWeight: '600',\n                      color: '#374151'\n                    },\n                    children: \"Last Name *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1243,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"lastName\",\n                    value: formData.lastName,\n                    onChange: handleInputChange,\n                    required: true,\n                    style: {\n                      width: '100%',\n                      padding: '0.75rem',\n                      border: '1px solid #d1d5db',\n                      borderRadius: '8px',\n                      fontSize: '1rem'\n                    },\n                    placeholder: \"Cruz\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1246,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1242,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1218,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'grid',\n                  gridTemplateColumns: '1fr 1fr',\n                  gap: '1rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    style: {\n                      display: 'block',\n                      marginBottom: '0.5rem',\n                      fontWeight: '600',\n                      color: '#374151'\n                    },\n                    children: \"Middle Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1267,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"middleName\",\n                    value: formData.middleName,\n                    onChange: handleInputChange,\n                    style: {\n                      width: '100%',\n                      padding: '0.75rem',\n                      border: '1px solid #d1d5db',\n                      borderRadius: '8px',\n                      fontSize: '1rem'\n                    },\n                    placeholder: \"Dela\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1270,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1266,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    style: {\n                      display: 'block',\n                      marginBottom: '0.5rem',\n                      fontWeight: '600',\n                      color: '#374151'\n                    },\n                    children: \"Suffix\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1288,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"suffix\",\n                    value: formData.suffix,\n                    onChange: handleInputChange,\n                    style: {\n                      width: '100%',\n                      padding: '0.75rem',\n                      border: '1px solid #d1d5db',\n                      borderRadius: '8px',\n                      fontSize: '1rem'\n                    },\n                    placeholder: \"Jr., Sr., III\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1291,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1287,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1264,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1079,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: '0.5rem',\n                    fontWeight: '600',\n                    color: '#374151'\n                  },\n                  children: \"Phone Number *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1313,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"tel\",\n                  name: \"phoneNumber\",\n                  value: formData.phoneNumber,\n                  onChange: handleInputChange,\n                  onInput: e => {\n                    // Allow only numbers\n                    e.currentTarget.value = e.currentTarget.value.replace(/[^0-9]/g, '');\n                  },\n                  required: true,\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '8px',\n                    fontSize: '1rem'\n                  },\n                  placeholder: \"09123456789\",\n                  maxLength: 11\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1316,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1312,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: '0.5rem',\n                    fontWeight: '600',\n                    color: '#374151'\n                  },\n                  children: \"Grade Level *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1340,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"gradeLevel\",\n                  value: formData.gradeLevel,\n                  onChange: handleInputChange,\n                  required: true,\n                  disabled: permissions.isProfessor && getAvailableGradeLevels().length === 1 // Disable for professor if only one option\n                  ,\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '8px',\n                    fontSize: '1rem',\n                    backgroundColor: 'white'\n                  },\n                  children: getAvailableGradeLevels().map(grade => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: grade,\n                    children: [\"Grade \", grade]\n                  }, grade, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1366,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1350,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1339,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: '0.5rem',\n                    fontWeight: '600',\n                    color: '#374151'\n                  },\n                  children: \"Section *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1375,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"section\",\n                  value: formData.section,\n                  onChange: handleInputChange,\n                  required: true,\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '8px',\n                    fontSize: '1rem'\n                  },\n                  placeholder: \"A, B, C, etc.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1378,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1374,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: '0.5rem',\n                    fontWeight: '600',\n                    color: '#374151'\n                  },\n                  children: \"Parent/Guardian Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1397,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"parentGuardianName\",\n                  value: formData.parentGuardianName,\n                  onChange: handleInputChange,\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '8px',\n                    fontSize: '1rem'\n                  },\n                  placeholder: \"Parent/Guardian Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1400,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1396,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: '0.5rem',\n                    fontWeight: '600',\n                    color: '#374151'\n                  },\n                  children: \"Parent/Guardian Phone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1418,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"tel\",\n                  name: \"parentGuardianPhone\",\n                  value: formData.parentGuardianPhone,\n                  onChange: handleInputChange,\n                  onInput: e => {\n                    // Allow only numbers\n                    e.currentTarget.value = e.currentTarget.value.replace(/[^0-9]/g, '');\n                  },\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '8px',\n                    fontSize: '1rem'\n                  },\n                  placeholder: \"09123456789\",\n                  maxLength: 11\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1421,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1417,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: '0.5rem',\n                    fontWeight: '600',\n                    color: '#374151'\n                  },\n                  children: \"Address\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1444,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  name: \"address\",\n                  value: formData.address,\n                  onChange: handleInputChange,\n                  rows: 4,\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '8px',\n                    fontSize: '1rem',\n                    resize: 'vertical'\n                  },\n                  placeholder: \"Complete address\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1447,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1443,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: '#f0f9ff',\n                  border: '1px solid #bae6fd',\n                  borderRadius: '8px',\n                  padding: '1rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem',\n                    marginBottom: '0.5rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Info, {\n                    size: 20,\n                    color: \"#0369a1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1472,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontWeight: '600',\n                      color: '#0369a1'\n                    },\n                    children: \"Default Login Credentials\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1473,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1471,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    color: '#0369a1',\n                    fontSize: '0.875rem'\n                  },\n                  children: [\"The student account will be created with the default password: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Student123\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1476,\n                    columnNumber: 86\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1477,\n                    columnNumber: 23\n                  }, this), \"Please share these credentials with the student and ask them to change the password on first login.\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1475,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1465,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1310,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                gridColumn: '1 / -1',\n                display: 'flex',\n                justifyContent: 'flex-end',\n                gap: '1rem',\n                marginTop: '1rem',\n                paddingTop: '1rem',\n                borderTop: '1px solid #e5e7eb'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => {\n                  setShowCreateModal(false);\n                  resetForm();\n                },\n                disabled: isSubmitting,\n                style: {\n                  background: '#f3f4f6',\n                  color: '#374151',\n                  border: 'none',\n                  borderRadius: '8px',\n                  padding: '0.75rem 1.5rem',\n                  fontSize: '1rem',\n                  cursor: isSubmitting ? 'not-allowed' : 'pointer',\n                  opacity: isSubmitting ? 0.6 : 1\n                },\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1494,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                disabled: isSubmitting,\n                style: {\n                  background: isSubmitting ? '#9ca3af' : 'linear-gradient(135deg, #2d5016 0%, #4ade80 100%)',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '8px',\n                  padding: '0.75rem 1.5rem',\n                  fontSize: '1rem',\n                  cursor: isSubmitting ? 'not-allowed' : 'pointer',\n                  fontWeight: '600'\n                },\n                children: isSubmitting ? 'Creating...' : 'Create Student Account'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1514,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1484,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1072,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1071,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1016,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1003,\n      columnNumber: 9\n    }, this), showEditModal && selectedStudent && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '16px',\n          padding: '2rem',\n          width: '95%',\n          maxWidth: '1200px',\n          maxHeight: '95vh',\n          overflow: 'hidden',\n          display: 'flex',\n          flexDirection: 'column'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              margin: 0,\n              color: '#2d5016',\n              fontSize: '1.5rem',\n              fontWeight: '700'\n            },\n            children: [\"Edit Student: \", selectedStudent.profile.full_name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1568,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setShowEditModal(false);\n              setSelectedStudent(null);\n              resetForm();\n            },\n            style: {\n              background: 'none',\n              border: 'none',\n              fontSize: '1.5rem',\n              cursor: 'pointer',\n              color: '#6b7280'\n            },\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1576,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1562,\n          columnNumber: 13\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#fef2f2',\n            border: '1px solid #fecaca',\n            borderRadius: '8px',\n            padding: '1rem',\n            marginBottom: '1rem',\n            color: '#dc2626'\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1595,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            overflow: 'auto',\n            paddingRight: '0.5rem'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: e => {\n              e.preventDefault();\n              handleUpdateStudent();\n            },\n            style: {\n              display: 'grid',\n              gridTemplateColumns: '1fr 1fr',\n              gap: '2rem',\n              height: 'fit-content'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: '0.5rem',\n                    fontWeight: '600',\n                    color: '#374151'\n                  },\n                  children: \"Profile Picture\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1618,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '1rem'\n                  },\n                  children: [profilePicturePreview ? /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: profilePicturePreview,\n                    alt: \"Profile preview\",\n                    style: {\n                      width: '80px',\n                      height: '80px',\n                      borderRadius: '50%',\n                      objectFit: 'cover',\n                      border: '2px solid #e8f5e8'\n                    },\n                    onError: e => {\n                      console.error('Edit modal profile picture preview failed to load:', profilePicturePreview);\n                      const target = e.target;\n                      target.style.display = 'none';\n                      const parent = target.parentElement;\n                      if (parent) {\n                        parent.innerHTML = `\n                                <div style=\"\n                                  width: 80px;\n                                  height: 80px;\n                                  border-radius: 50%;\n                                  background-color: #f3f4f6;\n                                  display: flex;\n                                  align-items: center;\n                                  justify-content: center;\n                                  color: #6b7280;\n                                  font-size: 0.875rem;\n                                \">\n                                  No Photo\n                                </div>\n                              `;\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1623,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '80px',\n                      height: '80px',\n                      borderRadius: '50%',\n                      backgroundColor: '#f3f4f6',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      color: '#6b7280',\n                      fontSize: '0.875rem'\n                    },\n                    children: \"No Photo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1658,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"file\",\n                      accept: \"image/*\",\n                      onChange: handleProfilePictureChange,\n                      style: {\n                        width: '100%',\n                        padding: '0.5rem',\n                        border: '1px solid #d1d5db',\n                        borderRadius: '8px',\n                        fontSize: '0.875rem'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1673,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        gap: '0.5rem',\n                        marginTop: '0.5rem'\n                      },\n                      children: profilePicturePreview && /*#__PURE__*/_jsxDEV(\"button\", {\n                        type: \"button\",\n                        onClick: removeProfilePicture,\n                        style: {\n                          padding: '0.25rem 0.5rem',\n                          backgroundColor: '#ef4444',\n                          color: 'white',\n                          border: 'none',\n                          borderRadius: '4px',\n                          fontSize: '0.75rem',\n                          cursor: 'pointer'\n                        },\n                        children: \"Remove Picture\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1687,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1685,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1672,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1621,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1617,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: '0.5rem',\n                    fontWeight: '600',\n                    color: '#374151'\n                  },\n                  children: \"Student Number *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1727,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"studentNumber\",\n                  value: formData.studentNumber,\n                  onChange: handleInputChange,\n                  required: true,\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '8px',\n                    fontSize: '1rem'\n                  },\n                  placeholder: \"e.g., 2025-0001\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1730,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1726,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: '0.5rem',\n                    fontWeight: '600',\n                    color: '#374151'\n                  },\n                  children: \"Email Address * (Auto-generated)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1749,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  name: \"email\",\n                  value: formData.email,\n                  readOnly: true,\n                  required: true,\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '8px',\n                    fontSize: '1rem',\n                    backgroundColor: '#f9fafb',\n                    color: '#6b7280'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1752,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1748,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: '0.5rem',\n                    fontWeight: '600',\n                    color: '#374151'\n                  },\n                  children: \"First Name *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1772,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"firstName\",\n                  value: formData.firstName,\n                  onChange: handleInputChange,\n                  required: true,\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '8px',\n                    fontSize: '1rem'\n                  },\n                  placeholder: \"Juan\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1775,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1771,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: '0.5rem',\n                    fontWeight: '600',\n                    color: '#374151'\n                  },\n                  children: \"Last Name *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1794,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"lastName\",\n                  value: formData.lastName,\n                  onChange: handleInputChange,\n                  required: true,\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '8px',\n                    fontSize: '1rem'\n                  },\n                  placeholder: \"Cruz\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1797,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1793,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: '0.5rem',\n                    fontWeight: '600',\n                    color: '#374151'\n                  },\n                  children: \"Middle Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1816,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"middleName\",\n                  value: formData.middleName,\n                  onChange: handleInputChange,\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '8px',\n                    fontSize: '1rem'\n                  },\n                  placeholder: \"Dela\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1819,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1815,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: '0.5rem',\n                    fontWeight: '600',\n                    color: '#374151'\n                  },\n                  children: \"Suffix\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1837,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"suffix\",\n                  value: formData.suffix,\n                  onChange: handleInputChange,\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '8px',\n                    fontSize: '1rem'\n                  },\n                  placeholder: \"Jr., Sr., III\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1840,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1836,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1615,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: '0.5rem',\n                    fontWeight: '600',\n                    color: '#374151'\n                  },\n                  children: \"Phone Number *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1861,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"tel\",\n                  name: \"phoneNumber\",\n                  value: formData.phoneNumber,\n                  onChange: handleInputChange,\n                  onInput: e => {\n                    // Allow only numbers\n                    e.currentTarget.value = e.currentTarget.value.replace(/[^0-9]/g, '');\n                  },\n                  required: true,\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '8px',\n                    fontSize: '1rem'\n                  },\n                  maxLength: 11\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1864,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1860,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: '0.5rem',\n                    fontWeight: '600',\n                    color: '#374151'\n                  },\n                  children: \"Grade Level *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1887,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"gradeLevel\",\n                  value: formData.gradeLevel,\n                  onChange: handleInputChange,\n                  required: true,\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '8px',\n                    fontSize: '1rem',\n                    backgroundColor: 'white'\n                  },\n                  children: getAvailableGradeLevels().map(grade => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: grade,\n                    children: [\"Grade \", grade]\n                  }, grade, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1905,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1890,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1886,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: '0.5rem',\n                    fontWeight: '600',\n                    color: '#374151'\n                  },\n                  children: \"Section *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1912,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"section\",\n                  value: formData.section,\n                  onChange: handleInputChange,\n                  required: true,\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '8px',\n                    fontSize: '1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1915,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1911,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: '0.5rem',\n                    fontWeight: '600',\n                    color: '#374151'\n                  },\n                  children: \"Parent/Guardian Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1933,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"parentGuardianName\",\n                  value: formData.parentGuardianName,\n                  onChange: handleInputChange,\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '8px',\n                    fontSize: '1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1936,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1932,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: '0.5rem',\n                    fontWeight: '600',\n                    color: '#374151'\n                  },\n                  children: \"Parent/Guardian Phone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1953,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"tel\",\n                  name: \"parentGuardianPhone\",\n                  value: formData.parentGuardianPhone,\n                  onChange: handleInputChange,\n                  onInput: e => {\n                    // Allow only numbers\n                    e.currentTarget.value = e.currentTarget.value.replace(/[^0-9]/g, '');\n                  },\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '8px',\n                    fontSize: '1rem'\n                  },\n                  maxLength: 11\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1956,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1952,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: 'block',\n                    marginBottom: '0.5rem',\n                    fontWeight: '600',\n                    color: '#374151'\n                  },\n                  children: \"Address\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1978,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  name: \"address\",\n                  value: formData.address,\n                  onChange: handleInputChange,\n                  rows: 4,\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '8px',\n                    fontSize: '1rem',\n                    resize: 'vertical'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1981,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1977,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1858,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                gridColumn: '1 / -1',\n                display: 'flex',\n                justifyContent: 'flex-end',\n                gap: '1rem',\n                marginTop: '1rem',\n                paddingTop: '1rem',\n                borderTop: '1px solid #e5e7eb'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => {\n                  setShowEditModal(false);\n                  setSelectedStudent(null);\n                  resetForm();\n                },\n                disabled: isSubmitting,\n                style: {\n                  background: '#f3f4f6',\n                  color: '#374151',\n                  border: 'none',\n                  borderRadius: '8px',\n                  padding: '0.75rem 1.5rem',\n                  fontSize: '1rem',\n                  cursor: isSubmitting ? 'not-allowed' : 'pointer',\n                  opacity: isSubmitting ? 0.6 : 1\n                },\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2008,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                disabled: isSubmitting,\n                style: {\n                  background: isSubmitting ? '#9ca3af' : 'linear-gradient(135deg, #2d5016 0%, #4ade80 100%)',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '8px',\n                  padding: '0.75rem 1.5rem',\n                  fontSize: '1rem',\n                  cursor: isSubmitting ? 'not-allowed' : 'pointer',\n                  fontWeight: '600'\n                },\n                children: isSubmitting ? 'Updating...' : 'Update Student'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2029,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1999,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1608,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1607,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1551,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1539,\n      columnNumber: 9\n    }, this), showDeleteModal && selectedStudent && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '16px',\n          padding: '2rem',\n          width: '90%',\n          maxWidth: '500px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              margin: 0,\n              color: '#dc2626',\n              fontSize: '1.5rem',\n              fontWeight: '700'\n            },\n            children: \"Deactivate Student Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2079,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setShowDeleteModal(false);\n              setSelectedStudent(null);\n            },\n            style: {\n              background: 'none',\n              border: 'none',\n              fontSize: '1.5rem',\n              cursor: 'pointer',\n              color: '#6b7280'\n            },\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2087,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2073,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: '0 0 1rem 0',\n              color: '#374151'\n            },\n            children: \"Are you sure you want to deactivate the account for:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2105,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: '#f9fafb',\n              border: '1px solid #e5e7eb',\n              borderRadius: '8px',\n              padding: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: 0,\n                fontWeight: '600',\n                color: '#2d5016'\n              },\n              children: selectedStudent.profile.full_name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2114,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0.25rem 0 0 0',\n                color: '#6b7280',\n                fontSize: '0.875rem'\n              },\n              children: [\"Student Number: \", selectedStudent.student_number]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2117,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0.25rem 0 0 0',\n                color: '#6b7280',\n                fontSize: '0.875rem'\n              },\n              children: [\"Email: \", selectedStudent.email]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2120,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2108,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: '1rem 0 0 0',\n              color: '#dc2626',\n              fontSize: '0.875rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: 'flex',\n                alignItems: 'flex-start',\n                gap: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n                size: 16,\n                color: \"#dc2626\",\n                style: {\n                  marginTop: '0.125rem',\n                  flexShrink: 0\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2126,\n                columnNumber: 19\n              }, this), \"This action will deactivate the student's account. They will not be able to log in until reactivated.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2125,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2124,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2104,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'flex-end',\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setShowDeleteModal(false);\n              setSelectedStudent(null);\n            },\n            disabled: isSubmitting,\n            style: {\n              background: '#f3f4f6',\n              color: '#374151',\n              border: 'none',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              fontSize: '1rem',\n              cursor: isSubmitting ? 'not-allowed' : 'pointer',\n              opacity: isSubmitting ? 0.6 : 1\n            },\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2137,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: confirmDeleteStudent,\n            disabled: isSubmitting,\n            style: {\n              background: isSubmitting ? '#9ca3af' : '#dc2626',\n              color: 'white',\n              border: 'none',\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem',\n              fontSize: '1rem',\n              cursor: isSubmitting ? 'not-allowed' : 'pointer',\n              opacity: isSubmitting ? 0.6 : 1\n            },\n            children: isSubmitting ? 'Deactivating...' : 'Deactivate Account'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2156,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2132,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2066,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2054,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 543,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentManagement, \"RTtYHRggYk7FklkblroGlcpoaKc=\", false, function () {\n  return [useAdminAuth, usePermissions];\n});\n_c = StudentManagement;\nexport default StudentManagement;\nvar _c;\n$RefreshReg$(_c, \"StudentManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "studentService", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RefreshCw", "Edit", "Key", "Trash2", "Info", "User", "Users", "useAdminAuth", "usePermissions", "getImageUrl", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "StudentManagement", "_s", "user", "permissions", "students", "setStudents", "loading", "setLoading", "searchTerm", "setSearchTerm", "filterStatus", "setFilterStatus", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "totalStudents", "setTotalStudents", "getAvailableGradeLevels", "isSuperAdmin", "grade_level", "showCreateModal", "setShowCreateModal", "showEditModal", "setShowEditModal", "showDeleteModal", "setShowDeleteModal", "selectedStudent", "setSelectedStudent", "formData", "setFormData", "studentNumber", "email", "firstName", "middleName", "lastName", "suffix", "phoneNumber", "gradeLevel", "isProfessor", "section", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parent<PERSON><PERSON>ianPhone", "address", "profilePictureFile", "setProfilePictureFile", "profilePicturePreview", "setProfilePicturePreview", "isSubmitting", "setIsSubmitting", "error", "setError", "debouncedSearchTerm", "setDebouncedSearchTerm", "loadStudents", "params", "page", "limit", "search", "undefined", "is_active", "console", "log", "response", "getStudents", "map", "s", "_s$profile", "name", "profile", "full_name", "status", "length", "pagination", "total", "canViewStudents", "timeoutId", "setTimeout", "clearTimeout", "getStatusColor", "isActive", "getStatusText", "handleSearchChange", "e", "target", "value", "generateEmail", "firstLetter", "char<PERSON>t", "toUpperCase", "middleInitial", "cleanLastName", "replace", "toLowerCase", "cleanSection", "handleInputChange", "newFormData", "parseInt", "includes", "generatedEmail", "toString", "resetForm", "handleProfilePictureChange", "_e$target$files", "file", "files", "type", "startsWith", "alert", "size", "reader", "FileReader", "onload", "_e$target", "result", "readAsDataURL", "removeProfilePictureLocal", "removeProfilePicture", "profile_picture", "confirmed", "window", "confirm", "updatedStudent", "removeStudentProfilePicture", "student_id", "message", "handleCreateStudent", "Error", "emailRegex", "test", "studentData", "student_number", "password", "created_by", "id", "first_name", "middle_name", "last_name", "phone_number", "parent_guardian_name", "parent_guardian_phone", "createdStudent", "createStudent", "uploadStudentProfilePicture", "profileError", "handleEditStudent", "student", "handleUpdateStudent", "updateData", "updateStudent", "handleDeleteStudent", "handleResetPassword", "resetStudentPassword", "confirmDeleteStudent", "deleteStudent", "handlePageChange", "handleStatusFilterChange", "style", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "textAlign", "color", "children", "marginBottom", "opacity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "margin", "fontSize", "fontWeight", "marginTop", "padding", "background", "getPositionBadgeColor", "borderRadius", "getPositionDisplayName", "gap", "marginLeft", "backgroundColor", "boxShadow", "border", "flexWrap", "canManageStudents", "onClick", "disabled", "cursor", "transition", "whiteSpace", "flex", "min<PERSON><PERSON><PERSON>", "placeholder", "onChange", "width", "outline", "marginRight", "overflow", "borderBottom", "gridTemplateColumns", "src", "alt", "height", "objectFit", "onError", "parent", "parentElement", "innerHTML", "title", "borderTop", "position", "top", "left", "right", "bottom", "zIndex", "max<PERSON><PERSON><PERSON>", "maxHeight", "paddingRight", "onSubmit", "preventDefault", "accept", "required", "readOnly", "onInput", "currentTarget", "max<PERSON><PERSON><PERSON>", "grade", "rows", "resize", "gridColumn", "paddingTop", "flexShrink", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/admin/StudentManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { studentService, CreateStudentRequest, Student, StudentsResponse } from '../../services/studentService';\nimport { AlertTriangle, RefreshCw, Edit, Key, Trash2, Info, User, Users } from 'lucide-react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\nimport { getImageUrl } from '../../config/constants';\n\nconst StudentManagement: React.FC = () => {\n  // Auth context\n  const { user } = useAdminAuth();\n  const permissions = usePermissions(user);\n\n  // State management\n  const [students, setStudents] = useState<Student[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive'>('active');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [totalStudents, setTotalStudents] = useState(0);\n\n  // Get available grade levels based on admin's position and assigned grade\n  const getAvailableGradeLevels = () => {\n    if (permissions.isSuperAdmin) {\n      // Super admin can manage all grades\n      return [11, 12];\n    } else if (user?.grade_level) {\n      // Grade-specific professor can only manage their assigned grade\n      return [user.grade_level];\n    } else {\n      // Default to grades 11 and 12\n      return [11, 12];\n    }\n  };\n\n  // Modal states\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\n  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);\n\n  // Form states for creating/editing student\n  const [formData, setFormData] = useState({\n    studentNumber: '',\n    email: '',\n    firstName: '',\n    middleName: '',\n    lastName: '',\n    suffix: '',\n    phoneNumber: '',\n    gradeLevel: permissions.isProfessor && user?.grade_level ? user.grade_level : 11,\n    section: '',\n    parentGuardianName: '',\n    parentGuardianPhone: '',\n    address: ''\n  });\n  const [profilePictureFile, setProfilePictureFile] = useState<File | null>(null);\n  const [profilePicturePreview, setProfilePicturePreview] = useState<string | null>(null);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  // State for debounced search term\n  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');\n\n  // Load students data - SIMPLIFIED WITHOUT AUTH GUARDS\n  const loadStudents = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Build filter parameters\n      const params: any = {\n        page: currentPage,\n        limit: 10,\n        search: debouncedSearchTerm || undefined,\n      };\n\n      // Add status filter if not 'all'\n      if (filterStatus === 'active') {\n        params.is_active = true;\n      } else if (filterStatus === 'inactive') {\n        params.is_active = false;\n      }\n      // If filterStatus === 'all', don't add is_active parameter\n\n      // Add grade level filter based on position and assigned grade\n      if (permissions.isSuperAdmin) {\n        // Super admin can see ALL students regardless of grade level\n        // Don't add grade_level filter parameter\n      } else if (user?.grade_level) {\n        // Professor with assigned grade level can only see their grade\n        params.grade_level = user.grade_level;\n      }\n      // If no grade_level specified, show all students (fallback)\n\n      console.log('Loading students with params:', params);\n      console.log('Filter status:', filterStatus, 'is_active param:', params.is_active);\n\n      const response = await studentService.getStudents(params);\n      console.log('API Response received:', response);\n      console.log('Students loaded:', response.students.map(s => ({\n        name: s.profile?.full_name || 'No name',\n        email: s.email,\n        is_active: s.is_active,\n        status: s.is_active ? 'Active' : 'Inactive'\n      })));\n\n      // Additional debug: Check what we're about to set in state\n      console.log('About to set students state with:', response.students.length, 'students');\n\n      setStudents(response.students);\n      setTotalPages(response.pagination.totalPages);\n      setTotalStudents(response.pagination.total);\n    } catch (error: any) {\n      console.error('Error loading students:', error);\n      setError('Failed to load students. Please check if the backend is running.');\n      setStudents([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [currentPage, debouncedSearchTerm, filterStatus, user]);\n\n  // Load students when dependencies change\n  useEffect(() => {\n    // Check if user has permission to view students\n    if (!permissions.canViewStudents) {\n      setError('You do not have permission to view student information');\n      setLoading(false);\n      return;\n    }\n\n    loadStudents();\n  }, [loadStudents, permissions.canViewStudents]);\n\n  // Debounced search - update debounced term after delay\n  useEffect(() => {\n    const timeoutId = setTimeout(() => {\n      setDebouncedSearchTerm(searchTerm);\n      setCurrentPage(1); // Reset to first page when searching\n    }, 500);\n\n    return () => clearTimeout(timeoutId);\n  }, [searchTerm]);\n\n  // Utility functions\n  const getStatusColor = (isActive: boolean) => {\n    return isActive ? '#22c55e' : '#f59e0b';\n  };\n\n  const getStatusText = (isActive: boolean) => {\n    return isActive ? 'Active' : 'Inactive';\n  };\n\n  // Search handler\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setSearchTerm(e.target.value);\n  };\n\n  // Email generation function\n  const generateEmail = (studentNumber: string, gradeLevel: string, section: string, lastName: string, firstName: string, middleName: string) => {\n    if (!studentNumber || !gradeLevel || !section || !lastName || !firstName) {\n      return '';\n    }\n\n    const firstLetter = firstName.charAt(0).toUpperCase();\n    const middleInitial = middleName ? middleName.charAt(0).toUpperCase() : '';\n    const cleanLastName = lastName.replace(/\\s+/g, '').toLowerCase();\n    const cleanSection = section.replace(/\\s+/g, '').toUpperCase();\n\n    return `${studentNumber}_${gradeLevel}_${cleanSection}_${cleanLastName}_${firstLetter}_${middleInitial}@gmail.com`;\n  };\n\n  // Form handlers\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    const newFormData = {\n      ...formData,\n      [name]: name === 'gradeLevel' ? parseInt(value) : value\n    };\n\n    // Auto-generate email when required fields are filled\n    if (['studentNumber', 'gradeLevel', 'section', 'lastName', 'firstName', 'middleName'].includes(name)) {\n      const generatedEmail = generateEmail(\n        newFormData.studentNumber,\n        newFormData.gradeLevel.toString(),\n        newFormData.section,\n        newFormData.lastName,\n        newFormData.firstName,\n        newFormData.middleName\n      );\n      if (generatedEmail) {\n        newFormData.email = generatedEmail;\n      }\n    }\n\n    setFormData(newFormData);\n  };\n\n  const resetForm = () => {\n    setFormData({\n      studentNumber: '',\n      email: '',\n      firstName: '',\n      middleName: '',\n      lastName: '',\n      suffix: '',\n      phoneNumber: '',\n      gradeLevel: permissions.isProfessor && user?.grade_level ? user.grade_level : 11,\n      section: '',\n      parentGuardianName: '',\n      parentGuardianPhone: '',\n      address: ''\n    });\n    setProfilePictureFile(null);\n    setProfilePicturePreview(null);\n  };\n\n  // Profile picture handling functions\n  const handleProfilePictureChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0];\n    if (file) {\n      // Validate file type\n      if (!file.type.startsWith('image/')) {\n        alert('Please select an image file');\n        return;\n      }\n\n      // Validate file size (2MB limit)\n      if (file.size > 2 * 1024 * 1024) {\n        alert('File size must be less than 2MB');\n        return;\n      }\n\n      setProfilePictureFile(file);\n\n      // Create preview\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        setProfilePicturePreview(e.target?.result as string);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  // Remove profile picture for create modal (local only)\n  const removeProfilePictureLocal = () => {\n    setProfilePictureFile(null);\n    setProfilePicturePreview(null);\n  };\n\n  // Remove profile picture for edit modal (calls API for existing students)\n  const removeProfilePicture = async () => {\n    try {\n      // If we're editing an existing student and they have a profile picture, remove it from the server\n      if (selectedStudent && selectedStudent.profile.profile_picture) {\n        // Confirm before removing\n        const confirmed = window.confirm(\n          `Are you sure you want to remove ${selectedStudent.profile.full_name}'s profile picture? This action cannot be undone.`\n        );\n\n        if (!confirmed) {\n          return;\n        }\n\n        setLoading(true);\n        const updatedStudent = await studentService.removeStudentProfilePicture(selectedStudent.student_id.toString());\n\n        // Update the selected student data immediately\n        setSelectedStudent(updatedStudent);\n\n        // Refresh the students list to show the updated data\n        await loadStudents();\n\n        alert('Profile picture removed successfully!');\n      }\n\n      // Clear local state regardless\n      setProfilePictureFile(null);\n      setProfilePicturePreview(null);\n    } catch (error: any) {\n      console.error('Error removing profile picture:', error);\n      alert(`Failed to remove profile picture: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // CRUD Operations\n  const handleCreateStudent = async () => {\n    setIsSubmitting(true);\n    setError(null);\n\n    try {\n      // Validate required fields\n      if (!formData.studentNumber || !formData.email || !formData.firstName || !formData.lastName || !formData.phoneNumber || !formData.section || !formData.gradeLevel) {\n        throw new Error('Please fill in all required fields (First Name, Last Name, Student Number, Email, Phone, Grade Level, Section)');\n      }\n\n      // Validate email format\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      if (!emailRegex.test(formData.email)) {\n        throw new Error('Please enter a valid email address');\n      }\n\n      // Prepare student data for API\n      const studentData: CreateStudentRequest = {\n        // Account data\n        student_number: formData.studentNumber,\n        email: formData.email,\n        password: 'Student123', // Default password\n        is_active: true,\n        created_by: user?.id || 1, // Current admin ID\n\n        // Profile data\n        first_name: formData.firstName,\n        middle_name: formData.middleName || undefined,\n        last_name: formData.lastName,\n        suffix: formData.suffix || undefined,\n        phone_number: formData.phoneNumber,\n        grade_level: formData.gradeLevel,\n        section: formData.section,\n        parent_guardian_name: formData.parentGuardianName || undefined,\n        parent_guardian_phone: formData.parentGuardianPhone || undefined,\n        address: formData.address || undefined\n      };\n\n      // Debug: Log the data being sent\n      console.log('Sending student data:', studentData);\n\n      // Call API to create student\n      const createdStudent = await studentService.createStudent(studentData);\n\n      // Upload profile picture if provided\n      if (profilePictureFile) {\n        try {\n          await studentService.uploadStudentProfilePicture(createdStudent.student_id.toString(), profilePictureFile);\n        } catch (profileError: any) {\n          console.error('Error uploading profile picture:', profileError);\n          // Don't fail the entire creation process for profile picture upload failure\n          alert(`Student account created successfully, but profile picture upload failed: ${profileError.message}`);\n        }\n      }\n\n      alert(`Student account created successfully!\\n\\nStudent Details:\\nName: ${createdStudent.profile.full_name}\\nStudent Number: ${createdStudent.student_number}\\nEmail: ${createdStudent.email}\\n\\nLogin Credentials:\\nEmail: ${createdStudent.email}\\nPassword: Student123\\n\\nPlease share these credentials with the student and ask them to change the password on first login.`);\n\n      resetForm();\n      setShowCreateModal(false);\n\n      // Refresh the students list\n      await loadStudents();\n\n    } catch (error: any) {\n      console.error('Error creating student:', error);\n      setError(error.message || 'Failed to create student account. Please try again.');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleEditStudent = (student: Student) => {\n    setSelectedStudent(student);\n    setFormData({\n      studentNumber: student.student_number,\n      email: student.email,\n      firstName: student.profile.first_name,\n      middleName: student.profile.middle_name || '',\n      lastName: student.profile.last_name,\n      suffix: student.profile.suffix || '',\n      phoneNumber: student.profile.phone_number,\n      gradeLevel: student.profile.grade_level,\n      section: student.profile.section,\n      parentGuardianName: student.profile.parent_guardian_name || '',\n      parentGuardianPhone: student.profile.parent_guardian_phone || '',\n      address: student.profile.address || ''\n    });\n\n    // Set existing profile picture preview\n    setProfilePictureFile(null);\n    setProfilePicturePreview(student.profile.profile_picture ? getImageUrl(student.profile.profile_picture) : null);\n\n    setShowEditModal(true);\n  };\n\n  const handleUpdateStudent = async () => {\n    if (!selectedStudent) return;\n\n    setIsSubmitting(true);\n    setError(null);\n\n    try {\n      // Validate required fields\n      if (!formData.studentNumber || !formData.email || !formData.firstName || !formData.lastName || !formData.phoneNumber || !formData.section) {\n        throw new Error('Please fill in all required fields (First Name, Last Name, Student Number, Email, Phone, Section)');\n      }\n\n      // Validate email format\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      if (!emailRegex.test(formData.email)) {\n        throw new Error('Please enter a valid email address');\n      }\n\n      // Prepare update data\n      const updateData: Partial<CreateStudentRequest> = {\n        student_number: formData.studentNumber,\n        email: formData.email,\n        first_name: formData.firstName,\n        middle_name: formData.middleName || undefined,\n        last_name: formData.lastName,\n        suffix: formData.suffix || undefined,\n        phone_number: formData.phoneNumber,\n        grade_level: formData.gradeLevel,\n        section: formData.section,\n        parent_guardian_name: formData.parentGuardianName || undefined,\n        parent_guardian_phone: formData.parentGuardianPhone || undefined,\n        address: formData.address || undefined\n      };\n\n      // Call API to update student\n      await studentService.updateStudent(selectedStudent.student_id.toString(), updateData);\n\n      // Handle profile picture upload if a new file was selected\n      if (profilePictureFile) {\n        try {\n          await studentService.uploadStudentProfilePicture(selectedStudent.student_id.toString(), profilePictureFile);\n        } catch (profileError: any) {\n          console.error('Error uploading profile picture:', profileError);\n          alert(`Student information updated successfully, but profile picture upload failed: ${profileError.message}`);\n        }\n      }\n\n      alert('Student information updated successfully!');\n\n      resetForm();\n      setShowEditModal(false);\n      setSelectedStudent(null);\n\n      // Refresh the students list\n      await loadStudents();\n\n    } catch (error: any) {\n      console.error('Error updating student:', error);\n      setError(error.message || 'Failed to update student information. Please try again.');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleDeleteStudent = (student: Student) => {\n    setSelectedStudent(student);\n    setShowDeleteModal(true);\n  };\n\n  const handleResetPassword = async (student: Student) => {\n    if (!window.confirm(`Are you sure you want to reset the password for ${student.profile.full_name}?\\n\\nThe password will be reset to: Student123`)) {\n      return;\n    }\n\n    try {\n      setIsSubmitting(true);\n      await studentService.resetStudentPassword(student.student_id.toString());\n\n      // Show success message\n      alert(`Password reset successfully for ${student.profile.full_name}!\\n\\nNew password: Student123`);\n\n    } catch (error: any) {\n      console.error('Error resetting password:', error);\n      alert(`Failed to reset password: ${error.message}`);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const confirmDeleteStudent = async () => {\n    if (!selectedStudent) return;\n\n    setIsSubmitting(true);\n    setError(null);\n\n    try {\n      // Call API to soft delete student (deactivate)\n      await studentService.deleteStudent(selectedStudent.student_id.toString());\n\n      alert('Student account has been deactivated successfully!');\n\n      setShowDeleteModal(false);\n      setSelectedStudent(null);\n\n      // Refresh the students list\n      await loadStudents();\n\n    } catch (error: any) {\n      console.error('Error deleting student:', error);\n      setError(error.message || 'Failed to deactivate student account. Please try again.');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handlePageChange = (page: number) => {\n    setCurrentPage(page);\n  };\n\n  const handleStatusFilterChange = (value: 'all' | 'active' | 'inactive') => {\n    setFilterStatus(value);\n    setCurrentPage(1);\n  };\n\n  // Check permissions first\n  if (!permissions.canViewStudents) {\n    return (\n      <div style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      }}>\n        <Users size={64} style={{ marginBottom: '1rem', opacity: 0.5 }} />\n        <h2 style={{ margin: '0 0 0.5rem', fontSize: '1.5rem', fontWeight: '600' }}>\n          Access Denied\n        </h2>\n        <p style={{ margin: 0, fontSize: '1rem' }}>\n          You do not have permission to view student information.\n        </p>\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        }}>\n          Current Role: {permissions.getPositionDisplayName()}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      {/* Header */}\n      <div style={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '1.5rem'\n      }}>\n        <div>\n          <h1 style={{\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#2d5016',\n            margin: '0 0 0.5rem 0'\n          }}>\n            <span style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <User size={20} color=\"#1e40af\" />\n              Student Management\n            </span>\n          </h1>\n          <p style={{\n            color: '#6b7280',\n            margin: 0,\n            fontSize: '1.1rem'\n          }}>\n            {permissions.isSuperAdmin\n              ? 'Create, edit, and manage student accounts across all grades'\n              : permissions.isProfessor\n                ? 'Create, edit, and manage student accounts in your assigned grade'\n                : 'View student information (read-only)'\n            }\n            {permissions.isSuperAdmin && (\n              <span style={{\n                display: 'inline-block',\n                marginLeft: '0.5rem',\n                padding: '0.25rem 0.5rem',\n                backgroundColor: '#fef3c7',\n                color: '#92400e',\n                borderRadius: '0.375rem',\n                fontSize: '0.875rem',\n                fontWeight: '500'\n              }}>\n                All Grades\n              </span>\n            )}\n            {permissions.isProfessor && user?.grade_level && (\n              <span style={{\n                display: 'inline-block',\n                marginLeft: '0.5rem',\n                padding: '0.25rem 0.5rem',\n                backgroundColor: '#dbeafe',\n                color: '#1e40af',\n                borderRadius: '0.375rem',\n                fontSize: '0.875rem',\n                fontWeight: '500'\n              }}>\n                Grade {user.grade_level} Only\n              </span>\n            )}\n          </p>\n        </div>\n      </div>\n\n      {/* Filters and Search */}\n      <div style={{\n        background: 'white',\n        borderRadius: '16px',\n        padding: '1.5rem',\n        marginBottom: '2rem',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8'\n      }}>\n        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', flexWrap: 'wrap' }}>\n          {/* Create Student Account Button - Only for super_admin */}\n          {permissions.canManageStudents && (\n            <button\n              onClick={() => setShowCreateModal(true)}\n              disabled={loading}\n              style={{\n                background: 'linear-gradient(135deg, #2d5016 0%, #4ade80 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '8px',\n                padding: '0.5rem 1rem',\n                fontSize: '0.9rem',\n                fontWeight: '600',\n                cursor: loading ? 'not-allowed' : 'pointer',\n                opacity: loading ? 0.6 : 1,\n                transition: 'all 0.2s ease',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.4rem',\n                whiteSpace: 'nowrap'\n              }}\n            >\n              <span style={{ fontSize: '1rem' }}>+</span>\n              Create Student Account\n            </button>\n          )}\n\n          {/* Search */}\n          <div style={{ flex: 1, minWidth: '300px' }}>\n            <input\n              type=\"text\"\n              placeholder=\"Search students\"\n              value={searchTerm}\n              onChange={handleSearchChange}\n              style={{\n                width: '100%',\n                padding: '0.75rem 1rem',\n                border: '1px solid #e8f5e8',\n                borderRadius: '8px',\n                fontSize: '1rem',\n                outline: 'none'\n              }}\n            />\n          </div>\n\n          {/* Status Filter Buttons */}\n          <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>\n            <span style={{ fontSize: '0.9rem', color: '#666', marginRight: '0.5rem' }}>Filter:</span>\n            <button\n              onClick={() => handleStatusFilterChange('all')}\n              style={{\n                padding: '0.5rem 1rem',\n                border: filterStatus === 'all' ? '2px solid #4CAF50' : '1px solid #ddd',\n                borderRadius: '6px',\n                fontSize: '0.85rem',\n                backgroundColor: filterStatus === 'all' ? '#4CAF50' : '#fff',\n                color: filterStatus === 'all' ? '#fff' : '#333',\n                cursor: 'pointer',\n                fontWeight: filterStatus === 'all' ? 'bold' : 'normal',\n                transition: 'all 0.2s ease'\n              }}\n            >\n              All\n            </button>\n            <button\n              onClick={() => handleStatusFilterChange('active')}\n              style={{\n                padding: '0.5rem 1rem',\n                border: filterStatus === 'active' ? '2px solid #4CAF50' : '1px solid #ddd',\n                borderRadius: '6px',\n                fontSize: '0.85rem',\n                backgroundColor: filterStatus === 'active' ? '#4CAF50' : '#fff',\n                color: filterStatus === 'active' ? '#fff' : '#333',\n                cursor: 'pointer',\n                fontWeight: filterStatus === 'active' ? 'bold' : 'normal',\n                transition: 'all 0.2s ease'\n              }}\n            >\n              Active\n            </button>\n            <button\n              onClick={() => handleStatusFilterChange('inactive')}\n              style={{\n                padding: '0.5rem 1rem',\n                border: filterStatus === 'inactive' ? '2px solid #f44336' : '1px solid #ddd',\n                borderRadius: '6px',\n                fontSize: '0.85rem',\n                backgroundColor: filterStatus === 'inactive' ? '#f44336' : '#fff',\n                color: filterStatus === 'inactive' ? '#fff' : '#333',\n                cursor: 'pointer',\n                fontWeight: filterStatus === 'inactive' ? 'bold' : 'normal',\n                transition: 'all 0.2s ease'\n              }}\n            >\n              Inactive\n            </button>\n          </div>\n\n          {/* Refresh Button */}\n          <button\n            onClick={loadStudents}\n            disabled={loading}\n            style={{\n              background: '#f8fdf8',\n              border: '1px solid #e8f5e8',\n              borderRadius: '8px',\n              padding: '0.75rem 1rem',\n              fontSize: '1rem',\n              cursor: loading ? 'not-allowed' : 'pointer',\n              opacity: loading ? 0.6 : 1,\n              transition: 'all 0.2s ease'\n            }}\n          >\n            <span style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <RefreshCw size={16} />\n              Refresh\n            </span>\n          </button>\n        </div>\n      </div>\n\n      {/* Students Table */}\n      <div style={{\n        background: 'white',\n        borderRadius: '16px',\n        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n        border: '1px solid #e8f5e8',\n        overflow: 'hidden'\n      }}>\n        {/* Table Header */}\n        <div style={{\n          background: '#f8fdf8',\n          padding: '1rem 1.5rem',\n          borderBottom: '1px solid #e8f5e8',\n          display: 'grid',\n          gridTemplateColumns: '60px 1fr 2fr 2fr 1fr 1fr 1fr 150px',\n          gap: '1rem',\n          fontWeight: '600',\n          color: '#2d5016',\n          fontSize: '0.875rem'\n        }}>\n          <div>Photo</div>\n          <div>Student #</div>\n          <div>Name</div>\n          <div>Email</div>\n          <div>Grade & Section</div>\n          <div>Phone</div>\n          <div>Status</div>\n          <div>Actions</div>\n        </div>\n\n        {/* Table Body */}\n        {loading ? (\n          <div style={{ padding: '2rem', textAlign: 'center', color: '#6b7280' }}>\n            Loading students...\n          </div>\n        ) : students.length === 0 ? (\n          <div style={{ padding: '2rem', textAlign: 'center', color: '#6b7280' }}>\n            No students found. {searchTerm && 'Try adjusting your search criteria.'}\n          </div>\n        ) : (\n          students.map(student => (\n            <div\n              key={student.student_id}\n              style={{\n                padding: '1rem 1.5rem',\n                borderBottom: '1px solid #f3f4f6',\n                display: 'grid',\n                gridTemplateColumns: '60px 1fr 2fr 2fr 1fr 1fr 1fr 150px',\n                gap: '1rem',\n                alignItems: 'center',\n                fontSize: '0.875rem'\n              }}\n            >\n              <div style={{ display: 'flex', justifyContent: 'center' }}>\n                {student.profile.profile_picture ? (\n                  <img\n                    src={getImageUrl(student.profile.profile_picture) || ''}\n                    alt={`${student.profile.full_name} profile`}\n                    style={{\n                      width: '40px',\n                      height: '40px',\n                      borderRadius: '50%',\n                      objectFit: 'cover',\n                      border: '2px solid #e8f5e8'\n                    }}\n                    onError={(e) => {\n                      // Fallback to initials if image fails to load\n                      const target = e.target as HTMLImageElement;\n                      target.style.display = 'none';\n                      const parent = target.parentElement;\n                      if (parent) {\n                        parent.innerHTML = `\n                          <div style=\"\n                            width: 40px;\n                            height: 40px;\n                            border-radius: 50%;\n                            background-color: #f3f4f6;\n                            display: flex;\n                            align-items: center;\n                            justify-content: center;\n                            color: #6b7280;\n                            font-size: 0.75rem;\n                            font-weight: 600;\n                          \">\n                            ${student.profile.first_name.charAt(0)}${student.profile.last_name.charAt(0)}\n                          </div>\n                        `;\n                      }\n                    }}\n                  />\n                ) : (\n                  <div style={{\n                    width: '40px',\n                    height: '40px',\n                    borderRadius: '50%',\n                    backgroundColor: '#f3f4f6',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    color: '#6b7280',\n                    fontSize: '0.75rem',\n                    fontWeight: '600'\n                  }}>\n                    {student.profile.first_name.charAt(0)}{student.profile.last_name.charAt(0)}\n                  </div>\n                )}\n              </div>\n              <div style={{ fontWeight: '600', color: '#2d5016' }}>\n                {student.student_number}\n              </div>\n              <div style={{ color: '#374151' }}>\n                {student.profile.full_name}\n              </div>\n              <div style={{ color: '#6b7280' }}>\n                {student.email}\n              </div>\n              <div style={{ color: '#374151' }}>\n                Grade {student.profile.grade_level} - {student.profile.section}\n              </div>\n              <div style={{ color: '#374151' }}>\n                {student.profile.phone_number}\n              </div>\n              <div>\n                <span style={{\n                  background: getStatusColor(student.is_active),\n                  color: 'white',\n                  padding: '0.25rem 0.75rem',\n                  borderRadius: '12px',\n                  fontSize: '0.75rem',\n                  fontWeight: '600'\n                }}>\n                  {getStatusText(student.is_active)}\n                </span>\n              </div>\n              <div style={{ display: 'flex', gap: '0.25rem' }}>\n                {permissions.canManageStudents ? (\n                  <>\n                    <button\n                      onClick={() => handleEditStudent(student)}\n                      title=\"Edit Student\"\n                      style={{\n                        background: '#3b82f6',\n                        color: 'white',\n                        border: 'none',\n                        borderRadius: '6px',\n                        padding: '0.5rem',\n                        cursor: 'pointer',\n                        fontSize: '0.75rem'\n                      }}\n                    >\n                      <Edit size={16} />\n                    </button>\n                    <button\n                      onClick={() => handleResetPassword(student)}\n                      title=\"Reset Password to Default (Student123)\"\n                      style={{\n                        background: '#f59e0b',\n                        color: 'white',\n                        border: 'none',\n                        borderRadius: '6px',\n                        padding: '0.5rem',\n                        cursor: 'pointer',\n                        fontSize: '0.75rem'\n                      }}\n                    >\n                      <Key size={16} />\n                    </button>\n                    {permissions.isSuperAdmin && (\n                      <button\n                        onClick={() => handleDeleteStudent(student)}\n                        title=\"Deactivate Student\"\n                        style={{\n                          background: '#ef4444',\n                          color: 'white',\n                          border: 'none',\n                          borderRadius: '6px',\n                          padding: '0.5rem',\n                          cursor: 'pointer',\n                          fontSize: '0.75rem'\n                        }}\n                      >\n                        <Trash2 size={16} />\n                      </button>\n                    )}\n                    {permissions.isProfessor && (\n                      <div style={{\n                        padding: '0.5rem',\n                        background: '#f3f4f6',\n                        color: '#6b7280',\n                        borderRadius: '6px',\n                        fontSize: '0.65rem',\n                        fontWeight: '500',\n                        display: 'flex',\n                        alignItems: 'center'\n                      }}>\n                        Edit Only\n                      </div>\n                    )}\n                  </>\n                ) : (\n                  <div style={{\n                    padding: '0.5rem 1rem',\n                    background: '#f3f4f6',\n                    color: '#6b7280',\n                    borderRadius: '6px',\n                    fontSize: '0.75rem',\n                    fontWeight: '500'\n                  }}>\n                    View Only\n                  </div>\n                )}\n              </div>\n            </div>\n          ))\n        )}\n\n        {/* Pagination */}\n        {totalPages > 1 && (\n          <div style={{\n            padding: '1rem 1.5rem',\n            borderTop: '1px solid #f3f4f6',\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          }}>\n            <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n              Page {currentPage} of {totalPages}\n            </div>\n            <div style={{ display: 'flex', gap: '0.5rem' }}>\n              <button\n                onClick={() => handlePageChange(currentPage - 1)}\n                disabled={currentPage === 1}\n                style={{\n                  background: currentPage === 1 ? '#f3f4f6' : '#2d5016',\n                  color: currentPage === 1 ? '#9ca3af' : 'white',\n                  border: 'none',\n                  borderRadius: '6px',\n                  padding: '0.5rem 1rem',\n                  cursor: currentPage === 1 ? 'not-allowed' : 'pointer',\n                  fontSize: '0.875rem'\n                }}\n              >\n                Previous\n              </button>\n              <button\n                onClick={() => handlePageChange(currentPage + 1)}\n                disabled={currentPage === totalPages}\n                style={{\n                  background: currentPage === totalPages ? '#f3f4f6' : '#2d5016',\n                  color: currentPage === totalPages ? '#9ca3af' : 'white',\n                  border: 'none',\n                  borderRadius: '6px',\n                  padding: '0.5rem 1rem',\n                  cursor: currentPage === totalPages ? 'not-allowed' : 'pointer',\n                  fontSize: '0.875rem'\n                }}\n              >\n                Next\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Create Student Modal */}\n      {showCreateModal && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000,\n          padding: '1rem'\n        }}>\n          <div style={{\n            background: 'white',\n            borderRadius: '16px',\n            padding: '2rem',\n            width: '95%',\n            maxWidth: '1200px',\n            maxHeight: '95vh',\n            overflow: 'hidden',\n            display: 'flex',\n            flexDirection: 'column'\n          }}>\n            <div style={{\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: '2rem'\n            }}>\n              <h2 style={{\n                margin: 0,\n                color: '#2d5016',\n                fontSize: '1.5rem',\n                fontWeight: '700'\n              }}>\n                Create New Student Account\n              </h2>\n              <button\n                onClick={() => {\n                  setShowCreateModal(false);\n                  resetForm();\n                }}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  fontSize: '1.5rem',\n                  cursor: 'pointer',\n                  color: '#6b7280'\n                }}\n              >\n                ×\n              </button>\n            </div>\n\n            {error && (\n              <div style={{\n                background: '#fef2f2',\n                border: '1px solid #fecaca',\n                borderRadius: '8px',\n                padding: '1rem',\n                marginBottom: '1rem',\n                color: '#dc2626'\n              }}>\n                {error}\n              </div>\n            )}\n\n            <div style={{ flex: 1, overflow: 'auto', paddingRight: '0.5rem' }}>\n              <form onSubmit={(e) => { e.preventDefault(); handleCreateStudent(); }} style={{\n                display: 'grid',\n                gridTemplateColumns: '1fr 1fr',\n                gap: '2rem',\n                height: 'fit-content'\n              }}>\n                {/* Left Column */}\n                <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n                  {/* Profile Picture Upload */}\n                  <div>\n                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>\n                      Profile Picture\n                    </label>\n                    <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>\n                      {profilePicturePreview ? (\n                        <img\n                          src={profilePicturePreview}\n                          alt=\"Profile preview\"\n                          style={{\n                            width: '80px',\n                            height: '80px',\n                            borderRadius: '50%',\n                            objectFit: 'cover',\n                            border: '2px solid #e8f5e8'\n                          }}\n                          onError={(e) => {\n                            console.error('Profile picture preview failed to load:', profilePicturePreview);\n                            const target = e.target as HTMLImageElement;\n                            target.style.display = 'none';\n                            const parent = target.parentElement;\n                            if (parent) {\n                              parent.innerHTML = `\n                                <div style=\"\n                                  width: 80px;\n                                  height: 80px;\n                                  border-radius: 50%;\n                                  background-color: #f3f4f6;\n                                  display: flex;\n                                  align-items: center;\n                                  justify-content: center;\n                                  color: #6b7280;\n                                  font-size: 0.875rem;\n                                \">\n                                  No Photo\n                                </div>\n                              `;\n                            }\n                          }}\n                        />\n                      ) : (\n                        <div style={{\n                          width: '80px',\n                          height: '80px',\n                          borderRadius: '50%',\n                          backgroundColor: '#f3f4f6',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          color: '#6b7280',\n                          fontSize: '0.875rem'\n                        }}>\n                          No Photo\n                        </div>\n                      )}\n                      <div style={{ flex: 1 }}>\n                        <input\n                          type=\"file\"\n                          accept=\"image/*\"\n                          onChange={handleProfilePictureChange}\n                          style={{\n                            width: '100%',\n                            padding: '0.5rem',\n                            border: '1px solid #d1d5db',\n                            borderRadius: '8px',\n                            fontSize: '0.875rem'\n                          }}\n                        />\n                        {profilePicturePreview && (\n                          <button\n                            type=\"button\"\n                            onClick={removeProfilePictureLocal}\n                            style={{\n                              marginTop: '0.5rem',\n                              padding: '0.25rem 0.5rem',\n                              backgroundColor: '#ef4444',\n                              color: 'white',\n                              border: 'none',\n                              borderRadius: '4px',\n                              fontSize: '0.75rem',\n                              cursor: 'pointer'\n                            }}\n                          >\n                            Remove\n                          </button>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Student Number */}\n                  <div>\n                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>\n                      Student Number *\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"studentNumber\"\n                      value={formData.studentNumber}\n                      onChange={handleInputChange}\n                      required\n                      style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #d1d5db',\n                        borderRadius: '8px',\n                        fontSize: '1rem'\n                      }}\n                      placeholder=\"e.g., 2025-0001\"\n                    />\n                  </div>\n\n                  {/* Email */}\n                  <div>\n                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>\n                      Email Address * (Auto-generated)\n                    </label>\n                    <input\n                      type=\"email\"\n                      name=\"email\"\n                      value={formData.email}\n                      readOnly\n                      required\n                      style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #d1d5db',\n                        borderRadius: '8px',\n                        fontSize: '1rem',\n                        backgroundColor: '#f9fafb',\n                        color: '#6b7280'\n                      }}\n                      placeholder=\"Email will be auto-generated based on student details\"\n                    />\n                  </div>\n\n                {/* Name Fields */}\n                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>\n                  {/* First Name */}\n                  <div>\n                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>\n                      First Name *\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"firstName\"\n                      value={formData.firstName}\n                      onChange={handleInputChange}\n                      required\n                      style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #d1d5db',\n                        borderRadius: '8px',\n                        fontSize: '1rem'\n                      }}\n                      placeholder=\"Juan\"\n                    />\n                  </div>\n\n                  {/* Last Name */}\n                  <div>\n                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>\n                      Last Name *\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"lastName\"\n                      value={formData.lastName}\n                      onChange={handleInputChange}\n                      required\n                      style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #d1d5db',\n                        borderRadius: '8px',\n                        fontSize: '1rem'\n                      }}\n                      placeholder=\"Cruz\"\n                    />\n                  </div>\n                </div>\n\n                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>\n                  {/* Middle Name */}\n                  <div>\n                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>\n                      Middle Name\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"middleName\"\n                      value={formData.middleName}\n                      onChange={handleInputChange}\n                      style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #d1d5db',\n                        borderRadius: '8px',\n                        fontSize: '1rem'\n                      }}\n                      placeholder=\"Dela\"\n                    />\n                  </div>\n\n                  {/* Suffix */}\n                  <div>\n                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>\n                      Suffix\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"suffix\"\n                      value={formData.suffix}\n                      onChange={handleInputChange}\n                      style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #d1d5db',\n                        borderRadius: '8px',\n                        fontSize: '1rem'\n                      }}\n                      placeholder=\"Jr., Sr., III\"\n                    />\n                  </div>\n                  </div>\n                </div>\n\n                {/* Right Column */}\n                <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n                  {/* Phone Number */}\n                  <div>\n                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>\n                      Phone Number *\n                    </label>\n                    <input\n                      type=\"tel\"\n                      name=\"phoneNumber\"\n                      value={formData.phoneNumber}\n                      onChange={handleInputChange}\n                      onInput={(e) => {\n                        // Allow only numbers\n                        e.currentTarget.value = e.currentTarget.value.replace(/[^0-9]/g, '');\n                      }}\n                      required\n                      style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #d1d5db',\n                        borderRadius: '8px',\n                        fontSize: '1rem'\n                      }}\n                      placeholder=\"09123456789\"\n                      maxLength={11}\n                    />\n                  </div>\n\n                  {/* Grade Level */}\n                  <div>\n                    <label\n                      style={{\n                        display: 'block',\n                        marginBottom: '0.5rem',\n                        fontWeight: '600',\n                        color: '#374151'\n                      }}\n                    >\n                      Grade Level *\n                    </label>\n                    <select\n                      name=\"gradeLevel\"\n                      value={formData.gradeLevel}\n                      onChange={handleInputChange}\n                      required\n                      disabled={permissions.isProfessor && getAvailableGradeLevels().length === 1} // Disable for professor if only one option\n                      style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #d1d5db',\n                        borderRadius: '8px',\n                        fontSize: '1rem',\n                        backgroundColor: 'white'\n                      }}\n                    >\n                      {getAvailableGradeLevels().map((grade) => (\n                        <option key={grade} value={grade}>\n                          Grade {grade}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n\n                  {/* Section */}\n                  <div>\n                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>\n                      Section *\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"section\"\n                      value={formData.section}\n                      onChange={handleInputChange}\n                      required\n                      style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #d1d5db',\n                        borderRadius: '8px',\n                        fontSize: '1rem'\n                      }}\n                      placeholder=\"A, B, C, etc.\"\n                    />\n                  </div>\n\n                  {/* Parent/Guardian Name */}\n                  <div>\n                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>\n                      Parent/Guardian Name\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"parentGuardianName\"\n                      value={formData.parentGuardianName}\n                      onChange={handleInputChange}\n                      style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #d1d5db',\n                        borderRadius: '8px',\n                        fontSize: '1rem'\n                      }}\n                      placeholder=\"Parent/Guardian Name\"\n                    />\n                  </div>\n\n                  {/* Parent/Guardian Phone */}\n                  <div>\n                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>\n                      Parent/Guardian Phone\n                    </label>\n                    <input\n                      type=\"tel\"\n                      name=\"parentGuardianPhone\"\n                      value={formData.parentGuardianPhone}\n                      onChange={handleInputChange}\n                      onInput={(e) => {\n                        // Allow only numbers\n                        e.currentTarget.value = e.currentTarget.value.replace(/[^0-9]/g, '');\n                      }}\n                      style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #d1d5db',\n                        borderRadius: '8px',\n                        fontSize: '1rem'\n                      }}\n                      placeholder=\"09123456789\"\n                      maxLength={11}\n                    />\n                  </div>\n\n                  {/* Address */}\n                  <div>\n                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>\n                      Address\n                    </label>\n                    <textarea\n                      name=\"address\"\n                      value={formData.address}\n                      onChange={handleInputChange}\n                      rows={4}\n                      style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #d1d5db',\n                        borderRadius: '8px',\n                        fontSize: '1rem',\n                        resize: 'vertical'\n                      }}\n                      placeholder=\"Complete address\"\n                    />\n                  </div>\n\n                  {/* Default Password Info */}\n                  <div style={{\n                    background: '#f0f9ff',\n                    border: '1px solid #bae6fd',\n                    borderRadius: '8px',\n                    padding: '1rem'\n                  }}>\n                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>\n                      <Info size={20} color=\"#0369a1\" />\n                      <span style={{ fontWeight: '600', color: '#0369a1' }}>Default Login Credentials</span>\n                    </div>\n                    <p style={{ margin: 0, color: '#0369a1', fontSize: '0.875rem' }}>\n                      The student account will be created with the default password: <strong>Student123</strong>\n                      <br />\n                      Please share these credentials with the student and ask them to change the password on first login.\n                    </p>\n                  </div>\n                </div>\n\n                {/* Form Actions - Spans both columns */}\n                <div style={{\n                  gridColumn: '1 / -1',\n                  display: 'flex',\n                  justifyContent: 'flex-end',\n                  gap: '1rem',\n                  marginTop: '1rem',\n                  paddingTop: '1rem',\n                  borderTop: '1px solid #e5e7eb'\n                }}>\n\n                  <button\n                    type=\"button\"\n                    onClick={() => {\n                      setShowCreateModal(false);\n                      resetForm();\n                    }}\n                    disabled={isSubmitting}\n                    style={{\n                      background: '#f3f4f6',\n                      color: '#374151',\n                      border: 'none',\n                      borderRadius: '8px',\n                      padding: '0.75rem 1.5rem',\n                      fontSize: '1rem',\n                      cursor: isSubmitting ? 'not-allowed' : 'pointer',\n                      opacity: isSubmitting ? 0.6 : 1\n                    }}\n                  >\n                    Cancel\n                  </button>\n                  <button\n                    type=\"submit\"\n                    disabled={isSubmitting}\n                    style={{\n                      background: isSubmitting ? '#9ca3af' : 'linear-gradient(135deg, #2d5016 0%, #4ade80 100%)',\n                      color: 'white',\n                      border: 'none',\n                      borderRadius: '8px',\n                      padding: '0.75rem 1.5rem',\n                      fontSize: '1rem',\n                      cursor: isSubmitting ? 'not-allowed' : 'pointer',\n                      fontWeight: '600'\n                    }}\n                  >\n                    {isSubmitting ? 'Creating...' : 'Create Student Account'}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Edit Student Modal */}\n      {showEditModal && selectedStudent && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        }}>\n          <div style={{\n            background: 'white',\n            borderRadius: '16px',\n            padding: '2rem',\n            width: '95%',\n            maxWidth: '1200px',\n            maxHeight: '95vh',\n            overflow: 'hidden',\n            display: 'flex',\n            flexDirection: 'column'\n          }}>\n            <div style={{\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: '2rem'\n            }}>\n              <h2 style={{\n                margin: 0,\n                color: '#2d5016',\n                fontSize: '1.5rem',\n                fontWeight: '700'\n              }}>\n                Edit Student: {selectedStudent.profile.full_name}\n              </h2>\n              <button\n                onClick={() => {\n                  setShowEditModal(false);\n                  setSelectedStudent(null);\n                  resetForm();\n                }}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  fontSize: '1.5rem',\n                  cursor: 'pointer',\n                  color: '#6b7280'\n                }}\n              >\n                ×\n              </button>\n            </div>\n\n            {error && (\n              <div style={{\n                background: '#fef2f2',\n                border: '1px solid #fecaca',\n                borderRadius: '8px',\n                padding: '1rem',\n                marginBottom: '1rem',\n                color: '#dc2626'\n              }}>\n                {error}\n              </div>\n            )}\n\n            <div style={{ flex: 1, overflow: 'auto', paddingRight: '0.5rem' }}>\n              <form onSubmit={(e) => { e.preventDefault(); handleUpdateStudent(); }} style={{\n                display: 'grid',\n                gridTemplateColumns: '1fr 1fr',\n                gap: '2rem',\n                height: 'fit-content'\n              }}>\n                {/* Left Column */}\n                <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n                  {/* Profile Picture Upload */}\n                  <div>\n                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>\n                      Profile Picture\n                    </label>\n                    <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>\n                      {profilePicturePreview ? (\n                        <img\n                          src={profilePicturePreview}\n                          alt=\"Profile preview\"\n                          style={{\n                            width: '80px',\n                            height: '80px',\n                            borderRadius: '50%',\n                            objectFit: 'cover',\n                            border: '2px solid #e8f5e8'\n                          }}\n                          onError={(e) => {\n                            console.error('Edit modal profile picture preview failed to load:', profilePicturePreview);\n                            const target = e.target as HTMLImageElement;\n                            target.style.display = 'none';\n                            const parent = target.parentElement;\n                            if (parent) {\n                              parent.innerHTML = `\n                                <div style=\"\n                                  width: 80px;\n                                  height: 80px;\n                                  border-radius: 50%;\n                                  background-color: #f3f4f6;\n                                  display: flex;\n                                  align-items: center;\n                                  justify-content: center;\n                                  color: #6b7280;\n                                  font-size: 0.875rem;\n                                \">\n                                  No Photo\n                                </div>\n                              `;\n                            }\n                          }}\n                        />\n                      ) : (\n                        <div style={{\n                          width: '80px',\n                          height: '80px',\n                          borderRadius: '50%',\n                          backgroundColor: '#f3f4f6',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          color: '#6b7280',\n                          fontSize: '0.875rem'\n                        }}>\n                          No Photo\n                        </div>\n                      )}\n                      <div style={{ flex: 1 }}>\n                        <input\n                          type=\"file\"\n                          accept=\"image/*\"\n                          onChange={handleProfilePictureChange}\n                          style={{\n                            width: '100%',\n                            padding: '0.5rem',\n                            border: '1px solid #d1d5db',\n                            borderRadius: '8px',\n                            fontSize: '0.875rem'\n                          }}\n                        />\n                        <div style={{ display: 'flex', gap: '0.5rem', marginTop: '0.5rem' }}>\n                          {profilePicturePreview && (\n                            <button\n                              type=\"button\"\n                              onClick={removeProfilePicture}\n                              style={{\n                                padding: '0.25rem 0.5rem',\n                                backgroundColor: '#ef4444',\n                                color: 'white',\n                                border: 'none',\n                                borderRadius: '4px',\n                                fontSize: '0.75rem',\n                                cursor: 'pointer'\n                              }}\n                            >\n                              Remove Picture\n                            </button>\n                          )}\n                          {/* {selectedStudent?.profile.profile_picture && !profilePictureFile && (\n                            <button\n                              type=\"button\"\n                              onClick={removeProfilePicture}\n                              style={{\n                                padding: '0.25rem 0.5rem',\n                                backgroundColor: '#dc2626',\n                                color: 'white',\n                                border: 'none',\n                                borderRadius: '4px',\n                                fontSize: '0.75rem',\n                                cursor: 'pointer'\n                              }}\n                            >\n                              Remove Current Picture\n                            </button>\n                          )} */}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Student Number */}\n                  <div>\n                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>\n                      Student Number *\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"studentNumber\"\n                      value={formData.studentNumber}\n                      onChange={handleInputChange}\n                      required\n                      style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #d1d5db',\n                        borderRadius: '8px',\n                        fontSize: '1rem'\n                      }}\n                      placeholder=\"e.g., 2025-0001\"\n                    />\n                  </div>\n\n                  {/* Email */}\n                  <div>\n                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>\n                      Email Address * (Auto-generated)\n                    </label>\n                    <input\n                      type=\"email\"\n                      name=\"email\"\n                      value={formData.email}\n                      readOnly\n                      required\n                      style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #d1d5db',\n                        borderRadius: '8px',\n                        fontSize: '1rem',\n                        backgroundColor: '#f9fafb',\n                        color: '#6b7280'\n                      }}\n                    />\n                  </div>\n\n                  {/* First Name */}\n                  <div>\n                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>\n                      First Name *\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"firstName\"\n                      value={formData.firstName}\n                      onChange={handleInputChange}\n                      required\n                      style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #d1d5db',\n                        borderRadius: '8px',\n                        fontSize: '1rem'\n                      }}\n                      placeholder=\"Juan\"\n                    />\n                  </div>\n\n                  {/* Last Name */}\n                  <div>\n                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>\n                      Last Name *\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"lastName\"\n                      value={formData.lastName}\n                      onChange={handleInputChange}\n                      required\n                      style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #d1d5db',\n                        borderRadius: '8px',\n                        fontSize: '1rem'\n                      }}\n                      placeholder=\"Cruz\"\n                    />\n                  </div>\n\n                  {/* Middle Name */}\n                  <div>\n                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>\n                      Middle Name\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"middleName\"\n                      value={formData.middleName}\n                      onChange={handleInputChange}\n                      style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #d1d5db',\n                        borderRadius: '8px',\n                        fontSize: '1rem'\n                      }}\n                      placeholder=\"Dela\"\n                    />\n                  </div>\n\n                  {/* Suffix */}\n                  <div>\n                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>\n                      Suffix\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"suffix\"\n                      value={formData.suffix}\n                      onChange={handleInputChange}\n                      style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #d1d5db',\n                        borderRadius: '8px',\n                        fontSize: '1rem'\n                      }}\n                      placeholder=\"Jr., Sr., III\"\n                    />\n                  </div>\n                </div>\n\n                {/* Right Column */}\n                <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n                  {/* Phone Number */}\n                  <div>\n                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>\n                      Phone Number *\n                    </label>\n                    <input\n                      type=\"tel\"\n                      name=\"phoneNumber\"\n                      value={formData.phoneNumber}\n                      onChange={handleInputChange}\n                      onInput={(e) => {\n                        // Allow only numbers\n                        e.currentTarget.value = e.currentTarget.value.replace(/[^0-9]/g, '');\n                      }}\n                      required\n                      style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #d1d5db',\n                        borderRadius: '8px',\n                        fontSize: '1rem'\n                      }}\n                      maxLength={11}\n                    />\n                  </div>\n\n                  {/* Grade Level */}\n                  <div>\n                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>\n                      Grade Level *\n                    </label>\n                    <select\n                      name=\"gradeLevel\"\n                      value={formData.gradeLevel}\n                      onChange={handleInputChange}\n                      required\n                      style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #d1d5db',\n                        borderRadius: '8px',\n                        fontSize: '1rem',\n                        backgroundColor: 'white'\n                      }}\n                    >\n                      {getAvailableGradeLevels().map(grade => (\n                        <option key={grade} value={grade}>Grade {grade}</option>\n                      ))}\n                    </select>\n                  </div>\n\n                  {/* Section */}\n                  <div>\n                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>\n                      Section *\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"section\"\n                      value={formData.section}\n                      onChange={handleInputChange}\n                      required\n                      style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #d1d5db',\n                        borderRadius: '8px',\n                        fontSize: '1rem'\n                      }}\n                    />\n                  </div>\n\n                  {/* Parent/Guardian Name */}\n                  <div>\n                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>\n                      Parent/Guardian Name\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"parentGuardianName\"\n                      value={formData.parentGuardianName}\n                      onChange={handleInputChange}\n                      style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #d1d5db',\n                        borderRadius: '8px',\n                        fontSize: '1rem'\n                      }}\n                    />\n                  </div>\n\n                  {/* Parent/Guardian Phone */}\n                  <div>\n                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>\n                      Parent/Guardian Phone\n                    </label>\n                    <input\n                      type=\"tel\"\n                      name=\"parentGuardianPhone\"\n                      value={formData.parentGuardianPhone}\n                      onChange={handleInputChange}\n                      onInput={(e) => {\n                        // Allow only numbers\n                        e.currentTarget.value = e.currentTarget.value.replace(/[^0-9]/g, '');\n                      }}\n                      style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #d1d5db',\n                        borderRadius: '8px',\n                        fontSize: '1rem'\n                      }}\n                      maxLength={11}\n                    />\n                  </div>\n\n                  {/* Address */}\n                  <div>\n                    <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600', color: '#374151' }}>\n                      Address\n                    </label>\n                    <textarea\n                      name=\"address\"\n                      value={formData.address}\n                      onChange={handleInputChange}\n                      rows={4}\n                      style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #d1d5db',\n                        borderRadius: '8px',\n                        fontSize: '1rem',\n                        resize: 'vertical'\n                      }}\n                    />\n                  </div>\n                </div>\n\n                {/* Form Actions - Spans both columns */}\n                <div style={{\n                  gridColumn: '1 / -1',\n                  display: 'flex',\n                  justifyContent: 'flex-end',\n                  gap: '1rem',\n                  marginTop: '1rem',\n                  paddingTop: '1rem',\n                  borderTop: '1px solid #e5e7eb'\n                }}>\n                  <button\n                    type=\"button\"\n                    onClick={() => {\n                      setShowEditModal(false);\n                      setSelectedStudent(null);\n                      resetForm();\n                    }}\n                    disabled={isSubmitting}\n                    style={{\n                      background: '#f3f4f6',\n                      color: '#374151',\n                      border: 'none',\n                      borderRadius: '8px',\n                      padding: '0.75rem 1.5rem',\n                      fontSize: '1rem',\n                      cursor: isSubmitting ? 'not-allowed' : 'pointer',\n                      opacity: isSubmitting ? 0.6 : 1\n                    }}\n                  >\n                    Cancel\n                  </button>\n                  <button\n                    type=\"submit\"\n                    disabled={isSubmitting}\n                    style={{\n                      background: isSubmitting ? '#9ca3af' : 'linear-gradient(135deg, #2d5016 0%, #4ade80 100%)',\n                      color: 'white',\n                      border: 'none',\n                      borderRadius: '8px',\n                      padding: '0.75rem 1.5rem',\n                      fontSize: '1rem',\n                      cursor: isSubmitting ? 'not-allowed' : 'pointer',\n                      fontWeight: '600'\n                    }}\n                  >\n                    {isSubmitting ? 'Updating...' : 'Update Student'}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Delete Confirmation Modal */}\n      {showDeleteModal && selectedStudent && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        }}>\n          <div style={{\n            background: 'white',\n            borderRadius: '16px',\n            padding: '2rem',\n            width: '90%',\n            maxWidth: '500px'\n          }}>\n            <div style={{\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: '1.5rem'\n            }}>\n              <h2 style={{\n                margin: 0,\n                color: '#dc2626',\n                fontSize: '1.5rem',\n                fontWeight: '700'\n              }}>\n                Deactivate Student Account\n              </h2>\n              <button\n                onClick={() => {\n                  setShowDeleteModal(false);\n                  setSelectedStudent(null);\n                }}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  fontSize: '1.5rem',\n                  cursor: 'pointer',\n                  color: '#6b7280'\n                }}\n              >\n                ×\n              </button>\n            </div>\n\n            <div style={{ marginBottom: '2rem' }}>\n              <p style={{ margin: '0 0 1rem 0', color: '#374151' }}>\n                Are you sure you want to deactivate the account for:\n              </p>\n              <div style={{\n                background: '#f9fafb',\n                border: '1px solid #e5e7eb',\n                borderRadius: '8px',\n                padding: '1rem'\n              }}>\n                <p style={{ margin: 0, fontWeight: '600', color: '#2d5016' }}>\n                  {selectedStudent.profile.full_name}\n                </p>\n                <p style={{ margin: '0.25rem 0 0 0', color: '#6b7280', fontSize: '0.875rem' }}>\n                  Student Number: {selectedStudent.student_number}\n                </p>\n                <p style={{ margin: '0.25rem 0 0 0', color: '#6b7280', fontSize: '0.875rem' }}>\n                  Email: {selectedStudent.email}\n                </p>\n              </div>\n              <p style={{ margin: '1rem 0 0 0', color: '#dc2626', fontSize: '0.875rem' }}>\n                <span style={{ display: 'flex', alignItems: 'flex-start', gap: '0.5rem' }}>\n                  <AlertTriangle size={16} color=\"#dc2626\" style={{ marginTop: '0.125rem', flexShrink: 0 }} />\n                  This action will deactivate the student's account. They will not be able to log in until reactivated.\n                </span>\n              </p>\n            </div>\n\n            <div style={{\n              display: 'flex',\n              justifyContent: 'flex-end',\n              gap: '1rem'\n            }}>\n              <button\n                onClick={() => {\n                  setShowDeleteModal(false);\n                  setSelectedStudent(null);\n                }}\n                disabled={isSubmitting}\n                style={{\n                  background: '#f3f4f6',\n                  color: '#374151',\n                  border: 'none',\n                  borderRadius: '8px',\n                  padding: '0.75rem 1.5rem',\n                  fontSize: '1rem',\n                  cursor: isSubmitting ? 'not-allowed' : 'pointer',\n                  opacity: isSubmitting ? 0.6 : 1\n                }}\n              >\n                Cancel\n              </button>\n              <button\n                onClick={confirmDeleteStudent}\n                disabled={isSubmitting}\n                style={{\n                  background: isSubmitting ? '#9ca3af' : '#dc2626',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '8px',\n                  padding: '0.75rem 1.5rem',\n                  fontSize: '1rem',\n                  cursor: isSubmitting ? 'not-allowed' : 'pointer',\n                  opacity: isSubmitting ? 0.6 : 1\n                }}\n              >\n                {isSubmitting ? 'Deactivating...' : 'Deactivate Account'}\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default StudentManagement;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,cAAc,QAAyD,+BAA+B;AAC/G,SAASC,aAAa,EAAEC,SAAS,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,KAAK,QAAQ,cAAc;AAC7F,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,WAAW,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAMC,iBAA2B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxC;EACA,MAAM;IAAEC;EAAK,CAAC,GAAGT,YAAY,CAAC,CAAC;EAC/B,MAAMU,WAAW,GAAGT,cAAc,CAACQ,IAAI,CAAC;;EAExC;EACA,MAAM,CAACE,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAgC,QAAQ,CAAC;EACzF,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACmC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC;;EAErD;EACA,MAAMqC,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAIf,WAAW,CAACgB,YAAY,EAAE;MAC5B;MACA,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC;IACjB,CAAC,MAAM,IAAIjB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkB,WAAW,EAAE;MAC5B;MACA,OAAO,CAAClB,IAAI,CAACkB,WAAW,CAAC;IAC3B,CAAC,MAAM;MACL;MACA,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC;IACjB;EACF,CAAC;;EAED;EACA,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4C,eAAe,EAAEC,kBAAkB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC8C,eAAe,EAAEC,kBAAkB,CAAC,GAAG/C,QAAQ,CAAiB,IAAI,CAAC;;EAE5E;EACA,MAAM,CAACgD,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAC;IACvCkD,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE,EAAE;IACTC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,EAAE;IACVC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAEnC,WAAW,CAACoC,WAAW,IAAIrC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkB,WAAW,GAAGlB,IAAI,CAACkB,WAAW,GAAG,EAAE;IAChFoB,OAAO,EAAE,EAAE;IACXC,kBAAkB,EAAE,EAAE;IACtBC,mBAAmB,EAAE,EAAE;IACvBC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhE,QAAQ,CAAc,IAAI,CAAC;EAC/E,MAAM,CAACiE,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGlE,QAAQ,CAAgB,IAAI,CAAC;EACvF,MAAM,CAACmE,YAAY,EAAEC,eAAe,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqE,KAAK,EAAEC,QAAQ,CAAC,GAAGtE,QAAQ,CAAgB,IAAI,CAAC;;EAEvD;EACA,MAAM,CAACuE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;;EAElE;EACA,MAAMyE,YAAY,GAAGvE,WAAW,CAAC,YAAY;IAC3C,IAAI;MACFwB,UAAU,CAAC,IAAI,CAAC;MAChB4C,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAMI,MAAW,GAAG;QAClBC,IAAI,EAAE5C,WAAW;QACjB6C,KAAK,EAAE,EAAE;QACTC,MAAM,EAAEN,mBAAmB,IAAIO;MACjC,CAAC;;MAED;MACA,IAAIjD,YAAY,KAAK,QAAQ,EAAE;QAC7B6C,MAAM,CAACK,SAAS,GAAG,IAAI;MACzB,CAAC,MAAM,IAAIlD,YAAY,KAAK,UAAU,EAAE;QACtC6C,MAAM,CAACK,SAAS,GAAG,KAAK;MAC1B;MACA;;MAEA;MACA,IAAIzD,WAAW,CAACgB,YAAY,EAAE;QAC5B;QACA;MAAA,CACD,MAAM,IAAIjB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkB,WAAW,EAAE;QAC5B;QACAmC,MAAM,CAACnC,WAAW,GAAGlB,IAAI,CAACkB,WAAW;MACvC;MACA;;MAEAyC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEP,MAAM,CAAC;MACpDM,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEpD,YAAY,EAAE,kBAAkB,EAAE6C,MAAM,CAACK,SAAS,CAAC;MAEjF,MAAMG,QAAQ,GAAG,MAAM/E,cAAc,CAACgF,WAAW,CAACT,MAAM,CAAC;MACzDM,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEC,QAAQ,CAAC;MAC/CF,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEC,QAAQ,CAAC3D,QAAQ,CAAC6D,GAAG,CAACC,CAAC;QAAA,IAAAC,UAAA;QAAA,OAAK;UAC1DC,IAAI,EAAE,EAAAD,UAAA,GAAAD,CAAC,CAACG,OAAO,cAAAF,UAAA,uBAATA,UAAA,CAAWG,SAAS,KAAI,SAAS;UACvCtC,KAAK,EAAEkC,CAAC,CAAClC,KAAK;UACd4B,SAAS,EAAEM,CAAC,CAACN,SAAS;UACtBW,MAAM,EAAEL,CAAC,CAACN,SAAS,GAAG,QAAQ,GAAG;QACnC,CAAC;MAAA,CAAC,CAAC,CAAC;;MAEJ;MACAC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEC,QAAQ,CAAC3D,QAAQ,CAACoE,MAAM,EAAE,UAAU,CAAC;MAEtFnE,WAAW,CAAC0D,QAAQ,CAAC3D,QAAQ,CAAC;MAC9BW,aAAa,CAACgD,QAAQ,CAACU,UAAU,CAAC3D,UAAU,CAAC;MAC7CG,gBAAgB,CAAC8C,QAAQ,CAACU,UAAU,CAACC,KAAK,CAAC;IAC7C,CAAC,CAAC,OAAOxB,KAAU,EAAE;MACnBW,OAAO,CAACX,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAAC,kEAAkE,CAAC;MAC5E9C,WAAW,CAAC,EAAE,CAAC;IACjB,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACK,WAAW,EAAEwC,mBAAmB,EAAE1C,YAAY,EAAER,IAAI,CAAC,CAAC;;EAE1D;EACApB,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACqB,WAAW,CAACwE,eAAe,EAAE;MAChCxB,QAAQ,CAAC,wDAAwD,CAAC;MAClE5C,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEA+C,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACA,YAAY,EAAEnD,WAAW,CAACwE,eAAe,CAAC,CAAC;;EAE/C;EACA7F,SAAS,CAAC,MAAM;IACd,MAAM8F,SAAS,GAAGC,UAAU,CAAC,MAAM;MACjCxB,sBAAsB,CAAC7C,UAAU,CAAC;MAClCK,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAMiE,YAAY,CAACF,SAAS,CAAC;EACtC,CAAC,EAAE,CAACpE,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMuE,cAAc,GAAIC,QAAiB,IAAK;IAC5C,OAAOA,QAAQ,GAAG,SAAS,GAAG,SAAS;EACzC,CAAC;EAED,MAAMC,aAAa,GAAID,QAAiB,IAAK;IAC3C,OAAOA,QAAQ,GAAG,QAAQ,GAAG,UAAU;EACzC,CAAC;;EAED;EACA,MAAME,kBAAkB,GAAIC,CAAsC,IAAK;IACrE1E,aAAa,CAAC0E,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAACvD,aAAqB,EAAEO,UAAkB,EAAEE,OAAe,EAAEL,QAAgB,EAAEF,SAAiB,EAAEC,UAAkB,KAAK;IAC7I,IAAI,CAACH,aAAa,IAAI,CAACO,UAAU,IAAI,CAACE,OAAO,IAAI,CAACL,QAAQ,IAAI,CAACF,SAAS,EAAE;MACxE,OAAO,EAAE;IACX;IAEA,MAAMsD,WAAW,GAAGtD,SAAS,CAACuD,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IACrD,MAAMC,aAAa,GAAGxD,UAAU,GAAGA,UAAU,CAACsD,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG,EAAE;IAC1E,MAAME,aAAa,GAAGxD,QAAQ,CAACyD,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;IAChE,MAAMC,YAAY,GAAGtD,OAAO,CAACoD,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACH,WAAW,CAAC,CAAC;IAE9D,OAAO,GAAG1D,aAAa,IAAIO,UAAU,IAAIwD,YAAY,IAAIH,aAAa,IAAIJ,WAAW,IAAIG,aAAa,YAAY;EACpH,CAAC;;EAED;EACA,MAAMK,iBAAiB,GAAIZ,CAAgF,IAAK;IAC9G,MAAM;MAAEf,IAAI;MAAEiB;IAAM,CAAC,GAAGF,CAAC,CAACC,MAAM;IAChC,MAAMY,WAAW,GAAG;MAClB,GAAGnE,QAAQ;MACX,CAACuC,IAAI,GAAGA,IAAI,KAAK,YAAY,GAAG6B,QAAQ,CAACZ,KAAK,CAAC,GAAGA;IACpD,CAAC;;IAED;IACA,IAAI,CAAC,eAAe,EAAE,YAAY,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,CAAC,CAACa,QAAQ,CAAC9B,IAAI,CAAC,EAAE;MACpG,MAAM+B,cAAc,GAAGb,aAAa,CAClCU,WAAW,CAACjE,aAAa,EACzBiE,WAAW,CAAC1D,UAAU,CAAC8D,QAAQ,CAAC,CAAC,EACjCJ,WAAW,CAACxD,OAAO,EACnBwD,WAAW,CAAC7D,QAAQ,EACpB6D,WAAW,CAAC/D,SAAS,EACrB+D,WAAW,CAAC9D,UACd,CAAC;MACD,IAAIiE,cAAc,EAAE;QAClBH,WAAW,CAAChE,KAAK,GAAGmE,cAAc;MACpC;IACF;IAEArE,WAAW,CAACkE,WAAW,CAAC;EAC1B,CAAC;EAED,MAAMK,SAAS,GAAGA,CAAA,KAAM;IACtBvE,WAAW,CAAC;MACVC,aAAa,EAAE,EAAE;MACjBC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,EAAE;MACbC,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,EAAE;MACVC,WAAW,EAAE,EAAE;MACfC,UAAU,EAAEnC,WAAW,CAACoC,WAAW,IAAIrC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkB,WAAW,GAAGlB,IAAI,CAACkB,WAAW,GAAG,EAAE;MAChFoB,OAAO,EAAE,EAAE;MACXC,kBAAkB,EAAE,EAAE;MACtBC,mBAAmB,EAAE,EAAE;MACvBC,OAAO,EAAE;IACX,CAAC,CAAC;IACFE,qBAAqB,CAAC,IAAI,CAAC;IAC3BE,wBAAwB,CAAC,IAAI,CAAC;EAChC,CAAC;;EAED;EACA,MAAMuD,0BAA0B,GAAInB,CAAsC,IAAK;IAAA,IAAAoB,eAAA;IAC7E,MAAMC,IAAI,IAAAD,eAAA,GAAGpB,CAAC,CAACC,MAAM,CAACqB,KAAK,cAAAF,eAAA,uBAAdA,eAAA,CAAiB,CAAC,CAAC;IAChC,IAAIC,IAAI,EAAE;MACR;MACA,IAAI,CAACA,IAAI,CAACE,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;QACnCC,KAAK,CAAC,6BAA6B,CAAC;QACpC;MACF;;MAEA;MACA,IAAIJ,IAAI,CAACK,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;QAC/BD,KAAK,CAAC,iCAAiC,CAAC;QACxC;MACF;MAEA/D,qBAAqB,CAAC2D,IAAI,CAAC;;MAE3B;MACA,MAAMM,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAI7B,CAAC,IAAK;QAAA,IAAA8B,SAAA;QACrBlE,wBAAwB,EAAAkE,SAAA,GAAC9B,CAAC,CAACC,MAAM,cAAA6B,SAAA,uBAARA,SAAA,CAAUC,MAAgB,CAAC;MACtD,CAAC;MACDJ,MAAM,CAACK,aAAa,CAACX,IAAI,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMY,yBAAyB,GAAGA,CAAA,KAAM;IACtCvE,qBAAqB,CAAC,IAAI,CAAC;IAC3BE,wBAAwB,CAAC,IAAI,CAAC;EAChC,CAAC;;EAED;EACA,MAAMsE,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF;MACA,IAAI1F,eAAe,IAAIA,eAAe,CAAC0C,OAAO,CAACiD,eAAe,EAAE;QAC9D;QACA,MAAMC,SAAS,GAAGC,MAAM,CAACC,OAAO,CAC9B,mCAAmC9F,eAAe,CAAC0C,OAAO,CAACC,SAAS,mDACtE,CAAC;QAED,IAAI,CAACiD,SAAS,EAAE;UACd;QACF;QAEAhH,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMmH,cAAc,GAAG,MAAM1I,cAAc,CAAC2I,2BAA2B,CAAChG,eAAe,CAACiG,UAAU,CAACxB,QAAQ,CAAC,CAAC,CAAC;;QAE9G;QACAxE,kBAAkB,CAAC8F,cAAc,CAAC;;QAElC;QACA,MAAMpE,YAAY,CAAC,CAAC;QAEpBsD,KAAK,CAAC,uCAAuC,CAAC;MAChD;;MAEA;MACA/D,qBAAqB,CAAC,IAAI,CAAC;MAC3BE,wBAAwB,CAAC,IAAI,CAAC;IAChC,CAAC,CAAC,OAAOG,KAAU,EAAE;MACnBW,OAAO,CAACX,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD0D,KAAK,CAAC,qCAAqC1D,KAAK,CAAC2E,OAAO,EAAE,CAAC;IAC7D,CAAC,SAAS;MACRtH,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMuH,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC7E,eAAe,CAAC,IAAI,CAAC;IACrBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF;MACA,IAAI,CAACtB,QAAQ,CAACE,aAAa,IAAI,CAACF,QAAQ,CAACG,KAAK,IAAI,CAACH,QAAQ,CAACI,SAAS,IAAI,CAACJ,QAAQ,CAACM,QAAQ,IAAI,CAACN,QAAQ,CAACQ,WAAW,IAAI,CAACR,QAAQ,CAACW,OAAO,IAAI,CAACX,QAAQ,CAACS,UAAU,EAAE;QACjK,MAAM,IAAIyF,KAAK,CAAC,gHAAgH,CAAC;MACnI;;MAEA;MACA,MAAMC,UAAU,GAAG,4BAA4B;MAC/C,IAAI,CAACA,UAAU,CAACC,IAAI,CAACpG,QAAQ,CAACG,KAAK,CAAC,EAAE;QACpC,MAAM,IAAI+F,KAAK,CAAC,oCAAoC,CAAC;MACvD;;MAEA;MACA,MAAMG,WAAiC,GAAG;QACxC;QACAC,cAAc,EAAEtG,QAAQ,CAACE,aAAa;QACtCC,KAAK,EAAEH,QAAQ,CAACG,KAAK;QACrBoG,QAAQ,EAAE,YAAY;QAAE;QACxBxE,SAAS,EAAE,IAAI;QACfyE,UAAU,EAAE,CAAAnI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoI,EAAE,KAAI,CAAC;QAAE;;QAE3B;QACAC,UAAU,EAAE1G,QAAQ,CAACI,SAAS;QAC9BuG,WAAW,EAAE3G,QAAQ,CAACK,UAAU,IAAIyB,SAAS;QAC7C8E,SAAS,EAAE5G,QAAQ,CAACM,QAAQ;QAC5BC,MAAM,EAAEP,QAAQ,CAACO,MAAM,IAAIuB,SAAS;QACpC+E,YAAY,EAAE7G,QAAQ,CAACQ,WAAW;QAClCjB,WAAW,EAAES,QAAQ,CAACS,UAAU;QAChCE,OAAO,EAAEX,QAAQ,CAACW,OAAO;QACzBmG,oBAAoB,EAAE9G,QAAQ,CAACY,kBAAkB,IAAIkB,SAAS;QAC9DiF,qBAAqB,EAAE/G,QAAQ,CAACa,mBAAmB,IAAIiB,SAAS;QAChEhB,OAAO,EAAEd,QAAQ,CAACc,OAAO,IAAIgB;MAC/B,CAAC;;MAED;MACAE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEoE,WAAW,CAAC;;MAEjD;MACA,MAAMW,cAAc,GAAG,MAAM7J,cAAc,CAAC8J,aAAa,CAACZ,WAAW,CAAC;;MAEtE;MACA,IAAItF,kBAAkB,EAAE;QACtB,IAAI;UACF,MAAM5D,cAAc,CAAC+J,2BAA2B,CAACF,cAAc,CAACjB,UAAU,CAACxB,QAAQ,CAAC,CAAC,EAAExD,kBAAkB,CAAC;QAC5G,CAAC,CAAC,OAAOoG,YAAiB,EAAE;UAC1BnF,OAAO,CAACX,KAAK,CAAC,kCAAkC,EAAE8F,YAAY,CAAC;UAC/D;UACApC,KAAK,CAAC,4EAA4EoC,YAAY,CAACnB,OAAO,EAAE,CAAC;QAC3G;MACF;MAEAjB,KAAK,CAAC,oEAAoEiC,cAAc,CAACxE,OAAO,CAACC,SAAS,qBAAqBuE,cAAc,CAACV,cAAc,YAAYU,cAAc,CAAC7G,KAAK,kCAAkC6G,cAAc,CAAC7G,KAAK,+HAA+H,CAAC;MAElXqE,SAAS,CAAC,CAAC;MACX/E,kBAAkB,CAAC,KAAK,CAAC;;MAEzB;MACA,MAAMgC,YAAY,CAAC,CAAC;IAEtB,CAAC,CAAC,OAAOJ,KAAU,EAAE;MACnBW,OAAO,CAACX,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAACD,KAAK,CAAC2E,OAAO,IAAI,qDAAqD,CAAC;IAClF,CAAC,SAAS;MACR5E,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMgG,iBAAiB,GAAIC,OAAgB,IAAK;IAC9CtH,kBAAkB,CAACsH,OAAO,CAAC;IAC3BpH,WAAW,CAAC;MACVC,aAAa,EAAEmH,OAAO,CAACf,cAAc;MACrCnG,KAAK,EAAEkH,OAAO,CAAClH,KAAK;MACpBC,SAAS,EAAEiH,OAAO,CAAC7E,OAAO,CAACkE,UAAU;MACrCrG,UAAU,EAAEgH,OAAO,CAAC7E,OAAO,CAACmE,WAAW,IAAI,EAAE;MAC7CrG,QAAQ,EAAE+G,OAAO,CAAC7E,OAAO,CAACoE,SAAS;MACnCrG,MAAM,EAAE8G,OAAO,CAAC7E,OAAO,CAACjC,MAAM,IAAI,EAAE;MACpCC,WAAW,EAAE6G,OAAO,CAAC7E,OAAO,CAACqE,YAAY;MACzCpG,UAAU,EAAE4G,OAAO,CAAC7E,OAAO,CAACjD,WAAW;MACvCoB,OAAO,EAAE0G,OAAO,CAAC7E,OAAO,CAAC7B,OAAO;MAChCC,kBAAkB,EAAEyG,OAAO,CAAC7E,OAAO,CAACsE,oBAAoB,IAAI,EAAE;MAC9DjG,mBAAmB,EAAEwG,OAAO,CAAC7E,OAAO,CAACuE,qBAAqB,IAAI,EAAE;MAChEjG,OAAO,EAAEuG,OAAO,CAAC7E,OAAO,CAAC1B,OAAO,IAAI;IACtC,CAAC,CAAC;;IAEF;IACAE,qBAAqB,CAAC,IAAI,CAAC;IAC3BE,wBAAwB,CAACmG,OAAO,CAAC7E,OAAO,CAACiD,eAAe,GAAG3H,WAAW,CAACuJ,OAAO,CAAC7E,OAAO,CAACiD,eAAe,CAAC,GAAG,IAAI,CAAC;IAE/G9F,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAM2H,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI,CAACxH,eAAe,EAAE;IAEtBsB,eAAe,CAAC,IAAI,CAAC;IACrBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF;MACA,IAAI,CAACtB,QAAQ,CAACE,aAAa,IAAI,CAACF,QAAQ,CAACG,KAAK,IAAI,CAACH,QAAQ,CAACI,SAAS,IAAI,CAACJ,QAAQ,CAACM,QAAQ,IAAI,CAACN,QAAQ,CAACQ,WAAW,IAAI,CAACR,QAAQ,CAACW,OAAO,EAAE;QACzI,MAAM,IAAIuF,KAAK,CAAC,mGAAmG,CAAC;MACtH;;MAEA;MACA,MAAMC,UAAU,GAAG,4BAA4B;MAC/C,IAAI,CAACA,UAAU,CAACC,IAAI,CAACpG,QAAQ,CAACG,KAAK,CAAC,EAAE;QACpC,MAAM,IAAI+F,KAAK,CAAC,oCAAoC,CAAC;MACvD;;MAEA;MACA,MAAMqB,UAAyC,GAAG;QAChDjB,cAAc,EAAEtG,QAAQ,CAACE,aAAa;QACtCC,KAAK,EAAEH,QAAQ,CAACG,KAAK;QACrBuG,UAAU,EAAE1G,QAAQ,CAACI,SAAS;QAC9BuG,WAAW,EAAE3G,QAAQ,CAACK,UAAU,IAAIyB,SAAS;QAC7C8E,SAAS,EAAE5G,QAAQ,CAACM,QAAQ;QAC5BC,MAAM,EAAEP,QAAQ,CAACO,MAAM,IAAIuB,SAAS;QACpC+E,YAAY,EAAE7G,QAAQ,CAACQ,WAAW;QAClCjB,WAAW,EAAES,QAAQ,CAACS,UAAU;QAChCE,OAAO,EAAEX,QAAQ,CAACW,OAAO;QACzBmG,oBAAoB,EAAE9G,QAAQ,CAACY,kBAAkB,IAAIkB,SAAS;QAC9DiF,qBAAqB,EAAE/G,QAAQ,CAACa,mBAAmB,IAAIiB,SAAS;QAChEhB,OAAO,EAAEd,QAAQ,CAACc,OAAO,IAAIgB;MAC/B,CAAC;;MAED;MACA,MAAM3E,cAAc,CAACqK,aAAa,CAAC1H,eAAe,CAACiG,UAAU,CAACxB,QAAQ,CAAC,CAAC,EAAEgD,UAAU,CAAC;;MAErF;MACA,IAAIxG,kBAAkB,EAAE;QACtB,IAAI;UACF,MAAM5D,cAAc,CAAC+J,2BAA2B,CAACpH,eAAe,CAACiG,UAAU,CAACxB,QAAQ,CAAC,CAAC,EAAExD,kBAAkB,CAAC;QAC7G,CAAC,CAAC,OAAOoG,YAAiB,EAAE;UAC1BnF,OAAO,CAACX,KAAK,CAAC,kCAAkC,EAAE8F,YAAY,CAAC;UAC/DpC,KAAK,CAAC,gFAAgFoC,YAAY,CAACnB,OAAO,EAAE,CAAC;QAC/G;MACF;MAEAjB,KAAK,CAAC,2CAA2C,CAAC;MAElDP,SAAS,CAAC,CAAC;MACX7E,gBAAgB,CAAC,KAAK,CAAC;MACvBI,kBAAkB,CAAC,IAAI,CAAC;;MAExB;MACA,MAAM0B,YAAY,CAAC,CAAC;IAEtB,CAAC,CAAC,OAAOJ,KAAU,EAAE;MACnBW,OAAO,CAACX,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAACD,KAAK,CAAC2E,OAAO,IAAI,yDAAyD,CAAC;IACtF,CAAC,SAAS;MACR5E,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMqG,mBAAmB,GAAIJ,OAAgB,IAAK;IAChDtH,kBAAkB,CAACsH,OAAO,CAAC;IAC3BxH,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM6H,mBAAmB,GAAG,MAAOL,OAAgB,IAAK;IACtD,IAAI,CAAC1B,MAAM,CAACC,OAAO,CAAC,mDAAmDyB,OAAO,CAAC7E,OAAO,CAACC,SAAS,gDAAgD,CAAC,EAAE;MACjJ;IACF;IAEA,IAAI;MACFrB,eAAe,CAAC,IAAI,CAAC;MACrB,MAAMjE,cAAc,CAACwK,oBAAoB,CAACN,OAAO,CAACtB,UAAU,CAACxB,QAAQ,CAAC,CAAC,CAAC;;MAExE;MACAQ,KAAK,CAAC,mCAAmCsC,OAAO,CAAC7E,OAAO,CAACC,SAAS,+BAA+B,CAAC;IAEpG,CAAC,CAAC,OAAOpB,KAAU,EAAE;MACnBW,OAAO,CAACX,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD0D,KAAK,CAAC,6BAA6B1D,KAAK,CAAC2E,OAAO,EAAE,CAAC;IACrD,CAAC,SAAS;MACR5E,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMwG,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAAC9H,eAAe,EAAE;IAEtBsB,eAAe,CAAC,IAAI,CAAC;IACrBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF;MACA,MAAMnE,cAAc,CAAC0K,aAAa,CAAC/H,eAAe,CAACiG,UAAU,CAACxB,QAAQ,CAAC,CAAC,CAAC;MAEzEQ,KAAK,CAAC,oDAAoD,CAAC;MAE3DlF,kBAAkB,CAAC,KAAK,CAAC;MACzBE,kBAAkB,CAAC,IAAI,CAAC;;MAExB;MACA,MAAM0B,YAAY,CAAC,CAAC;IAEtB,CAAC,CAAC,OAAOJ,KAAU,EAAE;MACnBW,OAAO,CAACX,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAACD,KAAK,CAAC2E,OAAO,IAAI,yDAAyD,CAAC;IACtF,CAAC,SAAS;MACR5E,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAM0G,gBAAgB,GAAInG,IAAY,IAAK;IACzC3C,cAAc,CAAC2C,IAAI,CAAC;EACtB,CAAC;EAED,MAAMoG,wBAAwB,GAAIvE,KAAoC,IAAK;IACzE1E,eAAe,CAAC0E,KAAK,CAAC;IACtBxE,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC;;EAED;EACA,IAAI,CAACV,WAAW,CAACwE,eAAe,EAAE;IAChC,oBACE9E,OAAA;MAAKgK,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE,OAAO;QAClBC,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACAxK,OAAA,CAACL,KAAK;QAACqH,IAAI,EAAE,EAAG;QAACgD,KAAK,EAAE;UAAES,YAAY,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAI;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClE9K,OAAA;QAAIgK,KAAK,EAAE;UAAEe,MAAM,EAAE,YAAY;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAM,CAAE;QAAAT,QAAA,EAAC;MAE5E;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL9K,OAAA;QAAGgK,KAAK,EAAE;UAAEe,MAAM,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAR,QAAA,EAAC;MAE3C;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJ9K,OAAA;QAAKgK,KAAK,EAAE;UACVkB,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAE9K,WAAW,CAAC+K,qBAAqB,CAAC,CAAC;UAC/CC,YAAY,EAAE,KAAK;UACnBf,KAAK,EAAE,OAAO;UACdS,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE;QACd,CAAE;QAAAT,QAAA,GAAC,gBACa,EAAClK,WAAW,CAACiL,sBAAsB,CAAC,CAAC;MAAA;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE9K,OAAA;IAAAwK,QAAA,gBAEExK,OAAA;MAAKgK,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfG,cAAc,EAAE,eAAe;QAC/BD,UAAU,EAAE,QAAQ;QACpBM,YAAY,EAAE;MAChB,CAAE;MAAAD,QAAA,eACAxK,OAAA;QAAAwK,QAAA,gBACExK,OAAA;UAAIgK,KAAK,EAAE;YACTgB,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjBV,KAAK,EAAE,SAAS;YAChBQ,MAAM,EAAE;UACV,CAAE;UAAAP,QAAA,eACAxK,OAAA;YAAMgK,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEqB,GAAG,EAAE;YAAS,CAAE;YAAAhB,QAAA,gBACpExK,OAAA,CAACN,IAAI;cAACsH,IAAI,EAAE,EAAG;cAACuD,KAAK,EAAC;YAAS;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,sBAEpC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACL9K,OAAA;UAAGgK,KAAK,EAAE;YACRO,KAAK,EAAE,SAAS;YAChBQ,MAAM,EAAE,CAAC;YACTC,QAAQ,EAAE;UACZ,CAAE;UAAAR,QAAA,GACClK,WAAW,CAACgB,YAAY,GACrB,6DAA6D,GAC7DhB,WAAW,CAACoC,WAAW,GACrB,kEAAkE,GAClE,sCAAsC,EAE3CpC,WAAW,CAACgB,YAAY,iBACvBtB,OAAA;YAAMgK,KAAK,EAAE;cACXC,OAAO,EAAE,cAAc;cACvBwB,UAAU,EAAE,QAAQ;cACpBN,OAAO,EAAE,gBAAgB;cACzBO,eAAe,EAAE,SAAS;cAC1BnB,KAAK,EAAE,SAAS;cAChBe,YAAY,EAAE,UAAU;cACxBN,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE;YACd,CAAE;YAAAT,QAAA,EAAC;UAEH;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP,EACAxK,WAAW,CAACoC,WAAW,KAAIrC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,WAAW,kBAC3CvB,OAAA;YAAMgK,KAAK,EAAE;cACXC,OAAO,EAAE,cAAc;cACvBwB,UAAU,EAAE,QAAQ;cACpBN,OAAO,EAAE,gBAAgB;cACzBO,eAAe,EAAE,SAAS;cAC1BnB,KAAK,EAAE,SAAS;cAChBe,YAAY,EAAE,UAAU;cACxBN,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE;YACd,CAAE;YAAAT,QAAA,GAAC,QACK,EAACnK,IAAI,CAACkB,WAAW,EAAC,OAC1B;UAAA;YAAAoJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9K,OAAA;MAAKgK,KAAK,EAAE;QACVoB,UAAU,EAAE,OAAO;QACnBE,YAAY,EAAE,MAAM;QACpBH,OAAO,EAAE,QAAQ;QACjBV,YAAY,EAAE,MAAM;QACpBkB,SAAS,EAAE,gCAAgC;QAC3CC,MAAM,EAAE;MACV,CAAE;MAAApB,QAAA,eACAxK,OAAA;QAAKgK,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEuB,GAAG,EAAE,MAAM;UAAErB,UAAU,EAAE,QAAQ;UAAE0B,QAAQ,EAAE;QAAO,CAAE;QAAArB,QAAA,GAElFlK,WAAW,CAACwL,iBAAiB,iBAC5B9L,OAAA;UACE+L,OAAO,EAAEA,CAAA,KAAMtK,kBAAkB,CAAC,IAAI,CAAE;UACxCuK,QAAQ,EAAEvL,OAAQ;UAClBuJ,KAAK,EAAE;YACLoB,UAAU,EAAE,mDAAmD;YAC/Db,KAAK,EAAE,OAAO;YACdqB,MAAM,EAAE,MAAM;YACdN,YAAY,EAAE,KAAK;YACnBH,OAAO,EAAE,aAAa;YACtBH,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE,KAAK;YACjBgB,MAAM,EAAExL,OAAO,GAAG,aAAa,GAAG,SAAS;YAC3CiK,OAAO,EAAEjK,OAAO,GAAG,GAAG,GAAG,CAAC;YAC1ByL,UAAU,EAAE,eAAe;YAC3BjC,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBqB,GAAG,EAAE,QAAQ;YACbW,UAAU,EAAE;UACd,CAAE;UAAA3B,QAAA,gBAEFxK,OAAA;YAAMgK,KAAK,EAAE;cAAEgB,QAAQ,EAAE;YAAO,CAAE;YAAAR,QAAA,EAAC;UAAC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,0BAE7C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,eAGD9K,OAAA;UAAKgK,KAAK,EAAE;YAAEoC,IAAI,EAAE,CAAC;YAAEC,QAAQ,EAAE;UAAQ,CAAE;UAAA7B,QAAA,eACzCxK,OAAA;YACE6G,IAAI,EAAC,MAAM;YACXyF,WAAW,EAAC,iBAAiB;YAC7B9G,KAAK,EAAE7E,UAAW;YAClB4L,QAAQ,EAAElH,kBAAmB;YAC7B2E,KAAK,EAAE;cACLwC,KAAK,EAAE,MAAM;cACbrB,OAAO,EAAE,cAAc;cACvBS,MAAM,EAAE,mBAAmB;cAC3BN,YAAY,EAAE,KAAK;cACnBN,QAAQ,EAAE,MAAM;cAChByB,OAAO,EAAE;YACX;UAAE;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN9K,OAAA;UAAKgK,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEuB,GAAG,EAAE,QAAQ;YAAErB,UAAU,EAAE;UAAS,CAAE;UAAAK,QAAA,gBACnExK,OAAA;YAAMgK,KAAK,EAAE;cAAEgB,QAAQ,EAAE,QAAQ;cAAET,KAAK,EAAE,MAAM;cAAEmC,WAAW,EAAE;YAAS,CAAE;YAAAlC,QAAA,EAAC;UAAO;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzF9K,OAAA;YACE+L,OAAO,EAAEA,CAAA,KAAMhC,wBAAwB,CAAC,KAAK,CAAE;YAC/CC,KAAK,EAAE;cACLmB,OAAO,EAAE,aAAa;cACtBS,MAAM,EAAE/K,YAAY,KAAK,KAAK,GAAG,mBAAmB,GAAG,gBAAgB;cACvEyK,YAAY,EAAE,KAAK;cACnBN,QAAQ,EAAE,SAAS;cACnBU,eAAe,EAAE7K,YAAY,KAAK,KAAK,GAAG,SAAS,GAAG,MAAM;cAC5D0J,KAAK,EAAE1J,YAAY,KAAK,KAAK,GAAG,MAAM,GAAG,MAAM;cAC/CoL,MAAM,EAAE,SAAS;cACjBhB,UAAU,EAAEpK,YAAY,KAAK,KAAK,GAAG,MAAM,GAAG,QAAQ;cACtDqL,UAAU,EAAE;YACd,CAAE;YAAA1B,QAAA,EACH;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9K,OAAA;YACE+L,OAAO,EAAEA,CAAA,KAAMhC,wBAAwB,CAAC,QAAQ,CAAE;YAClDC,KAAK,EAAE;cACLmB,OAAO,EAAE,aAAa;cACtBS,MAAM,EAAE/K,YAAY,KAAK,QAAQ,GAAG,mBAAmB,GAAG,gBAAgB;cAC1EyK,YAAY,EAAE,KAAK;cACnBN,QAAQ,EAAE,SAAS;cACnBU,eAAe,EAAE7K,YAAY,KAAK,QAAQ,GAAG,SAAS,GAAG,MAAM;cAC/D0J,KAAK,EAAE1J,YAAY,KAAK,QAAQ,GAAG,MAAM,GAAG,MAAM;cAClDoL,MAAM,EAAE,SAAS;cACjBhB,UAAU,EAAEpK,YAAY,KAAK,QAAQ,GAAG,MAAM,GAAG,QAAQ;cACzDqL,UAAU,EAAE;YACd,CAAE;YAAA1B,QAAA,EACH;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9K,OAAA;YACE+L,OAAO,EAAEA,CAAA,KAAMhC,wBAAwB,CAAC,UAAU,CAAE;YACpDC,KAAK,EAAE;cACLmB,OAAO,EAAE,aAAa;cACtBS,MAAM,EAAE/K,YAAY,KAAK,UAAU,GAAG,mBAAmB,GAAG,gBAAgB;cAC5EyK,YAAY,EAAE,KAAK;cACnBN,QAAQ,EAAE,SAAS;cACnBU,eAAe,EAAE7K,YAAY,KAAK,UAAU,GAAG,SAAS,GAAG,MAAM;cACjE0J,KAAK,EAAE1J,YAAY,KAAK,UAAU,GAAG,MAAM,GAAG,MAAM;cACpDoL,MAAM,EAAE,SAAS;cACjBhB,UAAU,EAAEpK,YAAY,KAAK,UAAU,GAAG,MAAM,GAAG,QAAQ;cAC3DqL,UAAU,EAAE;YACd,CAAE;YAAA1B,QAAA,EACH;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN9K,OAAA;UACE+L,OAAO,EAAEtI,YAAa;UACtBuI,QAAQ,EAAEvL,OAAQ;UAClBuJ,KAAK,EAAE;YACLoB,UAAU,EAAE,SAAS;YACrBQ,MAAM,EAAE,mBAAmB;YAC3BN,YAAY,EAAE,KAAK;YACnBH,OAAO,EAAE,cAAc;YACvBH,QAAQ,EAAE,MAAM;YAChBiB,MAAM,EAAExL,OAAO,GAAG,aAAa,GAAG,SAAS;YAC3CiK,OAAO,EAAEjK,OAAO,GAAG,GAAG,GAAG,CAAC;YAC1ByL,UAAU,EAAE;UACd,CAAE;UAAA1B,QAAA,eAEFxK,OAAA;YAAMgK,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEqB,GAAG,EAAE;YAAS,CAAE;YAAAhB,QAAA,gBACpExK,OAAA,CAACX,SAAS;cAAC2H,IAAI,EAAE;YAAG;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,WAEzB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9K,OAAA;MAAKgK,KAAK,EAAE;QACVoB,UAAU,EAAE,OAAO;QACnBE,YAAY,EAAE,MAAM;QACpBK,SAAS,EAAE,gCAAgC;QAC3CC,MAAM,EAAE,mBAAmB;QAC3Be,QAAQ,EAAE;MACZ,CAAE;MAAAnC,QAAA,gBAEAxK,OAAA;QAAKgK,KAAK,EAAE;UACVoB,UAAU,EAAE,SAAS;UACrBD,OAAO,EAAE,aAAa;UACtByB,YAAY,EAAE,mBAAmB;UACjC3C,OAAO,EAAE,MAAM;UACf4C,mBAAmB,EAAE,oCAAoC;UACzDrB,GAAG,EAAE,MAAM;UACXP,UAAU,EAAE,KAAK;UACjBV,KAAK,EAAE,SAAS;UAChBS,QAAQ,EAAE;QACZ,CAAE;QAAAR,QAAA,gBACAxK,OAAA;UAAAwK,QAAA,EAAK;QAAK;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAChB9K,OAAA;UAAAwK,QAAA,EAAK;QAAS;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpB9K,OAAA;UAAAwK,QAAA,EAAK;QAAI;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACf9K,OAAA;UAAAwK,QAAA,EAAK;QAAK;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAChB9K,OAAA;UAAAwK,QAAA,EAAK;QAAe;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1B9K,OAAA;UAAAwK,QAAA,EAAK;QAAK;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAChB9K,OAAA;UAAAwK,QAAA,EAAK;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACjB9K,OAAA;UAAAwK,QAAA,EAAK;QAAO;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,EAGLrK,OAAO,gBACNT,OAAA;QAAKgK,KAAK,EAAE;UAAEmB,OAAO,EAAE,MAAM;UAAEb,SAAS,EAAE,QAAQ;UAAEC,KAAK,EAAE;QAAU,CAAE;QAAAC,QAAA,EAAC;MAExE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,GACJvK,QAAQ,CAACoE,MAAM,KAAK,CAAC,gBACvB3E,OAAA;QAAKgK,KAAK,EAAE;UAAEmB,OAAO,EAAE,MAAM;UAAEb,SAAS,EAAE,QAAQ;UAAEC,KAAK,EAAE;QAAU,CAAE;QAAAC,QAAA,GAAC,qBACnD,EAAC7J,UAAU,IAAI,qCAAqC;MAAA;QAAAgK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC,GAENvK,QAAQ,CAAC6D,GAAG,CAACiF,OAAO,iBAClBrJ,OAAA;QAEEgK,KAAK,EAAE;UACLmB,OAAO,EAAE,aAAa;UACtByB,YAAY,EAAE,mBAAmB;UACjC3C,OAAO,EAAE,MAAM;UACf4C,mBAAmB,EAAE,oCAAoC;UACzDrB,GAAG,EAAE,MAAM;UACXrB,UAAU,EAAE,QAAQ;UACpBa,QAAQ,EAAE;QACZ,CAAE;QAAAR,QAAA,gBAEFxK,OAAA;UAAKgK,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEG,cAAc,EAAE;UAAS,CAAE;UAAAI,QAAA,EACvDnB,OAAO,CAAC7E,OAAO,CAACiD,eAAe,gBAC9BzH,OAAA;YACE8M,GAAG,EAAEhN,WAAW,CAACuJ,OAAO,CAAC7E,OAAO,CAACiD,eAAe,CAAC,IAAI,EAAG;YACxDsF,GAAG,EAAE,GAAG1D,OAAO,CAAC7E,OAAO,CAACC,SAAS,UAAW;YAC5CuF,KAAK,EAAE;cACLwC,KAAK,EAAE,MAAM;cACbQ,MAAM,EAAE,MAAM;cACd1B,YAAY,EAAE,KAAK;cACnB2B,SAAS,EAAE,OAAO;cAClBrB,MAAM,EAAE;YACV,CAAE;YACFsB,OAAO,EAAG5H,CAAC,IAAK;cACd;cACA,MAAMC,MAAM,GAAGD,CAAC,CAACC,MAA0B;cAC3CA,MAAM,CAACyE,KAAK,CAACC,OAAO,GAAG,MAAM;cAC7B,MAAMkD,MAAM,GAAG5H,MAAM,CAAC6H,aAAa;cACnC,IAAID,MAAM,EAAE;gBACVA,MAAM,CAACE,SAAS,GAAG;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8BhE,OAAO,CAAC7E,OAAO,CAACkE,UAAU,CAAC/C,MAAM,CAAC,CAAC,CAAC,GAAG0D,OAAO,CAAC7E,OAAO,CAACoE,SAAS,CAACjD,MAAM,CAAC,CAAC,CAAC;AACxG;AACA,yBAAyB;cACH;YACF;UAAE;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEF9K,OAAA;YAAKgK,KAAK,EAAE;cACVwC,KAAK,EAAE,MAAM;cACbQ,MAAM,EAAE,MAAM;cACd1B,YAAY,EAAE,KAAK;cACnBI,eAAe,EAAE,SAAS;cAC1BzB,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBG,KAAK,EAAE,SAAS;cAChBS,QAAQ,EAAE,SAAS;cACnBC,UAAU,EAAE;YACd,CAAE;YAAAT,QAAA,GACCnB,OAAO,CAAC7E,OAAO,CAACkE,UAAU,CAAC/C,MAAM,CAAC,CAAC,CAAC,EAAE0D,OAAO,CAAC7E,OAAO,CAACoE,SAAS,CAACjD,MAAM,CAAC,CAAC,CAAC;UAAA;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACN9K,OAAA;UAAKgK,KAAK,EAAE;YAAEiB,UAAU,EAAE,KAAK;YAAEV,KAAK,EAAE;UAAU,CAAE;UAAAC,QAAA,EACjDnB,OAAO,CAACf;QAAc;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACN9K,OAAA;UAAKgK,KAAK,EAAE;YAAEO,KAAK,EAAE;UAAU,CAAE;UAAAC,QAAA,EAC9BnB,OAAO,CAAC7E,OAAO,CAACC;QAAS;UAAAkG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eACN9K,OAAA;UAAKgK,KAAK,EAAE;YAAEO,KAAK,EAAE;UAAU,CAAE;UAAAC,QAAA,EAC9BnB,OAAO,CAAClH;QAAK;UAAAwI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACN9K,OAAA;UAAKgK,KAAK,EAAE;YAAEO,KAAK,EAAE;UAAU,CAAE;UAAAC,QAAA,GAAC,QAC1B,EAACnB,OAAO,CAAC7E,OAAO,CAACjD,WAAW,EAAC,KAAG,EAAC8H,OAAO,CAAC7E,OAAO,CAAC7B,OAAO;QAAA;UAAAgI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACN9K,OAAA;UAAKgK,KAAK,EAAE;YAAEO,KAAK,EAAE;UAAU,CAAE;UAAAC,QAAA,EAC9BnB,OAAO,CAAC7E,OAAO,CAACqE;QAAY;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACN9K,OAAA;UAAAwK,QAAA,eACExK,OAAA;YAAMgK,KAAK,EAAE;cACXoB,UAAU,EAAElG,cAAc,CAACmE,OAAO,CAACtF,SAAS,CAAC;cAC7CwG,KAAK,EAAE,OAAO;cACdY,OAAO,EAAE,iBAAiB;cAC1BG,YAAY,EAAE,MAAM;cACpBN,QAAQ,EAAE,SAAS;cACnBC,UAAU,EAAE;YACd,CAAE;YAAAT,QAAA,EACCpF,aAAa,CAACiE,OAAO,CAACtF,SAAS;UAAC;YAAA4G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN9K,OAAA;UAAKgK,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEuB,GAAG,EAAE;UAAU,CAAE;UAAAhB,QAAA,EAC7ClK,WAAW,CAACwL,iBAAiB,gBAC5B9L,OAAA,CAAAE,SAAA;YAAAsK,QAAA,gBACExK,OAAA;cACE+L,OAAO,EAAEA,CAAA,KAAM3C,iBAAiB,CAACC,OAAO,CAAE;cAC1CiE,KAAK,EAAC,cAAc;cACpBtD,KAAK,EAAE;gBACLoB,UAAU,EAAE,SAAS;gBACrBb,KAAK,EAAE,OAAO;gBACdqB,MAAM,EAAE,MAAM;gBACdN,YAAY,EAAE,KAAK;gBACnBH,OAAO,EAAE,QAAQ;gBACjBc,MAAM,EAAE,SAAS;gBACjBjB,QAAQ,EAAE;cACZ,CAAE;cAAAR,QAAA,eAEFxK,OAAA,CAACV,IAAI;gBAAC0H,IAAI,EAAE;cAAG;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACT9K,OAAA;cACE+L,OAAO,EAAEA,CAAA,KAAMrC,mBAAmB,CAACL,OAAO,CAAE;cAC5CiE,KAAK,EAAC,wCAAwC;cAC9CtD,KAAK,EAAE;gBACLoB,UAAU,EAAE,SAAS;gBACrBb,KAAK,EAAE,OAAO;gBACdqB,MAAM,EAAE,MAAM;gBACdN,YAAY,EAAE,KAAK;gBACnBH,OAAO,EAAE,QAAQ;gBACjBc,MAAM,EAAE,SAAS;gBACjBjB,QAAQ,EAAE;cACZ,CAAE;cAAAR,QAAA,eAEFxK,OAAA,CAACT,GAAG;gBAACyH,IAAI,EAAE;cAAG;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,EACRxK,WAAW,CAACgB,YAAY,iBACvBtB,OAAA;cACE+L,OAAO,EAAEA,CAAA,KAAMtC,mBAAmB,CAACJ,OAAO,CAAE;cAC5CiE,KAAK,EAAC,oBAAoB;cAC1BtD,KAAK,EAAE;gBACLoB,UAAU,EAAE,SAAS;gBACrBb,KAAK,EAAE,OAAO;gBACdqB,MAAM,EAAE,MAAM;gBACdN,YAAY,EAAE,KAAK;gBACnBH,OAAO,EAAE,QAAQ;gBACjBc,MAAM,EAAE,SAAS;gBACjBjB,QAAQ,EAAE;cACZ,CAAE;cAAAR,QAAA,eAEFxK,OAAA,CAACR,MAAM;gBAACwH,IAAI,EAAE;cAAG;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CACT,EACAxK,WAAW,CAACoC,WAAW,iBACtB1C,OAAA;cAAKgK,KAAK,EAAE;gBACVmB,OAAO,EAAE,QAAQ;gBACjBC,UAAU,EAAE,SAAS;gBACrBb,KAAK,EAAE,SAAS;gBAChBe,YAAY,EAAE,KAAK;gBACnBN,QAAQ,EAAE,SAAS;gBACnBC,UAAU,EAAE,KAAK;gBACjBhB,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE;cACd,CAAE;cAAAK,QAAA,EAAC;YAEH;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA,eACD,CAAC,gBAEH9K,OAAA;YAAKgK,KAAK,EAAE;cACVmB,OAAO,EAAE,aAAa;cACtBC,UAAU,EAAE,SAAS;cACrBb,KAAK,EAAE,SAAS;cAChBe,YAAY,EAAE,KAAK;cACnBN,QAAQ,EAAE,SAAS;cACnBC,UAAU,EAAE;YACd,CAAE;YAAAT,QAAA,EAAC;UAEH;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA,GAzKDzB,OAAO,CAACtB,UAAU;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA0KpB,CACN,CACF,EAGA7J,UAAU,GAAG,CAAC,iBACbjB,OAAA;QAAKgK,KAAK,EAAE;UACVmB,OAAO,EAAE,aAAa;UACtBoC,SAAS,EAAE,mBAAmB;UAC9BtD,OAAO,EAAE,MAAM;UACfG,cAAc,EAAE,eAAe;UAC/BD,UAAU,EAAE;QACd,CAAE;QAAAK,QAAA,gBACAxK,OAAA;UAAKgK,KAAK,EAAE;YAAEO,KAAK,EAAE,SAAS;YAAES,QAAQ,EAAE;UAAW,CAAE;UAAAR,QAAA,GAAC,OACjD,EAACzJ,WAAW,EAAC,MAAI,EAACE,UAAU;QAAA;UAAA0J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACN9K,OAAA;UAAKgK,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEuB,GAAG,EAAE;UAAS,CAAE;UAAAhB,QAAA,gBAC7CxK,OAAA;YACE+L,OAAO,EAAEA,CAAA,KAAMjC,gBAAgB,CAAC/I,WAAW,GAAG,CAAC,CAAE;YACjDiL,QAAQ,EAAEjL,WAAW,KAAK,CAAE;YAC5BiJ,KAAK,EAAE;cACLoB,UAAU,EAAErK,WAAW,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;cACrDwJ,KAAK,EAAExJ,WAAW,KAAK,CAAC,GAAG,SAAS,GAAG,OAAO;cAC9C6K,MAAM,EAAE,MAAM;cACdN,YAAY,EAAE,KAAK;cACnBH,OAAO,EAAE,aAAa;cACtBc,MAAM,EAAElL,WAAW,KAAK,CAAC,GAAG,aAAa,GAAG,SAAS;cACrDiK,QAAQ,EAAE;YACZ,CAAE;YAAAR,QAAA,EACH;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9K,OAAA;YACE+L,OAAO,EAAEA,CAAA,KAAMjC,gBAAgB,CAAC/I,WAAW,GAAG,CAAC,CAAE;YACjDiL,QAAQ,EAAEjL,WAAW,KAAKE,UAAW;YACrC+I,KAAK,EAAE;cACLoB,UAAU,EAAErK,WAAW,KAAKE,UAAU,GAAG,SAAS,GAAG,SAAS;cAC9DsJ,KAAK,EAAExJ,WAAW,KAAKE,UAAU,GAAG,SAAS,GAAG,OAAO;cACvD2K,MAAM,EAAE,MAAM;cACdN,YAAY,EAAE,KAAK;cACnBH,OAAO,EAAE,aAAa;cACtBc,MAAM,EAAElL,WAAW,KAAKE,UAAU,GAAG,aAAa,GAAG,SAAS;cAC9D+J,QAAQ,EAAE;YACZ,CAAE;YAAAR,QAAA,EACH;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLtJ,eAAe,iBACdxB,OAAA;MAAKgK,KAAK,EAAE;QACVwD,QAAQ,EAAE,OAAO;QACjBC,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTxC,UAAU,EAAE,oBAAoB;QAChCnB,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxByD,MAAM,EAAE,IAAI;QACZ1C,OAAO,EAAE;MACX,CAAE;MAAAX,QAAA,eACAxK,OAAA;QAAKgK,KAAK,EAAE;UACVoB,UAAU,EAAE,OAAO;UACnBE,YAAY,EAAE,MAAM;UACpBH,OAAO,EAAE,MAAM;UACfqB,KAAK,EAAE,KAAK;UACZsB,QAAQ,EAAE,QAAQ;UAClBC,SAAS,EAAE,MAAM;UACjBpB,QAAQ,EAAE,QAAQ;UAClB1C,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE;QACjB,CAAE;QAAAM,QAAA,gBACAxK,OAAA;UAAKgK,KAAK,EAAE;YACVC,OAAO,EAAE,MAAM;YACfG,cAAc,EAAE,eAAe;YAC/BD,UAAU,EAAE,QAAQ;YACpBM,YAAY,EAAE;UAChB,CAAE;UAAAD,QAAA,gBACAxK,OAAA;YAAIgK,KAAK,EAAE;cACTe,MAAM,EAAE,CAAC;cACTR,KAAK,EAAE,SAAS;cAChBS,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACd,CAAE;YAAAT,QAAA,EAAC;UAEH;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL9K,OAAA;YACE+L,OAAO,EAAEA,CAAA,KAAM;cACbtK,kBAAkB,CAAC,KAAK,CAAC;cACzB+E,SAAS,CAAC,CAAC;YACb,CAAE;YACFwD,KAAK,EAAE;cACLoB,UAAU,EAAE,MAAM;cAClBQ,MAAM,EAAE,MAAM;cACdZ,QAAQ,EAAE,QAAQ;cAClBiB,MAAM,EAAE,SAAS;cACjB1B,KAAK,EAAE;YACT,CAAE;YAAAC,QAAA,EACH;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELzH,KAAK,iBACJrD,OAAA;UAAKgK,KAAK,EAAE;YACVoB,UAAU,EAAE,SAAS;YACrBQ,MAAM,EAAE,mBAAmB;YAC3BN,YAAY,EAAE,KAAK;YACnBH,OAAO,EAAE,MAAM;YACfV,YAAY,EAAE,MAAM;YACpBF,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,EACCnH;QAAK;UAAAsH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAED9K,OAAA;UAAKgK,KAAK,EAAE;YAAEoC,IAAI,EAAE,CAAC;YAAEO,QAAQ,EAAE,MAAM;YAAEqB,YAAY,EAAE;UAAS,CAAE;UAAAxD,QAAA,eAChExK,OAAA;YAAMiO,QAAQ,EAAG3I,CAAC,IAAK;cAAEA,CAAC,CAAC4I,cAAc,CAAC,CAAC;cAAEjG,mBAAmB,CAAC,CAAC;YAAE,CAAE;YAAC+B,KAAK,EAAE;cAC5EC,OAAO,EAAE,MAAM;cACf4C,mBAAmB,EAAE,SAAS;cAC9BrB,GAAG,EAAE,MAAM;cACXwB,MAAM,EAAE;YACV,CAAE;YAAAxC,QAAA,gBAEAxK,OAAA;cAAKgK,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,aAAa,EAAE,QAAQ;gBAAEsB,GAAG,EAAE;cAAO,CAAE;cAAAhB,QAAA,gBAEpExK,OAAA;gBAAAwK,QAAA,gBACExK,OAAA;kBAAOgK,KAAK,EAAE;oBAAEC,OAAO,EAAE,OAAO;oBAAEQ,YAAY,EAAE,QAAQ;oBAAEQ,UAAU,EAAE,KAAK;oBAAEV,KAAK,EAAE;kBAAU,CAAE;kBAAAC,QAAA,EAAC;gBAEjG;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9K,OAAA;kBAAKgK,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEqB,GAAG,EAAE;kBAAO,CAAE;kBAAAhB,QAAA,GAChEvH,qBAAqB,gBACpBjD,OAAA;oBACE8M,GAAG,EAAE7J,qBAAsB;oBAC3B8J,GAAG,EAAC,iBAAiB;oBACrB/C,KAAK,EAAE;sBACLwC,KAAK,EAAE,MAAM;sBACbQ,MAAM,EAAE,MAAM;sBACd1B,YAAY,EAAE,KAAK;sBACnB2B,SAAS,EAAE,OAAO;sBAClBrB,MAAM,EAAE;oBACV,CAAE;oBACFsB,OAAO,EAAG5H,CAAC,IAAK;sBACdtB,OAAO,CAACX,KAAK,CAAC,yCAAyC,EAAEJ,qBAAqB,CAAC;sBAC/E,MAAMsC,MAAM,GAAGD,CAAC,CAACC,MAA0B;sBAC3CA,MAAM,CAACyE,KAAK,CAACC,OAAO,GAAG,MAAM;sBAC7B,MAAMkD,MAAM,GAAG5H,MAAM,CAAC6H,aAAa;sBACnC,IAAID,MAAM,EAAE;wBACVA,MAAM,CAACE,SAAS,GAAG;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;sBACH;oBACF;kBAAE;oBAAA1C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,gBAEF9K,OAAA;oBAAKgK,KAAK,EAAE;sBACVwC,KAAK,EAAE,MAAM;sBACbQ,MAAM,EAAE,MAAM;sBACd1B,YAAY,EAAE,KAAK;sBACnBI,eAAe,EAAE,SAAS;sBAC1BzB,OAAO,EAAE,MAAM;sBACfE,UAAU,EAAE,QAAQ;sBACpBC,cAAc,EAAE,QAAQ;sBACxBG,KAAK,EAAE,SAAS;sBAChBS,QAAQ,EAAE;oBACZ,CAAE;oBAAAR,QAAA,EAAC;kBAEH;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CACN,eACD9K,OAAA;oBAAKgK,KAAK,EAAE;sBAAEoC,IAAI,EAAE;oBAAE,CAAE;oBAAA5B,QAAA,gBACtBxK,OAAA;sBACE6G,IAAI,EAAC,MAAM;sBACXsH,MAAM,EAAC,SAAS;sBAChB5B,QAAQ,EAAE9F,0BAA2B;sBACrCuD,KAAK,EAAE;wBACLwC,KAAK,EAAE,MAAM;wBACbrB,OAAO,EAAE,QAAQ;wBACjBS,MAAM,EAAE,mBAAmB;wBAC3BN,YAAY,EAAE,KAAK;wBACnBN,QAAQ,EAAE;sBACZ;oBAAE;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,EACD7H,qBAAqB,iBACpBjD,OAAA;sBACE6G,IAAI,EAAC,QAAQ;sBACbkF,OAAO,EAAExE,yBAA0B;sBACnCyC,KAAK,EAAE;wBACLkB,SAAS,EAAE,QAAQ;wBACnBC,OAAO,EAAE,gBAAgB;wBACzBO,eAAe,EAAE,SAAS;wBAC1BnB,KAAK,EAAE,OAAO;wBACdqB,MAAM,EAAE,MAAM;wBACdN,YAAY,EAAE,KAAK;wBACnBN,QAAQ,EAAE,SAAS;wBACnBiB,MAAM,EAAE;sBACV,CAAE;sBAAAzB,QAAA,EACH;oBAED;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CACT;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN9K,OAAA;gBAAAwK,QAAA,gBACExK,OAAA;kBAAOgK,KAAK,EAAE;oBAAEC,OAAO,EAAE,OAAO;oBAAEQ,YAAY,EAAE,QAAQ;oBAAEQ,UAAU,EAAE,KAAK;oBAAEV,KAAK,EAAE;kBAAU,CAAE;kBAAAC,QAAA,EAAC;gBAEjG;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9K,OAAA;kBACE6G,IAAI,EAAC,MAAM;kBACXtC,IAAI,EAAC,eAAe;kBACpBiB,KAAK,EAAExD,QAAQ,CAACE,aAAc;kBAC9BqK,QAAQ,EAAErG,iBAAkB;kBAC5BkI,QAAQ;kBACRpE,KAAK,EAAE;oBACLwC,KAAK,EAAE,MAAM;oBACbrB,OAAO,EAAE,SAAS;oBAClBS,MAAM,EAAE,mBAAmB;oBAC3BN,YAAY,EAAE,KAAK;oBACnBN,QAAQ,EAAE;kBACZ,CAAE;kBACFsB,WAAW,EAAC;gBAAiB;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGN9K,OAAA;gBAAAwK,QAAA,gBACExK,OAAA;kBAAOgK,KAAK,EAAE;oBAAEC,OAAO,EAAE,OAAO;oBAAEQ,YAAY,EAAE,QAAQ;oBAAEQ,UAAU,EAAE,KAAK;oBAAEV,KAAK,EAAE;kBAAU,CAAE;kBAAAC,QAAA,EAAC;gBAEjG;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9K,OAAA;kBACE6G,IAAI,EAAC,OAAO;kBACZtC,IAAI,EAAC,OAAO;kBACZiB,KAAK,EAAExD,QAAQ,CAACG,KAAM;kBACtBkM,QAAQ;kBACRD,QAAQ;kBACRpE,KAAK,EAAE;oBACLwC,KAAK,EAAE,MAAM;oBACbrB,OAAO,EAAE,SAAS;oBAClBS,MAAM,EAAE,mBAAmB;oBAC3BN,YAAY,EAAE,KAAK;oBACnBN,QAAQ,EAAE,MAAM;oBAChBU,eAAe,EAAE,SAAS;oBAC1BnB,KAAK,EAAE;kBACT,CAAE;kBACF+B,WAAW,EAAC;gBAAuD;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGR9K,OAAA;gBAAKgK,KAAK,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAE4C,mBAAmB,EAAE,SAAS;kBAAErB,GAAG,EAAE;gBAAO,CAAE;gBAAAhB,QAAA,gBAE3ExK,OAAA;kBAAAwK,QAAA,gBACExK,OAAA;oBAAOgK,KAAK,EAAE;sBAAEC,OAAO,EAAE,OAAO;sBAAEQ,YAAY,EAAE,QAAQ;sBAAEQ,UAAU,EAAE,KAAK;sBAAEV,KAAK,EAAE;oBAAU,CAAE;oBAAAC,QAAA,EAAC;kBAEjG;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR9K,OAAA;oBACE6G,IAAI,EAAC,MAAM;oBACXtC,IAAI,EAAC,WAAW;oBAChBiB,KAAK,EAAExD,QAAQ,CAACI,SAAU;oBAC1BmK,QAAQ,EAAErG,iBAAkB;oBAC5BkI,QAAQ;oBACRpE,KAAK,EAAE;sBACLwC,KAAK,EAAE,MAAM;sBACbrB,OAAO,EAAE,SAAS;sBAClBS,MAAM,EAAE,mBAAmB;sBAC3BN,YAAY,EAAE,KAAK;sBACnBN,QAAQ,EAAE;oBACZ,CAAE;oBACFsB,WAAW,EAAC;kBAAM;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAGN9K,OAAA;kBAAAwK,QAAA,gBACExK,OAAA;oBAAOgK,KAAK,EAAE;sBAAEC,OAAO,EAAE,OAAO;sBAAEQ,YAAY,EAAE,QAAQ;sBAAEQ,UAAU,EAAE,KAAK;sBAAEV,KAAK,EAAE;oBAAU,CAAE;oBAAAC,QAAA,EAAC;kBAEjG;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR9K,OAAA;oBACE6G,IAAI,EAAC,MAAM;oBACXtC,IAAI,EAAC,UAAU;oBACfiB,KAAK,EAAExD,QAAQ,CAACM,QAAS;oBACzBiK,QAAQ,EAAErG,iBAAkB;oBAC5BkI,QAAQ;oBACRpE,KAAK,EAAE;sBACLwC,KAAK,EAAE,MAAM;sBACbrB,OAAO,EAAE,SAAS;sBAClBS,MAAM,EAAE,mBAAmB;sBAC3BN,YAAY,EAAE,KAAK;sBACnBN,QAAQ,EAAE;oBACZ,CAAE;oBACFsB,WAAW,EAAC;kBAAM;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN9K,OAAA;gBAAKgK,KAAK,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAE4C,mBAAmB,EAAE,SAAS;kBAAErB,GAAG,EAAE;gBAAO,CAAE;gBAAAhB,QAAA,gBAE3ExK,OAAA;kBAAAwK,QAAA,gBACExK,OAAA;oBAAOgK,KAAK,EAAE;sBAAEC,OAAO,EAAE,OAAO;sBAAEQ,YAAY,EAAE,QAAQ;sBAAEQ,UAAU,EAAE,KAAK;sBAAEV,KAAK,EAAE;oBAAU,CAAE;oBAAAC,QAAA,EAAC;kBAEjG;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR9K,OAAA;oBACE6G,IAAI,EAAC,MAAM;oBACXtC,IAAI,EAAC,YAAY;oBACjBiB,KAAK,EAAExD,QAAQ,CAACK,UAAW;oBAC3BkK,QAAQ,EAAErG,iBAAkB;oBAC5B8D,KAAK,EAAE;sBACLwC,KAAK,EAAE,MAAM;sBACbrB,OAAO,EAAE,SAAS;sBAClBS,MAAM,EAAE,mBAAmB;sBAC3BN,YAAY,EAAE,KAAK;sBACnBN,QAAQ,EAAE;oBACZ,CAAE;oBACFsB,WAAW,EAAC;kBAAM;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAGN9K,OAAA;kBAAAwK,QAAA,gBACExK,OAAA;oBAAOgK,KAAK,EAAE;sBAAEC,OAAO,EAAE,OAAO;sBAAEQ,YAAY,EAAE,QAAQ;sBAAEQ,UAAU,EAAE,KAAK;sBAAEV,KAAK,EAAE;oBAAU,CAAE;oBAAAC,QAAA,EAAC;kBAEjG;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR9K,OAAA;oBACE6G,IAAI,EAAC,MAAM;oBACXtC,IAAI,EAAC,QAAQ;oBACbiB,KAAK,EAAExD,QAAQ,CAACO,MAAO;oBACvBgK,QAAQ,EAAErG,iBAAkB;oBAC5B8D,KAAK,EAAE;sBACLwC,KAAK,EAAE,MAAM;sBACbrB,OAAO,EAAE,SAAS;sBAClBS,MAAM,EAAE,mBAAmB;sBAC3BN,YAAY,EAAE,KAAK;sBACnBN,QAAQ,EAAE;oBACZ,CAAE;oBACFsB,WAAW,EAAC;kBAAe;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN9K,OAAA;cAAKgK,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,aAAa,EAAE,QAAQ;gBAAEsB,GAAG,EAAE;cAAO,CAAE;cAAAhB,QAAA,gBAEpExK,OAAA;gBAAAwK,QAAA,gBACExK,OAAA;kBAAOgK,KAAK,EAAE;oBAAEC,OAAO,EAAE,OAAO;oBAAEQ,YAAY,EAAE,QAAQ;oBAAEQ,UAAU,EAAE,KAAK;oBAAEV,KAAK,EAAE;kBAAU,CAAE;kBAAAC,QAAA,EAAC;gBAEjG;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9K,OAAA;kBACE6G,IAAI,EAAC,KAAK;kBACVtC,IAAI,EAAC,aAAa;kBAClBiB,KAAK,EAAExD,QAAQ,CAACQ,WAAY;kBAC5B+J,QAAQ,EAAErG,iBAAkB;kBAC5BoI,OAAO,EAAGhJ,CAAC,IAAK;oBACd;oBACAA,CAAC,CAACiJ,aAAa,CAAC/I,KAAK,GAAGF,CAAC,CAACiJ,aAAa,CAAC/I,KAAK,CAACO,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;kBACtE,CAAE;kBACFqI,QAAQ;kBACRpE,KAAK,EAAE;oBACLwC,KAAK,EAAE,MAAM;oBACbrB,OAAO,EAAE,SAAS;oBAClBS,MAAM,EAAE,mBAAmB;oBAC3BN,YAAY,EAAE,KAAK;oBACnBN,QAAQ,EAAE;kBACZ,CAAE;kBACFsB,WAAW,EAAC,aAAa;kBACzBkC,SAAS,EAAE;gBAAG;kBAAA7D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGN9K,OAAA;gBAAAwK,QAAA,gBACExK,OAAA;kBACEgK,KAAK,EAAE;oBACLC,OAAO,EAAE,OAAO;oBAChBQ,YAAY,EAAE,QAAQ;oBACtBQ,UAAU,EAAE,KAAK;oBACjBV,KAAK,EAAE;kBACT,CAAE;kBAAAC,QAAA,EACH;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9K,OAAA;kBACEuE,IAAI,EAAC,YAAY;kBACjBiB,KAAK,EAAExD,QAAQ,CAACS,UAAW;kBAC3B8J,QAAQ,EAAErG,iBAAkB;kBAC5BkI,QAAQ;kBACRpC,QAAQ,EAAE1L,WAAW,CAACoC,WAAW,IAAIrB,uBAAuB,CAAC,CAAC,CAACsD,MAAM,KAAK,CAAE,CAAC;kBAAA;kBAC7EqF,KAAK,EAAE;oBACLwC,KAAK,EAAE,MAAM;oBACbrB,OAAO,EAAE,SAAS;oBAClBS,MAAM,EAAE,mBAAmB;oBAC3BN,YAAY,EAAE,KAAK;oBACnBN,QAAQ,EAAE,MAAM;oBAChBU,eAAe,EAAE;kBACnB,CAAE;kBAAAlB,QAAA,EAEDnJ,uBAAuB,CAAC,CAAC,CAAC+C,GAAG,CAAEqK,KAAK,iBACnCzO,OAAA;oBAAoBwF,KAAK,EAAEiJ,KAAM;oBAAAjE,QAAA,GAAC,QAC1B,EAACiE,KAAK;kBAAA,GADDA,KAAK;oBAAA9D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEV,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAGN9K,OAAA;gBAAAwK,QAAA,gBACExK,OAAA;kBAAOgK,KAAK,EAAE;oBAAEC,OAAO,EAAE,OAAO;oBAAEQ,YAAY,EAAE,QAAQ;oBAAEQ,UAAU,EAAE,KAAK;oBAAEV,KAAK,EAAE;kBAAU,CAAE;kBAAAC,QAAA,EAAC;gBAEjG;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9K,OAAA;kBACE6G,IAAI,EAAC,MAAM;kBACXtC,IAAI,EAAC,SAAS;kBACdiB,KAAK,EAAExD,QAAQ,CAACW,OAAQ;kBACxB4J,QAAQ,EAAErG,iBAAkB;kBAC5BkI,QAAQ;kBACRpE,KAAK,EAAE;oBACLwC,KAAK,EAAE,MAAM;oBACbrB,OAAO,EAAE,SAAS;oBAClBS,MAAM,EAAE,mBAAmB;oBAC3BN,YAAY,EAAE,KAAK;oBACnBN,QAAQ,EAAE;kBACZ,CAAE;kBACFsB,WAAW,EAAC;gBAAe;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGN9K,OAAA;gBAAAwK,QAAA,gBACExK,OAAA;kBAAOgK,KAAK,EAAE;oBAAEC,OAAO,EAAE,OAAO;oBAAEQ,YAAY,EAAE,QAAQ;oBAAEQ,UAAU,EAAE,KAAK;oBAAEV,KAAK,EAAE;kBAAU,CAAE;kBAAAC,QAAA,EAAC;gBAEjG;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9K,OAAA;kBACE6G,IAAI,EAAC,MAAM;kBACXtC,IAAI,EAAC,oBAAoB;kBACzBiB,KAAK,EAAExD,QAAQ,CAACY,kBAAmB;kBACnC2J,QAAQ,EAAErG,iBAAkB;kBAC5B8D,KAAK,EAAE;oBACLwC,KAAK,EAAE,MAAM;oBACbrB,OAAO,EAAE,SAAS;oBAClBS,MAAM,EAAE,mBAAmB;oBAC3BN,YAAY,EAAE,KAAK;oBACnBN,QAAQ,EAAE;kBACZ,CAAE;kBACFsB,WAAW,EAAC;gBAAsB;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGN9K,OAAA;gBAAAwK,QAAA,gBACExK,OAAA;kBAAOgK,KAAK,EAAE;oBAAEC,OAAO,EAAE,OAAO;oBAAEQ,YAAY,EAAE,QAAQ;oBAAEQ,UAAU,EAAE,KAAK;oBAAEV,KAAK,EAAE;kBAAU,CAAE;kBAAAC,QAAA,EAAC;gBAEjG;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9K,OAAA;kBACE6G,IAAI,EAAC,KAAK;kBACVtC,IAAI,EAAC,qBAAqB;kBAC1BiB,KAAK,EAAExD,QAAQ,CAACa,mBAAoB;kBACpC0J,QAAQ,EAAErG,iBAAkB;kBAC5BoI,OAAO,EAAGhJ,CAAC,IAAK;oBACd;oBACAA,CAAC,CAACiJ,aAAa,CAAC/I,KAAK,GAAGF,CAAC,CAACiJ,aAAa,CAAC/I,KAAK,CAACO,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;kBACtE,CAAE;kBACFiE,KAAK,EAAE;oBACLwC,KAAK,EAAE,MAAM;oBACbrB,OAAO,EAAE,SAAS;oBAClBS,MAAM,EAAE,mBAAmB;oBAC3BN,YAAY,EAAE,KAAK;oBACnBN,QAAQ,EAAE;kBACZ,CAAE;kBACFsB,WAAW,EAAC,aAAa;kBACzBkC,SAAS,EAAE;gBAAG;kBAAA7D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGN9K,OAAA;gBAAAwK,QAAA,gBACExK,OAAA;kBAAOgK,KAAK,EAAE;oBAAEC,OAAO,EAAE,OAAO;oBAAEQ,YAAY,EAAE,QAAQ;oBAAEQ,UAAU,EAAE,KAAK;oBAAEV,KAAK,EAAE;kBAAU,CAAE;kBAAAC,QAAA,EAAC;gBAEjG;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9K,OAAA;kBACEuE,IAAI,EAAC,SAAS;kBACdiB,KAAK,EAAExD,QAAQ,CAACc,OAAQ;kBACxByJ,QAAQ,EAAErG,iBAAkB;kBAC5BwI,IAAI,EAAE,CAAE;kBACR1E,KAAK,EAAE;oBACLwC,KAAK,EAAE,MAAM;oBACbrB,OAAO,EAAE,SAAS;oBAClBS,MAAM,EAAE,mBAAmB;oBAC3BN,YAAY,EAAE,KAAK;oBACnBN,QAAQ,EAAE,MAAM;oBAChB2D,MAAM,EAAE;kBACV,CAAE;kBACFrC,WAAW,EAAC;gBAAkB;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGN9K,OAAA;gBAAKgK,KAAK,EAAE;kBACVoB,UAAU,EAAE,SAAS;kBACrBQ,MAAM,EAAE,mBAAmB;kBAC3BN,YAAY,EAAE,KAAK;kBACnBH,OAAO,EAAE;gBACX,CAAE;gBAAAX,QAAA,gBACAxK,OAAA;kBAAKgK,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEqB,GAAG,EAAE,QAAQ;oBAAEf,YAAY,EAAE;kBAAS,CAAE;kBAAAD,QAAA,gBAC3FxK,OAAA,CAACP,IAAI;oBAACuH,IAAI,EAAE,EAAG;oBAACuD,KAAK,EAAC;kBAAS;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClC9K,OAAA;oBAAMgK,KAAK,EAAE;sBAAEiB,UAAU,EAAE,KAAK;sBAAEV,KAAK,EAAE;oBAAU,CAAE;oBAAAC,QAAA,EAAC;kBAAyB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC,eACN9K,OAAA;kBAAGgK,KAAK,EAAE;oBAAEe,MAAM,EAAE,CAAC;oBAAER,KAAK,EAAE,SAAS;oBAAES,QAAQ,EAAE;kBAAW,CAAE;kBAAAR,QAAA,GAAC,iEACA,eAAAxK,OAAA;oBAAAwK,QAAA,EAAQ;kBAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC1F9K,OAAA;oBAAA2K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,uGAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN9K,OAAA;cAAKgK,KAAK,EAAE;gBACV4E,UAAU,EAAE,QAAQ;gBACpB3E,OAAO,EAAE,MAAM;gBACfG,cAAc,EAAE,UAAU;gBAC1BoB,GAAG,EAAE,MAAM;gBACXN,SAAS,EAAE,MAAM;gBACjB2D,UAAU,EAAE,MAAM;gBAClBtB,SAAS,EAAE;cACb,CAAE;cAAA/C,QAAA,gBAEAxK,OAAA;gBACE6G,IAAI,EAAC,QAAQ;gBACbkF,OAAO,EAAEA,CAAA,KAAM;kBACbtK,kBAAkB,CAAC,KAAK,CAAC;kBACzB+E,SAAS,CAAC,CAAC;gBACb,CAAE;gBACFwF,QAAQ,EAAE7I,YAAa;gBACvB6G,KAAK,EAAE;kBACLoB,UAAU,EAAE,SAAS;kBACrBb,KAAK,EAAE,SAAS;kBAChBqB,MAAM,EAAE,MAAM;kBACdN,YAAY,EAAE,KAAK;kBACnBH,OAAO,EAAE,gBAAgB;kBACzBH,QAAQ,EAAE,MAAM;kBAChBiB,MAAM,EAAE9I,YAAY,GAAG,aAAa,GAAG,SAAS;kBAChDuH,OAAO,EAAEvH,YAAY,GAAG,GAAG,GAAG;gBAChC,CAAE;gBAAAqH,QAAA,EACH;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT9K,OAAA;gBACE6G,IAAI,EAAC,QAAQ;gBACbmF,QAAQ,EAAE7I,YAAa;gBACvB6G,KAAK,EAAE;kBACLoB,UAAU,EAAEjI,YAAY,GAAG,SAAS,GAAG,mDAAmD;kBAC1FoH,KAAK,EAAE,OAAO;kBACdqB,MAAM,EAAE,MAAM;kBACdN,YAAY,EAAE,KAAK;kBACnBH,OAAO,EAAE,gBAAgB;kBACzBH,QAAQ,EAAE,MAAM;kBAChBiB,MAAM,EAAE9I,YAAY,GAAG,aAAa,GAAG,SAAS;kBAChD8H,UAAU,EAAE;gBACd,CAAE;gBAAAT,QAAA,EAEDrH,YAAY,GAAG,aAAa,GAAG;cAAwB;gBAAAwH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGApJ,aAAa,IAAII,eAAe,iBAC/B9B,OAAA;MAAKgK,KAAK,EAAE;QACVwD,QAAQ,EAAE,OAAO;QACjBC,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTxC,UAAU,EAAE,oBAAoB;QAChCnB,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxByD,MAAM,EAAE;MACV,CAAE;MAAArD,QAAA,eACAxK,OAAA;QAAKgK,KAAK,EAAE;UACVoB,UAAU,EAAE,OAAO;UACnBE,YAAY,EAAE,MAAM;UACpBH,OAAO,EAAE,MAAM;UACfqB,KAAK,EAAE,KAAK;UACZsB,QAAQ,EAAE,QAAQ;UAClBC,SAAS,EAAE,MAAM;UACjBpB,QAAQ,EAAE,QAAQ;UAClB1C,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE;QACjB,CAAE;QAAAM,QAAA,gBACAxK,OAAA;UAAKgK,KAAK,EAAE;YACVC,OAAO,EAAE,MAAM;YACfG,cAAc,EAAE,eAAe;YAC/BD,UAAU,EAAE,QAAQ;YACpBM,YAAY,EAAE;UAChB,CAAE;UAAAD,QAAA,gBACAxK,OAAA;YAAIgK,KAAK,EAAE;cACTe,MAAM,EAAE,CAAC;cACTR,KAAK,EAAE,SAAS;cAChBS,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACd,CAAE;YAAAT,QAAA,GAAC,gBACa,EAAC1I,eAAe,CAAC0C,OAAO,CAACC,SAAS;UAAA;YAAAkG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACL9K,OAAA;YACE+L,OAAO,EAAEA,CAAA,KAAM;cACbpK,gBAAgB,CAAC,KAAK,CAAC;cACvBI,kBAAkB,CAAC,IAAI,CAAC;cACxByE,SAAS,CAAC,CAAC;YACb,CAAE;YACFwD,KAAK,EAAE;cACLoB,UAAU,EAAE,MAAM;cAClBQ,MAAM,EAAE,MAAM;cACdZ,QAAQ,EAAE,QAAQ;cAClBiB,MAAM,EAAE,SAAS;cACjB1B,KAAK,EAAE;YACT,CAAE;YAAAC,QAAA,EACH;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELzH,KAAK,iBACJrD,OAAA;UAAKgK,KAAK,EAAE;YACVoB,UAAU,EAAE,SAAS;YACrBQ,MAAM,EAAE,mBAAmB;YAC3BN,YAAY,EAAE,KAAK;YACnBH,OAAO,EAAE,MAAM;YACfV,YAAY,EAAE,MAAM;YACpBF,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,EACCnH;QAAK;UAAAsH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAED9K,OAAA;UAAKgK,KAAK,EAAE;YAAEoC,IAAI,EAAE,CAAC;YAAEO,QAAQ,EAAE,MAAM;YAAEqB,YAAY,EAAE;UAAS,CAAE;UAAAxD,QAAA,eAChExK,OAAA;YAAMiO,QAAQ,EAAG3I,CAAC,IAAK;cAAEA,CAAC,CAAC4I,cAAc,CAAC,CAAC;cAAE5E,mBAAmB,CAAC,CAAC;YAAE,CAAE;YAACU,KAAK,EAAE;cAC5EC,OAAO,EAAE,MAAM;cACf4C,mBAAmB,EAAE,SAAS;cAC9BrB,GAAG,EAAE,MAAM;cACXwB,MAAM,EAAE;YACV,CAAE;YAAAxC,QAAA,gBAEAxK,OAAA;cAAKgK,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,aAAa,EAAE,QAAQ;gBAAEsB,GAAG,EAAE;cAAO,CAAE;cAAAhB,QAAA,gBAEpExK,OAAA;gBAAAwK,QAAA,gBACExK,OAAA;kBAAOgK,KAAK,EAAE;oBAAEC,OAAO,EAAE,OAAO;oBAAEQ,YAAY,EAAE,QAAQ;oBAAEQ,UAAU,EAAE,KAAK;oBAAEV,KAAK,EAAE;kBAAU,CAAE;kBAAAC,QAAA,EAAC;gBAEjG;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9K,OAAA;kBAAKgK,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEqB,GAAG,EAAE;kBAAO,CAAE;kBAAAhB,QAAA,GAChEvH,qBAAqB,gBACpBjD,OAAA;oBACE8M,GAAG,EAAE7J,qBAAsB;oBAC3B8J,GAAG,EAAC,iBAAiB;oBACrB/C,KAAK,EAAE;sBACLwC,KAAK,EAAE,MAAM;sBACbQ,MAAM,EAAE,MAAM;sBACd1B,YAAY,EAAE,KAAK;sBACnB2B,SAAS,EAAE,OAAO;sBAClBrB,MAAM,EAAE;oBACV,CAAE;oBACFsB,OAAO,EAAG5H,CAAC,IAAK;sBACdtB,OAAO,CAACX,KAAK,CAAC,oDAAoD,EAAEJ,qBAAqB,CAAC;sBAC1F,MAAMsC,MAAM,GAAGD,CAAC,CAACC,MAA0B;sBAC3CA,MAAM,CAACyE,KAAK,CAACC,OAAO,GAAG,MAAM;sBAC7B,MAAMkD,MAAM,GAAG5H,MAAM,CAAC6H,aAAa;sBACnC,IAAID,MAAM,EAAE;wBACVA,MAAM,CAACE,SAAS,GAAG;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;sBACH;oBACF;kBAAE;oBAAA1C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,gBAEF9K,OAAA;oBAAKgK,KAAK,EAAE;sBACVwC,KAAK,EAAE,MAAM;sBACbQ,MAAM,EAAE,MAAM;sBACd1B,YAAY,EAAE,KAAK;sBACnBI,eAAe,EAAE,SAAS;sBAC1BzB,OAAO,EAAE,MAAM;sBACfE,UAAU,EAAE,QAAQ;sBACpBC,cAAc,EAAE,QAAQ;sBACxBG,KAAK,EAAE,SAAS;sBAChBS,QAAQ,EAAE;oBACZ,CAAE;oBAAAR,QAAA,EAAC;kBAEH;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CACN,eACD9K,OAAA;oBAAKgK,KAAK,EAAE;sBAAEoC,IAAI,EAAE;oBAAE,CAAE;oBAAA5B,QAAA,gBACtBxK,OAAA;sBACE6G,IAAI,EAAC,MAAM;sBACXsH,MAAM,EAAC,SAAS;sBAChB5B,QAAQ,EAAE9F,0BAA2B;sBACrCuD,KAAK,EAAE;wBACLwC,KAAK,EAAE,MAAM;wBACbrB,OAAO,EAAE,QAAQ;wBACjBS,MAAM,EAAE,mBAAmB;wBAC3BN,YAAY,EAAE,KAAK;wBACnBN,QAAQ,EAAE;sBACZ;oBAAE;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACF9K,OAAA;sBAAKgK,KAAK,EAAE;wBAAEC,OAAO,EAAE,MAAM;wBAAEuB,GAAG,EAAE,QAAQ;wBAAEN,SAAS,EAAE;sBAAS,CAAE;sBAAAV,QAAA,EACjEvH,qBAAqB,iBACpBjD,OAAA;wBACE6G,IAAI,EAAC,QAAQ;wBACbkF,OAAO,EAAEvE,oBAAqB;wBAC9BwC,KAAK,EAAE;0BACLmB,OAAO,EAAE,gBAAgB;0BACzBO,eAAe,EAAE,SAAS;0BAC1BnB,KAAK,EAAE,OAAO;0BACdqB,MAAM,EAAE,MAAM;0BACdN,YAAY,EAAE,KAAK;0BACnBN,QAAQ,EAAE,SAAS;0BACnBiB,MAAM,EAAE;wBACV,CAAE;wBAAAzB,QAAA,EACH;sBAED;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ;oBACT;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAkBE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN9K,OAAA;gBAAAwK,QAAA,gBACExK,OAAA;kBAAOgK,KAAK,EAAE;oBAAEC,OAAO,EAAE,OAAO;oBAAEQ,YAAY,EAAE,QAAQ;oBAAEQ,UAAU,EAAE,KAAK;oBAAEV,KAAK,EAAE;kBAAU,CAAE;kBAAAC,QAAA,EAAC;gBAEjG;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9K,OAAA;kBACE6G,IAAI,EAAC,MAAM;kBACXtC,IAAI,EAAC,eAAe;kBACpBiB,KAAK,EAAExD,QAAQ,CAACE,aAAc;kBAC9BqK,QAAQ,EAAErG,iBAAkB;kBAC5BkI,QAAQ;kBACRpE,KAAK,EAAE;oBACLwC,KAAK,EAAE,MAAM;oBACbrB,OAAO,EAAE,SAAS;oBAClBS,MAAM,EAAE,mBAAmB;oBAC3BN,YAAY,EAAE,KAAK;oBACnBN,QAAQ,EAAE;kBACZ,CAAE;kBACFsB,WAAW,EAAC;gBAAiB;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGN9K,OAAA;gBAAAwK,QAAA,gBACExK,OAAA;kBAAOgK,KAAK,EAAE;oBAAEC,OAAO,EAAE,OAAO;oBAAEQ,YAAY,EAAE,QAAQ;oBAAEQ,UAAU,EAAE,KAAK;oBAAEV,KAAK,EAAE;kBAAU,CAAE;kBAAAC,QAAA,EAAC;gBAEjG;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9K,OAAA;kBACE6G,IAAI,EAAC,OAAO;kBACZtC,IAAI,EAAC,OAAO;kBACZiB,KAAK,EAAExD,QAAQ,CAACG,KAAM;kBACtBkM,QAAQ;kBACRD,QAAQ;kBACRpE,KAAK,EAAE;oBACLwC,KAAK,EAAE,MAAM;oBACbrB,OAAO,EAAE,SAAS;oBAClBS,MAAM,EAAE,mBAAmB;oBAC3BN,YAAY,EAAE,KAAK;oBACnBN,QAAQ,EAAE,MAAM;oBAChBU,eAAe,EAAE,SAAS;oBAC1BnB,KAAK,EAAE;kBACT;gBAAE;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGN9K,OAAA;gBAAAwK,QAAA,gBACExK,OAAA;kBAAOgK,KAAK,EAAE;oBAAEC,OAAO,EAAE,OAAO;oBAAEQ,YAAY,EAAE,QAAQ;oBAAEQ,UAAU,EAAE,KAAK;oBAAEV,KAAK,EAAE;kBAAU,CAAE;kBAAAC,QAAA,EAAC;gBAEjG;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9K,OAAA;kBACE6G,IAAI,EAAC,MAAM;kBACXtC,IAAI,EAAC,WAAW;kBAChBiB,KAAK,EAAExD,QAAQ,CAACI,SAAU;kBAC1BmK,QAAQ,EAAErG,iBAAkB;kBAC5BkI,QAAQ;kBACRpE,KAAK,EAAE;oBACLwC,KAAK,EAAE,MAAM;oBACbrB,OAAO,EAAE,SAAS;oBAClBS,MAAM,EAAE,mBAAmB;oBAC3BN,YAAY,EAAE,KAAK;oBACnBN,QAAQ,EAAE;kBACZ,CAAE;kBACFsB,WAAW,EAAC;gBAAM;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGN9K,OAAA;gBAAAwK,QAAA,gBACExK,OAAA;kBAAOgK,KAAK,EAAE;oBAAEC,OAAO,EAAE,OAAO;oBAAEQ,YAAY,EAAE,QAAQ;oBAAEQ,UAAU,EAAE,KAAK;oBAAEV,KAAK,EAAE;kBAAU,CAAE;kBAAAC,QAAA,EAAC;gBAEjG;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9K,OAAA;kBACE6G,IAAI,EAAC,MAAM;kBACXtC,IAAI,EAAC,UAAU;kBACfiB,KAAK,EAAExD,QAAQ,CAACM,QAAS;kBACzBiK,QAAQ,EAAErG,iBAAkB;kBAC5BkI,QAAQ;kBACRpE,KAAK,EAAE;oBACLwC,KAAK,EAAE,MAAM;oBACbrB,OAAO,EAAE,SAAS;oBAClBS,MAAM,EAAE,mBAAmB;oBAC3BN,YAAY,EAAE,KAAK;oBACnBN,QAAQ,EAAE;kBACZ,CAAE;kBACFsB,WAAW,EAAC;gBAAM;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGN9K,OAAA;gBAAAwK,QAAA,gBACExK,OAAA;kBAAOgK,KAAK,EAAE;oBAAEC,OAAO,EAAE,OAAO;oBAAEQ,YAAY,EAAE,QAAQ;oBAAEQ,UAAU,EAAE,KAAK;oBAAEV,KAAK,EAAE;kBAAU,CAAE;kBAAAC,QAAA,EAAC;gBAEjG;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9K,OAAA;kBACE6G,IAAI,EAAC,MAAM;kBACXtC,IAAI,EAAC,YAAY;kBACjBiB,KAAK,EAAExD,QAAQ,CAACK,UAAW;kBAC3BkK,QAAQ,EAAErG,iBAAkB;kBAC5B8D,KAAK,EAAE;oBACLwC,KAAK,EAAE,MAAM;oBACbrB,OAAO,EAAE,SAAS;oBAClBS,MAAM,EAAE,mBAAmB;oBAC3BN,YAAY,EAAE,KAAK;oBACnBN,QAAQ,EAAE;kBACZ,CAAE;kBACFsB,WAAW,EAAC;gBAAM;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGN9K,OAAA;gBAAAwK,QAAA,gBACExK,OAAA;kBAAOgK,KAAK,EAAE;oBAAEC,OAAO,EAAE,OAAO;oBAAEQ,YAAY,EAAE,QAAQ;oBAAEQ,UAAU,EAAE,KAAK;oBAAEV,KAAK,EAAE;kBAAU,CAAE;kBAAAC,QAAA,EAAC;gBAEjG;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9K,OAAA;kBACE6G,IAAI,EAAC,MAAM;kBACXtC,IAAI,EAAC,QAAQ;kBACbiB,KAAK,EAAExD,QAAQ,CAACO,MAAO;kBACvBgK,QAAQ,EAAErG,iBAAkB;kBAC5B8D,KAAK,EAAE;oBACLwC,KAAK,EAAE,MAAM;oBACbrB,OAAO,EAAE,SAAS;oBAClBS,MAAM,EAAE,mBAAmB;oBAC3BN,YAAY,EAAE,KAAK;oBACnBN,QAAQ,EAAE;kBACZ,CAAE;kBACFsB,WAAW,EAAC;gBAAe;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN9K,OAAA;cAAKgK,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,aAAa,EAAE,QAAQ;gBAAEsB,GAAG,EAAE;cAAO,CAAE;cAAAhB,QAAA,gBAEpExK,OAAA;gBAAAwK,QAAA,gBACExK,OAAA;kBAAOgK,KAAK,EAAE;oBAAEC,OAAO,EAAE,OAAO;oBAAEQ,YAAY,EAAE,QAAQ;oBAAEQ,UAAU,EAAE,KAAK;oBAAEV,KAAK,EAAE;kBAAU,CAAE;kBAAAC,QAAA,EAAC;gBAEjG;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9K,OAAA;kBACE6G,IAAI,EAAC,KAAK;kBACVtC,IAAI,EAAC,aAAa;kBAClBiB,KAAK,EAAExD,QAAQ,CAACQ,WAAY;kBAC5B+J,QAAQ,EAAErG,iBAAkB;kBAC5BoI,OAAO,EAAGhJ,CAAC,IAAK;oBACd;oBACAA,CAAC,CAACiJ,aAAa,CAAC/I,KAAK,GAAGF,CAAC,CAACiJ,aAAa,CAAC/I,KAAK,CAACO,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;kBACtE,CAAE;kBACFqI,QAAQ;kBACRpE,KAAK,EAAE;oBACLwC,KAAK,EAAE,MAAM;oBACbrB,OAAO,EAAE,SAAS;oBAClBS,MAAM,EAAE,mBAAmB;oBAC3BN,YAAY,EAAE,KAAK;oBACnBN,QAAQ,EAAE;kBACZ,CAAE;kBACFwD,SAAS,EAAE;gBAAG;kBAAA7D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGN9K,OAAA;gBAAAwK,QAAA,gBACExK,OAAA;kBAAOgK,KAAK,EAAE;oBAAEC,OAAO,EAAE,OAAO;oBAAEQ,YAAY,EAAE,QAAQ;oBAAEQ,UAAU,EAAE,KAAK;oBAAEV,KAAK,EAAE;kBAAU,CAAE;kBAAAC,QAAA,EAAC;gBAEjG;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9K,OAAA;kBACEuE,IAAI,EAAC,YAAY;kBACjBiB,KAAK,EAAExD,QAAQ,CAACS,UAAW;kBAC3B8J,QAAQ,EAAErG,iBAAkB;kBAC5BkI,QAAQ;kBACRpE,KAAK,EAAE;oBACLwC,KAAK,EAAE,MAAM;oBACbrB,OAAO,EAAE,SAAS;oBAClBS,MAAM,EAAE,mBAAmB;oBAC3BN,YAAY,EAAE,KAAK;oBACnBN,QAAQ,EAAE,MAAM;oBAChBU,eAAe,EAAE;kBACnB,CAAE;kBAAAlB,QAAA,EAEDnJ,uBAAuB,CAAC,CAAC,CAAC+C,GAAG,CAACqK,KAAK,iBAClCzO,OAAA;oBAAoBwF,KAAK,EAAEiJ,KAAM;oBAAAjE,QAAA,GAAC,QAAM,EAACiE,KAAK;kBAAA,GAAjCA,KAAK;oBAAA9D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAqC,CACxD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAGN9K,OAAA;gBAAAwK,QAAA,gBACExK,OAAA;kBAAOgK,KAAK,EAAE;oBAAEC,OAAO,EAAE,OAAO;oBAAEQ,YAAY,EAAE,QAAQ;oBAAEQ,UAAU,EAAE,KAAK;oBAAEV,KAAK,EAAE;kBAAU,CAAE;kBAAAC,QAAA,EAAC;gBAEjG;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9K,OAAA;kBACE6G,IAAI,EAAC,MAAM;kBACXtC,IAAI,EAAC,SAAS;kBACdiB,KAAK,EAAExD,QAAQ,CAACW,OAAQ;kBACxB4J,QAAQ,EAAErG,iBAAkB;kBAC5BkI,QAAQ;kBACRpE,KAAK,EAAE;oBACLwC,KAAK,EAAE,MAAM;oBACbrB,OAAO,EAAE,SAAS;oBAClBS,MAAM,EAAE,mBAAmB;oBAC3BN,YAAY,EAAE,KAAK;oBACnBN,QAAQ,EAAE;kBACZ;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGN9K,OAAA;gBAAAwK,QAAA,gBACExK,OAAA;kBAAOgK,KAAK,EAAE;oBAAEC,OAAO,EAAE,OAAO;oBAAEQ,YAAY,EAAE,QAAQ;oBAAEQ,UAAU,EAAE,KAAK;oBAAEV,KAAK,EAAE;kBAAU,CAAE;kBAAAC,QAAA,EAAC;gBAEjG;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9K,OAAA;kBACE6G,IAAI,EAAC,MAAM;kBACXtC,IAAI,EAAC,oBAAoB;kBACzBiB,KAAK,EAAExD,QAAQ,CAACY,kBAAmB;kBACnC2J,QAAQ,EAAErG,iBAAkB;kBAC5B8D,KAAK,EAAE;oBACLwC,KAAK,EAAE,MAAM;oBACbrB,OAAO,EAAE,SAAS;oBAClBS,MAAM,EAAE,mBAAmB;oBAC3BN,YAAY,EAAE,KAAK;oBACnBN,QAAQ,EAAE;kBACZ;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGN9K,OAAA;gBAAAwK,QAAA,gBACExK,OAAA;kBAAOgK,KAAK,EAAE;oBAAEC,OAAO,EAAE,OAAO;oBAAEQ,YAAY,EAAE,QAAQ;oBAAEQ,UAAU,EAAE,KAAK;oBAAEV,KAAK,EAAE;kBAAU,CAAE;kBAAAC,QAAA,EAAC;gBAEjG;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9K,OAAA;kBACE6G,IAAI,EAAC,KAAK;kBACVtC,IAAI,EAAC,qBAAqB;kBAC1BiB,KAAK,EAAExD,QAAQ,CAACa,mBAAoB;kBACpC0J,QAAQ,EAAErG,iBAAkB;kBAC5BoI,OAAO,EAAGhJ,CAAC,IAAK;oBACd;oBACAA,CAAC,CAACiJ,aAAa,CAAC/I,KAAK,GAAGF,CAAC,CAACiJ,aAAa,CAAC/I,KAAK,CAACO,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;kBACtE,CAAE;kBACFiE,KAAK,EAAE;oBACLwC,KAAK,EAAE,MAAM;oBACbrB,OAAO,EAAE,SAAS;oBAClBS,MAAM,EAAE,mBAAmB;oBAC3BN,YAAY,EAAE,KAAK;oBACnBN,QAAQ,EAAE;kBACZ,CAAE;kBACFwD,SAAS,EAAE;gBAAG;kBAAA7D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGN9K,OAAA;gBAAAwK,QAAA,gBACExK,OAAA;kBAAOgK,KAAK,EAAE;oBAAEC,OAAO,EAAE,OAAO;oBAAEQ,YAAY,EAAE,QAAQ;oBAAEQ,UAAU,EAAE,KAAK;oBAAEV,KAAK,EAAE;kBAAU,CAAE;kBAAAC,QAAA,EAAC;gBAEjG;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9K,OAAA;kBACEuE,IAAI,EAAC,SAAS;kBACdiB,KAAK,EAAExD,QAAQ,CAACc,OAAQ;kBACxByJ,QAAQ,EAAErG,iBAAkB;kBAC5BwI,IAAI,EAAE,CAAE;kBACR1E,KAAK,EAAE;oBACLwC,KAAK,EAAE,MAAM;oBACbrB,OAAO,EAAE,SAAS;oBAClBS,MAAM,EAAE,mBAAmB;oBAC3BN,YAAY,EAAE,KAAK;oBACnBN,QAAQ,EAAE,MAAM;oBAChB2D,MAAM,EAAE;kBACV;gBAAE;kBAAAhE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN9K,OAAA;cAAKgK,KAAK,EAAE;gBACV4E,UAAU,EAAE,QAAQ;gBACpB3E,OAAO,EAAE,MAAM;gBACfG,cAAc,EAAE,UAAU;gBAC1BoB,GAAG,EAAE,MAAM;gBACXN,SAAS,EAAE,MAAM;gBACjB2D,UAAU,EAAE,MAAM;gBAClBtB,SAAS,EAAE;cACb,CAAE;cAAA/C,QAAA,gBACAxK,OAAA;gBACE6G,IAAI,EAAC,QAAQ;gBACbkF,OAAO,EAAEA,CAAA,KAAM;kBACbpK,gBAAgB,CAAC,KAAK,CAAC;kBACvBI,kBAAkB,CAAC,IAAI,CAAC;kBACxByE,SAAS,CAAC,CAAC;gBACb,CAAE;gBACFwF,QAAQ,EAAE7I,YAAa;gBACvB6G,KAAK,EAAE;kBACLoB,UAAU,EAAE,SAAS;kBACrBb,KAAK,EAAE,SAAS;kBAChBqB,MAAM,EAAE,MAAM;kBACdN,YAAY,EAAE,KAAK;kBACnBH,OAAO,EAAE,gBAAgB;kBACzBH,QAAQ,EAAE,MAAM;kBAChBiB,MAAM,EAAE9I,YAAY,GAAG,aAAa,GAAG,SAAS;kBAChDuH,OAAO,EAAEvH,YAAY,GAAG,GAAG,GAAG;gBAChC,CAAE;gBAAAqH,QAAA,EACH;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT9K,OAAA;gBACE6G,IAAI,EAAC,QAAQ;gBACbmF,QAAQ,EAAE7I,YAAa;gBACvB6G,KAAK,EAAE;kBACLoB,UAAU,EAAEjI,YAAY,GAAG,SAAS,GAAG,mDAAmD;kBAC1FoH,KAAK,EAAE,OAAO;kBACdqB,MAAM,EAAE,MAAM;kBACdN,YAAY,EAAE,KAAK;kBACnBH,OAAO,EAAE,gBAAgB;kBACzBH,QAAQ,EAAE,MAAM;kBAChBiB,MAAM,EAAE9I,YAAY,GAAG,aAAa,GAAG,SAAS;kBAChD8H,UAAU,EAAE;gBACd,CAAE;gBAAAT,QAAA,EAEDrH,YAAY,GAAG,aAAa,GAAG;cAAgB;gBAAAwH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAlJ,eAAe,IAAIE,eAAe,iBACjC9B,OAAA;MAAKgK,KAAK,EAAE;QACVwD,QAAQ,EAAE,OAAO;QACjBC,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTxC,UAAU,EAAE,oBAAoB;QAChCnB,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxByD,MAAM,EAAE;MACV,CAAE;MAAArD,QAAA,eACAxK,OAAA;QAAKgK,KAAK,EAAE;UACVoB,UAAU,EAAE,OAAO;UACnBE,YAAY,EAAE,MAAM;UACpBH,OAAO,EAAE,MAAM;UACfqB,KAAK,EAAE,KAAK;UACZsB,QAAQ,EAAE;QACZ,CAAE;QAAAtD,QAAA,gBACAxK,OAAA;UAAKgK,KAAK,EAAE;YACVC,OAAO,EAAE,MAAM;YACfG,cAAc,EAAE,eAAe;YAC/BD,UAAU,EAAE,QAAQ;YACpBM,YAAY,EAAE;UAChB,CAAE;UAAAD,QAAA,gBACAxK,OAAA;YAAIgK,KAAK,EAAE;cACTe,MAAM,EAAE,CAAC;cACTR,KAAK,EAAE,SAAS;cAChBS,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACd,CAAE;YAAAT,QAAA,EAAC;UAEH;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL9K,OAAA;YACE+L,OAAO,EAAEA,CAAA,KAAM;cACblK,kBAAkB,CAAC,KAAK,CAAC;cACzBE,kBAAkB,CAAC,IAAI,CAAC;YAC1B,CAAE;YACFiI,KAAK,EAAE;cACLoB,UAAU,EAAE,MAAM;cAClBQ,MAAM,EAAE,MAAM;cACdZ,QAAQ,EAAE,QAAQ;cAClBiB,MAAM,EAAE,SAAS;cACjB1B,KAAK,EAAE;YACT,CAAE;YAAAC,QAAA,EACH;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN9K,OAAA;UAAKgK,KAAK,EAAE;YAAES,YAAY,EAAE;UAAO,CAAE;UAAAD,QAAA,gBACnCxK,OAAA;YAAGgK,KAAK,EAAE;cAAEe,MAAM,EAAE,YAAY;cAAER,KAAK,EAAE;YAAU,CAAE;YAAAC,QAAA,EAAC;UAEtD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ9K,OAAA;YAAKgK,KAAK,EAAE;cACVoB,UAAU,EAAE,SAAS;cACrBQ,MAAM,EAAE,mBAAmB;cAC3BN,YAAY,EAAE,KAAK;cACnBH,OAAO,EAAE;YACX,CAAE;YAAAX,QAAA,gBACAxK,OAAA;cAAGgK,KAAK,EAAE;gBAAEe,MAAM,EAAE,CAAC;gBAAEE,UAAU,EAAE,KAAK;gBAAEV,KAAK,EAAE;cAAU,CAAE;cAAAC,QAAA,EAC1D1I,eAAe,CAAC0C,OAAO,CAACC;YAAS;cAAAkG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACJ9K,OAAA;cAAGgK,KAAK,EAAE;gBAAEe,MAAM,EAAE,eAAe;gBAAER,KAAK,EAAE,SAAS;gBAAES,QAAQ,EAAE;cAAW,CAAE;cAAAR,QAAA,GAAC,kBAC7D,EAAC1I,eAAe,CAACwG,cAAc;YAAA;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACJ9K,OAAA;cAAGgK,KAAK,EAAE;gBAAEe,MAAM,EAAE,eAAe;gBAAER,KAAK,EAAE,SAAS;gBAAES,QAAQ,EAAE;cAAW,CAAE;cAAAR,QAAA,GAAC,SACtE,EAAC1I,eAAe,CAACK,KAAK;YAAA;cAAAwI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN9K,OAAA;YAAGgK,KAAK,EAAE;cAAEe,MAAM,EAAE,YAAY;cAAER,KAAK,EAAE,SAAS;cAAES,QAAQ,EAAE;YAAW,CAAE;YAAAR,QAAA,eACzExK,OAAA;cAAMgK,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,YAAY;gBAAEqB,GAAG,EAAE;cAAS,CAAE;cAAAhB,QAAA,gBACxExK,OAAA,CAACZ,aAAa;gBAAC4H,IAAI,EAAE,EAAG;gBAACuD,KAAK,EAAC,SAAS;gBAACP,KAAK,EAAE;kBAAEkB,SAAS,EAAE,UAAU;kBAAE4D,UAAU,EAAE;gBAAE;cAAE;gBAAAnE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,yGAE9F;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN9K,OAAA;UAAKgK,KAAK,EAAE;YACVC,OAAO,EAAE,MAAM;YACfG,cAAc,EAAE,UAAU;YAC1BoB,GAAG,EAAE;UACP,CAAE;UAAAhB,QAAA,gBACAxK,OAAA;YACE+L,OAAO,EAAEA,CAAA,KAAM;cACblK,kBAAkB,CAAC,KAAK,CAAC;cACzBE,kBAAkB,CAAC,IAAI,CAAC;YAC1B,CAAE;YACFiK,QAAQ,EAAE7I,YAAa;YACvB6G,KAAK,EAAE;cACLoB,UAAU,EAAE,SAAS;cACrBb,KAAK,EAAE,SAAS;cAChBqB,MAAM,EAAE,MAAM;cACdN,YAAY,EAAE,KAAK;cACnBH,OAAO,EAAE,gBAAgB;cACzBH,QAAQ,EAAE,MAAM;cAChBiB,MAAM,EAAE9I,YAAY,GAAG,aAAa,GAAG,SAAS;cAChDuH,OAAO,EAAEvH,YAAY,GAAG,GAAG,GAAG;YAChC,CAAE;YAAAqH,QAAA,EACH;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9K,OAAA;YACE+L,OAAO,EAAEnC,oBAAqB;YAC9BoC,QAAQ,EAAE7I,YAAa;YACvB6G,KAAK,EAAE;cACLoB,UAAU,EAAEjI,YAAY,GAAG,SAAS,GAAG,SAAS;cAChDoH,KAAK,EAAE,OAAO;cACdqB,MAAM,EAAE,MAAM;cACdN,YAAY,EAAE,KAAK;cACnBH,OAAO,EAAE,gBAAgB;cACzBH,QAAQ,EAAE,MAAM;cAChBiB,MAAM,EAAE9I,YAAY,GAAG,aAAa,GAAG,SAAS;cAChDuH,OAAO,EAAEvH,YAAY,GAAG,GAAG,GAAG;YAChC,CAAE;YAAAqH,QAAA,EAEDrH,YAAY,GAAG,iBAAiB,GAAG;UAAoB;YAAAwH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC1K,EAAA,CA1nEID,iBAA2B;EAAA,QAEdP,YAAY,EACTC,cAAc;AAAA;AAAAkP,EAAA,GAH9B5O,iBAA2B;AA4nEjC,eAAeA,iBAAiB;AAAC,IAAA4O,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}