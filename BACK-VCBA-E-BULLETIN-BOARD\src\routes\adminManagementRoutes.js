const express = require('express');
const { body, param, query } = require('express-validator');
const AdminManagementController = require('../controllers/AdminManagementController');
const { authenticate, adminOnly } = require('../middleware/auth');
const { requireManageAdminAccounts, requireManageAdminProfiles } = require('../middleware/permissions');
const { validateRequest, commonValidations } = require('../middleware/validation');

const router = express.Router();

// Apply authentication and admin-only middleware to all routes
router.use(authenticate);
router.use(adminOnly);

// Validation schemas
const adminIdValidation = [
  param('adminId')
    .isInt({ min: 1 })
    .withMessage('Admin ID must be a positive integer')
    .toInt(),
];

const createAdminValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  body('first_name')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name must be between 1 and 50 characters'),
  body('last_name')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name must be between 1 and 50 characters'),
  body('middle_name')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Middle name must not exceed 50 characters'),
  body('suffix')
    .optional()
    .trim()
    .isLength({ max: 10 })
    .withMessage('Suffix must not exceed 10 characters'),
  body('phone_number')
    .optional()
    .trim()
    .matches(/^(\+63|0)?[0-9]{10}$/)
    .withMessage('Phone number must be a valid Philippine number'),
  body('department')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Department must not exceed 100 characters'),
  body('position')
    .isIn(['super_admin', 'professor'])
    .withMessage('Position must be either super_admin or professor'),
  body('grade_level')
    .optional()
    .isInt({ min: 11, max: 12 })
    .withMessage('Grade level must be 11 or 12')
    .toInt(),
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('is_active must be a boolean')
    .toBoolean(),
];

const updateAdminValidation = [
  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  body('first_name')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name must be between 1 and 50 characters'),
  body('last_name')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name must be between 1 and 50 characters'),
  body('middle_name')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Middle name must not exceed 50 characters'),
  body('suffix')
    .optional()
    .trim()
    .isLength({ max: 10 })
    .withMessage('Suffix must not exceed 10 characters'),
  body('phone_number')
    .optional()
    .trim()
    .matches(/^(\+63|0)?[0-9]{10}$/)
    .withMessage('Phone number must be a valid Philippine number'),
  body('department')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Department must not exceed 100 characters'),
  body('position')
    .optional()
    .isIn(['super_admin', 'professor'])
    .withMessage('Position must be either super_admin or professor'),
  body('grade_level')
    .optional()
    .isInt({ min: 11, max: 12 })
    .withMessage('Grade level must be 11 or 12')
    .toInt(),
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('is_active must be a boolean')
    .toBoolean(),
];

const resetPasswordValidation = [
  body('new_password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
];

const getAdminsValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer')
    .toInt(),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100')
    .toInt(),
  query('position')
    .optional()
    .isIn(['super_admin', 'professor'])
    .withMessage('Position must be either super_admin or professor'),
  query('is_active')
    .optional()
    .isBoolean()
    .withMessage('is_active must be a boolean')
    .toBoolean(),
  query('search')
    .optional()
    .trim(),
];

// Admin management routes (super_admin only)
router.get('/admins', requireManageAdminAccounts, getAdminsValidation, validateRequest, AdminManagementController.getAdmins);
router.get('/admins/:adminId', requireManageAdminAccounts, adminIdValidation, validateRequest, AdminManagementController.getAdmin);
router.post('/admins', requireManageAdminAccounts, createAdminValidation, validateRequest, AdminManagementController.createAdmin);
router.put('/admins/:adminId', requireManageAdminAccounts, updateAdminValidation, validateRequest, AdminManagementController.updateAdmin);
router.delete('/admins/:adminId', requireManageAdminAccounts, adminIdValidation, validateRequest, AdminManagementController.deleteAdmin);
router.post('/admins/:adminId/reset-password', requireManageAdminAccounts, resetPasswordValidation, validateRequest, AdminManagementController.resetAdminPassword);

// Admin position management (super_admin only)
router.put('/admins/:adminId/position', requireManageAdminAccounts, adminIdValidation,
  body('position').isIn(['super_admin', 'professor']).withMessage('Position must be either super_admin or professor'),
  validateRequest, AdminManagementController.updateAdminPosition);
router.get('/admins/by-position/:position', requireManageAdminAccounts, validateRequest, AdminManagementController.getAdminsByPosition);

// Admin status management (super_admin only)
router.put('/admins/:adminId/activate', requireManageAdminAccounts, adminIdValidation, validateRequest, AdminManagementController.activateAdmin);
router.put('/admins/:adminId/deactivate', requireManageAdminAccounts, adminIdValidation, validateRequest, AdminManagementController.deactivateAdmin);

module.exports = router;
