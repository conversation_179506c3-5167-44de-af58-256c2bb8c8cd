{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\admin\\\\CategoryManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { FolderTree, Plus, AlertTriangle, CheckCircle } from 'lucide-react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\nimport { categoryService } from '../../services/categoryService';\nimport CategoryList from '../../components/admin/CategoryList';\nimport CategoryModal from '../../components/admin/CategoryModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CategoryManagement = () => {\n  _s();\n  const {\n    user\n  } = useAdminAuth();\n  const permissions = usePermissions(user);\n\n  // State management\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n\n  // Modal state\n  const [showModal, setShowModal] = useState(false);\n  const [modalMode, setModalMode] = useState('add_category');\n  const [editingCategory, setEditingCategory] = useState(null);\n  const [editingSubcategory, setEditingSubcategory] = useState(null);\n  const [parentCategory, setParentCategory] = useState(null);\n  const [modalLoading, setModalLoading] = useState(false);\n  useEffect(() => {\n    // Check if user has permission to manage categories\n    if (!permissions.canManageCategories) {\n      setError('You do not have permission to manage categories');\n      setLoading(false);\n      return;\n    }\n    loadCategories();\n  }, [permissions.canManageCategories]);\n  const loadCategories = async () => {\n    try {\n      var _response$data;\n      setLoading(true);\n      setError(null);\n      console.log('🔍 CategoryManagement - Loading categories from API...');\n      const response = await categoryService.getCategoriesWithSubcategories();\n      console.log('🔍 CategoryManagement - API response:', response);\n      if (response.success && (_response$data = response.data) !== null && _response$data !== void 0 && _response$data.categories) {\n        setCategories(response.data.categories);\n        console.log('✅ CategoryManagement - Categories loaded successfully:', response.data.categories.length);\n      } else {\n        throw new Error(response.message || 'Failed to load categories');\n      }\n    } catch (err) {\n      var _err$response;\n      console.error('❌ CategoryManagement - Error loading categories:', err);\n\n      // Check if it's a 404 or network error (API not available)\n      const isApiUnavailable = ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status) === 404 || err.code === 'ECONNREFUSED' || err.message.includes('Network Error') || err.message.includes('Failed to fetch');\n      if (isApiUnavailable) {\n        console.log('⚠️ CategoryManagement - API not available, using mock data');\n\n        // Use mock data as fallback\n        const mockCategories = [{\n          category_id: 1,\n          name: 'Academic',\n          description: 'Academic-related announcements and events',\n          color_code: '#3b82f6',\n          is_active: true,\n          created_at: '2025-01-01T00:00:00Z',\n          updated_at: '2025-01-01T00:00:00Z',\n          subcategories: [{\n            subcategory_id: 1,\n            category_id: 1,\n            name: 'Exams',\n            description: 'Examination schedules and updates',\n            color_code: '#ef4444',\n            display_order: 1,\n            is_active: true,\n            created_at: '2025-01-01T00:00:00Z',\n            updated_at: '2025-01-01T00:00:00Z'\n          }, {\n            subcategory_id: 2,\n            category_id: 1,\n            name: 'Assignments',\n            description: 'Assignment deadlines and submissions',\n            color_code: '#f59e0b',\n            display_order: 2,\n            is_active: true,\n            created_at: '2025-01-01T00:00:00Z',\n            updated_at: '2025-01-01T00:00:00Z'\n          }, {\n            subcategory_id: 3,\n            category_id: 1,\n            name: 'Class Schedules',\n            description: 'Class timing and schedule changes',\n            color_code: '#06b6d4',\n            display_order: 3,\n            is_active: true,\n            created_at: '2025-01-01T00:00:00Z',\n            updated_at: '2025-01-01T00:00:00Z'\n          }]\n        }, {\n          category_id: 2,\n          name: 'Events',\n          description: 'School events and activities',\n          color_code: '#10b981',\n          is_active: true,\n          created_at: '2025-01-01T00:00:00Z',\n          updated_at: '2025-01-01T00:00:00Z',\n          subcategories: [{\n            subcategory_id: 4,\n            category_id: 2,\n            name: 'Sports',\n            description: 'Sports events and competitions',\n            color_code: '#8b5cf6',\n            display_order: 1,\n            is_active: true,\n            created_at: '2025-01-01T00:00:00Z',\n            updated_at: '2025-01-01T00:00:00Z'\n          }, {\n            subcategory_id: 5,\n            category_id: 2,\n            name: 'Cultural',\n            description: 'Cultural events and celebrations',\n            color_code: '#ec4899',\n            display_order: 2,\n            is_active: true,\n            created_at: '2025-01-01T00:00:00Z',\n            updated_at: '2025-01-01T00:00:00Z'\n          }]\n        }, {\n          category_id: 3,\n          name: 'Administrative',\n          description: 'Administrative notices and updates',\n          color_code: '#f97316',\n          is_active: true,\n          created_at: '2025-01-01T00:00:00Z',\n          updated_at: '2025-01-01T00:00:00Z',\n          subcategories: [{\n            subcategory_id: 6,\n            category_id: 3,\n            name: 'Policies',\n            description: 'School policies and regulations',\n            color_code: '#6366f1',\n            display_order: 1,\n            is_active: true,\n            created_at: '2025-01-01T00:00:00Z',\n            updated_at: '2025-01-01T00:00:00Z'\n          }]\n        }, {\n          category_id: 4,\n          name: 'Emergency',\n          description: 'Emergency announcements and alerts',\n          color_code: '#dc2626',\n          is_active: true,\n          created_at: '2025-01-01T00:00:00Z',\n          updated_at: '2025-01-01T00:00:00Z',\n          subcategories: []\n        }];\n        setCategories(mockCategories);\n        setError('⚠️ Using demo data - Backend API not connected. Categories will work in demo mode.');\n      } else {\n        setError(err.message || 'Failed to load categories');\n        setCategories([]);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handler functions\n  const handleAddCategory = () => {\n    setModalMode('add_category');\n    setEditingCategory(null);\n    setEditingSubcategory(null);\n    setParentCategory(null);\n    setShowModal(true);\n  };\n  const handleEditCategory = category => {\n    setModalMode('edit_category');\n    setEditingCategory(category);\n    setEditingSubcategory(null);\n    setParentCategory(null);\n    setShowModal(true);\n  };\n  const handleDeleteCategory = async category => {\n    try {\n      setError(null);\n      setSuccess(null);\n      if (!category.category_id) {\n        throw new Error('Category ID is required');\n      }\n      console.log('🔍 CategoryManagement - Deleting category:', category.category_id);\n      const response = await categoryService.deleteCategory(category.category_id);\n      if (response.success) {\n        setSuccess(`Category \"${category.name}\" has been deleted successfully`);\n        loadCategories();\n      } else {\n        throw new Error(response.message || 'Failed to delete category');\n      }\n    } catch (err) {\n      var _err$response2;\n      console.error('❌ CategoryManagement - Error deleting category:', err);\n\n      // Check if API is unavailable\n      const isApiUnavailable = ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.status) === 404 || err.code === 'ECONNREFUSED' || err.message.includes('Network Error') || err.message.includes('Failed to fetch');\n      if (isApiUnavailable) {\n        setError('⚠️ Demo mode: Cannot delete categories without backend API connection');\n      } else {\n        setError(err.message || 'Failed to delete category');\n      }\n    }\n  };\n  const handleToggleCategoryStatus = async category => {\n    try {\n      setError(null);\n      setSuccess(null);\n      if (!category.category_id) {\n        throw new Error('Category ID is required');\n      }\n      console.log('🔍 CategoryManagement - Toggling category status:', category.category_id, !category.is_active);\n      const response = await categoryService.toggleCategoryStatus(category.category_id, !category.is_active);\n      if (response.success) {\n        const action = category.is_active ? 'deactivated' : 'activated';\n        setSuccess(`Category \"${category.name}\" has been ${action} successfully`);\n        loadCategories();\n      } else {\n        throw new Error(response.message || 'Failed to update category status');\n      }\n    } catch (err) {\n      console.error('❌ CategoryManagement - Error toggling category status:', err);\n      setError(err.message || 'Failed to update category status');\n    }\n  };\n  const handleAddSubcategory = category => {\n    setModalMode('add_subcategory');\n    setEditingCategory(null);\n    setEditingSubcategory(null);\n    setParentCategory(category);\n    setShowModal(true);\n  };\n  const handleEditSubcategory = (category, subcategory) => {\n    setModalMode('edit_subcategory');\n    setEditingCategory(null);\n    setEditingSubcategory(subcategory);\n    setParentCategory(category);\n    setShowModal(true);\n  };\n  const handleDeleteSubcategory = async (category, subcategory) => {\n    try {\n      setError(null);\n      setSuccess(null);\n      if (!category.category_id || !subcategory.subcategory_id) {\n        throw new Error('Category ID and Subcategory ID are required');\n      }\n      console.log('🔍 CategoryManagement - Deleting subcategory:', category.category_id, subcategory.subcategory_id);\n      const response = await categoryService.deleteSubcategory(category.category_id, subcategory.subcategory_id);\n      if (response.success) {\n        setSuccess(`Subcategory \"${subcategory.name}\" has been deleted successfully`);\n        loadCategories();\n      } else {\n        throw new Error(response.message || 'Failed to delete subcategory');\n      }\n    } catch (err) {\n      console.error('❌ CategoryManagement - Error deleting subcategory:', err);\n      setError(err.message || 'Failed to delete subcategory');\n    }\n  };\n  const handleToggleSubcategoryStatus = async (category, subcategory) => {\n    try {\n      setError(null);\n      setSuccess(null);\n      if (!category.category_id || !subcategory.subcategory_id) {\n        throw new Error('Category ID and Subcategory ID are required');\n      }\n      console.log('🔍 CategoryManagement - Toggling subcategory status:', category.category_id, subcategory.subcategory_id, !subcategory.is_active);\n      const response = await categoryService.toggleSubcategoryStatus(category.category_id, subcategory.subcategory_id, !subcategory.is_active);\n      if (response.success) {\n        const action = subcategory.is_active ? 'deactivated' : 'activated';\n        setSuccess(`Subcategory \"${subcategory.name}\" has been ${action} successfully`);\n        loadCategories();\n      } else {\n        throw new Error(response.message || 'Failed to update subcategory status');\n      }\n    } catch (err) {\n      console.error('❌ CategoryManagement - Error toggling subcategory status:', err);\n      setError(err.message || 'Failed to update subcategory status');\n    }\n  };\n  const handleSave = async (data, parentCat) => {\n    try {\n      var _response;\n      setModalLoading(true);\n      setError(null);\n      setSuccess(null);\n      let response;\n      if (modalMode === 'add_category') {\n        console.log('🔍 CategoryManagement - Creating category:', data);\n        response = await categoryService.createCategory(data);\n        if (response.success) {\n          setSuccess(`Category \"${data.name}\" has been created successfully`);\n        }\n      } else if (modalMode === 'edit_category') {\n        if (!(editingCategory !== null && editingCategory !== void 0 && editingCategory.category_id)) {\n          throw new Error('Category ID is required for editing');\n        }\n        console.log('🔍 CategoryManagement - Updating category:', editingCategory.category_id, data);\n        response = await categoryService.updateCategory(editingCategory.category_id, data);\n        if (response.success) {\n          setSuccess(`Category \"${data.name}\" has been updated successfully`);\n        }\n      } else if (modalMode === 'add_subcategory') {\n        if (!(parentCat !== null && parentCat !== void 0 && parentCat.category_id)) {\n          throw new Error('Parent category ID is required for creating subcategory');\n        }\n        console.log('🔍 CategoryManagement - Creating subcategory:', parentCat.category_id, data);\n        response = await categoryService.createSubcategory(parentCat.category_id, data);\n        if (response.success) {\n          setSuccess(`Subcategory \"${data.name}\" has been created successfully`);\n        }\n      } else if (modalMode === 'edit_subcategory') {\n        if (!(parentCat !== null && parentCat !== void 0 && parentCat.category_id) || !(editingSubcategory !== null && editingSubcategory !== void 0 && editingSubcategory.subcategory_id)) {\n          throw new Error('Category ID and Subcategory ID are required for editing');\n        }\n        console.log('🔍 CategoryManagement - Updating subcategory:', parentCat.category_id, editingSubcategory.subcategory_id, data);\n        response = await categoryService.updateSubcategory(parentCat.category_id, editingSubcategory.subcategory_id, data);\n        if (response.success) {\n          setSuccess(`Subcategory \"${data.name}\" has been updated successfully`);\n        }\n      }\n      if (!((_response = response) !== null && _response !== void 0 && _response.success)) {\n        var _response2;\n        throw new Error(((_response2 = response) === null || _response2 === void 0 ? void 0 : _response2.message) || 'Failed to save changes');\n      }\n      loadCategories();\n    } catch (err) {\n      console.error('❌ CategoryManagement - Error saving:', err);\n      setError(err.message || 'Failed to save changes');\n      throw err; // Re-throw to prevent modal from closing\n    } finally {\n      setModalLoading(false);\n    }\n  };\n  const clearMessages = () => {\n    setError(null);\n    setSuccess(null);\n  };\n  if (!permissions.canManageCategories) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      },\n      children: [/*#__PURE__*/_jsxDEV(FolderTree, {\n        size: 64,\n        style: {\n          marginBottom: '1rem',\n          opacity: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          margin: '0 0 0.5rem',\n          fontSize: '1.5rem',\n          fontWeight: '600'\n        },\n        children: \"Access Denied\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 434,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: 0,\n          fontSize: '1rem'\n        },\n        children: \"You do not have permission to manage categories and subcategories.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 437,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        },\n        children: [\"Current Role: \", permissions.getPositionDisplayName()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 440,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 424,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '40px',\n          height: '40px',\n          border: '4px solid #f3f4f6',\n          borderTop: '4px solid #3b82f6',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 463,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 457,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '2rem',\n        textAlign: 'center',\n        color: '#ef4444'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Error\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 482,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 483,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: loadCategories,\n        style: {\n          padding: '0.5rem 1rem',\n          background: '#3b82f6',\n          color: 'white',\n          border: 'none',\n          borderRadius: '6px',\n          cursor: 'pointer'\n        },\n        children: \"Retry\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 484,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 477,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      maxWidth: '1400px',\n      margin: '0 auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            margin: '0 0 0.5rem',\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          },\n          children: \"Category Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          },\n          children: \"Manage categories and subcategories for announcements and events\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 519,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 510,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleAddCategory,\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          padding: '0.75rem 1.5rem',\n          background: '#3b82f6',\n          color: 'white',\n          border: 'none',\n          borderRadius: '8px',\n          fontSize: '0.875rem',\n          fontWeight: '600',\n          cursor: 'pointer',\n          transition: 'background-color 0.2s'\n        },\n        onMouseEnter: e => e.currentTarget.style.background = '#2563eb',\n        onMouseLeave: e => e.currentTarget.style.background = '#3b82f6',\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 547,\n          columnNumber: 11\n        }, this), \"Add Category\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 528,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 504,\n      columnNumber: 7\n    }, this), (error || success) && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '1.5rem'\n      },\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '1rem',\n          background: '#fef2f2',\n          border: '1px solid #fecaca',\n          borderRadius: '8px',\n          color: '#dc2626',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 17\n          }, this), error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 566,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: clearMessages,\n          style: {\n            background: 'transparent',\n            border: 'none',\n            color: '#dc2626',\n            cursor: 'pointer',\n            padding: '0.25rem'\n          },\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 570,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 556,\n        columnNumber: 13\n      }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '1rem',\n          background: '#f0fdf4',\n          border: '1px solid #bbf7d0',\n          borderRadius: '8px',\n          color: '#166534',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 597,\n            columnNumber: 17\n          }, this), success]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 596,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: clearMessages,\n          style: {\n            background: 'transparent',\n            border: 'none',\n            color: '#166534',\n            cursor: 'pointer',\n            padding: '0.25rem'\n          },\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 600,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 586,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 554,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(CategoryList, {\n      categories: categories,\n      loading: loading,\n      onEditCategory: handleEditCategory,\n      onDeleteCategory: handleDeleteCategory,\n      onToggleCategoryStatus: handleToggleCategoryStatus,\n      onAddSubcategory: handleAddSubcategory,\n      onEditSubcategory: handleEditSubcategory,\n      onDeleteSubcategory: handleDeleteSubcategory,\n      onToggleSubcategoryStatus: handleToggleSubcategoryStatus\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 618,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CategoryModal, {\n      isOpen: showModal,\n      onClose: () => setShowModal(false),\n      onSave: handleSave,\n      category: editingCategory,\n      subcategory: editingSubcategory,\n      parentCategory: parentCategory,\n      mode: modalMode,\n      loading: modalLoading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 631,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 502,\n    columnNumber: 5\n  }, this);\n};\n_s(CategoryManagement, \"Fi2eW2xY3Tqt2uCjsGG8TTkc8Lc=\", false, function () {\n  return [useAdminAuth, usePermissions];\n});\n_c = CategoryManagement;\nexport default CategoryManagement;\nvar _c;\n$RefreshReg$(_c, \"CategoryManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FolderTree", "Plus", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CheckCircle", "useAdminAuth", "usePermissions", "categoryService", "CategoryList", "CategoryModal", "jsxDEV", "_jsxDEV", "CategoryManagement", "_s", "user", "permissions", "categories", "setCategories", "loading", "setLoading", "error", "setError", "success", "setSuccess", "showModal", "setShowModal", "modalMode", "setModalMode", "editingCategory", "setEditingCategory", "editingSubcategory", "setEditingSubcategory", "parentCategory", "setParentCategory", "modalLoading", "setModalLoading", "canManageCategories", "loadCategories", "_response$data", "console", "log", "response", "getCategoriesWithSubcategories", "data", "length", "Error", "message", "err", "_err$response", "isApiUnavailable", "status", "code", "includes", "mockCategories", "category_id", "name", "description", "color_code", "is_active", "created_at", "updated_at", "subcategories", "subcategory_id", "display_order", "handleAddCategory", "handleEditCategory", "category", "handleDeleteCategory", "deleteCategory", "_err$response2", "handleToggleCategoryStatus", "toggleCategoryStatus", "action", "handleAddSubcategory", "handleEditSubcategory", "subcategory", "handleDeleteSubcategory", "deleteSubcategory", "handleToggleSubcategoryStatus", "toggleSubcategoryStatus", "handleSave", "parentCat", "_response", "createCategory", "updateCategory", "createSubcategory", "updateSubcategory", "_response2", "clearMessages", "style", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "textAlign", "color", "children", "size", "marginBottom", "opacity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "margin", "fontSize", "fontWeight", "marginTop", "padding", "background", "getPositionBadgeColor", "borderRadius", "getPositionDisplayName", "width", "height", "border", "borderTop", "animation", "onClick", "cursor", "max<PERSON><PERSON><PERSON>", "gap", "transition", "onMouseEnter", "e", "currentTarget", "onMouseLeave", "onEditCategory", "onDeleteCategory", "onToggleCategoryStatus", "onAddSubcategory", "onEditSubcategory", "onDeleteSubcategory", "onToggleSubcategoryStatus", "isOpen", "onClose", "onSave", "mode", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/admin/CategoryManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { FolderTree, Plus, AlertTriangle, CheckCircle } from 'lucide-react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\nimport { categoryService } from '../../services/categoryService';\nimport CategoryList from '../../components/admin/CategoryList';\nimport CategoryModal from '../../components/admin/CategoryModal';\n\ninterface Category {\n  category_id?: number;\n  name: string;\n  description?: string;\n  color_code: string;\n  is_active: boolean;\n  created_at?: string;\n  updated_at?: string;\n  subcategories?: Subcategory[];\n}\n\ninterface Subcategory {\n  subcategory_id?: number;\n  category_id?: number;\n  name: string;\n  description?: string;\n  color_code: string;\n  is_active: boolean;\n  display_order: number;\n  created_at?: string;\n  updated_at?: string;\n}\n\nconst CategoryManagement: React.FC = () => {\n  const { user } = useAdminAuth();\n  const permissions = usePermissions(user);\n\n  // State management\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  // Modal state\n  const [showModal, setShowModal] = useState(false);\n  const [modalMode, setModalMode] = useState<'add_category' | 'edit_category' | 'add_subcategory' | 'edit_subcategory'>('add_category');\n  const [editingCategory, setEditingCategory] = useState<Category | null>(null);\n  const [editingSubcategory, setEditingSubcategory] = useState<Subcategory | null>(null);\n  const [parentCategory, setParentCategory] = useState<Category | null>(null);\n  const [modalLoading, setModalLoading] = useState(false);\n\n  useEffect(() => {\n    // Check if user has permission to manage categories\n    if (!permissions.canManageCategories) {\n      setError('You do not have permission to manage categories');\n      setLoading(false);\n      return;\n    }\n\n    loadCategories();\n  }, [permissions.canManageCategories]);\n\n  const loadCategories = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log('🔍 CategoryManagement - Loading categories from API...');\n      const response = await categoryService.getCategoriesWithSubcategories();\n      console.log('🔍 CategoryManagement - API response:', response);\n\n      if (response.success && response.data?.categories) {\n        setCategories(response.data.categories);\n        console.log('✅ CategoryManagement - Categories loaded successfully:', response.data.categories.length);\n      } else {\n        throw new Error(response.message || 'Failed to load categories');\n      }\n\n    } catch (err: any) {\n      console.error('❌ CategoryManagement - Error loading categories:', err);\n\n      // Check if it's a 404 or network error (API not available)\n      const isApiUnavailable = err.response?.status === 404 ||\n                              err.code === 'ECONNREFUSED' ||\n                              err.message.includes('Network Error') ||\n                              err.message.includes('Failed to fetch');\n\n      if (isApiUnavailable) {\n        console.log('⚠️ CategoryManagement - API not available, using mock data');\n\n        // Use mock data as fallback\n        const mockCategories: Category[] = [\n          {\n            category_id: 1,\n            name: 'Academic',\n            description: 'Academic-related announcements and events',\n            color_code: '#3b82f6',\n            is_active: true,\n            created_at: '2025-01-01T00:00:00Z',\n            updated_at: '2025-01-01T00:00:00Z',\n            subcategories: [\n              {\n                subcategory_id: 1,\n                category_id: 1,\n                name: 'Exams',\n                description: 'Examination schedules and updates',\n                color_code: '#ef4444',\n                display_order: 1,\n                is_active: true,\n                created_at: '2025-01-01T00:00:00Z',\n                updated_at: '2025-01-01T00:00:00Z'\n              },\n              {\n                subcategory_id: 2,\n                category_id: 1,\n                name: 'Assignments',\n                description: 'Assignment deadlines and submissions',\n                color_code: '#f59e0b',\n                display_order: 2,\n                is_active: true,\n                created_at: '2025-01-01T00:00:00Z',\n                updated_at: '2025-01-01T00:00:00Z'\n              },\n              {\n                subcategory_id: 3,\n                category_id: 1,\n                name: 'Class Schedules',\n                description: 'Class timing and schedule changes',\n                color_code: '#06b6d4',\n                display_order: 3,\n                is_active: true,\n                created_at: '2025-01-01T00:00:00Z',\n                updated_at: '2025-01-01T00:00:00Z'\n              }\n            ]\n          },\n          {\n            category_id: 2,\n            name: 'Events',\n            description: 'School events and activities',\n            color_code: '#10b981',\n            is_active: true,\n            created_at: '2025-01-01T00:00:00Z',\n            updated_at: '2025-01-01T00:00:00Z',\n            subcategories: [\n              {\n                subcategory_id: 4,\n                category_id: 2,\n                name: 'Sports',\n                description: 'Sports events and competitions',\n                color_code: '#8b5cf6',\n                display_order: 1,\n                is_active: true,\n                created_at: '2025-01-01T00:00:00Z',\n                updated_at: '2025-01-01T00:00:00Z'\n              },\n              {\n                subcategory_id: 5,\n                category_id: 2,\n                name: 'Cultural',\n                description: 'Cultural events and celebrations',\n                color_code: '#ec4899',\n                display_order: 2,\n                is_active: true,\n                created_at: '2025-01-01T00:00:00Z',\n                updated_at: '2025-01-01T00:00:00Z'\n              }\n            ]\n          },\n          {\n            category_id: 3,\n            name: 'Administrative',\n            description: 'Administrative notices and updates',\n            color_code: '#f97316',\n            is_active: true,\n            created_at: '2025-01-01T00:00:00Z',\n            updated_at: '2025-01-01T00:00:00Z',\n            subcategories: [\n              {\n                subcategory_id: 6,\n                category_id: 3,\n                name: 'Policies',\n                description: 'School policies and regulations',\n                color_code: '#6366f1',\n                display_order: 1,\n                is_active: true,\n                created_at: '2025-01-01T00:00:00Z',\n                updated_at: '2025-01-01T00:00:00Z'\n              }\n            ]\n          },\n          {\n            category_id: 4,\n            name: 'Emergency',\n            description: 'Emergency announcements and alerts',\n            color_code: '#dc2626',\n            is_active: true,\n            created_at: '2025-01-01T00:00:00Z',\n            updated_at: '2025-01-01T00:00:00Z',\n            subcategories: []\n          }\n        ];\n\n        setCategories(mockCategories);\n        setError('⚠️ Using demo data - Backend API not connected. Categories will work in demo mode.');\n      } else {\n        setError(err.message || 'Failed to load categories');\n        setCategories([]);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handler functions\n  const handleAddCategory = () => {\n    setModalMode('add_category');\n    setEditingCategory(null);\n    setEditingSubcategory(null);\n    setParentCategory(null);\n    setShowModal(true);\n  };\n\n  const handleEditCategory = (category: Category) => {\n    setModalMode('edit_category');\n    setEditingCategory(category);\n    setEditingSubcategory(null);\n    setParentCategory(null);\n    setShowModal(true);\n  };\n\n  const handleDeleteCategory = async (category: Category) => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      if (!category.category_id) {\n        throw new Error('Category ID is required');\n      }\n\n      console.log('🔍 CategoryManagement - Deleting category:', category.category_id);\n      const response = await categoryService.deleteCategory(category.category_id);\n\n      if (response.success) {\n        setSuccess(`Category \"${category.name}\" has been deleted successfully`);\n        loadCategories();\n      } else {\n        throw new Error(response.message || 'Failed to delete category');\n      }\n\n    } catch (err: any) {\n      console.error('❌ CategoryManagement - Error deleting category:', err);\n\n      // Check if API is unavailable\n      const isApiUnavailable = err.response?.status === 404 ||\n                              err.code === 'ECONNREFUSED' ||\n                              err.message.includes('Network Error') ||\n                              err.message.includes('Failed to fetch');\n\n      if (isApiUnavailable) {\n        setError('⚠️ Demo mode: Cannot delete categories without backend API connection');\n      } else {\n        setError(err.message || 'Failed to delete category');\n      }\n    }\n  };\n\n  const handleToggleCategoryStatus = async (category: Category) => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      if (!category.category_id) {\n        throw new Error('Category ID is required');\n      }\n\n      console.log('🔍 CategoryManagement - Toggling category status:', category.category_id, !category.is_active);\n      const response = await categoryService.toggleCategoryStatus(category.category_id, !category.is_active);\n\n      if (response.success) {\n        const action = category.is_active ? 'deactivated' : 'activated';\n        setSuccess(`Category \"${category.name}\" has been ${action} successfully`);\n        loadCategories();\n      } else {\n        throw new Error(response.message || 'Failed to update category status');\n      }\n\n    } catch (err: any) {\n      console.error('❌ CategoryManagement - Error toggling category status:', err);\n      setError(err.message || 'Failed to update category status');\n    }\n  };\n\n  const handleAddSubcategory = (category: Category) => {\n    setModalMode('add_subcategory');\n    setEditingCategory(null);\n    setEditingSubcategory(null);\n    setParentCategory(category);\n    setShowModal(true);\n  };\n\n  const handleEditSubcategory = (category: Category, subcategory: Subcategory) => {\n    setModalMode('edit_subcategory');\n    setEditingCategory(null);\n    setEditingSubcategory(subcategory);\n    setParentCategory(category);\n    setShowModal(true);\n  };\n\n  const handleDeleteSubcategory = async (category: Category, subcategory: Subcategory) => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      if (!category.category_id || !subcategory.subcategory_id) {\n        throw new Error('Category ID and Subcategory ID are required');\n      }\n\n      console.log('🔍 CategoryManagement - Deleting subcategory:', category.category_id, subcategory.subcategory_id);\n      const response = await categoryService.deleteSubcategory(category.category_id, subcategory.subcategory_id);\n\n      if (response.success) {\n        setSuccess(`Subcategory \"${subcategory.name}\" has been deleted successfully`);\n        loadCategories();\n      } else {\n        throw new Error(response.message || 'Failed to delete subcategory');\n      }\n\n    } catch (err: any) {\n      console.error('❌ CategoryManagement - Error deleting subcategory:', err);\n      setError(err.message || 'Failed to delete subcategory');\n    }\n  };\n\n  const handleToggleSubcategoryStatus = async (category: Category, subcategory: Subcategory) => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      if (!category.category_id || !subcategory.subcategory_id) {\n        throw new Error('Category ID and Subcategory ID are required');\n      }\n\n      console.log('🔍 CategoryManagement - Toggling subcategory status:', category.category_id, subcategory.subcategory_id, !subcategory.is_active);\n      const response = await categoryService.toggleSubcategoryStatus(category.category_id, subcategory.subcategory_id, !subcategory.is_active);\n\n      if (response.success) {\n        const action = subcategory.is_active ? 'deactivated' : 'activated';\n        setSuccess(`Subcategory \"${subcategory.name}\" has been ${action} successfully`);\n        loadCategories();\n      } else {\n        throw new Error(response.message || 'Failed to update subcategory status');\n      }\n\n    } catch (err: any) {\n      console.error('❌ CategoryManagement - Error toggling subcategory status:', err);\n      setError(err.message || 'Failed to update subcategory status');\n    }\n  };\n\n  const handleSave = async (data: Category | Subcategory, parentCat?: Category) => {\n    try {\n      setModalLoading(true);\n      setError(null);\n      setSuccess(null);\n\n      let response;\n\n      if (modalMode === 'add_category') {\n        console.log('🔍 CategoryManagement - Creating category:', data);\n        response = await categoryService.createCategory(data as Category);\n        if (response.success) {\n          setSuccess(`Category \"${data.name}\" has been created successfully`);\n        }\n      } else if (modalMode === 'edit_category') {\n        if (!editingCategory?.category_id) {\n          throw new Error('Category ID is required for editing');\n        }\n        console.log('🔍 CategoryManagement - Updating category:', editingCategory.category_id, data);\n        response = await categoryService.updateCategory(editingCategory.category_id, data as Category);\n        if (response.success) {\n          setSuccess(`Category \"${data.name}\" has been updated successfully`);\n        }\n      } else if (modalMode === 'add_subcategory') {\n        if (!parentCat?.category_id) {\n          throw new Error('Parent category ID is required for creating subcategory');\n        }\n        console.log('🔍 CategoryManagement - Creating subcategory:', parentCat.category_id, data);\n        response = await categoryService.createSubcategory(parentCat.category_id, data as Subcategory);\n        if (response.success) {\n          setSuccess(`Subcategory \"${data.name}\" has been created successfully`);\n        }\n      } else if (modalMode === 'edit_subcategory') {\n        if (!parentCat?.category_id || !editingSubcategory?.subcategory_id) {\n          throw new Error('Category ID and Subcategory ID are required for editing');\n        }\n        console.log('🔍 CategoryManagement - Updating subcategory:', parentCat.category_id, editingSubcategory.subcategory_id, data);\n        response = await categoryService.updateSubcategory(parentCat.category_id, editingSubcategory.subcategory_id, data as Subcategory);\n        if (response.success) {\n          setSuccess(`Subcategory \"${data.name}\" has been updated successfully`);\n        }\n      }\n\n      if (!response?.success) {\n        throw new Error(response?.message || 'Failed to save changes');\n      }\n\n      loadCategories();\n\n    } catch (err: any) {\n      console.error('❌ CategoryManagement - Error saving:', err);\n      setError(err.message || 'Failed to save changes');\n      throw err; // Re-throw to prevent modal from closing\n    } finally {\n      setModalLoading(false);\n    }\n  };\n\n  const clearMessages = () => {\n    setError(null);\n    setSuccess(null);\n  };\n\n  if (!permissions.canManageCategories) {\n    return (\n      <div style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      }}>\n        <FolderTree size={64} style={{ marginBottom: '1rem', opacity: 0.5 }} />\n        <h2 style={{ margin: '0 0 0.5rem', fontSize: '1.5rem', fontWeight: '600' }}>\n          Access Denied\n        </h2>\n        <p style={{ margin: 0, fontSize: '1rem' }}>\n          You do not have permission to manage categories and subcategories.\n        </p>\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        }}>\n          Current Role: {permissions.getPositionDisplayName()}\n        </div>\n      </div>\n    );\n  }\n\n  if (loading) {\n    return (\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px'\n      }}>\n        <div style={{\n          width: '40px',\n          height: '40px',\n          border: '4px solid #f3f4f6',\n          borderTop: '4px solid #3b82f6',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }} />\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div style={{\n        padding: '2rem',\n        textAlign: 'center',\n        color: '#ef4444'\n      }}>\n        <h2>Error</h2>\n        <p>{error}</p>\n        <button\n          onClick={loadCategories}\n          style={{\n            padding: '0.5rem 1rem',\n            background: '#3b82f6',\n            color: 'white',\n            border: 'none',\n            borderRadius: '6px',\n            cursor: 'pointer'\n          }}\n        >\n          Retry\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ maxWidth: '1400px', margin: '0 auto' }}>\n      {/* Header */}\n      <div style={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      }}>\n        <div>\n          <h1 style={{\n            margin: '0 0 0.5rem',\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          }}>\n            Category Management\n          </h1>\n          <p style={{\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          }}>\n            Manage categories and subcategories for announcements and events\n          </p>\n        </div>\n\n        <button\n          onClick={handleAddCategory}\n          style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            padding: '0.75rem 1.5rem',\n            background: '#3b82f6',\n            color: 'white',\n            border: 'none',\n            borderRadius: '8px',\n            fontSize: '0.875rem',\n            fontWeight: '600',\n            cursor: 'pointer',\n            transition: 'background-color 0.2s'\n          }}\n          onMouseEnter={(e) => e.currentTarget.style.background = '#2563eb'}\n          onMouseLeave={(e) => e.currentTarget.style.background = '#3b82f6'}\n        >\n          <Plus size={16} />\n          Add Category\n        </button>\n      </div>\n\n      {/* Success/Error Messages */}\n      {(error || success) && (\n        <div style={{ marginBottom: '1.5rem' }}>\n          {error && (\n            <div style={{\n              padding: '1rem',\n              background: '#fef2f2',\n              border: '1px solid #fecaca',\n              borderRadius: '8px',\n              color: '#dc2626',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            }}>\n              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                <AlertTriangle size={16} />\n                {error}\n              </div>\n              <button\n                onClick={clearMessages}\n                style={{\n                  background: 'transparent',\n                  border: 'none',\n                  color: '#dc2626',\n                  cursor: 'pointer',\n                  padding: '0.25rem'\n                }}\n              >\n                ×\n              </button>\n            </div>\n          )}\n\n          {success && (\n            <div style={{\n              padding: '1rem',\n              background: '#f0fdf4',\n              border: '1px solid #bbf7d0',\n              borderRadius: '8px',\n              color: '#166534',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            }}>\n              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                <CheckCircle size={16} />\n                {success}\n              </div>\n              <button\n                onClick={clearMessages}\n                style={{\n                  background: 'transparent',\n                  border: 'none',\n                  color: '#166534',\n                  cursor: 'pointer',\n                  padding: '0.25rem'\n                }}\n              >\n                ×\n              </button>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Category List */}\n      <CategoryList\n        categories={categories}\n        loading={loading}\n        onEditCategory={handleEditCategory}\n        onDeleteCategory={handleDeleteCategory}\n        onToggleCategoryStatus={handleToggleCategoryStatus}\n        onAddSubcategory={handleAddSubcategory}\n        onEditSubcategory={handleEditSubcategory}\n        onDeleteSubcategory={handleDeleteSubcategory}\n        onToggleSubcategoryStatus={handleToggleSubcategoryStatus}\n      />\n\n      {/* Category Modal */}\n      <CategoryModal\n        isOpen={showModal}\n        onClose={() => setShowModal(false)}\n        onSave={handleSave}\n        category={editingCategory}\n        subcategory={editingSubcategory}\n        parentCategory={parentCategory}\n        mode={modalMode}\n        loading={modalLoading}\n      />\n    </div>\n  );\n};\n\nexport default CategoryManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,UAAU,EAAEC,IAAI,EAAEC,aAAa,EAAEC,WAAW,QAAQ,cAAc;AAC3E,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,gCAAgC;AAChE,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,OAAOC,aAAa,MAAM,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAyBjE,MAAMC,kBAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM;IAAEC;EAAK,CAAC,GAAGT,YAAY,CAAC,CAAC;EAC/B,MAAMU,WAAW,GAAGT,cAAc,CAACQ,IAAI,CAAC;;EAExC;EACA,MAAM,CAACE,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAa,EAAE,CAAC;EAC5D,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAgB,IAAI,CAAC;;EAE3D;EACA,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAA4E,cAAc,CAAC;EACrI,MAAM,CAAC6B,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAkB,IAAI,CAAC;EAC7E,MAAM,CAAC+B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhC,QAAQ,CAAqB,IAAI,CAAC;EACtF,MAAM,CAACiC,cAAc,EAAEC,iBAAiB,CAAC,GAAGlC,QAAQ,CAAkB,IAAI,CAAC;EAC3E,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACe,WAAW,CAACqB,mBAAmB,EAAE;MACpCf,QAAQ,CAAC,iDAAiD,CAAC;MAC3DF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEAkB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACtB,WAAW,CAACqB,mBAAmB,CAAC,CAAC;EAErC,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MAAA,IAAAC,cAAA;MACFnB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEdkB,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;MACrE,MAAMC,QAAQ,GAAG,MAAMlC,eAAe,CAACmC,8BAA8B,CAAC,CAAC;MACvEH,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEC,QAAQ,CAAC;MAE9D,IAAIA,QAAQ,CAACnB,OAAO,KAAAgB,cAAA,GAAIG,QAAQ,CAACE,IAAI,cAAAL,cAAA,eAAbA,cAAA,CAAetB,UAAU,EAAE;QACjDC,aAAa,CAACwB,QAAQ,CAACE,IAAI,CAAC3B,UAAU,CAAC;QACvCuB,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAEC,QAAQ,CAACE,IAAI,CAAC3B,UAAU,CAAC4B,MAAM,CAAC;MACxG,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAACJ,QAAQ,CAACK,OAAO,IAAI,2BAA2B,CAAC;MAClE;IAEF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MAAA,IAAAC,aAAA;MACjBT,OAAO,CAACnB,KAAK,CAAC,kDAAkD,EAAE2B,GAAG,CAAC;;MAEtE;MACA,MAAME,gBAAgB,GAAG,EAAAD,aAAA,GAAAD,GAAG,CAACN,QAAQ,cAAAO,aAAA,uBAAZA,aAAA,CAAcE,MAAM,MAAK,GAAG,IAC7BH,GAAG,CAACI,IAAI,KAAK,cAAc,IAC3BJ,GAAG,CAACD,OAAO,CAACM,QAAQ,CAAC,eAAe,CAAC,IACrCL,GAAG,CAACD,OAAO,CAACM,QAAQ,CAAC,iBAAiB,CAAC;MAE/D,IAAIH,gBAAgB,EAAE;QACpBV,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;;QAEzE;QACA,MAAMa,cAA0B,GAAG,CACjC;UACEC,WAAW,EAAE,CAAC;UACdC,IAAI,EAAE,UAAU;UAChBC,WAAW,EAAE,2CAA2C;UACxDC,UAAU,EAAE,SAAS;UACrBC,SAAS,EAAE,IAAI;UACfC,UAAU,EAAE,sBAAsB;UAClCC,UAAU,EAAE,sBAAsB;UAClCC,aAAa,EAAE,CACb;YACEC,cAAc,EAAE,CAAC;YACjBR,WAAW,EAAE,CAAC;YACdC,IAAI,EAAE,OAAO;YACbC,WAAW,EAAE,mCAAmC;YAChDC,UAAU,EAAE,SAAS;YACrBM,aAAa,EAAE,CAAC;YAChBL,SAAS,EAAE,IAAI;YACfC,UAAU,EAAE,sBAAsB;YAClCC,UAAU,EAAE;UACd,CAAC,EACD;YACEE,cAAc,EAAE,CAAC;YACjBR,WAAW,EAAE,CAAC;YACdC,IAAI,EAAE,aAAa;YACnBC,WAAW,EAAE,sCAAsC;YACnDC,UAAU,EAAE,SAAS;YACrBM,aAAa,EAAE,CAAC;YAChBL,SAAS,EAAE,IAAI;YACfC,UAAU,EAAE,sBAAsB;YAClCC,UAAU,EAAE;UACd,CAAC,EACD;YACEE,cAAc,EAAE,CAAC;YACjBR,WAAW,EAAE,CAAC;YACdC,IAAI,EAAE,iBAAiB;YACvBC,WAAW,EAAE,mCAAmC;YAChDC,UAAU,EAAE,SAAS;YACrBM,aAAa,EAAE,CAAC;YAChBL,SAAS,EAAE,IAAI;YACfC,UAAU,EAAE,sBAAsB;YAClCC,UAAU,EAAE;UACd,CAAC;QAEL,CAAC,EACD;UACEN,WAAW,EAAE,CAAC;UACdC,IAAI,EAAE,QAAQ;UACdC,WAAW,EAAE,8BAA8B;UAC3CC,UAAU,EAAE,SAAS;UACrBC,SAAS,EAAE,IAAI;UACfC,UAAU,EAAE,sBAAsB;UAClCC,UAAU,EAAE,sBAAsB;UAClCC,aAAa,EAAE,CACb;YACEC,cAAc,EAAE,CAAC;YACjBR,WAAW,EAAE,CAAC;YACdC,IAAI,EAAE,QAAQ;YACdC,WAAW,EAAE,gCAAgC;YAC7CC,UAAU,EAAE,SAAS;YACrBM,aAAa,EAAE,CAAC;YAChBL,SAAS,EAAE,IAAI;YACfC,UAAU,EAAE,sBAAsB;YAClCC,UAAU,EAAE;UACd,CAAC,EACD;YACEE,cAAc,EAAE,CAAC;YACjBR,WAAW,EAAE,CAAC;YACdC,IAAI,EAAE,UAAU;YAChBC,WAAW,EAAE,kCAAkC;YAC/CC,UAAU,EAAE,SAAS;YACrBM,aAAa,EAAE,CAAC;YAChBL,SAAS,EAAE,IAAI;YACfC,UAAU,EAAE,sBAAsB;YAClCC,UAAU,EAAE;UACd,CAAC;QAEL,CAAC,EACD;UACEN,WAAW,EAAE,CAAC;UACdC,IAAI,EAAE,gBAAgB;UACtBC,WAAW,EAAE,oCAAoC;UACjDC,UAAU,EAAE,SAAS;UACrBC,SAAS,EAAE,IAAI;UACfC,UAAU,EAAE,sBAAsB;UAClCC,UAAU,EAAE,sBAAsB;UAClCC,aAAa,EAAE,CACb;YACEC,cAAc,EAAE,CAAC;YACjBR,WAAW,EAAE,CAAC;YACdC,IAAI,EAAE,UAAU;YAChBC,WAAW,EAAE,iCAAiC;YAC9CC,UAAU,EAAE,SAAS;YACrBM,aAAa,EAAE,CAAC;YAChBL,SAAS,EAAE,IAAI;YACfC,UAAU,EAAE,sBAAsB;YAClCC,UAAU,EAAE;UACd,CAAC;QAEL,CAAC,EACD;UACEN,WAAW,EAAE,CAAC;UACdC,IAAI,EAAE,WAAW;UACjBC,WAAW,EAAE,oCAAoC;UACjDC,UAAU,EAAE,SAAS;UACrBC,SAAS,EAAE,IAAI;UACfC,UAAU,EAAE,sBAAsB;UAClCC,UAAU,EAAE,sBAAsB;UAClCC,aAAa,EAAE;QACjB,CAAC,CACF;QAED5C,aAAa,CAACoC,cAAc,CAAC;QAC7BhC,QAAQ,CAAC,oFAAoF,CAAC;MAChG,CAAC,MAAM;QACLA,QAAQ,CAAC0B,GAAG,CAACD,OAAO,IAAI,2BAA2B,CAAC;QACpD7B,aAAa,CAAC,EAAE,CAAC;MACnB;IACF,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM6C,iBAAiB,GAAGA,CAAA,KAAM;IAC9BrC,YAAY,CAAC,cAAc,CAAC;IAC5BE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,qBAAqB,CAAC,IAAI,CAAC;IAC3BE,iBAAiB,CAAC,IAAI,CAAC;IACvBR,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMwC,kBAAkB,GAAIC,QAAkB,IAAK;IACjDvC,YAAY,CAAC,eAAe,CAAC;IAC7BE,kBAAkB,CAACqC,QAAQ,CAAC;IAC5BnC,qBAAqB,CAAC,IAAI,CAAC;IAC3BE,iBAAiB,CAAC,IAAI,CAAC;IACvBR,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAM0C,oBAAoB,GAAG,MAAOD,QAAkB,IAAK;IACzD,IAAI;MACF7C,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAI,CAAC2C,QAAQ,CAACZ,WAAW,EAAE;QACzB,MAAM,IAAIT,KAAK,CAAC,yBAAyB,CAAC;MAC5C;MAEAN,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE0B,QAAQ,CAACZ,WAAW,CAAC;MAC/E,MAAMb,QAAQ,GAAG,MAAMlC,eAAe,CAAC6D,cAAc,CAACF,QAAQ,CAACZ,WAAW,CAAC;MAE3E,IAAIb,QAAQ,CAACnB,OAAO,EAAE;QACpBC,UAAU,CAAC,aAAa2C,QAAQ,CAACX,IAAI,iCAAiC,CAAC;QACvElB,cAAc,CAAC,CAAC;MAClB,CAAC,MAAM;QACL,MAAM,IAAIQ,KAAK,CAACJ,QAAQ,CAACK,OAAO,IAAI,2BAA2B,CAAC;MAClE;IAEF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MAAA,IAAAsB,cAAA;MACjB9B,OAAO,CAACnB,KAAK,CAAC,iDAAiD,EAAE2B,GAAG,CAAC;;MAErE;MACA,MAAME,gBAAgB,GAAG,EAAAoB,cAAA,GAAAtB,GAAG,CAACN,QAAQ,cAAA4B,cAAA,uBAAZA,cAAA,CAAcnB,MAAM,MAAK,GAAG,IAC7BH,GAAG,CAACI,IAAI,KAAK,cAAc,IAC3BJ,GAAG,CAACD,OAAO,CAACM,QAAQ,CAAC,eAAe,CAAC,IACrCL,GAAG,CAACD,OAAO,CAACM,QAAQ,CAAC,iBAAiB,CAAC;MAE/D,IAAIH,gBAAgB,EAAE;QACpB5B,QAAQ,CAAC,uEAAuE,CAAC;MACnF,CAAC,MAAM;QACLA,QAAQ,CAAC0B,GAAG,CAACD,OAAO,IAAI,2BAA2B,CAAC;MACtD;IACF;EACF,CAAC;EAED,MAAMwB,0BAA0B,GAAG,MAAOJ,QAAkB,IAAK;IAC/D,IAAI;MACF7C,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAI,CAAC2C,QAAQ,CAACZ,WAAW,EAAE;QACzB,MAAM,IAAIT,KAAK,CAAC,yBAAyB,CAAC;MAC5C;MAEAN,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE0B,QAAQ,CAACZ,WAAW,EAAE,CAACY,QAAQ,CAACR,SAAS,CAAC;MAC3G,MAAMjB,QAAQ,GAAG,MAAMlC,eAAe,CAACgE,oBAAoB,CAACL,QAAQ,CAACZ,WAAW,EAAE,CAACY,QAAQ,CAACR,SAAS,CAAC;MAEtG,IAAIjB,QAAQ,CAACnB,OAAO,EAAE;QACpB,MAAMkD,MAAM,GAAGN,QAAQ,CAACR,SAAS,GAAG,aAAa,GAAG,WAAW;QAC/DnC,UAAU,CAAC,aAAa2C,QAAQ,CAACX,IAAI,cAAciB,MAAM,eAAe,CAAC;QACzEnC,cAAc,CAAC,CAAC;MAClB,CAAC,MAAM;QACL,MAAM,IAAIQ,KAAK,CAACJ,QAAQ,CAACK,OAAO,IAAI,kCAAkC,CAAC;MACzE;IAEF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBR,OAAO,CAACnB,KAAK,CAAC,wDAAwD,EAAE2B,GAAG,CAAC;MAC5E1B,QAAQ,CAAC0B,GAAG,CAACD,OAAO,IAAI,kCAAkC,CAAC;IAC7D;EACF,CAAC;EAED,MAAM2B,oBAAoB,GAAIP,QAAkB,IAAK;IACnDvC,YAAY,CAAC,iBAAiB,CAAC;IAC/BE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,qBAAqB,CAAC,IAAI,CAAC;IAC3BE,iBAAiB,CAACiC,QAAQ,CAAC;IAC3BzC,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMiD,qBAAqB,GAAGA,CAACR,QAAkB,EAAES,WAAwB,KAAK;IAC9EhD,YAAY,CAAC,kBAAkB,CAAC;IAChCE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,qBAAqB,CAAC4C,WAAW,CAAC;IAClC1C,iBAAiB,CAACiC,QAAQ,CAAC;IAC3BzC,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMmD,uBAAuB,GAAG,MAAAA,CAAOV,QAAkB,EAAES,WAAwB,KAAK;IACtF,IAAI;MACFtD,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAI,CAAC2C,QAAQ,CAACZ,WAAW,IAAI,CAACqB,WAAW,CAACb,cAAc,EAAE;QACxD,MAAM,IAAIjB,KAAK,CAAC,6CAA6C,CAAC;MAChE;MAEAN,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAE0B,QAAQ,CAACZ,WAAW,EAAEqB,WAAW,CAACb,cAAc,CAAC;MAC9G,MAAMrB,QAAQ,GAAG,MAAMlC,eAAe,CAACsE,iBAAiB,CAACX,QAAQ,CAACZ,WAAW,EAAEqB,WAAW,CAACb,cAAc,CAAC;MAE1G,IAAIrB,QAAQ,CAACnB,OAAO,EAAE;QACpBC,UAAU,CAAC,gBAAgBoD,WAAW,CAACpB,IAAI,iCAAiC,CAAC;QAC7ElB,cAAc,CAAC,CAAC;MAClB,CAAC,MAAM;QACL,MAAM,IAAIQ,KAAK,CAACJ,QAAQ,CAACK,OAAO,IAAI,8BAA8B,CAAC;MACrE;IAEF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBR,OAAO,CAACnB,KAAK,CAAC,oDAAoD,EAAE2B,GAAG,CAAC;MACxE1B,QAAQ,CAAC0B,GAAG,CAACD,OAAO,IAAI,8BAA8B,CAAC;IACzD;EACF,CAAC;EAED,MAAMgC,6BAA6B,GAAG,MAAAA,CAAOZ,QAAkB,EAAES,WAAwB,KAAK;IAC5F,IAAI;MACFtD,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAI,CAAC2C,QAAQ,CAACZ,WAAW,IAAI,CAACqB,WAAW,CAACb,cAAc,EAAE;QACxD,MAAM,IAAIjB,KAAK,CAAC,6CAA6C,CAAC;MAChE;MAEAN,OAAO,CAACC,GAAG,CAAC,sDAAsD,EAAE0B,QAAQ,CAACZ,WAAW,EAAEqB,WAAW,CAACb,cAAc,EAAE,CAACa,WAAW,CAACjB,SAAS,CAAC;MAC7I,MAAMjB,QAAQ,GAAG,MAAMlC,eAAe,CAACwE,uBAAuB,CAACb,QAAQ,CAACZ,WAAW,EAAEqB,WAAW,CAACb,cAAc,EAAE,CAACa,WAAW,CAACjB,SAAS,CAAC;MAExI,IAAIjB,QAAQ,CAACnB,OAAO,EAAE;QACpB,MAAMkD,MAAM,GAAGG,WAAW,CAACjB,SAAS,GAAG,aAAa,GAAG,WAAW;QAClEnC,UAAU,CAAC,gBAAgBoD,WAAW,CAACpB,IAAI,cAAciB,MAAM,eAAe,CAAC;QAC/EnC,cAAc,CAAC,CAAC;MAClB,CAAC,MAAM;QACL,MAAM,IAAIQ,KAAK,CAACJ,QAAQ,CAACK,OAAO,IAAI,qCAAqC,CAAC;MAC5E;IAEF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBR,OAAO,CAACnB,KAAK,CAAC,2DAA2D,EAAE2B,GAAG,CAAC;MAC/E1B,QAAQ,CAAC0B,GAAG,CAACD,OAAO,IAAI,qCAAqC,CAAC;IAChE;EACF,CAAC;EAED,MAAMkC,UAAU,GAAG,MAAAA,CAAOrC,IAA4B,EAAEsC,SAAoB,KAAK;IAC/E,IAAI;MAAA,IAAAC,SAAA;MACF/C,eAAe,CAAC,IAAI,CAAC;MACrBd,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIkB,QAAQ;MAEZ,IAAIf,SAAS,KAAK,cAAc,EAAE;QAChCa,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEG,IAAI,CAAC;QAC/DF,QAAQ,GAAG,MAAMlC,eAAe,CAAC4E,cAAc,CAACxC,IAAgB,CAAC;QACjE,IAAIF,QAAQ,CAACnB,OAAO,EAAE;UACpBC,UAAU,CAAC,aAAaoB,IAAI,CAACY,IAAI,iCAAiC,CAAC;QACrE;MACF,CAAC,MAAM,IAAI7B,SAAS,KAAK,eAAe,EAAE;QACxC,IAAI,EAACE,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAE0B,WAAW,GAAE;UACjC,MAAM,IAAIT,KAAK,CAAC,qCAAqC,CAAC;QACxD;QACAN,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEZ,eAAe,CAAC0B,WAAW,EAAEX,IAAI,CAAC;QAC5FF,QAAQ,GAAG,MAAMlC,eAAe,CAAC6E,cAAc,CAACxD,eAAe,CAAC0B,WAAW,EAAEX,IAAgB,CAAC;QAC9F,IAAIF,QAAQ,CAACnB,OAAO,EAAE;UACpBC,UAAU,CAAC,aAAaoB,IAAI,CAACY,IAAI,iCAAiC,CAAC;QACrE;MACF,CAAC,MAAM,IAAI7B,SAAS,KAAK,iBAAiB,EAAE;QAC1C,IAAI,EAACuD,SAAS,aAATA,SAAS,eAATA,SAAS,CAAE3B,WAAW,GAAE;UAC3B,MAAM,IAAIT,KAAK,CAAC,yDAAyD,CAAC;QAC5E;QACAN,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAEyC,SAAS,CAAC3B,WAAW,EAAEX,IAAI,CAAC;QACzFF,QAAQ,GAAG,MAAMlC,eAAe,CAAC8E,iBAAiB,CAACJ,SAAS,CAAC3B,WAAW,EAAEX,IAAmB,CAAC;QAC9F,IAAIF,QAAQ,CAACnB,OAAO,EAAE;UACpBC,UAAU,CAAC,gBAAgBoB,IAAI,CAACY,IAAI,iCAAiC,CAAC;QACxE;MACF,CAAC,MAAM,IAAI7B,SAAS,KAAK,kBAAkB,EAAE;QAC3C,IAAI,EAACuD,SAAS,aAATA,SAAS,eAATA,SAAS,CAAE3B,WAAW,KAAI,EAACxB,kBAAkB,aAAlBA,kBAAkB,eAAlBA,kBAAkB,CAAEgC,cAAc,GAAE;UAClE,MAAM,IAAIjB,KAAK,CAAC,yDAAyD,CAAC;QAC5E;QACAN,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAEyC,SAAS,CAAC3B,WAAW,EAAExB,kBAAkB,CAACgC,cAAc,EAAEnB,IAAI,CAAC;QAC5HF,QAAQ,GAAG,MAAMlC,eAAe,CAAC+E,iBAAiB,CAACL,SAAS,CAAC3B,WAAW,EAAExB,kBAAkB,CAACgC,cAAc,EAAEnB,IAAmB,CAAC;QACjI,IAAIF,QAAQ,CAACnB,OAAO,EAAE;UACpBC,UAAU,CAAC,gBAAgBoB,IAAI,CAACY,IAAI,iCAAiC,CAAC;QACxE;MACF;MAEA,IAAI,GAAA2B,SAAA,GAACzC,QAAQ,cAAAyC,SAAA,eAARA,SAAA,CAAU5D,OAAO,GAAE;QAAA,IAAAiE,UAAA;QACtB,MAAM,IAAI1C,KAAK,CAAC,EAAA0C,UAAA,GAAA9C,QAAQ,cAAA8C,UAAA,uBAARA,UAAA,CAAUzC,OAAO,KAAI,wBAAwB,CAAC;MAChE;MAEAT,cAAc,CAAC,CAAC;IAElB,CAAC,CAAC,OAAOU,GAAQ,EAAE;MACjBR,OAAO,CAACnB,KAAK,CAAC,sCAAsC,EAAE2B,GAAG,CAAC;MAC1D1B,QAAQ,CAAC0B,GAAG,CAACD,OAAO,IAAI,wBAAwB,CAAC;MACjD,MAAMC,GAAG,CAAC,CAAC;IACb,CAAC,SAAS;MACRZ,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMqD,aAAa,GAAGA,CAAA,KAAM;IAC1BnE,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;EAClB,CAAC;EAED,IAAI,CAACR,WAAW,CAACqB,mBAAmB,EAAE;IACpC,oBACEzB,OAAA;MAAK8E,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE,OAAO;QAClBC,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACAtF,OAAA,CAACV,UAAU;QAACiG,IAAI,EAAE,EAAG;QAACT,KAAK,EAAE;UAAEU,YAAY,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAI;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvE7F,OAAA;QAAI8E,KAAK,EAAE;UAAEgB,MAAM,EAAE,YAAY;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAM,CAAE;QAAAV,QAAA,EAAC;MAE5E;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL7F,OAAA;QAAG8E,KAAK,EAAE;UAAEgB,MAAM,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAT,QAAA,EAAC;MAE3C;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJ7F,OAAA;QAAK8E,KAAK,EAAE;UACVmB,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAE/F,WAAW,CAACgG,qBAAqB,CAAC,CAAC;UAC/CC,YAAY,EAAE,KAAK;UACnBhB,KAAK,EAAE,OAAO;UACdU,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE;QACd,CAAE;QAAAV,QAAA,GAAC,gBACa,EAAClF,WAAW,CAACkG,sBAAsB,CAAC,CAAC;MAAA;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAItF,OAAO,EAAE;IACX,oBACEP,OAAA;MAAK8E,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE;MACb,CAAE;MAAAG,QAAA,eACAtF,OAAA;QAAK8E,KAAK,EAAE;UACVyB,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,mBAAmB;UAC3BC,SAAS,EAAE,mBAAmB;UAC9BL,YAAY,EAAE,KAAK;UACnBM,SAAS,EAAE;QACb;MAAE;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEV;EAEA,IAAIpF,KAAK,EAAE;IACT,oBACET,OAAA;MAAK8E,KAAK,EAAE;QACVoB,OAAO,EAAE,MAAM;QACfd,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACAtF,OAAA;QAAAsF,QAAA,EAAI;MAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACd7F,OAAA;QAAAsF,QAAA,EAAI7E;MAAK;QAAAiF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACd7F,OAAA;QACE4G,OAAO,EAAElF,cAAe;QACxBoD,KAAK,EAAE;UACLoB,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAE,SAAS;UACrBd,KAAK,EAAE,OAAO;UACdoB,MAAM,EAAE,MAAM;UACdJ,YAAY,EAAE,KAAK;UACnBQ,MAAM,EAAE;QACV,CAAE;QAAAvB,QAAA,EACH;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACE7F,OAAA;IAAK8E,KAAK,EAAE;MAAEgC,QAAQ,EAAE,QAAQ;MAAEhB,MAAM,EAAE;IAAS,CAAE;IAAAR,QAAA,gBAEnDtF,OAAA;MAAK8E,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfG,cAAc,EAAE,eAAe;QAC/BD,UAAU,EAAE,QAAQ;QACpBO,YAAY,EAAE;MAChB,CAAE;MAAAF,QAAA,gBACAtF,OAAA;QAAAsF,QAAA,gBACEtF,OAAA;UAAI8E,KAAK,EAAE;YACTgB,MAAM,EAAE,YAAY;YACpBC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjBX,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,EAAC;QAEH;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL7F,OAAA;UAAG8E,KAAK,EAAE;YACRgB,MAAM,EAAE,CAAC;YACTT,KAAK,EAAE,SAAS;YAChBU,QAAQ,EAAE;UACZ,CAAE;UAAAT,QAAA,EAAC;QAEH;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN7F,OAAA;QACE4G,OAAO,EAAEvD,iBAAkB;QAC3ByB,KAAK,EAAE;UACLC,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpB8B,GAAG,EAAE,QAAQ;UACbb,OAAO,EAAE,gBAAgB;UACzBC,UAAU,EAAE,SAAS;UACrBd,KAAK,EAAE,OAAO;UACdoB,MAAM,EAAE,MAAM;UACdJ,YAAY,EAAE,KAAK;UACnBN,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE,KAAK;UACjBa,MAAM,EAAE,SAAS;UACjBG,UAAU,EAAE;QACd,CAAE;QACFC,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACrC,KAAK,CAACqB,UAAU,GAAG,SAAU;QAClEiB,YAAY,EAAGF,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACrC,KAAK,CAACqB,UAAU,GAAG,SAAU;QAAAb,QAAA,gBAElEtF,OAAA,CAACT,IAAI;UAACgG,IAAI,EAAE;QAAG;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEpB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGL,CAACpF,KAAK,IAAIE,OAAO,kBAChBX,OAAA;MAAK8E,KAAK,EAAE;QAAEU,YAAY,EAAE;MAAS,CAAE;MAAAF,QAAA,GACpC7E,KAAK,iBACJT,OAAA;QAAK8E,KAAK,EAAE;UACVoB,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,SAAS;UACrBM,MAAM,EAAE,mBAAmB;UAC3BJ,YAAY,EAAE,KAAK;UACnBhB,KAAK,EAAE,SAAS;UAChBN,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE;QAClB,CAAE;QAAAI,QAAA,gBACAtF,OAAA;UAAK8E,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAE8B,GAAG,EAAE;UAAS,CAAE;UAAAzB,QAAA,gBACnEtF,OAAA,CAACR,aAAa;YAAC+F,IAAI,EAAE;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAC1BpF,KAAK;QAAA;UAAAiF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN7F,OAAA;UACE4G,OAAO,EAAE/B,aAAc;UACvBC,KAAK,EAAE;YACLqB,UAAU,EAAE,aAAa;YACzBM,MAAM,EAAE,MAAM;YACdpB,KAAK,EAAE,SAAS;YAChBwB,MAAM,EAAE,SAAS;YACjBX,OAAO,EAAE;UACX,CAAE;UAAAZ,QAAA,EACH;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAEAlF,OAAO,iBACNX,OAAA;QAAK8E,KAAK,EAAE;UACVoB,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,SAAS;UACrBM,MAAM,EAAE,mBAAmB;UAC3BJ,YAAY,EAAE,KAAK;UACnBhB,KAAK,EAAE,SAAS;UAChBN,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE;QAClB,CAAE;QAAAI,QAAA,gBACAtF,OAAA;UAAK8E,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAE8B,GAAG,EAAE;UAAS,CAAE;UAAAzB,QAAA,gBACnEtF,OAAA,CAACP,WAAW;YAAC8F,IAAI,EAAE;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACxBlF,OAAO;QAAA;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN7F,OAAA;UACE4G,OAAO,EAAE/B,aAAc;UACvBC,KAAK,EAAE;YACLqB,UAAU,EAAE,aAAa;YACzBM,MAAM,EAAE,MAAM;YACdpB,KAAK,EAAE,SAAS;YAChBwB,MAAM,EAAE,SAAS;YACjBX,OAAO,EAAE;UACX,CAAE;UAAAZ,QAAA,EACH;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAGD7F,OAAA,CAACH,YAAY;MACXQ,UAAU,EAAEA,UAAW;MACvBE,OAAO,EAAEA,OAAQ;MACjB8G,cAAc,EAAE/D,kBAAmB;MACnCgE,gBAAgB,EAAE9D,oBAAqB;MACvC+D,sBAAsB,EAAE5D,0BAA2B;MACnD6D,gBAAgB,EAAE1D,oBAAqB;MACvC2D,iBAAiB,EAAE1D,qBAAsB;MACzC2D,mBAAmB,EAAEzD,uBAAwB;MAC7C0D,yBAAyB,EAAExD;IAA8B;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC,eAGF7F,OAAA,CAACF,aAAa;MACZ8H,MAAM,EAAE/G,SAAU;MAClBgH,OAAO,EAAEA,CAAA,KAAM/G,YAAY,CAAC,KAAK,CAAE;MACnCgH,MAAM,EAAEzD,UAAW;MACnBd,QAAQ,EAAEtC,eAAgB;MAC1B+C,WAAW,EAAE7C,kBAAmB;MAChCE,cAAc,EAAEA,cAAe;MAC/B0G,IAAI,EAAEhH,SAAU;MAChBR,OAAO,EAAEgB;IAAa;MAAAmE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC3F,EAAA,CAnmBID,kBAA4B;EAAA,QACfP,YAAY,EACTC,cAAc;AAAA;AAAAqI,EAAA,GAF9B/H,kBAA4B;AAqmBlC,eAAeA,kBAAkB;AAAC,IAAA+H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}