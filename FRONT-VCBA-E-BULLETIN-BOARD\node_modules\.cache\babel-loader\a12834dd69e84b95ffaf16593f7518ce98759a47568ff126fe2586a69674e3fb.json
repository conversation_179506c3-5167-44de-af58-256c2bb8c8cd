{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\contexts\\\\AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react';\nimport { AuthService, setupResponseInterceptor } from '../services';\n\n// Auth state interface\n\n// Auth actions\n\n// Auth context interface\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Initial state\nconst initialState = {\n  user: null,\n  isAuthenticated: false,\n  isLoading: true,\n  error: null\n};\n\n// Auth reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case 'AUTH_START':\n      return {\n        ...state,\n        isLoading: true,\n        error: null\n      };\n    case 'AUTH_SUCCESS':\n      return {\n        ...state,\n        user: action.payload,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      };\n    case 'AUTH_ERROR':\n      return {\n        ...state,\n        user: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload\n      };\n    case 'AUTH_LOGOUT':\n      return {\n        ...state,\n        user: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null\n      };\n    case 'CLEAR_ERROR':\n      return {\n        ...state,\n        error: null\n      };\n    case 'SET_LOADING':\n      return {\n        ...state,\n        isLoading: action.payload\n      };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AuthContext = /*#__PURE__*/createContext(undefined);\n\n// Auth provider props\n\n// Auth provider component\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Check authentication status\n  const checkAuthStatus = useCallback(async () => {\n    try {\n      console.log('🔍 AuthContext - Starting auth check');\n      dispatch({\n        type: 'SET_LOADING',\n        payload: true\n      });\n\n      // Quick check for local authentication data first\n      const user = AuthService.getStoredUser();\n      const token = AuthService.isAuthenticated();\n      console.log('🔍 AuthContext - Local auth data:', {\n        hasUser: !!user,\n        hasToken: !!token,\n        userRole: user === null || user === void 0 ? void 0 : user.role,\n        userEmail: user === null || user === void 0 ? void 0 : user.email\n      });\n      if (!user || !token) {\n        // No local auth data, definitely not authenticated\n        console.log('❌ AuthContext - No local auth data, logging out');\n        dispatch({\n          type: 'AUTH_LOGOUT'\n        });\n        return;\n      }\n\n      // We have local data, set user immediately for better UX\n      console.log('✅ AuthContext - Setting user from local data');\n      dispatch({\n        type: 'AUTH_SUCCESS',\n        payload: user\n      });\n\n      // DISABLED SERVER VALIDATION - No more automatic logouts\n      console.log('🔍 AuthContext - Server validation DISABLED to prevent logouts');\n    } catch (error) {\n      console.error('❌ AuthContext - Auth check failed:', error);\n      dispatch({\n        type: 'AUTH_LOGOUT'\n      });\n    } finally {\n      dispatch({\n        type: 'SET_LOADING',\n        payload: false\n      });\n      console.log('🔍 AuthContext - Auth check completed');\n    }\n  }, []);\n\n  // Login function\n  const login = async credentials => {\n    try {\n      dispatch({\n        type: 'AUTH_START'\n      });\n      const response = await AuthService.login(credentials);\n      if (response.success && response.data) {\n        dispatch({\n          type: 'AUTH_SUCCESS',\n          payload: response.data.user\n        });\n      } else {\n        throw new Error('Login failed');\n      }\n    } catch (error) {\n      const errorMessage = error.message || 'Login failed. Please try again.';\n      dispatch({\n        type: 'AUTH_ERROR',\n        payload: errorMessage\n      });\n      throw error;\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      var _state$user;\n      console.log('🚪 AuthContext - Starting logout process');\n\n      // Determine redirect path based on current user role\n      const currentUserRole = (_state$user = state.user) === null || _state$user === void 0 ? void 0 : _state$user.role;\n      const redirectPath = currentUserRole === 'student' ? '/student/login' : '/admin/login';\n      await AuthService.logout();\n      console.log('✅ AuthContext - Server logout successful');\n    } catch (error) {\n      console.error('❌ AuthContext - Logout error:', error);\n    } finally {\n      var _state$user2;\n      console.log('🧹 AuthContext - Clearing local state and redirecting');\n\n      // Determine redirect path based on current user role before clearing state\n      const currentUserRole = (_state$user2 = state.user) === null || _state$user2 === void 0 ? void 0 : _state$user2.role;\n      const redirectPath = currentUserRole === 'student' ? '/student/login' : '/admin/login';\n      dispatch({\n        type: 'AUTH_LOGOUT'\n      });\n\n      // Force redirect to appropriate login page\n      console.log(`🔄 AuthContext - Redirecting to ${redirectPath} for ${currentUserRole || 'unknown'} user`);\n      window.location.href = redirectPath;\n    }\n  };\n\n  // Register admin function\n  const register = async data => {\n    try {\n      dispatch({\n        type: 'AUTH_START'\n      });\n      const result = await AuthService.registerAdmin(data);\n\n      // Clear loading state and error on successful registration\n      dispatch({\n        type: 'SET_LOADING',\n        payload: false\n      });\n      dispatch({\n        type: 'CLEAR_ERROR'\n      });\n      return result;\n    } catch (error) {\n      const errorMessage = error.message || 'Registration failed. Please try again.';\n      dispatch({\n        type: 'AUTH_ERROR',\n        payload: errorMessage\n      });\n      throw error;\n    }\n  };\n\n  // Verify OTP function\n  const verifyOtp = async data => {\n    try {\n      dispatch({\n        type: 'AUTH_START'\n      });\n      await AuthService.verifyOtp(data);\n      dispatch({\n        type: 'SET_LOADING',\n        payload: false\n      });\n    } catch (error) {\n      const errorMessage = error.message || 'OTP verification failed. Please try again.';\n      dispatch({\n        type: 'AUTH_ERROR',\n        payload: errorMessage\n      });\n      throw error;\n    }\n  };\n\n  // Resend OTP function\n  const resendOtp = async email => {\n    try {\n      dispatch({\n        type: 'SET_LOADING',\n        payload: true\n      });\n      await AuthService.resendOtp(email);\n      dispatch({\n        type: 'SET_LOADING',\n        payload: false\n      });\n    } catch (error) {\n      const errorMessage = error.message || 'Failed to resend OTP. Please try again.';\n      dispatch({\n        type: 'AUTH_ERROR',\n        payload: errorMessage\n      });\n      throw error;\n    }\n  };\n\n  // Clear error function\n  const clearError = () => {\n    dispatch({\n      type: 'CLEAR_ERROR'\n    });\n  };\n\n  // Setup response interceptor for handling unauthorized requests\n  useEffect(() => {\n    console.log('🔧 AuthContext - Setting up response interceptor');\n    setupResponseInterceptor(() => {\n      console.log('🚨 AuthContext - Unauthorized request detected, logging out');\n      dispatch({\n        type: 'AUTH_LOGOUT'\n      });\n    });\n  }, []);\n\n  // Check authentication status on mount\n  useEffect(() => {\n    console.log('🚀 AuthContext - Component mounted, checking auth status');\n    checkAuthStatus();\n  }, [checkAuthStatus]);\n\n  // Context value\n  const value = {\n    ...state,\n    login,\n    logout,\n    register,\n    verifyOtp,\n    resendOtp,\n    clearError,\n    checkAuthStatus,\n    userType: 'admin' // Default to admin for legacy compatibility\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 271,\n    columnNumber: 5\n  }, this);\n};\n\n// Custom hook to use auth context\n_s(AuthProvider, \"D2NzTFKVeaVx/hXrvvO1PpzOaUE=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useEffect", "useCallback", "AuthService", "setupResponseInterceptor", "jsxDEV", "_jsxDEV", "initialState", "user", "isAuthenticated", "isLoading", "error", "authReducer", "state", "action", "type", "payload", "AuthContext", "undefined", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "dispatch", "checkAuthStatus", "console", "log", "getStoredUser", "token", "<PERSON><PERSON>ser", "hasToken", "userRole", "role", "userEmail", "email", "login", "credentials", "response", "success", "data", "Error", "errorMessage", "message", "logout", "_state$user", "currentUserRole", "redirectPath", "_state$user2", "window", "location", "href", "register", "result", "registerAdmin", "verifyOtp", "resendOtp", "clearError", "value", "userType", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useReducer, useEffect, useCallback, ReactNode } from 'react';\nimport { AuthService, setupResponseInterceptor } from '../services';\nimport { User, LoginCredentials, AdminRegistrationData, OtpVerificationData, RegistrationResponse } from '../types';\n\n// Auth state interface\ninterface AuthState {\n  user: User | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  error: string | null;\n}\n\n// Auth actions\ntype AuthAction =\n  | { type: 'AUTH_START' }\n  | { type: 'AUTH_SUCCESS'; payload: User }\n  | { type: 'AUTH_ERROR'; payload: string }\n  | { type: 'AUTH_LOGOUT' }\n  | { type: 'CLEAR_ERROR' }\n  | { type: 'SET_LOADING'; payload: boolean };\n\n// Auth context interface\ninterface AuthContextType extends AuthState {\n  login: (credentials: LoginCredentials) => Promise<void>;\n  logout: () => Promise<void>;\n  register: (data: AdminRegistrationData) => Promise<RegistrationResponse>;\n  verifyOtp: (data: OtpVerificationData) => Promise<void>;\n  resendOtp: (email: string) => Promise<void>;\n  clearError: () => void;\n  checkAuthStatus: () => Promise<void>;\n  userType: 'admin' | 'student'; // Add user type to context\n}\n\n// Initial state\nconst initialState: AuthState = {\n  user: null,\n  isAuthenticated: false,\n  isLoading: true,\n  error: null,\n};\n\n// Auth reducer\nconst authReducer = (state: AuthState, action: AuthAction): AuthState => {\n  switch (action.type) {\n    case 'AUTH_START':\n      return {\n        ...state,\n        isLoading: true,\n        error: null,\n      };\n    case 'AUTH_SUCCESS':\n      return {\n        ...state,\n        user: action.payload,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null,\n      };\n    case 'AUTH_ERROR':\n      return {\n        ...state,\n        user: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload,\n      };\n    case 'AUTH_LOGOUT':\n      return {\n        ...state,\n        user: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null,\n      };\n    case 'CLEAR_ERROR':\n      return {\n        ...state,\n        error: null,\n      };\n    case 'SET_LOADING':\n      return {\n        ...state,\n        isLoading: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\n// Auth provider props\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\n// Auth provider component\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Check authentication status\n  const checkAuthStatus = useCallback(async (): Promise<void> => {\n    try {\n      console.log('🔍 AuthContext - Starting auth check');\n      dispatch({ type: 'SET_LOADING', payload: true });\n\n      // Quick check for local authentication data first\n      const user = AuthService.getStoredUser();\n      const token = AuthService.isAuthenticated();\n\n      console.log('🔍 AuthContext - Local auth data:', {\n        hasUser: !!user,\n        hasToken: !!token,\n        userRole: user?.role,\n        userEmail: user?.email\n      });\n\n      if (!user || !token) {\n        // No local auth data, definitely not authenticated\n        console.log('❌ AuthContext - No local auth data, logging out');\n        dispatch({ type: 'AUTH_LOGOUT' });\n        return;\n      }\n\n      // We have local data, set user immediately for better UX\n      console.log('✅ AuthContext - Setting user from local data');\n      dispatch({ type: 'AUTH_SUCCESS', payload: user });\n\n      // DISABLED SERVER VALIDATION - No more automatic logouts\n      console.log('🔍 AuthContext - Server validation DISABLED to prevent logouts');\n    } catch (error) {\n      console.error('❌ AuthContext - Auth check failed:', error);\n      dispatch({ type: 'AUTH_LOGOUT' });\n    } finally {\n      dispatch({ type: 'SET_LOADING', payload: false });\n      console.log('🔍 AuthContext - Auth check completed');\n    }\n  }, []);\n\n  // Login function\n  const login = async (credentials: LoginCredentials): Promise<void> => {\n    try {\n      dispatch({ type: 'AUTH_START' });\n      \n      const response = await AuthService.login(credentials);\n      \n      if (response.success && response.data) {\n        dispatch({ type: 'AUTH_SUCCESS', payload: response.data.user });\n      } else {\n        throw new Error('Login failed');\n      }\n    } catch (error: any) {\n      const errorMessage = error.message || 'Login failed. Please try again.';\n      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });\n      throw error;\n    }\n  };\n\n  // Logout function\n  const logout = async (): Promise<void> => {\n    try {\n      console.log('🚪 AuthContext - Starting logout process');\n\n      // Determine redirect path based on current user role\n      const currentUserRole = state.user?.role;\n      const redirectPath = currentUserRole === 'student' ? '/student/login' : '/admin/login';\n\n      await AuthService.logout();\n      console.log('✅ AuthContext - Server logout successful');\n    } catch (error) {\n      console.error('❌ AuthContext - Logout error:', error);\n    } finally {\n      console.log('🧹 AuthContext - Clearing local state and redirecting');\n\n      // Determine redirect path based on current user role before clearing state\n      const currentUserRole = state.user?.role;\n      const redirectPath = currentUserRole === 'student' ? '/student/login' : '/admin/login';\n\n      dispatch({ type: 'AUTH_LOGOUT' });\n\n      // Force redirect to appropriate login page\n      console.log(`🔄 AuthContext - Redirecting to ${redirectPath} for ${currentUserRole || 'unknown'} user`);\n      window.location.href = redirectPath;\n    }\n  };\n\n  // Register admin function\n  const register = async (data: AdminRegistrationData): Promise<RegistrationResponse> => {\n    try {\n      dispatch({ type: 'AUTH_START' });\n\n      const result = await AuthService.registerAdmin(data);\n\n      // Clear loading state and error on successful registration\n      dispatch({ type: 'SET_LOADING', payload: false });\n      dispatch({ type: 'CLEAR_ERROR' });\n\n      return result;\n    } catch (error: any) {\n      const errorMessage = error.message || 'Registration failed. Please try again.';\n      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });\n      throw error;\n    }\n  };\n\n  // Verify OTP function\n  const verifyOtp = async (data: OtpVerificationData): Promise<void> => {\n    try {\n      dispatch({ type: 'AUTH_START' });\n      \n      await AuthService.verifyOtp(data);\n      \n      dispatch({ type: 'SET_LOADING', payload: false });\n    } catch (error: any) {\n      const errorMessage = error.message || 'OTP verification failed. Please try again.';\n      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });\n      throw error;\n    }\n  };\n\n  // Resend OTP function\n  const resendOtp = async (email: string): Promise<void> => {\n    try {\n      dispatch({ type: 'SET_LOADING', payload: true });\n      \n      await AuthService.resendOtp(email);\n      \n      dispatch({ type: 'SET_LOADING', payload: false });\n    } catch (error: any) {\n      const errorMessage = error.message || 'Failed to resend OTP. Please try again.';\n      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });\n      throw error;\n    }\n  };\n\n  // Clear error function\n  const clearError = (): void => {\n    dispatch({ type: 'CLEAR_ERROR' });\n  };\n\n  // Setup response interceptor for handling unauthorized requests\n  useEffect(() => {\n    console.log('🔧 AuthContext - Setting up response interceptor');\n    setupResponseInterceptor(() => {\n      console.log('🚨 AuthContext - Unauthorized request detected, logging out');\n      dispatch({ type: 'AUTH_LOGOUT' });\n    });\n  }, []);\n\n  // Check authentication status on mount\n  useEffect(() => {\n    console.log('🚀 AuthContext - Component mounted, checking auth status');\n    checkAuthStatus();\n  }, [checkAuthStatus]);\n\n  // Context value\n  const value: AuthContextType = {\n    ...state,\n    login,\n    logout,\n    register,\n    verifyOtp,\n    resendOtp,\n    clearError,\n    checkAuthStatus,\n    userType: 'admin', // Default to admin for legacy compatibility\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n// Custom hook to use auth context\nexport const useAuth = (): AuthContextType => {\n  const context = useContext(AuthContext);\n  \n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  \n  return context;\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,EAAEC,WAAW,QAAmB,OAAO;AACvG,SAASC,WAAW,EAAEC,wBAAwB,QAAQ,aAAa;;AAGnE;;AAQA;;AASA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAYA;AACA,MAAMC,YAAuB,GAAG;EAC9BC,IAAI,EAAE,IAAI;EACVC,eAAe,EAAE,KAAK;EACtBC,SAAS,EAAE,IAAI;EACfC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,MAAMC,WAAW,GAAGA,CAACC,KAAgB,EAAEC,MAAkB,KAAgB;EACvE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK,YAAY;MACf,OAAO;QACL,GAAGF,KAAK;QACRH,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE;MACT,CAAC;IACH,KAAK,cAAc;MACjB,OAAO;QACL,GAAGE,KAAK;QACRL,IAAI,EAAEM,MAAM,CAACE,OAAO;QACpBP,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IACH,KAAK,YAAY;MACf,OAAO;QACL,GAAGE,KAAK;QACRL,IAAI,EAAE,IAAI;QACVC,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAEG,MAAM,CAACE;MAChB,CAAC;IACH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGH,KAAK;QACRL,IAAI,EAAE,IAAI;QACVC,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IACH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGE,KAAK;QACRF,KAAK,EAAE;MACT,CAAC;IACH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGE,KAAK;QACRH,SAAS,EAAEI,MAAM,CAACE;MACpB,CAAC;IACH;MACE,OAAOH,KAAK;EAChB;AACF,CAAC;;AAED;AACA,MAAMI,WAAW,gBAAGnB,aAAa,CAA8BoB,SAAS,CAAC;;AAEzE;;AAKA;AACA,OAAO,MAAMC,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzE,MAAM,CAACR,KAAK,EAAES,QAAQ,CAAC,GAAGtB,UAAU,CAACY,WAAW,EAAEL,YAAY,CAAC;;EAE/D;EACA,MAAMgB,eAAe,GAAGrB,WAAW,CAAC,YAA2B;IAC7D,IAAI;MACFsB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnDH,QAAQ,CAAC;QAAEP,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;;MAEhD;MACA,MAAMR,IAAI,GAAGL,WAAW,CAACuB,aAAa,CAAC,CAAC;MACxC,MAAMC,KAAK,GAAGxB,WAAW,CAACM,eAAe,CAAC,CAAC;MAE3Ce,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE;QAC/CG,OAAO,EAAE,CAAC,CAACpB,IAAI;QACfqB,QAAQ,EAAE,CAAC,CAACF,KAAK;QACjBG,QAAQ,EAAEtB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuB,IAAI;QACpBC,SAAS,EAAExB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyB;MACnB,CAAC,CAAC;MAEF,IAAI,CAACzB,IAAI,IAAI,CAACmB,KAAK,EAAE;QACnB;QACAH,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAC9DH,QAAQ,CAAC;UAAEP,IAAI,EAAE;QAAc,CAAC,CAAC;QACjC;MACF;;MAEA;MACAS,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;MAC3DH,QAAQ,CAAC;QAAEP,IAAI,EAAE,cAAc;QAAEC,OAAO,EAAER;MAAK,CAAC,CAAC;;MAEjD;MACAgB,OAAO,CAACC,GAAG,CAAC,gEAAgE,CAAC;IAC/E,CAAC,CAAC,OAAOd,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1DW,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAc,CAAC,CAAC;IACnC,CAAC,SAAS;MACRO,QAAQ,CAAC;QAAEP,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;MACjDQ,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;IACtD;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMS,KAAK,GAAG,MAAOC,WAA6B,IAAoB;IACpE,IAAI;MACFb,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAa,CAAC,CAAC;MAEhC,MAAMqB,QAAQ,GAAG,MAAMjC,WAAW,CAAC+B,KAAK,CAACC,WAAW,CAAC;MAErD,IAAIC,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,EAAE;QACrChB,QAAQ,CAAC;UAAEP,IAAI,EAAE,cAAc;UAAEC,OAAO,EAAEoB,QAAQ,CAACE,IAAI,CAAC9B;QAAK,CAAC,CAAC;MACjE,CAAC,MAAM;QACL,MAAM,IAAI+B,KAAK,CAAC,cAAc,CAAC;MACjC;IACF,CAAC,CAAC,OAAO5B,KAAU,EAAE;MACnB,MAAM6B,YAAY,GAAG7B,KAAK,CAAC8B,OAAO,IAAI,iCAAiC;MACvEnB,QAAQ,CAAC;QAAEP,IAAI,EAAE,YAAY;QAAEC,OAAO,EAAEwB;MAAa,CAAC,CAAC;MACvD,MAAM7B,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAM+B,MAAM,GAAG,MAAAA,CAAA,KAA2B;IACxC,IAAI;MAAA,IAAAC,WAAA;MACFnB,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;;MAEvD;MACA,MAAMmB,eAAe,IAAAD,WAAA,GAAG9B,KAAK,CAACL,IAAI,cAAAmC,WAAA,uBAAVA,WAAA,CAAYZ,IAAI;MACxC,MAAMc,YAAY,GAAGD,eAAe,KAAK,SAAS,GAAG,gBAAgB,GAAG,cAAc;MAEtF,MAAMzC,WAAW,CAACuC,MAAM,CAAC,CAAC;MAC1BlB,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACzD,CAAC,CAAC,OAAOd,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MAAA,IAAAmC,YAAA;MACRtB,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;;MAEpE;MACA,MAAMmB,eAAe,IAAAE,YAAA,GAAGjC,KAAK,CAACL,IAAI,cAAAsC,YAAA,uBAAVA,YAAA,CAAYf,IAAI;MACxC,MAAMc,YAAY,GAAGD,eAAe,KAAK,SAAS,GAAG,gBAAgB,GAAG,cAAc;MAEtFtB,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAc,CAAC,CAAC;;MAEjC;MACAS,OAAO,CAACC,GAAG,CAAC,mCAAmCoB,YAAY,QAAQD,eAAe,IAAI,SAAS,OAAO,CAAC;MACvGG,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGJ,YAAY;IACrC;EACF,CAAC;;EAED;EACA,MAAMK,QAAQ,GAAG,MAAOZ,IAA2B,IAAoC;IACrF,IAAI;MACFhB,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAa,CAAC,CAAC;MAEhC,MAAMoC,MAAM,GAAG,MAAMhD,WAAW,CAACiD,aAAa,CAACd,IAAI,CAAC;;MAEpD;MACAhB,QAAQ,CAAC;QAAEP,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;MACjDM,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAc,CAAC,CAAC;MAEjC,OAAOoC,MAAM;IACf,CAAC,CAAC,OAAOxC,KAAU,EAAE;MACnB,MAAM6B,YAAY,GAAG7B,KAAK,CAAC8B,OAAO,IAAI,wCAAwC;MAC9EnB,QAAQ,CAAC;QAAEP,IAAI,EAAE,YAAY;QAAEC,OAAO,EAAEwB;MAAa,CAAC,CAAC;MACvD,MAAM7B,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAM0C,SAAS,GAAG,MAAOf,IAAyB,IAAoB;IACpE,IAAI;MACFhB,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAa,CAAC,CAAC;MAEhC,MAAMZ,WAAW,CAACkD,SAAS,CAACf,IAAI,CAAC;MAEjChB,QAAQ,CAAC;QAAEP,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IACnD,CAAC,CAAC,OAAOL,KAAU,EAAE;MACnB,MAAM6B,YAAY,GAAG7B,KAAK,CAAC8B,OAAO,IAAI,4CAA4C;MAClFnB,QAAQ,CAAC;QAAEP,IAAI,EAAE,YAAY;QAAEC,OAAO,EAAEwB;MAAa,CAAC,CAAC;MACvD,MAAM7B,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAM2C,SAAS,GAAG,MAAOrB,KAAa,IAAoB;IACxD,IAAI;MACFX,QAAQ,CAAC;QAAEP,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;MAEhD,MAAMb,WAAW,CAACmD,SAAS,CAACrB,KAAK,CAAC;MAElCX,QAAQ,CAAC;QAAEP,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IACnD,CAAC,CAAC,OAAOL,KAAU,EAAE;MACnB,MAAM6B,YAAY,GAAG7B,KAAK,CAAC8B,OAAO,IAAI,yCAAyC;MAC/EnB,QAAQ,CAAC;QAAEP,IAAI,EAAE,YAAY;QAAEC,OAAO,EAAEwB;MAAa,CAAC,CAAC;MACvD,MAAM7B,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAM4C,UAAU,GAAGA,CAAA,KAAY;IAC7BjC,QAAQ,CAAC;MAAEP,IAAI,EAAE;IAAc,CAAC,CAAC;EACnC,CAAC;;EAED;EACAd,SAAS,CAAC,MAAM;IACduB,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;IAC/DrB,wBAAwB,CAAC,MAAM;MAC7BoB,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1EH,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAc,CAAC,CAAC;IACnC,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAd,SAAS,CAAC,MAAM;IACduB,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;IACvEF,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;;EAErB;EACA,MAAMiC,KAAsB,GAAG;IAC7B,GAAG3C,KAAK;IACRqB,KAAK;IACLQ,MAAM;IACNQ,QAAQ;IACRG,SAAS;IACTC,SAAS;IACTC,UAAU;IACVhC,eAAe;IACfkC,QAAQ,EAAE,OAAO,CAAE;EACrB,CAAC;EAED,oBACEnD,OAAA,CAACW,WAAW,CAACyC,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAApC,QAAA,EAChCA;EAAQ;IAAAuC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;;AAED;AAAAzC,EAAA,CAlLaF,YAAyC;AAAA4C,EAAA,GAAzC5C,YAAyC;AAmLtD,OAAO,MAAM6C,OAAO,GAAGA,CAAA,KAAuB;EAAAC,GAAA;EAC5C,MAAMC,OAAO,GAAGnE,UAAU,CAACkB,WAAW,CAAC;EAEvC,IAAIiD,OAAO,KAAKhD,SAAS,EAAE;IACzB,MAAM,IAAIqB,KAAK,CAAC,6CAA6C,CAAC;EAChE;EAEA,OAAO2B,OAAO;AAChB,CAAC;AAACD,GAAA,CARWD,OAAO;AAUpB,eAAe/C,WAAW;AAAC,IAAA8C,EAAA;AAAAI,YAAA,CAAAJ,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}