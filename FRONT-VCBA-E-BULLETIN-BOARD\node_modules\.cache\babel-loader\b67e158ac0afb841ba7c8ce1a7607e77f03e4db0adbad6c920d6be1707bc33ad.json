{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\admin\\\\layout\\\\AdminHeader.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport { useAdminAuth } from '../../../contexts/AdminAuthContext';\nimport NotificationBell from '../NotificationBell';\nimport { BarChart3, Calendar, Newspaper, Users, Settings, School, Menu, LogOut, Rss, Archive, FolderTree, UserCog, MessageSquare } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminHeader = ({\n  onToggleSidebar\n}) => {\n  _s();\n  var _user$firstName2, _user$lastName2, _user$firstName4, _user$lastName4;\n  const {\n    user,\n    logout\n  } = useAdminAuth();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const [showUserMenu, setShowUserMenu] = useState(false);\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Logout failed:', error);\n    }\n  };\n  const getCurrentTime = () => {\n    return new Date().toLocaleString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const getPageInfo = () => {\n    const path = location.pathname;\n    switch (path) {\n      case '/admin':\n      case '/admin/dashboard':\n        return {\n          title: 'Dashboard',\n          subtitle: 'Overview & Analytics',\n          icon: BarChart3,\n          description: 'Welcome to your admin dashboard'\n        };\n      case '/admin/newsfeed':\n        return {\n          title: 'Admin Newsfeed',\n          subtitle: 'Monitor & Engage',\n          icon: Rss,\n          description: 'Monitor announcements, events, and community engagement'\n        };\n      case '/admin/calendar':\n        return {\n          title: 'Calendar & Events',\n          subtitle: 'Schedule Management',\n          icon: Calendar,\n          description: 'Manage academic calendar, events, and announcements'\n        };\n      case '/admin/posts':\n        return {\n          title: 'Post Management',\n          subtitle: 'Content Publishing',\n          icon: Newspaper,\n          description: 'Create and manage announcements, news, and bulletin posts'\n        };\n      case '/admin/student-management':\n        return {\n          title: 'Student Management',\n          subtitle: 'User Administration',\n          icon: Users,\n          description: 'Manage student accounts, profiles, and academic information'\n        };\n      case '/admin/categories':\n        return {\n          title: 'Category Management',\n          subtitle: 'Content Organization',\n          icon: FolderTree,\n          description: 'Manage categories and subcategories for announcements and events'\n        };\n      case '/admin/admin-management':\n        return {\n          title: 'Admin Management',\n          subtitle: 'Administrator Accounts',\n          icon: UserCog,\n          description: 'Manage administrator accounts and permissions'\n        };\n      case '/admin/sms-settings':\n        return {\n          title: 'SMS Settings',\n          subtitle: 'Messaging Configuration',\n          icon: MessageSquare,\n          description: 'Configure SMS notifications and messaging settings'\n        };\n      case '/admin/archive':\n        return {\n          title: 'Archive',\n          subtitle: 'Archived Records',\n          icon: Archive,\n          description: 'View and manage archived announcements, events, and student accounts'\n        };\n      case '/admin/settings':\n        return {\n          title: 'Settings',\n          subtitle: 'Configuration',\n          icon: Settings,\n          description: 'Manage your profile, account settings, and preferences'\n        };\n      default:\n        return {\n          title: 'Admin Panel',\n          subtitle: 'VCBA E-Bulletin Board',\n          icon: School,\n          description: 'Villamor College of Business and Arts, Inc.'\n        };\n    }\n  };\n  const pageInfo = getPageInfo();\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    style: {\n      background: 'white',\n      borderBottom: '1px solid #e8f5e8',\n      padding: '1rem 2rem',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'space-between',\n      boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)',\n      position: 'sticky',\n      top: 0,\n      zIndex: 100\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '1rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onToggleSidebar,\n        style: {\n          background: 'none',\n          border: 'none',\n          padding: '0.5rem',\n          borderRadius: '8px',\n          cursor: 'pointer',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          transition: 'background-color 0.2s ease',\n          fontSize: '1.25rem'\n        },\n        onMouseEnter: e => {\n          e.currentTarget.style.background = '#f3f4f6';\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.background = 'none';\n        },\n        children: /*#__PURE__*/_jsxDEV(Menu, {\n          size: 20,\n          color: \"#2d5016\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.75rem',\n            marginBottom: '0.25rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(pageInfo.icon, {\n            size: 24,\n            color: \"#2d5016\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            style: {\n              margin: 0,\n              color: '#2d5016',\n              fontSize: '1.5rem',\n              fontWeight: '700'\n            },\n            children: pageInfo.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n              color: 'white',\n              padding: '0.25rem 0.75rem',\n              borderRadius: '12px',\n              fontSize: '0.75rem',\n              fontWeight: '600'\n            },\n            children: pageInfo.subtitle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '0.875rem',\n            marginBottom: '0.25rem'\n          },\n          children: pageInfo.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#9ca3af',\n            fontSize: '0.75rem'\n          },\n          children: getCurrentTime()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '1rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(NotificationBell, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowUserMenu(!showUserMenu),\n          style: {\n            background: 'none',\n            border: 'none',\n            padding: '0.5rem',\n            borderRadius: '12px',\n            cursor: 'pointer',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.75rem',\n            transition: 'background-color 0.2s ease'\n          },\n          onMouseEnter: e => {\n            e.currentTarget.style.background = '#f3f4f6';\n          },\n          onMouseLeave: e => {\n            if (!showUserMenu) {\n              e.currentTarget.style.background = 'none';\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '40px',\n              height: '40px',\n              borderRadius: '50%',\n              background: user !== null && user !== void 0 && user.profilePicture ? 'transparent' : 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: 'white',\n              fontWeight: '600',\n              fontSize: '1rem',\n              overflow: 'hidden',\n              border: '2px solid #e8f5e8'\n            },\n            children: user !== null && user !== void 0 && user.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n              src: `http://localhost:5000${user.profilePicture}`,\n              alt: `${user.firstName} ${user.lastName}`,\n              style: {\n                width: '100%',\n                height: '100%',\n                objectFit: 'cover',\n                borderRadius: '50%'\n              },\n              onError: e => {\n                // Fallback to initials if image fails to load\n                console.log('Profile picture failed to load, falling back to initials');\n                const target = e.target;\n                target.style.display = 'none';\n                const parent = target.parentElement;\n                if (parent) {\n                  var _user$firstName, _user$lastName;\n                  parent.style.background = 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)';\n                  parent.innerHTML = `${(user === null || user === void 0 ? void 0 : (_user$firstName = user.firstName) === null || _user$firstName === void 0 ? void 0 : _user$firstName.charAt(0)) || ''}${(user === null || user === void 0 ? void 0 : (_user$lastName = user.lastName) === null || _user$lastName === void 0 ? void 0 : _user$lastName.charAt(0)) || ''}`;\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 17\n            }, this) : `${(user === null || user === void 0 ? void 0 : (_user$firstName2 = user.firstName) === null || _user$firstName2 === void 0 ? void 0 : _user$firstName2.charAt(0)) || ''}${(user === null || user === void 0 ? void 0 : (_user$lastName2 = user.lastName) === null || _user$lastName2 === void 0 ? void 0 : _user$lastName2.charAt(0)) || ''}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'left'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#2d5016',\n                fontWeight: '600',\n                fontSize: '0.9rem',\n                lineHeight: '1.2'\n              },\n              children: [user === null || user === void 0 ? void 0 : user.firstName, \" \", user === null || user === void 0 ? void 0 : user.lastName]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#6b7280',\n                fontSize: '0.75rem',\n                lineHeight: '1.2'\n              },\n              children: (user === null || user === void 0 ? void 0 : user.position) || 'Administrator'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#6b7280',\n              fontSize: '0.75rem',\n              transform: showUserMenu ? 'rotate(180deg)' : 'rotate(0deg)',\n              transition: 'transform 0.2s ease'\n            },\n            children: \"\\u25BC\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), showUserMenu && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: '100%',\n            right: 0,\n            marginTop: '0.5rem',\n            background: 'white',\n            borderRadius: '12px',\n            boxShadow: '0 10px 40px rgba(0, 0, 0, 0.15)',\n            border: '1px solid #e8f5e8',\n            minWidth: '200px',\n            zIndex: 1000\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem',\n                marginBottom: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '50px',\n                  height: '50px',\n                  borderRadius: '50%',\n                  background: user !== null && user !== void 0 && user.profilePicture ? 'transparent' : 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  color: 'white',\n                  fontWeight: '600',\n                  fontSize: '1.1rem',\n                  overflow: 'hidden',\n                  border: '2px solid #e8f5e8',\n                  flexShrink: 0\n                },\n                children: user !== null && user !== void 0 && user.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: `http://localhost:5000${user.profilePicture}`,\n                  alt: `${user.firstName} ${user.lastName}`,\n                  style: {\n                    width: '100%',\n                    height: '100%',\n                    objectFit: 'cover',\n                    borderRadius: '50%'\n                  },\n                  onError: e => {\n                    // Fallback to initials if image fails to load\n                    console.log('Dropdown profile picture failed to load, falling back to initials');\n                    const target = e.target;\n                    target.style.display = 'none';\n                    const parent = target.parentElement;\n                    if (parent) {\n                      var _user$firstName3, _user$lastName3;\n                      parent.style.background = 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)';\n                      parent.innerHTML = `${(user === null || user === void 0 ? void 0 : (_user$firstName3 = user.firstName) === null || _user$firstName3 === void 0 ? void 0 : _user$firstName3.charAt(0)) || ''}${(user === null || user === void 0 ? void 0 : (_user$lastName3 = user.lastName) === null || _user$lastName3 === void 0 ? void 0 : _user$lastName3.charAt(0)) || ''}`;\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 23\n                }, this) : `${(user === null || user === void 0 ? void 0 : (_user$firstName4 = user.firstName) === null || _user$firstName4 === void 0 ? void 0 : _user$firstName4.charAt(0)) || ''}${(user === null || user === void 0 ? void 0 : (_user$lastName4 = user.lastName) === null || _user$lastName4 === void 0 ? void 0 : _user$lastName4.charAt(0)) || ''}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  flex: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#2d5016',\n                    fontWeight: '600',\n                    marginBottom: '0.25rem',\n                    fontSize: '0.95rem'\n                  },\n                  children: [user === null || user === void 0 ? void 0 : user.firstName, \" \", user === null || user === void 0 ? void 0 : user.lastName]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#6b7280',\n                    fontSize: '0.8rem'\n                  },\n                  children: user === null || user === void 0 ? void 0 : user.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n              style: {\n                border: 'none',\n                borderTop: '1px solid #e8f5e8',\n                margin: '1rem 0'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setShowUserMenu(false);\n                handleLogout();\n              },\n              style: {\n                width: '100%',\n                background: 'none',\n                border: 'none',\n                padding: '0.75rem',\n                borderRadius: '8px',\n                cursor: 'pointer',\n                textAlign: 'left',\n                color: '#dc2626',\n                fontSize: '0.875rem',\n                transition: 'background-color 0.2s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.background = '#fef2f2';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.background = 'none';\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(LogOut, {\n                  size: 16,\n                  color: \"#ef4444\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 21\n                }, this), \"Logout\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this), showUserMenu && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        zIndex: 999\n      },\n      onClick: () => setShowUserMenu(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 464,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 124,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminHeader, \"w6TRZ+ktZdrL5dIzb8rjMQoX1PI=\", false, function () {\n  return [useAdminAuth, useLocation, useNavigate];\n});\n_c = AdminHeader;\nexport default AdminHeader;\nvar _c;\n$RefreshReg$(_c, \"AdminHeader\");", "map": {"version": 3, "names": ["React", "useState", "useLocation", "useNavigate", "useAdminAuth", "NotificationBell", "BarChart3", "Calendar", "Newspaper", "Users", "Settings", "School", "<PERSON><PERSON>", "LogOut", "Rss", "Archive", "FolderTree", "UserCog", "MessageSquare", "jsxDEV", "_jsxDEV", "Ad<PERSON><PERSON><PERSON><PERSON>", "onToggleSidebar", "_s", "_user$firstName2", "_user$lastName2", "_user$firstName4", "_user$lastName4", "user", "logout", "location", "navigate", "showUserMenu", "setShowUserMenu", "handleLogout", "error", "console", "getCurrentTime", "Date", "toLocaleString", "weekday", "year", "month", "day", "hour", "minute", "getPageInfo", "path", "pathname", "title", "subtitle", "icon", "description", "pageInfo", "style", "background", "borderBottom", "padding", "display", "alignItems", "justifyContent", "boxShadow", "position", "top", "zIndex", "children", "gap", "onClick", "border", "borderRadius", "cursor", "transition", "fontSize", "onMouseEnter", "e", "currentTarget", "onMouseLeave", "size", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginBottom", "margin", "fontWeight", "width", "height", "profilePicture", "overflow", "src", "alt", "firstName", "lastName", "objectFit", "onError", "log", "target", "parent", "parentElement", "_user$firstName", "_user$lastName", "innerHTML", "char<PERSON>t", "textAlign", "lineHeight", "transform", "right", "marginTop", "min<PERSON><PERSON><PERSON>", "flexShrink", "_user$firstName3", "_user$lastName3", "flex", "email", "borderTop", "left", "bottom", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/admin/layout/AdminHeader.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport { useAdminAuth } from '../../../contexts/AdminAuthContext';\nimport NotificationBell from '../NotificationBell';\nimport { BarChart3, Calendar, Newspaper, Users, Settings, School, Menu, User, LogOut, Rss, Archive, FolderTree, UserCog, MessageSquare, Upload, Shield } from 'lucide-react';\n\ninterface AdminHeaderProps {\n  onToggleSidebar: () => void;\n}\n\nconst AdminHeader: React.FC<AdminHeaderProps> = ({ onToggleSidebar }) => {\n  const { user, logout } = useAdminAuth();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const [showUserMenu, setShowUserMenu] = useState(false);\n\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Logout failed:', error);\n    }\n  };\n\n  const getCurrentTime = () => {\n    return new Date().toLocaleString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  const getPageInfo = () => {\n    const path = location.pathname;\n\n    switch (path) {\n      case '/admin':\n      case '/admin/dashboard':\n        return {\n          title: 'Dashboard',\n          subtitle: 'Overview & Analytics',\n          icon: BarChart3,\n          description: 'Welcome to your admin dashboard'\n        };\n      case '/admin/newsfeed':\n        return {\n          title: 'Admin Newsfeed',\n          subtitle: 'Monitor & Engage',\n          icon: Rss,\n          description: 'Monitor announcements, events, and community engagement'\n        };\n      case '/admin/calendar':\n        return {\n          title: 'Calendar & Events',\n          subtitle: 'Schedule Management',\n          icon: Calendar,\n          description: 'Manage academic calendar, events, and announcements'\n        };\n      case '/admin/posts':\n        return {\n          title: 'Post Management',\n          subtitle: 'Content Publishing',\n          icon: Newspaper,\n          description: 'Create and manage announcements, news, and bulletin posts'\n        };\n      case '/admin/student-management':\n        return {\n          title: 'Student Management',\n          subtitle: 'User Administration',\n          icon: Users,\n          description: 'Manage student accounts, profiles, and academic information'\n        };\n      case '/admin/categories':\n        return {\n          title: 'Category Management',\n          subtitle: 'Content Organization',\n          icon: FolderTree,\n          description: 'Manage categories and subcategories for announcements and events'\n        };\n      case '/admin/admin-management':\n        return {\n          title: 'Admin Management',\n          subtitle: 'Administrator Accounts',\n          icon: UserCog,\n          description: 'Manage administrator accounts and permissions'\n        };\n      case '/admin/sms-settings':\n        return {\n          title: 'SMS Settings',\n          subtitle: 'Messaging Configuration',\n          icon: MessageSquare,\n          description: 'Configure SMS notifications and messaging settings'\n        };\n      case '/admin/archive':\n        return {\n          title: 'Archive',\n          subtitle: 'Archived Records',\n          icon: Archive,\n          description: 'View and manage archived announcements, events, and student accounts'\n        };\n      case '/admin/settings':\n        return {\n          title: 'Settings',\n          subtitle: 'Configuration',\n          icon: Settings,\n          description: 'Manage your profile, account settings, and preferences'\n        };\n      default:\n        return {\n          title: 'Admin Panel',\n          subtitle: 'VCBA E-Bulletin Board',\n          icon: School,\n          description: 'Villamor College of Business and Arts, Inc.'\n        };\n    }\n  };\n\n  const pageInfo = getPageInfo();\n\n  return (\n    <header style={{\n      background: 'white',\n      borderBottom: '1px solid #e8f5e8',\n      padding: '1rem 2rem',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'space-between',\n      boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)',\n      position: 'sticky',\n      top: 0,\n      zIndex: 100\n    }}>\n      {/* Left Section */}\n      <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>\n        {/* Sidebar Toggle */}\n        <button\n          onClick={onToggleSidebar}\n          style={{\n            background: 'none',\n            border: 'none',\n            padding: '0.5rem',\n            borderRadius: '8px',\n            cursor: 'pointer',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            transition: 'background-color 0.2s ease',\n            fontSize: '1.25rem'\n          }}\n          onMouseEnter={(e) => {\n            e.currentTarget.style.background = '#f3f4f6';\n          }}\n          onMouseLeave={(e) => {\n            e.currentTarget.style.background = 'none';\n          }}\n        >\n          <Menu size={20} color=\"#2d5016\" />\n        </button>\n\n        {/* Page Title */}\n        <div>\n          <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginBottom: '0.25rem' }}>\n            <pageInfo.icon size={24} color=\"#2d5016\" />\n            <h1 style={{\n              margin: 0,\n              color: '#2d5016',\n              fontSize: '1.5rem',\n              fontWeight: '700'\n            }}>\n              {pageInfo.title}\n            </h1>\n            <span style={{\n              background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n              color: 'white',\n              padding: '0.25rem 0.75rem',\n              borderRadius: '12px',\n              fontSize: '0.75rem',\n              fontWeight: '600'\n            }}>\n              {pageInfo.subtitle}\n            </span>\n          </div>\n          <p style={{\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '0.875rem',\n            marginBottom: '0.25rem'\n          }}>\n            {pageInfo.description}\n          </p>\n          <p style={{\n            margin: 0,\n            color: '#9ca3af',\n            fontSize: '0.75rem'\n          }}>\n            {getCurrentTime()}\n          </p>\n        </div>\n      </div>\n\n      {/* Right Section */}\n      <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>\n        {/* Notifications */}\n        <NotificationBell />\n\n        {/* User Profile */}\n        <div style={{ position: 'relative' }}>\n          <button\n            onClick={() => setShowUserMenu(!showUserMenu)}\n            style={{\n              background: 'none',\n              border: 'none',\n              padding: '0.5rem',\n              borderRadius: '12px',\n              cursor: 'pointer',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.75rem',\n              transition: 'background-color 0.2s ease'\n            }}\n            onMouseEnter={(e) => {\n              e.currentTarget.style.background = '#f3f4f6';\n            }}\n            onMouseLeave={(e) => {\n              if (!showUserMenu) {\n                e.currentTarget.style.background = 'none';\n              }\n            }}\n          >\n            {/* Avatar */}\n            <div style={{\n              width: '40px',\n              height: '40px',\n              borderRadius: '50%',\n              background: user?.profilePicture ? 'transparent' : 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: 'white',\n              fontWeight: '600',\n              fontSize: '1rem',\n              overflow: 'hidden',\n              border: '2px solid #e8f5e8'\n            }}>\n              {user?.profilePicture ? (\n                <img\n                  src={`http://localhost:5000${user.profilePicture}`}\n                  alt={`${user.firstName} ${user.lastName}`}\n                  style={{\n                    width: '100%',\n                    height: '100%',\n                    objectFit: 'cover',\n                    borderRadius: '50%'\n                  }}\n                  onError={(e) => {\n                    // Fallback to initials if image fails to load\n                    console.log('Profile picture failed to load, falling back to initials');\n                    const target = e.target as HTMLImageElement;\n                    target.style.display = 'none';\n                    const parent = target.parentElement;\n                    if (parent) {\n                      parent.style.background = 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)';\n                      parent.innerHTML = `${user?.firstName?.charAt(0) || ''}${user?.lastName?.charAt(0) || ''}`;\n                    }\n                  }}\n                />\n              ) : (\n                `${user?.firstName?.charAt(0) || ''}${user?.lastName?.charAt(0) || ''}`\n              )}\n            </div>\n\n            {/* User Info */}\n            <div style={{ textAlign: 'left' }}>\n              <div style={{\n                color: '#2d5016',\n                fontWeight: '600',\n                fontSize: '0.9rem',\n                lineHeight: '1.2'\n              }}>\n                {user?.firstName} {user?.lastName}\n              </div>\n              <div style={{\n                color: '#6b7280',\n                fontSize: '0.75rem',\n                lineHeight: '1.2'\n              }}>\n                {user?.position || 'Administrator'}\n              </div>\n            </div>\n\n            {/* Dropdown Arrow */}\n            <span style={{\n              color: '#6b7280',\n              fontSize: '0.75rem',\n              transform: showUserMenu ? 'rotate(180deg)' : 'rotate(0deg)',\n              transition: 'transform 0.2s ease'\n            }}>\n              ▼\n            </span>\n          </button>\n\n          {/* User Dropdown Menu */}\n          {showUserMenu && (\n            <div style={{\n              position: 'absolute',\n              top: '100%',\n              right: 0,\n              marginTop: '0.5rem',\n              background: 'white',\n              borderRadius: '12px',\n              boxShadow: '0 10px 40px rgba(0, 0, 0, 0.15)',\n              border: '1px solid #e8f5e8',\n              minWidth: '200px',\n              zIndex: 1000\n            }}>\n              <div style={{ padding: '1rem' }}>\n                {/* Profile Picture and Info */}\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.75rem',\n                  marginBottom: '1rem'\n                }}>\n                  {/* Profile Picture */}\n                  <div style={{\n                    width: '50px',\n                    height: '50px',\n                    borderRadius: '50%',\n                    background: user?.profilePicture ? 'transparent' : 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    color: 'white',\n                    fontWeight: '600',\n                    fontSize: '1.1rem',\n                    overflow: 'hidden',\n                    border: '2px solid #e8f5e8',\n                    flexShrink: 0\n                  }}>\n                    {user?.profilePicture ? (\n                      <img\n                        src={`http://localhost:5000${user.profilePicture}`}\n                        alt={`${user.firstName} ${user.lastName}`}\n                        style={{\n                          width: '100%',\n                          height: '100%',\n                          objectFit: 'cover',\n                          borderRadius: '50%'\n                        }}\n                        onError={(e) => {\n                          // Fallback to initials if image fails to load\n                          console.log('Dropdown profile picture failed to load, falling back to initials');\n                          const target = e.target as HTMLImageElement;\n                          target.style.display = 'none';\n                          const parent = target.parentElement;\n                          if (parent) {\n                            parent.style.background = 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)';\n                            parent.innerHTML = `${user?.firstName?.charAt(0) || ''}${user?.lastName?.charAt(0) || ''}`;\n                          }\n                        }}\n                      />\n                    ) : (\n                      `${user?.firstName?.charAt(0) || ''}${user?.lastName?.charAt(0) || ''}`\n                    )}\n                  </div>\n\n                  {/* User Info */}\n                  <div style={{ flex: 1 }}>\n                    <div style={{\n                      color: '#2d5016',\n                      fontWeight: '600',\n                      marginBottom: '0.25rem',\n                      fontSize: '0.95rem'\n                    }}>\n                      {user?.firstName} {user?.lastName}\n                    </div>\n                    <div style={{\n                      color: '#6b7280',\n                      fontSize: '0.8rem'\n                    }}>\n                      {user?.email}\n                    </div>\n                  </div>\n                </div>\n                \n                <hr style={{\n                  border: 'none',\n                  borderTop: '1px solid #e8f5e8',\n                  margin: '1rem 0'\n                }} />\n\n                {/* ill uncomment this soon */}\n                {/* <button\n                  onClick={() => {\n                    setShowUserMenu(false);\n                    navigate('/admin/settings');\n                  }}\n                  style={{\n                    width: '100%',\n                    background: 'none',\n                    border: 'none',\n                    padding: '0.75rem',\n                    borderRadius: '8px',\n                    cursor: 'pointer',\n                    textAlign: 'left',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    marginBottom: '0.5rem',\n                    transition: 'background-color 0.2s ease'\n                  }}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.background = '#f3f4f6';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.background = 'none';\n                  }}\n                >\n                  <span style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                    <User size={16} color=\"#6b7280\" />\n                    Profile Settings\n                  </span>\n                </button> */}\n\n                <button\n                  onClick={() => {\n                    setShowUserMenu(false);\n                    handleLogout();\n                  }}\n                  style={{\n                    width: '100%',\n                    background: 'none',\n                    border: 'none',\n                    padding: '0.75rem',\n                    borderRadius: '8px',\n                    cursor: 'pointer',\n                    textAlign: 'left',\n                    color: '#dc2626',\n                    fontSize: '0.875rem',\n                    transition: 'background-color 0.2s ease'\n                  }}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.background = '#fef2f2';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.background = 'none';\n                  }}\n                >\n                  <span style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                    <LogOut size={16} color=\"#ef4444\" />\n                    Logout\n                  </span>\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Click outside to close menu */}\n      {showUserMenu && (\n        <div\n          style={{\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            zIndex: 999\n          }}\n          onClick={() => setShowUserMenu(false)}\n        />\n      )}\n    </header>\n  );\n};\n\nexport default AdminHeader;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,YAAY,QAAQ,oCAAoC;AACjE,OAAOC,gBAAgB,MAAM,qBAAqB;AAClD,SAASC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,IAAI,EAAQC,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAEC,UAAU,EAAEC,OAAO,EAAEC,aAAa,QAAwB,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM7K,MAAMC,WAAuC,GAAGA,CAAC;EAAEC;AAAgB,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,gBAAA,EAAAC,eAAA,EAAAC,gBAAA,EAAAC,eAAA;EACvE,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGzB,YAAY,CAAC,CAAC;EACvC,MAAM0B,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM6B,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMiC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAML,MAAM,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;IACxC;EACF,CAAC;EAED,MAAME,cAAc,GAAGA,CAAA,KAAM;IAC3B,OAAO,IAAIC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,OAAO,EAAE;MACxCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,IAAI,GAAGjB,QAAQ,CAACkB,QAAQ;IAE9B,QAAQD,IAAI;MACV,KAAK,QAAQ;MACb,KAAK,kBAAkB;QACrB,OAAO;UACLE,KAAK,EAAE,WAAW;UAClBC,QAAQ,EAAE,sBAAsB;UAChCC,IAAI,EAAE7C,SAAS;UACf8C,WAAW,EAAE;QACf,CAAC;MACH,KAAK,iBAAiB;QACpB,OAAO;UACLH,KAAK,EAAE,gBAAgB;UACvBC,QAAQ,EAAE,kBAAkB;UAC5BC,IAAI,EAAErC,GAAG;UACTsC,WAAW,EAAE;QACf,CAAC;MACH,KAAK,iBAAiB;QACpB,OAAO;UACLH,KAAK,EAAE,mBAAmB;UAC1BC,QAAQ,EAAE,qBAAqB;UAC/BC,IAAI,EAAE5C,QAAQ;UACd6C,WAAW,EAAE;QACf,CAAC;MACH,KAAK,cAAc;QACjB,OAAO;UACLH,KAAK,EAAE,iBAAiB;UACxBC,QAAQ,EAAE,oBAAoB;UAC9BC,IAAI,EAAE3C,SAAS;UACf4C,WAAW,EAAE;QACf,CAAC;MACH,KAAK,2BAA2B;QAC9B,OAAO;UACLH,KAAK,EAAE,oBAAoB;UAC3BC,QAAQ,EAAE,qBAAqB;UAC/BC,IAAI,EAAE1C,KAAK;UACX2C,WAAW,EAAE;QACf,CAAC;MACH,KAAK,mBAAmB;QACtB,OAAO;UACLH,KAAK,EAAE,qBAAqB;UAC5BC,QAAQ,EAAE,sBAAsB;UAChCC,IAAI,EAAEnC,UAAU;UAChBoC,WAAW,EAAE;QACf,CAAC;MACH,KAAK,yBAAyB;QAC5B,OAAO;UACLH,KAAK,EAAE,kBAAkB;UACzBC,QAAQ,EAAE,wBAAwB;UAClCC,IAAI,EAAElC,OAAO;UACbmC,WAAW,EAAE;QACf,CAAC;MACH,KAAK,qBAAqB;QACxB,OAAO;UACLH,KAAK,EAAE,cAAc;UACrBC,QAAQ,EAAE,yBAAyB;UACnCC,IAAI,EAAEjC,aAAa;UACnBkC,WAAW,EAAE;QACf,CAAC;MACH,KAAK,gBAAgB;QACnB,OAAO;UACLH,KAAK,EAAE,SAAS;UAChBC,QAAQ,EAAE,kBAAkB;UAC5BC,IAAI,EAAEpC,OAAO;UACbqC,WAAW,EAAE;QACf,CAAC;MACH,KAAK,iBAAiB;QACpB,OAAO;UACLH,KAAK,EAAE,UAAU;UACjBC,QAAQ,EAAE,eAAe;UACzBC,IAAI,EAAEzC,QAAQ;UACd0C,WAAW,EAAE;QACf,CAAC;MACH;QACE,OAAO;UACLH,KAAK,EAAE,aAAa;UACpBC,QAAQ,EAAE,uBAAuB;UACjCC,IAAI,EAAExC,MAAM;UACZyC,WAAW,EAAE;QACf,CAAC;IACL;EACF,CAAC;EAED,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAE9B,oBACE1B,OAAA;IAAQkC,KAAK,EAAE;MACbC,UAAU,EAAE,OAAO;MACnBC,YAAY,EAAE,mBAAmB;MACjCC,OAAO,EAAE,WAAW;MACpBC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,eAAe;MAC/BC,SAAS,EAAE,gCAAgC;MAC3CC,QAAQ,EAAE,QAAQ;MAClBC,GAAG,EAAE,CAAC;MACNC,MAAM,EAAE;IACV,CAAE;IAAAC,QAAA,gBAEA7C,OAAA;MAAKkC,KAAK,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEO,GAAG,EAAE;MAAO,CAAE;MAAAD,QAAA,gBAEjE7C,OAAA;QACE+C,OAAO,EAAE7C,eAAgB;QACzBgC,KAAK,EAAE;UACLC,UAAU,EAAE,MAAM;UAClBa,MAAM,EAAE,MAAM;UACdX,OAAO,EAAE,QAAQ;UACjBY,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE,SAAS;UACjBZ,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBW,UAAU,EAAE,4BAA4B;UACxCC,QAAQ,EAAE;QACZ,CAAE;QACFC,YAAY,EAAGC,CAAC,IAAK;UACnBA,CAAC,CAACC,aAAa,CAACrB,KAAK,CAACC,UAAU,GAAG,SAAS;QAC9C,CAAE;QACFqB,YAAY,EAAGF,CAAC,IAAK;UACnBA,CAAC,CAACC,aAAa,CAACrB,KAAK,CAACC,UAAU,GAAG,MAAM;QAC3C,CAAE;QAAAU,QAAA,eAEF7C,OAAA,CAACR,IAAI;UAACiE,IAAI,EAAE,EAAG;UAACC,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eAGT9D,OAAA;QAAA6C,QAAA,gBACE7C,OAAA;UAAKkC,KAAK,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEO,GAAG,EAAE,SAAS;YAAEiB,YAAY,EAAE;UAAU,CAAE;UAAAlB,QAAA,gBAC7F7C,OAAA,CAACiC,QAAQ,CAACF,IAAI;YAAC0B,IAAI,EAAE,EAAG;YAACC,KAAK,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3C9D,OAAA;YAAIkC,KAAK,EAAE;cACT8B,MAAM,EAAE,CAAC;cACTN,KAAK,EAAE,SAAS;cAChBN,QAAQ,EAAE,QAAQ;cAClBa,UAAU,EAAE;YACd,CAAE;YAAApB,QAAA,EACCZ,QAAQ,CAACJ;UAAK;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eACL9D,OAAA;YAAMkC,KAAK,EAAE;cACXC,UAAU,EAAE,mDAAmD;cAC/DuB,KAAK,EAAE,OAAO;cACdrB,OAAO,EAAE,iBAAiB;cAC1BY,YAAY,EAAE,MAAM;cACpBG,QAAQ,EAAE,SAAS;cACnBa,UAAU,EAAE;YACd,CAAE;YAAApB,QAAA,EACCZ,QAAQ,CAACH;UAAQ;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN9D,OAAA;UAAGkC,KAAK,EAAE;YACR8B,MAAM,EAAE,CAAC;YACTN,KAAK,EAAE,SAAS;YAChBN,QAAQ,EAAE,UAAU;YACpBW,YAAY,EAAE;UAChB,CAAE;UAAAlB,QAAA,EACCZ,QAAQ,CAACD;QAAW;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACJ9D,OAAA;UAAGkC,KAAK,EAAE;YACR8B,MAAM,EAAE,CAAC;YACTN,KAAK,EAAE,SAAS;YAChBN,QAAQ,EAAE;UACZ,CAAE;UAAAP,QAAA,EACC5B,cAAc,CAAC;QAAC;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9D,OAAA;MAAKkC,KAAK,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEO,GAAG,EAAE;MAAO,CAAE;MAAAD,QAAA,gBAEjE7C,OAAA,CAACf,gBAAgB;QAAA0E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGpB9D,OAAA;QAAKkC,KAAK,EAAE;UAAEQ,QAAQ,EAAE;QAAW,CAAE;QAAAG,QAAA,gBACnC7C,OAAA;UACE+C,OAAO,EAAEA,CAAA,KAAMlC,eAAe,CAAC,CAACD,YAAY,CAAE;UAC9CsB,KAAK,EAAE;YACLC,UAAU,EAAE,MAAM;YAClBa,MAAM,EAAE,MAAM;YACdX,OAAO,EAAE,QAAQ;YACjBY,YAAY,EAAE,MAAM;YACpBC,MAAM,EAAE,SAAS;YACjBZ,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBO,GAAG,EAAE,SAAS;YACdK,UAAU,EAAE;UACd,CAAE;UACFE,YAAY,EAAGC,CAAC,IAAK;YACnBA,CAAC,CAACC,aAAa,CAACrB,KAAK,CAACC,UAAU,GAAG,SAAS;UAC9C,CAAE;UACFqB,YAAY,EAAGF,CAAC,IAAK;YACnB,IAAI,CAAC1C,YAAY,EAAE;cACjB0C,CAAC,CAACC,aAAa,CAACrB,KAAK,CAACC,UAAU,GAAG,MAAM;YAC3C;UACF,CAAE;UAAAU,QAAA,gBAGF7C,OAAA;YAAKkC,KAAK,EAAE;cACVgC,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdlB,YAAY,EAAE,KAAK;cACnBd,UAAU,EAAE3B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE4D,cAAc,GAAG,aAAa,GAAG,mDAAmD;cACtG9B,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBkB,KAAK,EAAE,OAAO;cACdO,UAAU,EAAE,KAAK;cACjBb,QAAQ,EAAE,MAAM;cAChBiB,QAAQ,EAAE,QAAQ;cAClBrB,MAAM,EAAE;YACV,CAAE;YAAAH,QAAA,EACCrC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE4D,cAAc,gBACnBpE,OAAA;cACEsE,GAAG,EAAE,wBAAwB9D,IAAI,CAAC4D,cAAc,EAAG;cACnDG,GAAG,EAAE,GAAG/D,IAAI,CAACgE,SAAS,IAAIhE,IAAI,CAACiE,QAAQ,EAAG;cAC1CvC,KAAK,EAAE;gBACLgC,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdO,SAAS,EAAE,OAAO;gBAClBzB,YAAY,EAAE;cAChB,CAAE;cACF0B,OAAO,EAAGrB,CAAC,IAAK;gBACd;gBACAtC,OAAO,CAAC4D,GAAG,CAAC,0DAA0D,CAAC;gBACvE,MAAMC,MAAM,GAAGvB,CAAC,CAACuB,MAA0B;gBAC3CA,MAAM,CAAC3C,KAAK,CAACI,OAAO,GAAG,MAAM;gBAC7B,MAAMwC,MAAM,GAAGD,MAAM,CAACE,aAAa;gBACnC,IAAID,MAAM,EAAE;kBAAA,IAAAE,eAAA,EAAAC,cAAA;kBACVH,MAAM,CAAC5C,KAAK,CAACC,UAAU,GAAG,mDAAmD;kBAC7E2C,MAAM,CAACI,SAAS,GAAG,GAAG,CAAA1E,IAAI,aAAJA,IAAI,wBAAAwE,eAAA,GAAJxE,IAAI,CAAEgE,SAAS,cAAAQ,eAAA,uBAAfA,eAAA,CAAiBG,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE,GAAG,CAAA3E,IAAI,aAAJA,IAAI,wBAAAyE,cAAA,GAAJzE,IAAI,CAAEiE,QAAQ,cAAAQ,cAAA,uBAAdA,cAAA,CAAgBE,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE,EAAE;gBAC5F;cACF;YAAE;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GAEF,GAAG,CAAAtD,IAAI,aAAJA,IAAI,wBAAAJ,gBAAA,GAAJI,IAAI,CAAEgE,SAAS,cAAApE,gBAAA,uBAAfA,gBAAA,CAAiB+E,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE,GAAG,CAAA3E,IAAI,aAAJA,IAAI,wBAAAH,eAAA,GAAJG,IAAI,CAAEiE,QAAQ,cAAApE,eAAA,uBAAdA,eAAA,CAAgB8E,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE;UACtE;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN9D,OAAA;YAAKkC,KAAK,EAAE;cAAEkD,SAAS,EAAE;YAAO,CAAE;YAAAvC,QAAA,gBAChC7C,OAAA;cAAKkC,KAAK,EAAE;gBACVwB,KAAK,EAAE,SAAS;gBAChBO,UAAU,EAAE,KAAK;gBACjBb,QAAQ,EAAE,QAAQ;gBAClBiC,UAAU,EAAE;cACd,CAAE;cAAAxC,QAAA,GACCrC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgE,SAAS,EAAC,GAAC,EAAChE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiE,QAAQ;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACN9D,OAAA;cAAKkC,KAAK,EAAE;gBACVwB,KAAK,EAAE,SAAS;gBAChBN,QAAQ,EAAE,SAAS;gBACnBiC,UAAU,EAAE;cACd,CAAE;cAAAxC,QAAA,EACC,CAAArC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkC,QAAQ,KAAI;YAAe;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN9D,OAAA;YAAMkC,KAAK,EAAE;cACXwB,KAAK,EAAE,SAAS;cAChBN,QAAQ,EAAE,SAAS;cACnBkC,SAAS,EAAE1E,YAAY,GAAG,gBAAgB,GAAG,cAAc;cAC3DuC,UAAU,EAAE;YACd,CAAE;YAAAN,QAAA,EAAC;UAEH;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EAGRlD,YAAY,iBACXZ,OAAA;UAAKkC,KAAK,EAAE;YACVQ,QAAQ,EAAE,UAAU;YACpBC,GAAG,EAAE,MAAM;YACX4C,KAAK,EAAE,CAAC;YACRC,SAAS,EAAE,QAAQ;YACnBrD,UAAU,EAAE,OAAO;YACnBc,YAAY,EAAE,MAAM;YACpBR,SAAS,EAAE,iCAAiC;YAC5CO,MAAM,EAAE,mBAAmB;YAC3ByC,QAAQ,EAAE,OAAO;YACjB7C,MAAM,EAAE;UACV,CAAE;UAAAC,QAAA,eACA7C,OAAA;YAAKkC,KAAK,EAAE;cAAEG,OAAO,EAAE;YAAO,CAAE;YAAAQ,QAAA,gBAE9B7C,OAAA;cAAKkC,KAAK,EAAE;gBACVI,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBO,GAAG,EAAE,SAAS;gBACdiB,YAAY,EAAE;cAChB,CAAE;cAAAlB,QAAA,gBAEA7C,OAAA;gBAAKkC,KAAK,EAAE;kBACVgC,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdlB,YAAY,EAAE,KAAK;kBACnBd,UAAU,EAAE3B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE4D,cAAc,GAAG,aAAa,GAAG,mDAAmD;kBACtG9B,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBC,cAAc,EAAE,QAAQ;kBACxBkB,KAAK,EAAE,OAAO;kBACdO,UAAU,EAAE,KAAK;kBACjBb,QAAQ,EAAE,QAAQ;kBAClBiB,QAAQ,EAAE,QAAQ;kBAClBrB,MAAM,EAAE,mBAAmB;kBAC3B0C,UAAU,EAAE;gBACd,CAAE;gBAAA7C,QAAA,EACCrC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE4D,cAAc,gBACnBpE,OAAA;kBACEsE,GAAG,EAAE,wBAAwB9D,IAAI,CAAC4D,cAAc,EAAG;kBACnDG,GAAG,EAAE,GAAG/D,IAAI,CAACgE,SAAS,IAAIhE,IAAI,CAACiE,QAAQ,EAAG;kBAC1CvC,KAAK,EAAE;oBACLgC,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE,MAAM;oBACdO,SAAS,EAAE,OAAO;oBAClBzB,YAAY,EAAE;kBAChB,CAAE;kBACF0B,OAAO,EAAGrB,CAAC,IAAK;oBACd;oBACAtC,OAAO,CAAC4D,GAAG,CAAC,mEAAmE,CAAC;oBAChF,MAAMC,MAAM,GAAGvB,CAAC,CAACuB,MAA0B;oBAC3CA,MAAM,CAAC3C,KAAK,CAACI,OAAO,GAAG,MAAM;oBAC7B,MAAMwC,MAAM,GAAGD,MAAM,CAACE,aAAa;oBACnC,IAAID,MAAM,EAAE;sBAAA,IAAAa,gBAAA,EAAAC,eAAA;sBACVd,MAAM,CAAC5C,KAAK,CAACC,UAAU,GAAG,mDAAmD;sBAC7E2C,MAAM,CAACI,SAAS,GAAG,GAAG,CAAA1E,IAAI,aAAJA,IAAI,wBAAAmF,gBAAA,GAAJnF,IAAI,CAAEgE,SAAS,cAAAmB,gBAAA,uBAAfA,gBAAA,CAAiBR,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE,GAAG,CAAA3E,IAAI,aAAJA,IAAI,wBAAAoF,eAAA,GAAJpF,IAAI,CAAEiE,QAAQ,cAAAmB,eAAA,uBAAdA,eAAA,CAAgBT,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE,EAAE;oBAC5F;kBACF;gBAAE;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,GAEF,GAAG,CAAAtD,IAAI,aAAJA,IAAI,wBAAAF,gBAAA,GAAJE,IAAI,CAAEgE,SAAS,cAAAlE,gBAAA,uBAAfA,gBAAA,CAAiB6E,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE,GAAG,CAAA3E,IAAI,aAAJA,IAAI,wBAAAD,eAAA,GAAJC,IAAI,CAAEiE,QAAQ,cAAAlE,eAAA,uBAAdA,eAAA,CAAgB4E,MAAM,CAAC,CAAC,CAAC,KAAI,EAAE;cACtE;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGN9D,OAAA;gBAAKkC,KAAK,EAAE;kBAAE2D,IAAI,EAAE;gBAAE,CAAE;gBAAAhD,QAAA,gBACtB7C,OAAA;kBAAKkC,KAAK,EAAE;oBACVwB,KAAK,EAAE,SAAS;oBAChBO,UAAU,EAAE,KAAK;oBACjBF,YAAY,EAAE,SAAS;oBACvBX,QAAQ,EAAE;kBACZ,CAAE;kBAAAP,QAAA,GACCrC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgE,SAAS,EAAC,GAAC,EAAChE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiE,QAAQ;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC,eACN9D,OAAA;kBAAKkC,KAAK,EAAE;oBACVwB,KAAK,EAAE,SAAS;oBAChBN,QAAQ,EAAE;kBACZ,CAAE;kBAAAP,QAAA,EACCrC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsF;gBAAK;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9D,OAAA;cAAIkC,KAAK,EAAE;gBACTc,MAAM,EAAE,MAAM;gBACd+C,SAAS,EAAE,mBAAmB;gBAC9B/B,MAAM,EAAE;cACV;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAkCL9D,OAAA;cACE+C,OAAO,EAAEA,CAAA,KAAM;gBACblC,eAAe,CAAC,KAAK,CAAC;gBACtBC,YAAY,CAAC,CAAC;cAChB,CAAE;cACFoB,KAAK,EAAE;gBACLgC,KAAK,EAAE,MAAM;gBACb/B,UAAU,EAAE,MAAM;gBAClBa,MAAM,EAAE,MAAM;gBACdX,OAAO,EAAE,SAAS;gBAClBY,YAAY,EAAE,KAAK;gBACnBC,MAAM,EAAE,SAAS;gBACjBkC,SAAS,EAAE,MAAM;gBACjB1B,KAAK,EAAE,SAAS;gBAChBN,QAAQ,EAAE,UAAU;gBACpBD,UAAU,EAAE;cACd,CAAE;cACFE,YAAY,EAAGC,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAACrB,KAAK,CAACC,UAAU,GAAG,SAAS;cAC9C,CAAE;cACFqB,YAAY,EAAGF,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAACrB,KAAK,CAACC,UAAU,GAAG,MAAM;cAC3C,CAAE;cAAAU,QAAA,eAEF7C,OAAA;gBAAMkC,KAAK,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEO,GAAG,EAAE;gBAAS,CAAE;gBAAAD,QAAA,gBACpE7C,OAAA,CAACP,MAAM;kBAACgE,IAAI,EAAE,EAAG;kBAACC,KAAK,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,UAEtC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLlD,YAAY,iBACXZ,OAAA;MACEkC,KAAK,EAAE;QACLQ,QAAQ,EAAE,OAAO;QACjBC,GAAG,EAAE,CAAC;QACNqD,IAAI,EAAE,CAAC;QACPT,KAAK,EAAE,CAAC;QACRU,MAAM,EAAE,CAAC;QACTrD,MAAM,EAAE;MACV,CAAE;MACFG,OAAO,EAAEA,CAAA,KAAMlC,eAAe,CAAC,KAAK;IAAE;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAAC3D,EAAA,CAndIF,WAAuC;EAAA,QAClBjB,YAAY,EACpBF,WAAW,EACXC,WAAW;AAAA;AAAAmH,EAAA,GAHxBjG,WAAuC;AAqd7C,eAAeA,WAAW;AAAC,IAAAiG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}