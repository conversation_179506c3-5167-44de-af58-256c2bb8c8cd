{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\admin\\\\AdminManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { UserPlus, Search, Users, AlertTriangle, CheckCircle } from 'lucide-react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\nimport AdminAccountList from '../../components/admin/AdminAccountList';\nimport AdminAccountModal from '../../components/admin/AdminAccountModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminManagement = () => {\n  _s();\n  const {\n    user\n  } = useAdminAuth();\n  const permissions = usePermissions(user);\n\n  // State management\n  const [admins, setAdmins] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n\n  // Modal state\n  const [showModal, setShowModal] = useState(false);\n  const [editingAdmin, setEditingAdmin] = useState(null);\n  const [modalLoading, setModalLoading] = useState(false);\n\n  // Search and filter state\n  const [searchTerm, setSearchTerm] = useState('');\n  const [positionFilter, setPositionFilter] = useState('');\n  const [statusFilter, setStatusFilter] = useState('');\n\n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(10);\n  const [totalItems, setTotalItems] = useState(0);\n  useEffect(() => {\n    // Check if user has permission to manage admins\n    if (!permissions.canManageAdmins) {\n      setError('You do not have permission to manage admin accounts');\n      setLoading(false);\n      return;\n    }\n    loadAdmins();\n  }, [permissions.canManageAdmins, currentPage, itemsPerPage, searchTerm, positionFilter, statusFilter]);\n\n  // Reset to page 1 when filters change\n  useEffect(() => {\n    setCurrentPage(1);\n  }, [searchTerm, positionFilter, statusFilter]);\n  const loadAdmins = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // TODO: Implement actual API call\n      // const response = await adminManagementService.getAdmins({\n      //   page: currentPage,\n      //   limit: itemsPerPage,\n      //   search: searchTerm,\n      //   position: positionFilter,\n      //   status: statusFilter\n      // });\n\n      // Mock data with proper structure\n      const mockAdmins = [{\n        admin_id: 1,\n        email: '<EMAIL>',\n        is_active: true,\n        last_login: '2025-08-05T10:30:00Z',\n        created_at: '2025-01-01T00:00:00Z',\n        profile: {\n          first_name: 'Josh',\n          middle_name: 'Christian',\n          last_name: 'Mojica',\n          full_name: 'Josh Christian Mojica',\n          phone_number: '+************',\n          department: 'Administration',\n          position: 'super_admin',\n          grade_level: 12,\n          bio: 'System Administrator with expertise in educational technology.'\n        }\n      }, {\n        admin_id: 2,\n        email: '<EMAIL>',\n        is_active: true,\n        last_login: '2025-08-05T09:15:00Z',\n        created_at: '2025-01-15T00:00:00Z',\n        profile: {\n          first_name: 'Zaira',\n          last_name: 'Plarisan',\n          full_name: 'Zaira Plarisan',\n          phone_number: '+***********1',\n          department: 'Mathematics',\n          position: 'professor',\n          grade_level: 11,\n          bio: 'Mathematics professor specializing in Grade 11 curriculum.'\n        }\n      }];\n\n      // Apply filters\n      let filteredAdmins = [...mockAdmins];\n      if (searchTerm.trim()) {\n        const searchLower = searchTerm.toLowerCase().trim();\n        filteredAdmins = filteredAdmins.filter(admin => admin.profile.full_name.toLowerCase().includes(searchLower) || admin.email.toLowerCase().includes(searchLower) || admin.profile.first_name.toLowerCase().includes(searchLower) || admin.profile.last_name.toLowerCase().includes(searchLower) || admin.profile.department && admin.profile.department.toLowerCase().includes(searchLower));\n      }\n      if (positionFilter) {\n        filteredAdmins = filteredAdmins.filter(admin => admin.profile.position === positionFilter);\n      }\n      if (statusFilter) {\n        const isActive = statusFilter === 'active';\n        filteredAdmins = filteredAdmins.filter(admin => admin.is_active === isActive);\n      }\n      setTotalItems(filteredAdmins.length);\n\n      // Apply pagination\n      const startIndex = (currentPage - 1) * itemsPerPage;\n      const paginatedAdmins = filteredAdmins.slice(startIndex, startIndex + itemsPerPage);\n      setAdmins(paginatedAdmins);\n    } catch (err) {\n      setError(err.message || 'Failed to load admin accounts');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handler functions\n  const handleAddAdmin = () => {\n    setEditingAdmin(null);\n    setShowModal(true);\n  };\n  const handleEditAdmin = admin => {\n    setEditingAdmin(admin);\n    setShowModal(true);\n  };\n  const handleDeleteAdmin = async admin => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await adminManagementService.deleteAdmin(admin.admin_id);\n\n      // Mock success\n      setSuccess(`Admin account for ${admin.profile.full_name} has been deactivated`);\n      loadAdmins();\n    } catch (err) {\n      setError(err.message || 'Failed to delete admin account');\n    }\n  };\n  const handleToggleStatus = async admin => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await adminManagementService.toggleAdminStatus(admin.admin_id, !admin.is_active);\n\n      // Mock success\n      const action = admin.is_active ? 'deactivated' : 'activated';\n      setSuccess(`Admin account for ${admin.profile.full_name} has been ${action}`);\n      loadAdmins();\n    } catch (err) {\n      setError(err.message || 'Failed to update admin status');\n    }\n  };\n  const handleSaveAdmin = async adminData => {\n    try {\n      setModalLoading(true);\n      setError(null);\n      setSuccess(null);\n      if (editingAdmin) {\n        // TODO: Implement actual API call for update\n        // await adminManagementService.updateAdmin(editingAdmin.admin_id, adminData);\n        setSuccess(`Admin account for ${adminData.profile.first_name} ${adminData.profile.last_name} has been updated`);\n      } else {\n        // TODO: Implement actual API call for create\n        // await adminManagementService.createAdmin(adminData);\n        setSuccess(`Admin account for ${adminData.profile.first_name} ${adminData.profile.last_name} has been created`);\n      }\n      loadAdmins();\n    } catch (err) {\n      setError(err.message || 'Failed to save admin account');\n      throw err; // Re-throw to prevent modal from closing\n    } finally {\n      setModalLoading(false);\n    }\n  };\n  const handlePageChange = page => {\n    setCurrentPage(page);\n  };\n  const handleItemsPerPageChange = itemsPerPage => {\n    setItemsPerPage(itemsPerPage);\n    setCurrentPage(1); // Reset to first page\n  };\n  const clearMessages = () => {\n    setError(null);\n    setSuccess(null);\n  };\n  if (!permissions.canManageAdmins) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Users, {\n        size: 64,\n        style: {\n          marginBottom: '1rem',\n          opacity: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          margin: '0 0 0.5rem',\n          fontSize: '1.5rem',\n          fontWeight: '600'\n        },\n        children: \"Access Denied\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: 0,\n          fontSize: '1rem'\n        },\n        children: \"You do not have permission to manage admin accounts.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        },\n        children: [\"Current Role: \", permissions.getPositionDisplayName()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '40px',\n          height: '40px',\n          border: '4px solid #f3f4f6',\n          borderTop: '4px solid #3b82f6',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '2rem',\n        textAlign: 'center',\n        color: '#ef4444'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Error\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: loadAdmins,\n        style: {\n          padding: '0.5rem 1rem',\n          background: '#3b82f6',\n          color: 'white',\n          border: 'none',\n          borderRadius: '6px',\n          cursor: 'pointer'\n        },\n        children: \"Retry\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      maxWidth: '1400px',\n      margin: '0 auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            margin: '0 0 0.5rem',\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          },\n          children: \"Admin Account Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          },\n          children: \"Manage administrator accounts and permissions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleAddAdmin,\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          padding: '0.75rem 1.5rem',\n          background: '#3b82f6',\n          color: 'white',\n          border: 'none',\n          borderRadius: '8px',\n          fontSize: '0.875rem',\n          fontWeight: '600',\n          cursor: 'pointer',\n          transition: 'background-color 0.2s'\n        },\n        onMouseEnter: e => e.currentTarget.style.background = '#2563eb',\n        onMouseLeave: e => e.currentTarget.style.background = '#3b82f6',\n        children: [/*#__PURE__*/_jsxDEV(UserPlus, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 11\n        }, this), \"Add Admin\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        gap: '1rem',\n        marginBottom: '2rem',\n        flexWrap: 'wrap'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          minWidth: '300px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'relative'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Search, {\n            size: 20,\n            style: {\n              position: 'absolute',\n              left: '0.75rem',\n              top: '50%',\n              transform: 'translateY(-50%)',\n              color: '#6b7280'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search admins by name or email...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            style: {\n              width: '100%',\n              padding: '0.75rem 0.75rem 0.75rem 2.5rem',\n              border: '1px solid #d1d5db',\n              borderRadius: '8px',\n              fontSize: '0.875rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n        value: positionFilter,\n        onChange: e => setPositionFilter(e.target.value),\n        style: {\n          padding: '0.75rem',\n          border: '1px solid #d1d5db',\n          borderRadius: '8px',\n          fontSize: '0.875rem',\n          minWidth: '150px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"\",\n          children: \"All Positions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"super_admin\",\n          children: \"Super Admin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"professor\",\n          children: \"Professor\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n        value: statusFilter,\n        onChange: e => setStatusFilter(e.target.value),\n        style: {\n          padding: '0.75rem',\n          border: '1px solid #d1d5db',\n          borderRadius: '8px',\n          fontSize: '0.875rem',\n          minWidth: '120px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"\",\n          children: \"All Status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"active\",\n          children: \"Active\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"inactive\",\n          children: \"Inactive\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 379,\n      columnNumber: 7\n    }, this), (error || success) && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '1.5rem'\n      },\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '1rem',\n          background: '#fef2f2',\n          border: '1px solid #fecaca',\n          borderRadius: '8px',\n          color: '#dc2626',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 17\n          }, this), error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: clearMessages,\n          style: {\n            background: 'transparent',\n            border: 'none',\n            color: '#dc2626',\n            cursor: 'pointer',\n            padding: '0.25rem'\n          },\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 447,\n        columnNumber: 13\n      }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '1rem',\n          background: '#f0fdf4',\n          border: '1px solid #bbf7d0',\n          borderRadius: '8px',\n          color: '#166534',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 17\n          }, this), success]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: clearMessages,\n          style: {\n            background: 'transparent',\n            border: 'none',\n            color: '#166534',\n            cursor: 'pointer',\n            padding: '0.25rem'\n          },\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 491,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 477,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 445,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(AdminAccountList, {\n      admins: admins,\n      loading: loading,\n      onEdit: handleEditAdmin,\n      onDelete: handleDeleteAdmin,\n      onToggleStatus: handleToggleStatus,\n      currentPage: currentPage,\n      totalPages: Math.ceil(totalItems / itemsPerPage),\n      itemsPerPage: itemsPerPage,\n      totalItems: totalItems,\n      onPageChange: handlePageChange,\n      onItemsPerPageChange: handleItemsPerPageChange\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 509,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AdminAccountModal, {\n      isOpen: showModal,\n      onClose: () => setShowModal(false),\n      onSave: handleSaveAdmin,\n      admin: editingAdmin,\n      loading: modalLoading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 524,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 328,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminManagement, \"2LV4tC1oMDp47W/1IsKQ7F5LLfg=\", false, function () {\n  return [useAdminAuth, usePermissions];\n});\n_c = AdminManagement;\nexport default AdminManagement;\nvar _c;\n$RefreshReg$(_c, \"AdminManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "UserPlus", "Search", "Users", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CheckCircle", "useAdminAuth", "usePermissions", "AdminAccountList", "AdminAccountModal", "jsxDEV", "_jsxDEV", "AdminManagement", "_s", "user", "permissions", "admins", "setAdmins", "loading", "setLoading", "error", "setError", "success", "setSuccess", "showModal", "setShowModal", "editingAdmin", "setEditingAdmin", "modalLoading", "setModalLoading", "searchTerm", "setSearchTerm", "positionFilter", "setPositionFilter", "statusFilter", "setStatus<PERSON>ilter", "currentPage", "setCurrentPage", "itemsPerPage", "setItemsPerPage", "totalItems", "setTotalItems", "canManageAdmins", "loadAdmins", "mockAdmins", "admin_id", "email", "is_active", "last_login", "created_at", "profile", "first_name", "middle_name", "last_name", "full_name", "phone_number", "department", "position", "grade_level", "bio", "filteredAdmins", "trim", "searchLower", "toLowerCase", "filter", "admin", "includes", "isActive", "length", "startIndex", "paginatedAdmins", "slice", "err", "message", "handleAddAdmin", "handleEditAdmin", "handleDeleteAdmin", "handleToggleStatus", "action", "handleSaveAdmin", "adminData", "handlePageChange", "page", "handleItemsPerPageChange", "clearMessages", "style", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "textAlign", "color", "children", "size", "marginBottom", "opacity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "margin", "fontSize", "fontWeight", "marginTop", "padding", "background", "getPositionBadgeColor", "borderRadius", "getPositionDisplayName", "width", "height", "border", "borderTop", "animation", "onClick", "cursor", "max<PERSON><PERSON><PERSON>", "gap", "transition", "onMouseEnter", "e", "currentTarget", "onMouseLeave", "flexWrap", "flex", "min<PERSON><PERSON><PERSON>", "left", "top", "transform", "type", "placeholder", "value", "onChange", "target", "onEdit", "onDelete", "onToggleStatus", "totalPages", "Math", "ceil", "onPageChange", "onItemsPerPageChange", "isOpen", "onClose", "onSave", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/admin/AdminManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { UserPlus, Search, Filter, Users, AlertTriangle, CheckCircle } from 'lucide-react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\nimport { adminManagementService } from '../../services/adminManagementService';\nimport AdminAccountList from '../../components/admin/AdminAccountList';\nimport AdminAccountModal from '../../components/admin/AdminAccountModal';\n\ninterface AdminAccount {\n  admin_id?: number;\n  email: string;\n  is_active: boolean;\n  last_login?: string | null;\n  created_at?: string;\n  profile: {\n    first_name: string;\n    middle_name?: string;\n    last_name: string;\n    suffix?: string;\n    full_name: string;\n    phone_number?: string;\n    department?: string;\n    position: 'super_admin' | 'professor';\n    grade_level?: number;\n    bio?: string;\n    profile_picture?: string;\n  };\n}\n\nconst AdminManagement: React.FC = () => {\n  const { user } = useAdminAuth();\n  const permissions = usePermissions(user);\n\n  // State management\n  const [admins, setAdmins] = useState<AdminAccount[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  // Modal state\n  const [showModal, setShowModal] = useState(false);\n  const [editingAdmin, setEditingAdmin] = useState<AdminAccount | null>(null);\n  const [modalLoading, setModalLoading] = useState(false);\n\n  // Search and filter state\n  const [searchTerm, setSearchTerm] = useState('');\n  const [positionFilter, setPositionFilter] = useState<string>('');\n  const [statusFilter, setStatusFilter] = useState<string>('');\n\n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(10);\n  const [totalItems, setTotalItems] = useState(0);\n\n  useEffect(() => {\n    // Check if user has permission to manage admins\n    if (!permissions.canManageAdmins) {\n      setError('You do not have permission to manage admin accounts');\n      setLoading(false);\n      return;\n    }\n\n    loadAdmins();\n  }, [permissions.canManageAdmins, currentPage, itemsPerPage, searchTerm, positionFilter, statusFilter]);\n\n  // Reset to page 1 when filters change\n  useEffect(() => {\n    setCurrentPage(1);\n  }, [searchTerm, positionFilter, statusFilter]);\n\n  const loadAdmins = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // TODO: Implement actual API call\n      // const response = await adminManagementService.getAdmins({\n      //   page: currentPage,\n      //   limit: itemsPerPage,\n      //   search: searchTerm,\n      //   position: positionFilter,\n      //   status: statusFilter\n      // });\n\n      // Mock data with proper structure\n      const mockAdmins: AdminAccount[] = [\n        {\n          admin_id: 1,\n          email: '<EMAIL>',\n          is_active: true,\n          last_login: '2025-08-05T10:30:00Z',\n          created_at: '2025-01-01T00:00:00Z',\n          profile: {\n            first_name: 'Josh',\n            middle_name: 'Christian',\n            last_name: 'Mojica',\n            full_name: 'Josh Christian Mojica',\n            phone_number: '+************',\n            department: 'Administration',\n            position: 'super_admin',\n            grade_level: 12,\n            bio: 'System Administrator with expertise in educational technology.'\n          }\n        },\n        {\n          admin_id: 2,\n          email: '<EMAIL>',\n          is_active: true,\n          last_login: '2025-08-05T09:15:00Z',\n          created_at: '2025-01-15T00:00:00Z',\n          profile: {\n            first_name: 'Zaira',\n            last_name: 'Plarisan',\n            full_name: 'Zaira Plarisan',\n            phone_number: '+***********1',\n            department: 'Mathematics',\n            position: 'professor',\n            grade_level: 11,\n            bio: 'Mathematics professor specializing in Grade 11 curriculum.'\n          }\n        }\n      ];\n\n      // Apply filters\n      let filteredAdmins = [...mockAdmins];\n\n      if (searchTerm.trim()) {\n        const searchLower = searchTerm.toLowerCase().trim();\n        filteredAdmins = filteredAdmins.filter(admin =>\n          admin.profile.full_name.toLowerCase().includes(searchLower) ||\n          admin.email.toLowerCase().includes(searchLower) ||\n          admin.profile.first_name.toLowerCase().includes(searchLower) ||\n          admin.profile.last_name.toLowerCase().includes(searchLower) ||\n          (admin.profile.department && admin.profile.department.toLowerCase().includes(searchLower))\n        );\n      }\n\n      if (positionFilter) {\n        filteredAdmins = filteredAdmins.filter(admin => admin.profile.position === positionFilter);\n      }\n\n      if (statusFilter) {\n        const isActive = statusFilter === 'active';\n        filteredAdmins = filteredAdmins.filter(admin => admin.is_active === isActive);\n      }\n\n      setTotalItems(filteredAdmins.length);\n\n      // Apply pagination\n      const startIndex = (currentPage - 1) * itemsPerPage;\n      const paginatedAdmins = filteredAdmins.slice(startIndex, startIndex + itemsPerPage);\n\n      setAdmins(paginatedAdmins);\n\n    } catch (err: any) {\n      setError(err.message || 'Failed to load admin accounts');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handler functions\n  const handleAddAdmin = () => {\n    setEditingAdmin(null);\n    setShowModal(true);\n  };\n\n  const handleEditAdmin = (admin: AdminAccount) => {\n    setEditingAdmin(admin);\n    setShowModal(true);\n  };\n\n  const handleDeleteAdmin = async (admin: AdminAccount) => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await adminManagementService.deleteAdmin(admin.admin_id);\n\n      // Mock success\n      setSuccess(`Admin account for ${admin.profile.full_name} has been deactivated`);\n      loadAdmins();\n\n    } catch (err: any) {\n      setError(err.message || 'Failed to delete admin account');\n    }\n  };\n\n  const handleToggleStatus = async (admin: AdminAccount) => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await adminManagementService.toggleAdminStatus(admin.admin_id, !admin.is_active);\n\n      // Mock success\n      const action = admin.is_active ? 'deactivated' : 'activated';\n      setSuccess(`Admin account for ${admin.profile.full_name} has been ${action}`);\n      loadAdmins();\n\n    } catch (err: any) {\n      setError(err.message || 'Failed to update admin status');\n    }\n  };\n\n  const handleSaveAdmin = async (adminData: AdminAccount) => {\n    try {\n      setModalLoading(true);\n      setError(null);\n      setSuccess(null);\n\n      if (editingAdmin) {\n        // TODO: Implement actual API call for update\n        // await adminManagementService.updateAdmin(editingAdmin.admin_id, adminData);\n        setSuccess(`Admin account for ${adminData.profile.first_name} ${adminData.profile.last_name} has been updated`);\n      } else {\n        // TODO: Implement actual API call for create\n        // await adminManagementService.createAdmin(adminData);\n        setSuccess(`Admin account for ${adminData.profile.first_name} ${adminData.profile.last_name} has been created`);\n      }\n\n      loadAdmins();\n\n    } catch (err: any) {\n      setError(err.message || 'Failed to save admin account');\n      throw err; // Re-throw to prevent modal from closing\n    } finally {\n      setModalLoading(false);\n    }\n  };\n\n  const handlePageChange = (page: number) => {\n    setCurrentPage(page);\n  };\n\n  const handleItemsPerPageChange = (itemsPerPage: number) => {\n    setItemsPerPage(itemsPerPage);\n    setCurrentPage(1); // Reset to first page\n  };\n\n  const clearMessages = () => {\n    setError(null);\n    setSuccess(null);\n  };\n\n  if (!permissions.canManageAdmins) {\n    return (\n      <div style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      }}>\n        <Users size={64} style={{ marginBottom: '1rem', opacity: 0.5 }} />\n        <h2 style={{ margin: '0 0 0.5rem', fontSize: '1.5rem', fontWeight: '600' }}>\n          Access Denied\n        </h2>\n        <p style={{ margin: 0, fontSize: '1rem' }}>\n          You do not have permission to manage admin accounts.\n        </p>\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        }}>\n          Current Role: {permissions.getPositionDisplayName()}\n        </div>\n      </div>\n    );\n  }\n\n  if (loading) {\n    return (\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px'\n      }}>\n        <div style={{\n          width: '40px',\n          height: '40px',\n          border: '4px solid #f3f4f6',\n          borderTop: '4px solid #3b82f6',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }} />\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div style={{\n        padding: '2rem',\n        textAlign: 'center',\n        color: '#ef4444'\n      }}>\n        <h2>Error</h2>\n        <p>{error}</p>\n        <button\n          onClick={loadAdmins}\n          style={{\n            padding: '0.5rem 1rem',\n            background: '#3b82f6',\n            color: 'white',\n            border: 'none',\n            borderRadius: '6px',\n            cursor: 'pointer'\n          }}\n        >\n          Retry\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ maxWidth: '1400px', margin: '0 auto' }}>\n      {/* Header */}\n      <div style={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      }}>\n        <div>\n          <h1 style={{\n            margin: '0 0 0.5rem',\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          }}>\n            Admin Account Management\n          </h1>\n          <p style={{\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          }}>\n            Manage administrator accounts and permissions\n          </p>\n        </div>\n\n        <button\n          onClick={handleAddAdmin}\n          style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            padding: '0.75rem 1.5rem',\n            background: '#3b82f6',\n            color: 'white',\n            border: 'none',\n            borderRadius: '8px',\n            fontSize: '0.875rem',\n            fontWeight: '600',\n            cursor: 'pointer',\n            transition: 'background-color 0.2s'\n          }}\n          onMouseEnter={(e) => e.currentTarget.style.background = '#2563eb'}\n          onMouseLeave={(e) => e.currentTarget.style.background = '#3b82f6'}\n        >\n          <UserPlus size={16} />\n          Add Admin\n        </button>\n      </div>\n\n      {/* Search and Filter Bar */}\n      <div style={{\n        display: 'flex',\n        gap: '1rem',\n        marginBottom: '2rem',\n        flexWrap: 'wrap'\n      }}>\n        <div style={{ flex: 1, minWidth: '300px' }}>\n          <div style={{ position: 'relative' }}>\n            <Search size={20} style={{\n              position: 'absolute',\n              left: '0.75rem',\n              top: '50%',\n              transform: 'translateY(-50%)',\n              color: '#6b7280'\n            }} />\n            <input\n              type=\"text\"\n              placeholder=\"Search admins by name or email...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              style={{\n                width: '100%',\n                padding: '0.75rem 0.75rem 0.75rem 2.5rem',\n                border: '1px solid #d1d5db',\n                borderRadius: '8px',\n                fontSize: '0.875rem'\n              }}\n            />\n          </div>\n        </div>\n\n        <select\n          value={positionFilter}\n          onChange={(e) => setPositionFilter(e.target.value)}\n          style={{\n            padding: '0.75rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '8px',\n            fontSize: '0.875rem',\n            minWidth: '150px'\n          }}\n        >\n          <option value=\"\">All Positions</option>\n          <option value=\"super_admin\">Super Admin</option>\n          <option value=\"professor\">Professor</option>\n        </select>\n\n        <select\n          value={statusFilter}\n          onChange={(e) => setStatusFilter(e.target.value)}\n          style={{\n            padding: '0.75rem',\n            border: '1px solid #d1d5db',\n            borderRadius: '8px',\n            fontSize: '0.875rem',\n            minWidth: '120px'\n          }}\n        >\n          <option value=\"\">All Status</option>\n          <option value=\"active\">Active</option>\n          <option value=\"inactive\">Inactive</option>\n        </select>\n      </div>\n\n      {/* Success/Error Messages */}\n      {(error || success) && (\n        <div style={{ marginBottom: '1.5rem' }}>\n          {error && (\n            <div style={{\n              padding: '1rem',\n              background: '#fef2f2',\n              border: '1px solid #fecaca',\n              borderRadius: '8px',\n              color: '#dc2626',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            }}>\n              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                <AlertTriangle size={16} />\n                {error}\n              </div>\n              <button\n                onClick={clearMessages}\n                style={{\n                  background: 'transparent',\n                  border: 'none',\n                  color: '#dc2626',\n                  cursor: 'pointer',\n                  padding: '0.25rem'\n                }}\n              >\n                ×\n              </button>\n            </div>\n          )}\n\n          {success && (\n            <div style={{\n              padding: '1rem',\n              background: '#f0fdf4',\n              border: '1px solid #bbf7d0',\n              borderRadius: '8px',\n              color: '#166534',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            }}>\n              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                <CheckCircle size={16} />\n                {success}\n              </div>\n              <button\n                onClick={clearMessages}\n                style={{\n                  background: 'transparent',\n                  border: 'none',\n                  color: '#166534',\n                  cursor: 'pointer',\n                  padding: '0.25rem'\n                }}\n              >\n                ×\n              </button>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Admin List */}\n      <AdminAccountList\n        admins={admins}\n        loading={loading}\n        onEdit={handleEditAdmin}\n        onDelete={handleDeleteAdmin}\n        onToggleStatus={handleToggleStatus}\n        currentPage={currentPage}\n        totalPages={Math.ceil(totalItems / itemsPerPage)}\n        itemsPerPage={itemsPerPage}\n        totalItems={totalItems}\n        onPageChange={handlePageChange}\n        onItemsPerPageChange={handleItemsPerPageChange}\n      />\n\n      {/* Admin Modal */}\n      <AdminAccountModal\n        isOpen={showModal}\n        onClose={() => setShowModal(false)}\n        onSave={handleSaveAdmin}\n        admin={editingAdmin}\n        loading={modalLoading}\n      />\n    </div>\n  );\n};\n\nexport default AdminManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,QAAQ,EAAEC,MAAM,EAAUC,KAAK,EAAEC,aAAa,EAAEC,WAAW,QAAQ,cAAc;AAC1F,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AAExD,OAAOC,gBAAgB,MAAM,yCAAyC;AACtE,OAAOC,iBAAiB,MAAM,0CAA0C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAuBzE,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IAAEC;EAAK,CAAC,GAAGR,YAAY,CAAC,CAAC;EAC/B,MAAMS,WAAW,GAAGR,cAAc,CAACO,IAAI,CAAC;;EAExC;EACA,MAAM,CAACE,MAAM,EAAEC,SAAS,CAAC,GAAGlB,QAAQ,CAAiB,EAAE,CAAC;EACxD,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAgB,IAAI,CAAC;;EAE3D;EACA,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAsB,IAAI,CAAC;EAC3E,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiC,cAAc,EAAEC,iBAAiB,CAAC,GAAGlC,QAAQ,CAAS,EAAE,CAAC;EAChE,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAS,EAAE,CAAC;;EAE5D;EACA,MAAM,CAACqC,WAAW,EAAEC,cAAc,CAAC,GAAGtC,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACyC,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAAC,CAAC,CAAC;EAE/CC,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACe,WAAW,CAAC2B,eAAe,EAAE;MAChCrB,QAAQ,CAAC,qDAAqD,CAAC;MAC/DF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEAwB,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAC5B,WAAW,CAAC2B,eAAe,EAAEN,WAAW,EAAEE,YAAY,EAAER,UAAU,EAAEE,cAAc,EAAEE,YAAY,CAAC,CAAC;;EAEtG;EACAlC,SAAS,CAAC,MAAM;IACdqC,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC,EAAE,CAACP,UAAU,EAAEE,cAAc,EAAEE,YAAY,CAAC,CAAC;EAE9C,MAAMS,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFxB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA,MAAMuB,UAA0B,GAAG,CACjC;QACEC,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE,0BAA0B;QACjCC,SAAS,EAAE,IAAI;QACfC,UAAU,EAAE,sBAAsB;QAClCC,UAAU,EAAE,sBAAsB;QAClCC,OAAO,EAAE;UACPC,UAAU,EAAE,MAAM;UAClBC,WAAW,EAAE,WAAW;UACxBC,SAAS,EAAE,QAAQ;UACnBC,SAAS,EAAE,uBAAuB;UAClCC,YAAY,EAAE,eAAe;UAC7BC,UAAU,EAAE,gBAAgB;UAC5BC,QAAQ,EAAE,aAAa;UACvBC,WAAW,EAAE,EAAE;UACfC,GAAG,EAAE;QACP;MACF,CAAC,EACD;QACEd,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE,oBAAoB;QAC3BC,SAAS,EAAE,IAAI;QACfC,UAAU,EAAE,sBAAsB;QAClCC,UAAU,EAAE,sBAAsB;QAClCC,OAAO,EAAE;UACPC,UAAU,EAAE,OAAO;UACnBE,SAAS,EAAE,UAAU;UACrBC,SAAS,EAAE,gBAAgB;UAC3BC,YAAY,EAAE,eAAe;UAC7BC,UAAU,EAAE,aAAa;UACzBC,QAAQ,EAAE,WAAW;UACrBC,WAAW,EAAE,EAAE;UACfC,GAAG,EAAE;QACP;MACF,CAAC,CACF;;MAED;MACA,IAAIC,cAAc,GAAG,CAAC,GAAGhB,UAAU,CAAC;MAEpC,IAAId,UAAU,CAAC+B,IAAI,CAAC,CAAC,EAAE;QACrB,MAAMC,WAAW,GAAGhC,UAAU,CAACiC,WAAW,CAAC,CAAC,CAACF,IAAI,CAAC,CAAC;QACnDD,cAAc,GAAGA,cAAc,CAACI,MAAM,CAACC,KAAK,IAC1CA,KAAK,CAACf,OAAO,CAACI,SAAS,CAACS,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,WAAW,CAAC,IAC3DG,KAAK,CAACnB,KAAK,CAACiB,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,WAAW,CAAC,IAC/CG,KAAK,CAACf,OAAO,CAACC,UAAU,CAACY,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,WAAW,CAAC,IAC5DG,KAAK,CAACf,OAAO,CAACG,SAAS,CAACU,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,WAAW,CAAC,IAC1DG,KAAK,CAACf,OAAO,CAACM,UAAU,IAAIS,KAAK,CAACf,OAAO,CAACM,UAAU,CAACO,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,WAAW,CAC1F,CAAC;MACH;MAEA,IAAI9B,cAAc,EAAE;QAClB4B,cAAc,GAAGA,cAAc,CAACI,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACf,OAAO,CAACO,QAAQ,KAAKzB,cAAc,CAAC;MAC5F;MAEA,IAAIE,YAAY,EAAE;QAChB,MAAMiC,QAAQ,GAAGjC,YAAY,KAAK,QAAQ;QAC1C0B,cAAc,GAAGA,cAAc,CAACI,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAClB,SAAS,KAAKoB,QAAQ,CAAC;MAC/E;MAEA1B,aAAa,CAACmB,cAAc,CAACQ,MAAM,CAAC;;MAEpC;MACA,MAAMC,UAAU,GAAG,CAACjC,WAAW,GAAG,CAAC,IAAIE,YAAY;MACnD,MAAMgC,eAAe,GAAGV,cAAc,CAACW,KAAK,CAACF,UAAU,EAAEA,UAAU,GAAG/B,YAAY,CAAC;MAEnFrB,SAAS,CAACqD,eAAe,CAAC;IAE5B,CAAC,CAAC,OAAOE,GAAQ,EAAE;MACjBnD,QAAQ,CAACmD,GAAG,CAACC,OAAO,IAAI,+BAA+B,CAAC;IAC1D,CAAC,SAAS;MACRtD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMuD,cAAc,GAAGA,CAAA,KAAM;IAC3B/C,eAAe,CAAC,IAAI,CAAC;IACrBF,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMkD,eAAe,GAAIV,KAAmB,IAAK;IAC/CtC,eAAe,CAACsC,KAAK,CAAC;IACtBxC,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMmD,iBAAiB,GAAG,MAAOX,KAAmB,IAAK;IACvD,IAAI;MACF5C,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA;;MAEA;MACAA,UAAU,CAAC,qBAAqB0C,KAAK,CAACf,OAAO,CAACI,SAAS,uBAAuB,CAAC;MAC/EX,UAAU,CAAC,CAAC;IAEd,CAAC,CAAC,OAAO6B,GAAQ,EAAE;MACjBnD,QAAQ,CAACmD,GAAG,CAACC,OAAO,IAAI,gCAAgC,CAAC;IAC3D;EACF,CAAC;EAED,MAAMI,kBAAkB,GAAG,MAAOZ,KAAmB,IAAK;IACxD,IAAI;MACF5C,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA;;MAEA;MACA,MAAMuD,MAAM,GAAGb,KAAK,CAAClB,SAAS,GAAG,aAAa,GAAG,WAAW;MAC5DxB,UAAU,CAAC,qBAAqB0C,KAAK,CAACf,OAAO,CAACI,SAAS,aAAawB,MAAM,EAAE,CAAC;MAC7EnC,UAAU,CAAC,CAAC;IAEd,CAAC,CAAC,OAAO6B,GAAQ,EAAE;MACjBnD,QAAQ,CAACmD,GAAG,CAACC,OAAO,IAAI,+BAA+B,CAAC;IAC1D;EACF,CAAC;EAED,MAAMM,eAAe,GAAG,MAAOC,SAAuB,IAAK;IACzD,IAAI;MACFnD,eAAe,CAAC,IAAI,CAAC;MACrBR,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIG,YAAY,EAAE;QAChB;QACA;QACAH,UAAU,CAAC,qBAAqByD,SAAS,CAAC9B,OAAO,CAACC,UAAU,IAAI6B,SAAS,CAAC9B,OAAO,CAACG,SAAS,mBAAmB,CAAC;MACjH,CAAC,MAAM;QACL;QACA;QACA9B,UAAU,CAAC,qBAAqByD,SAAS,CAAC9B,OAAO,CAACC,UAAU,IAAI6B,SAAS,CAAC9B,OAAO,CAACG,SAAS,mBAAmB,CAAC;MACjH;MAEAV,UAAU,CAAC,CAAC;IAEd,CAAC,CAAC,OAAO6B,GAAQ,EAAE;MACjBnD,QAAQ,CAACmD,GAAG,CAACC,OAAO,IAAI,8BAA8B,CAAC;MACvD,MAAMD,GAAG,CAAC,CAAC;IACb,CAAC,SAAS;MACR3C,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMoD,gBAAgB,GAAIC,IAAY,IAAK;IACzC7C,cAAc,CAAC6C,IAAI,CAAC;EACtB,CAAC;EAED,MAAMC,wBAAwB,GAAI7C,YAAoB,IAAK;IACzDC,eAAe,CAACD,YAAY,CAAC;IAC7BD,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;EAED,MAAM+C,aAAa,GAAGA,CAAA,KAAM;IAC1B/D,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;EAClB,CAAC;EAED,IAAI,CAACR,WAAW,CAAC2B,eAAe,EAAE;IAChC,oBACE/B,OAAA;MAAK0E,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE,OAAO;QAClBC,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACAlF,OAAA,CAACR,KAAK;QAAC2F,IAAI,EAAE,EAAG;QAACT,KAAK,EAAE;UAAEU,YAAY,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAI;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClEzF,OAAA;QAAI0E,KAAK,EAAE;UAAEgB,MAAM,EAAE,YAAY;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAM,CAAE;QAAAV,QAAA,EAAC;MAE5E;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLzF,OAAA;QAAG0E,KAAK,EAAE;UAAEgB,MAAM,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAT,QAAA,EAAC;MAE3C;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJzF,OAAA;QAAK0E,KAAK,EAAE;UACVmB,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAE3F,WAAW,CAAC4F,qBAAqB,CAAC,CAAC;UAC/CC,YAAY,EAAE,KAAK;UACnBhB,KAAK,EAAE,OAAO;UACdU,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE;QACd,CAAE;QAAAV,QAAA,GAAC,gBACa,EAAC9E,WAAW,CAAC8F,sBAAsB,CAAC,CAAC;MAAA;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIlF,OAAO,EAAE;IACX,oBACEP,OAAA;MAAK0E,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE;MACb,CAAE;MAAAG,QAAA,eACAlF,OAAA;QAAK0E,KAAK,EAAE;UACVyB,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,mBAAmB;UAC3BC,SAAS,EAAE,mBAAmB;UAC9BL,YAAY,EAAE,KAAK;UACnBM,SAAS,EAAE;QACb;MAAE;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEV;EAEA,IAAIhF,KAAK,EAAE;IACT,oBACET,OAAA;MAAK0E,KAAK,EAAE;QACVoB,OAAO,EAAE,MAAM;QACfd,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACAlF,OAAA;QAAAkF,QAAA,EAAI;MAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdzF,OAAA;QAAAkF,QAAA,EAAIzE;MAAK;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdzF,OAAA;QACEwG,OAAO,EAAExE,UAAW;QACpB0C,KAAK,EAAE;UACLoB,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAE,SAAS;UACrBd,KAAK,EAAE,OAAO;UACdoB,MAAM,EAAE,MAAM;UACdJ,YAAY,EAAE,KAAK;UACnBQ,MAAM,EAAE;QACV,CAAE;QAAAvB,QAAA,EACH;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACEzF,OAAA;IAAK0E,KAAK,EAAE;MAAEgC,QAAQ,EAAE,QAAQ;MAAEhB,MAAM,EAAE;IAAS,CAAE;IAAAR,QAAA,gBAEnDlF,OAAA;MAAK0E,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfG,cAAc,EAAE,eAAe;QAC/BD,UAAU,EAAE,QAAQ;QACpBO,YAAY,EAAE;MAChB,CAAE;MAAAF,QAAA,gBACAlF,OAAA;QAAAkF,QAAA,gBACElF,OAAA;UAAI0E,KAAK,EAAE;YACTgB,MAAM,EAAE,YAAY;YACpBC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjBX,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,EAAC;QAEH;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLzF,OAAA;UAAG0E,KAAK,EAAE;YACRgB,MAAM,EAAE,CAAC;YACTT,KAAK,EAAE,SAAS;YAChBU,QAAQ,EAAE;UACZ,CAAE;UAAAT,QAAA,EAAC;QAEH;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENzF,OAAA;QACEwG,OAAO,EAAEzC,cAAe;QACxBW,KAAK,EAAE;UACLC,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpB8B,GAAG,EAAE,QAAQ;UACbb,OAAO,EAAE,gBAAgB;UACzBC,UAAU,EAAE,SAAS;UACrBd,KAAK,EAAE,OAAO;UACdoB,MAAM,EAAE,MAAM;UACdJ,YAAY,EAAE,KAAK;UACnBN,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE,KAAK;UACjBa,MAAM,EAAE,SAAS;UACjBG,UAAU,EAAE;QACd,CAAE;QACFC,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACrC,KAAK,CAACqB,UAAU,GAAG,SAAU;QAClEiB,YAAY,EAAGF,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACrC,KAAK,CAACqB,UAAU,GAAG,SAAU;QAAAb,QAAA,gBAElElF,OAAA,CAACV,QAAQ;UAAC6F,IAAI,EAAE;QAAG;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,aAExB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNzF,OAAA;MAAK0E,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfgC,GAAG,EAAE,MAAM;QACXvB,YAAY,EAAE,MAAM;QACpB6B,QAAQ,EAAE;MACZ,CAAE;MAAA/B,QAAA,gBACAlF,OAAA;QAAK0E,KAAK,EAAE;UAAEwC,IAAI,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAQ,CAAE;QAAAjC,QAAA,eACzClF,OAAA;UAAK0E,KAAK,EAAE;YAAE5B,QAAQ,EAAE;UAAW,CAAE;UAAAoC,QAAA,gBACnClF,OAAA,CAACT,MAAM;YAAC4F,IAAI,EAAE,EAAG;YAACT,KAAK,EAAE;cACvB5B,QAAQ,EAAE,UAAU;cACpBsE,IAAI,EAAE,SAAS;cACfC,GAAG,EAAE,KAAK;cACVC,SAAS,EAAE,kBAAkB;cAC7BrC,KAAK,EAAE;YACT;UAAE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACLzF,OAAA;YACEuH,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,mCAAmC;YAC/CC,KAAK,EAAEtG,UAAW;YAClBuG,QAAQ,EAAGZ,CAAC,IAAK1F,aAAa,CAAC0F,CAAC,CAACa,MAAM,CAACF,KAAK,CAAE;YAC/C/C,KAAK,EAAE;cACLyB,KAAK,EAAE,MAAM;cACbL,OAAO,EAAE,gCAAgC;cACzCO,MAAM,EAAE,mBAAmB;cAC3BJ,YAAY,EAAE,KAAK;cACnBN,QAAQ,EAAE;YACZ;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzF,OAAA;QACEyH,KAAK,EAAEpG,cAAe;QACtBqG,QAAQ,EAAGZ,CAAC,IAAKxF,iBAAiB,CAACwF,CAAC,CAACa,MAAM,CAACF,KAAK,CAAE;QACnD/C,KAAK,EAAE;UACLoB,OAAO,EAAE,SAAS;UAClBO,MAAM,EAAE,mBAAmB;UAC3BJ,YAAY,EAAE,KAAK;UACnBN,QAAQ,EAAE,UAAU;UACpBwB,QAAQ,EAAE;QACZ,CAAE;QAAAjC,QAAA,gBAEFlF,OAAA;UAAQyH,KAAK,EAAC,EAAE;UAAAvC,QAAA,EAAC;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACvCzF,OAAA;UAAQyH,KAAK,EAAC,aAAa;UAAAvC,QAAA,EAAC;QAAW;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAChDzF,OAAA;UAAQyH,KAAK,EAAC,WAAW;UAAAvC,QAAA,EAAC;QAAS;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eAETzF,OAAA;QACEyH,KAAK,EAAElG,YAAa;QACpBmG,QAAQ,EAAGZ,CAAC,IAAKtF,eAAe,CAACsF,CAAC,CAACa,MAAM,CAACF,KAAK,CAAE;QACjD/C,KAAK,EAAE;UACLoB,OAAO,EAAE,SAAS;UAClBO,MAAM,EAAE,mBAAmB;UAC3BJ,YAAY,EAAE,KAAK;UACnBN,QAAQ,EAAE,UAAU;UACpBwB,QAAQ,EAAE;QACZ,CAAE;QAAAjC,QAAA,gBAEFlF,OAAA;UAAQyH,KAAK,EAAC,EAAE;UAAAvC,QAAA,EAAC;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACpCzF,OAAA;UAAQyH,KAAK,EAAC,QAAQ;UAAAvC,QAAA,EAAC;QAAM;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACtCzF,OAAA;UAAQyH,KAAK,EAAC,UAAU;UAAAvC,QAAA,EAAC;QAAQ;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGL,CAAChF,KAAK,IAAIE,OAAO,kBAChBX,OAAA;MAAK0E,KAAK,EAAE;QAAEU,YAAY,EAAE;MAAS,CAAE;MAAAF,QAAA,GACpCzE,KAAK,iBACJT,OAAA;QAAK0E,KAAK,EAAE;UACVoB,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,SAAS;UACrBM,MAAM,EAAE,mBAAmB;UAC3BJ,YAAY,EAAE,KAAK;UACnBhB,KAAK,EAAE,SAAS;UAChBN,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE;QAClB,CAAE;QAAAI,QAAA,gBACAlF,OAAA;UAAK0E,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAE8B,GAAG,EAAE;UAAS,CAAE;UAAAzB,QAAA,gBACnElF,OAAA,CAACP,aAAa;YAAC0F,IAAI,EAAE;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAC1BhF,KAAK;QAAA;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNzF,OAAA;UACEwG,OAAO,EAAE/B,aAAc;UACvBC,KAAK,EAAE;YACLqB,UAAU,EAAE,aAAa;YACzBM,MAAM,EAAE,MAAM;YACdpB,KAAK,EAAE,SAAS;YAChBwB,MAAM,EAAE,SAAS;YACjBX,OAAO,EAAE;UACX,CAAE;UAAAZ,QAAA,EACH;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAEA9E,OAAO,iBACNX,OAAA;QAAK0E,KAAK,EAAE;UACVoB,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,SAAS;UACrBM,MAAM,EAAE,mBAAmB;UAC3BJ,YAAY,EAAE,KAAK;UACnBhB,KAAK,EAAE,SAAS;UAChBN,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE;QAClB,CAAE;QAAAI,QAAA,gBACAlF,OAAA;UAAK0E,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAE8B,GAAG,EAAE;UAAS,CAAE;UAAAzB,QAAA,gBACnElF,OAAA,CAACN,WAAW;YAACyF,IAAI,EAAE;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACxB9E,OAAO;QAAA;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNzF,OAAA;UACEwG,OAAO,EAAE/B,aAAc;UACvBC,KAAK,EAAE;YACLqB,UAAU,EAAE,aAAa;YACzBM,MAAM,EAAE,MAAM;YACdpB,KAAK,EAAE,SAAS;YAChBwB,MAAM,EAAE,SAAS;YACjBX,OAAO,EAAE;UACX,CAAE;UAAAZ,QAAA,EACH;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAGDzF,OAAA,CAACH,gBAAgB;MACfQ,MAAM,EAAEA,MAAO;MACfE,OAAO,EAAEA,OAAQ;MACjBqH,MAAM,EAAE5D,eAAgB;MACxB6D,QAAQ,EAAE5D,iBAAkB;MAC5B6D,cAAc,EAAE5D,kBAAmB;MACnCzC,WAAW,EAAEA,WAAY;MACzBsG,UAAU,EAAEC,IAAI,CAACC,IAAI,CAACpG,UAAU,GAAGF,YAAY,CAAE;MACjDA,YAAY,EAAEA,YAAa;MAC3BE,UAAU,EAAEA,UAAW;MACvBqG,YAAY,EAAE5D,gBAAiB;MAC/B6D,oBAAoB,EAAE3D;IAAyB;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC,eAGFzF,OAAA,CAACF,iBAAiB;MAChBsI,MAAM,EAAEvH,SAAU;MAClBwH,OAAO,EAAEA,CAAA,KAAMvH,YAAY,CAAC,KAAK,CAAE;MACnCwH,MAAM,EAAElE,eAAgB;MACxBd,KAAK,EAAEvC,YAAa;MACpBR,OAAO,EAAEU;IAAa;MAAAqE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACvF,EAAA,CAvfID,eAAyB;EAAA,QACZN,YAAY,EACTC,cAAc;AAAA;AAAA2I,EAAA,GAF9BtI,eAAyB;AAyf/B,eAAeA,eAAe;AAAC,IAAAsI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}