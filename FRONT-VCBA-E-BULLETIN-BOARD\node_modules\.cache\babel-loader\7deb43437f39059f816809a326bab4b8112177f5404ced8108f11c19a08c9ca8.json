{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\admin\\\\CategoryManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { FolderTree, Plus, AlertTriangle, CheckCircle } from 'lucide-react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\nimport CategoryList from '../../components/admin/CategoryList';\nimport CategoryModal from '../../components/admin/CategoryModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CategoryManagement = () => {\n  _s();\n  const {\n    user\n  } = useAdminAuth();\n  const permissions = usePermissions(user);\n\n  // State management\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n\n  // Modal state\n  const [showModal, setShowModal] = useState(false);\n  const [modalMode, setModalMode] = useState('add_category');\n  const [editingCategory, setEditingCategory] = useState(null);\n  const [editingSubcategory, setEditingSubcategory] = useState(null);\n  const [parentCategory, setParentCategory] = useState(null);\n  const [modalLoading, setModalLoading] = useState(false);\n  useEffect(() => {\n    // Check if user has permission to manage categories\n    if (!permissions.canManageCategories) {\n      setError('You do not have permission to manage categories');\n      setLoading(false);\n      return;\n    }\n    loadCategories();\n  }, [permissions.canManageCategories]);\n  const loadCategories = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // TODO: Implement API call to fetch categories\n      // const response = await categoryService.getCategoriesWithSubcategories();\n      // setCategories(response.data.categories);\n\n      // Mock data with proper structure\n      const mockCategories = [{\n        category_id: 1,\n        name: 'Academic',\n        description: 'Academic-related announcements and events',\n        color_code: '#3b82f6',\n        is_active: true,\n        subcategories: [{\n          subcategory_id: 1,\n          name: 'Exams',\n          description: 'Examination schedules and updates',\n          color_code: '#ef4444',\n          display_order: 1,\n          is_active: true\n        }, {\n          subcategory_id: 2,\n          name: 'Assignments',\n          description: 'Assignment deadlines and submissions',\n          color_code: '#f59e0b',\n          display_order: 2,\n          is_active: true\n        }, {\n          subcategory_id: 3,\n          name: 'Class Schedules',\n          description: 'Class timing and schedule changes',\n          color_code: '#06b6d4',\n          display_order: 3,\n          is_active: true\n        }]\n      }, {\n        category_id: 2,\n        name: 'Events',\n        description: 'School events and activities',\n        color_code: '#10b981',\n        is_active: true,\n        subcategories: [{\n          subcategory_id: 4,\n          name: 'Sports',\n          description: 'Sports events and competitions',\n          color_code: '#8b5cf6',\n          display_order: 1,\n          is_active: true\n        }, {\n          subcategory_id: 5,\n          name: 'Cultural',\n          description: 'Cultural events and celebrations',\n          color_code: '#ec4899',\n          display_order: 2,\n          is_active: true\n        }]\n      }, {\n        category_id: 3,\n        name: 'Administrative',\n        description: 'Administrative notices and updates',\n        color_code: '#f97316',\n        is_active: true,\n        subcategories: [{\n          subcategory_id: 6,\n          name: 'Policies',\n          description: 'School policies and regulations',\n          color_code: '#6366f1',\n          display_order: 1,\n          is_active: true\n        }]\n      }, {\n        category_id: 4,\n        name: 'Emergency',\n        description: 'Emergency announcements and alerts',\n        color_code: '#dc2626',\n        is_active: true,\n        subcategories: []\n      }];\n      setCategories(mockCategories);\n    } catch (err) {\n      setError(err.message || 'Failed to load categories');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handler functions\n  const handleAddCategory = () => {\n    setModalMode('add_category');\n    setEditingCategory(null);\n    setEditingSubcategory(null);\n    setParentCategory(null);\n    setShowModal(true);\n  };\n  const handleEditCategory = category => {\n    setModalMode('edit_category');\n    setEditingCategory(category);\n    setEditingSubcategory(null);\n    setParentCategory(null);\n    setShowModal(true);\n  };\n  const handleDeleteCategory = async category => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await categoryService.deleteCategory(category.category_id!);\n\n      setSuccess(`Category \"${category.name}\" has been deleted successfully`);\n      loadCategories();\n    } catch (err) {\n      setError(err.message || 'Failed to delete category');\n    }\n  };\n  const handleToggleCategoryStatus = async category => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await categoryService.toggleCategoryStatus(category.category_id!, !category.is_active);\n\n      const action = category.is_active ? 'deactivated' : 'activated';\n      setSuccess(`Category \"${category.name}\" has been ${action} successfully`);\n      loadCategories();\n    } catch (err) {\n      setError(err.message || 'Failed to update category status');\n    }\n  };\n  const handleAddSubcategory = category => {\n    setModalMode('add_subcategory');\n    setEditingCategory(null);\n    setEditingSubcategory(null);\n    setParentCategory(category);\n    setShowModal(true);\n  };\n  const handleEditSubcategory = (category, subcategory) => {\n    setModalMode('edit_subcategory');\n    setEditingCategory(null);\n    setEditingSubcategory(subcategory);\n    setParentCategory(category);\n    setShowModal(true);\n  };\n  const handleDeleteSubcategory = async (category, subcategory) => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await categoryService.deleteSubcategory(category.category_id!, subcategory.subcategory_id!);\n\n      setSuccess(`Subcategory \"${subcategory.name}\" has been deleted successfully`);\n      loadCategories();\n    } catch (err) {\n      setError(err.message || 'Failed to delete subcategory');\n    }\n  };\n  const handleToggleSubcategoryStatus = async (category, subcategory) => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await categoryService.toggleSubcategoryStatus(category.category_id!, subcategory.subcategory_id!, !subcategory.is_active);\n\n      const action = subcategory.is_active ? 'deactivated' : 'activated';\n      setSuccess(`Subcategory \"${subcategory.name}\" has been ${action} successfully`);\n      loadCategories();\n    } catch (err) {\n      setError(err.message || 'Failed to update subcategory status');\n    }\n  };\n  const handleSave = async (data, parentCat) => {\n    try {\n      setModalLoading(true);\n      setError(null);\n      setSuccess(null);\n      if (modalMode === 'add_category') {\n        // TODO: Implement actual API call\n        // await categoryService.createCategory(data as Category);\n        setSuccess(`Category \"${data.name}\" has been created successfully`);\n      } else if (modalMode === 'edit_category') {\n        // TODO: Implement actual API call\n        // await categoryService.updateCategory(editingCategory!.category_id!, data as Category);\n        setSuccess(`Category \"${data.name}\" has been updated successfully`);\n      } else if (modalMode === 'add_subcategory') {\n        // TODO: Implement actual API call\n        // await categoryService.createSubcategory(parentCat!.category_id!, data as Subcategory);\n        setSuccess(`Subcategory \"${data.name}\" has been created successfully`);\n      } else if (modalMode === 'edit_subcategory') {\n        // TODO: Implement actual API call\n        // await categoryService.updateSubcategory(parentCat!.category_id!, editingSubcategory!.subcategory_id!, data as Subcategory);\n        setSuccess(`Subcategory \"${data.name}\" has been updated successfully`);\n      }\n      loadCategories();\n    } catch (err) {\n      setError(err.message || 'Failed to save changes');\n      throw err; // Re-throw to prevent modal from closing\n    } finally {\n      setModalLoading(false);\n    }\n  };\n  const clearMessages = () => {\n    setError(null);\n    setSuccess(null);\n  };\n  if (!permissions.canManageCategories) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      },\n      children: [/*#__PURE__*/_jsxDEV(FolderTree, {\n        size: 64,\n        style: {\n          marginBottom: '1rem',\n          opacity: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          margin: '0 0 0.5rem',\n          fontSize: '1.5rem',\n          fontWeight: '600'\n        },\n        children: \"Access Denied\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: 0,\n          fontSize: '1rem'\n        },\n        children: \"You do not have permission to manage categories and subcategories.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        },\n        children: [\"Current Role: \", permissions.getPositionDisplayName()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '40px',\n          height: '40px',\n          border: '4px solid #f3f4f6',\n          borderTop: '4px solid #3b82f6',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 333,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '2rem',\n        textAlign: 'center',\n        color: '#ef4444'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Error\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: loadCategories,\n        style: {\n          padding: '0.5rem 1rem',\n          background: '#3b82f6',\n          color: 'white',\n          border: 'none',\n          borderRadius: '6px',\n          cursor: 'pointer'\n        },\n        children: \"Retry\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 353,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      maxWidth: '1400px',\n      margin: '0 auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            margin: '0 0 0.5rem',\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          },\n          children: \"Category Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          },\n          children: \"Manage categories and subcategories for announcements and events\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleAddCategory,\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          padding: '0.75rem 1.5rem',\n          background: '#3b82f6',\n          color: 'white',\n          border: 'none',\n          borderRadius: '8px',\n          fontSize: '0.875rem',\n          fontWeight: '600',\n          cursor: 'pointer',\n          transition: 'background-color 0.2s'\n        },\n        onMouseEnter: e => e.currentTarget.style.background = '#2563eb',\n        onMouseLeave: e => e.currentTarget.style.background = '#3b82f6',\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 11\n        }, this), \"Add Category\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 380,\n      columnNumber: 7\n    }, this), (error || success) && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '1.5rem'\n      },\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '1rem',\n          background: '#fef2f2',\n          border: '1px solid #fecaca',\n          borderRadius: '8px',\n          color: '#dc2626',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 17\n          }, this), error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: clearMessages,\n          style: {\n            background: 'transparent',\n            border: 'none',\n            color: '#dc2626',\n            cursor: 'pointer',\n            padding: '0.25rem'\n          },\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 432,\n        columnNumber: 13\n      }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '1rem',\n          background: '#f0fdf4',\n          border: '1px solid #bbf7d0',\n          borderRadius: '8px',\n          color: '#166534',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 17\n          }, this), success]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: clearMessages,\n          style: {\n            background: 'transparent',\n            border: 'none',\n            color: '#166534',\n            cursor: 'pointer',\n            padding: '0.25rem'\n          },\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 476,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 430,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(CategoryList, {\n      categories: categories,\n      loading: loading,\n      onEditCategory: handleEditCategory,\n      onDeleteCategory: handleDeleteCategory,\n      onToggleCategoryStatus: handleToggleCategoryStatus,\n      onAddSubcategory: handleAddSubcategory,\n      onEditSubcategory: handleEditSubcategory,\n      onDeleteSubcategory: handleDeleteSubcategory,\n      onToggleSubcategoryStatus: handleToggleSubcategoryStatus\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 494,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CategoryModal, {\n      isOpen: showModal,\n      onClose: () => setShowModal(false),\n      onSave: handleSave,\n      category: editingCategory,\n      subcategory: editingSubcategory,\n      parentCategory: parentCategory,\n      mode: modalMode,\n      loading: modalLoading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 507,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 378,\n    columnNumber: 5\n  }, this);\n};\n_s(CategoryManagement, \"Fi2eW2xY3Tqt2uCjsGG8TTkc8Lc=\", false, function () {\n  return [useAdminAuth, usePermissions];\n});\n_c = CategoryManagement;\nexport default CategoryManagement;\nvar _c;\n$RefreshReg$(_c, \"CategoryManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FolderTree", "Plus", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CheckCircle", "useAdminAuth", "usePermissions", "CategoryList", "CategoryModal", "jsxDEV", "_jsxDEV", "CategoryManagement", "_s", "user", "permissions", "categories", "setCategories", "loading", "setLoading", "error", "setError", "success", "setSuccess", "showModal", "setShowModal", "modalMode", "setModalMode", "editingCategory", "setEditingCategory", "editingSubcategory", "setEditingSubcategory", "parentCategory", "setParentCategory", "modalLoading", "setModalLoading", "canManageCategories", "loadCategories", "mockCategories", "category_id", "name", "description", "color_code", "is_active", "subcategories", "subcategory_id", "display_order", "err", "message", "handleAddCategory", "handleEditCategory", "category", "handleDeleteCategory", "handleToggleCategoryStatus", "action", "handleAddSubcategory", "handleEditSubcategory", "subcategory", "handleDeleteSubcategory", "handleToggleSubcategoryStatus", "handleSave", "data", "parentCat", "clearMessages", "style", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "textAlign", "color", "children", "size", "marginBottom", "opacity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "margin", "fontSize", "fontWeight", "marginTop", "padding", "background", "getPositionBadgeColor", "borderRadius", "getPositionDisplayName", "width", "height", "border", "borderTop", "animation", "onClick", "cursor", "max<PERSON><PERSON><PERSON>", "gap", "transition", "onMouseEnter", "e", "currentTarget", "onMouseLeave", "onEditCategory", "onDeleteCategory", "onToggleCategoryStatus", "onAddSubcategory", "onEditSubcategory", "onDeleteSubcategory", "onToggleSubcategoryStatus", "isOpen", "onClose", "onSave", "mode", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/admin/CategoryManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { FolderTree, Plus, AlertTriangle, CheckCircle } from 'lucide-react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\nimport { categoryService } from '../../services/categoryService';\nimport CategoryList from '../../components/admin/CategoryList';\nimport CategoryModal from '../../components/admin/CategoryModal';\n\ninterface Category {\n  category_id: number;\n  name: string;\n  description?: string;\n  color_code: string;\n  is_active: boolean;\n  subcategories?: Subcategory[];\n}\n\ninterface Subcategory {\n  subcategory_id: number;\n  name: string;\n  description?: string;\n  color_code: string;\n  display_order: number;\n  is_active: boolean;\n}\n\nconst CategoryManagement: React.FC = () => {\n  const { user } = useAdminAuth();\n  const permissions = usePermissions(user);\n\n  // State management\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  // Modal state\n  const [showModal, setShowModal] = useState(false);\n  const [modalMode, setModalMode] = useState<'add_category' | 'edit_category' | 'add_subcategory' | 'edit_subcategory'>('add_category');\n  const [editingCategory, setEditingCategory] = useState<Category | null>(null);\n  const [editingSubcategory, setEditingSubcategory] = useState<Subcategory | null>(null);\n  const [parentCategory, setParentCategory] = useState<Category | null>(null);\n  const [modalLoading, setModalLoading] = useState(false);\n\n  useEffect(() => {\n    // Check if user has permission to manage categories\n    if (!permissions.canManageCategories) {\n      setError('You do not have permission to manage categories');\n      setLoading(false);\n      return;\n    }\n\n    loadCategories();\n  }, [permissions.canManageCategories]);\n\n  const loadCategories = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      // TODO: Implement API call to fetch categories\n      // const response = await categoryService.getCategoriesWithSubcategories();\n      // setCategories(response.data.categories);\n      \n      // Mock data with proper structure\n      const mockCategories: Category[] = [\n        {\n          category_id: 1,\n          name: 'Academic',\n          description: 'Academic-related announcements and events',\n          color_code: '#3b82f6',\n          is_active: true,\n          subcategories: [\n            {\n              subcategory_id: 1,\n              name: 'Exams',\n              description: 'Examination schedules and updates',\n              color_code: '#ef4444',\n              display_order: 1,\n              is_active: true\n            },\n            {\n              subcategory_id: 2,\n              name: 'Assignments',\n              description: 'Assignment deadlines and submissions',\n              color_code: '#f59e0b',\n              display_order: 2,\n              is_active: true\n            },\n            {\n              subcategory_id: 3,\n              name: 'Class Schedules',\n              description: 'Class timing and schedule changes',\n              color_code: '#06b6d4',\n              display_order: 3,\n              is_active: true\n            }\n          ]\n        },\n        {\n          category_id: 2,\n          name: 'Events',\n          description: 'School events and activities',\n          color_code: '#10b981',\n          is_active: true,\n          subcategories: [\n            {\n              subcategory_id: 4,\n              name: 'Sports',\n              description: 'Sports events and competitions',\n              color_code: '#8b5cf6',\n              display_order: 1,\n              is_active: true\n            },\n            {\n              subcategory_id: 5,\n              name: 'Cultural',\n              description: 'Cultural events and celebrations',\n              color_code: '#ec4899',\n              display_order: 2,\n              is_active: true\n            }\n          ]\n        },\n        {\n          category_id: 3,\n          name: 'Administrative',\n          description: 'Administrative notices and updates',\n          color_code: '#f97316',\n          is_active: true,\n          subcategories: [\n            {\n              subcategory_id: 6,\n              name: 'Policies',\n              description: 'School policies and regulations',\n              color_code: '#6366f1',\n              display_order: 1,\n              is_active: true\n            }\n          ]\n        },\n        {\n          category_id: 4,\n          name: 'Emergency',\n          description: 'Emergency announcements and alerts',\n          color_code: '#dc2626',\n          is_active: true,\n          subcategories: []\n        }\n      ];\n\n      setCategories(mockCategories);\n    } catch (err: any) {\n      setError(err.message || 'Failed to load categories');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handler functions\n  const handleAddCategory = () => {\n    setModalMode('add_category');\n    setEditingCategory(null);\n    setEditingSubcategory(null);\n    setParentCategory(null);\n    setShowModal(true);\n  };\n\n  const handleEditCategory = (category: Category) => {\n    setModalMode('edit_category');\n    setEditingCategory(category);\n    setEditingSubcategory(null);\n    setParentCategory(null);\n    setShowModal(true);\n  };\n\n  const handleDeleteCategory = async (category: Category) => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await categoryService.deleteCategory(category.category_id!);\n\n      setSuccess(`Category \"${category.name}\" has been deleted successfully`);\n      loadCategories();\n\n    } catch (err: any) {\n      setError(err.message || 'Failed to delete category');\n    }\n  };\n\n  const handleToggleCategoryStatus = async (category: Category) => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await categoryService.toggleCategoryStatus(category.category_id!, !category.is_active);\n\n      const action = category.is_active ? 'deactivated' : 'activated';\n      setSuccess(`Category \"${category.name}\" has been ${action} successfully`);\n      loadCategories();\n\n    } catch (err: any) {\n      setError(err.message || 'Failed to update category status');\n    }\n  };\n\n  const handleAddSubcategory = (category: Category) => {\n    setModalMode('add_subcategory');\n    setEditingCategory(null);\n    setEditingSubcategory(null);\n    setParentCategory(category);\n    setShowModal(true);\n  };\n\n  const handleEditSubcategory = (category: Category, subcategory: Subcategory) => {\n    setModalMode('edit_subcategory');\n    setEditingCategory(null);\n    setEditingSubcategory(subcategory);\n    setParentCategory(category);\n    setShowModal(true);\n  };\n\n  const handleDeleteSubcategory = async (category: Category, subcategory: Subcategory) => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await categoryService.deleteSubcategory(category.category_id!, subcategory.subcategory_id!);\n\n      setSuccess(`Subcategory \"${subcategory.name}\" has been deleted successfully`);\n      loadCategories();\n\n    } catch (err: any) {\n      setError(err.message || 'Failed to delete subcategory');\n    }\n  };\n\n  const handleToggleSubcategoryStatus = async (category: Category, subcategory: Subcategory) => {\n    try {\n      setError(null);\n      setSuccess(null);\n\n      // TODO: Implement actual API call\n      // await categoryService.toggleSubcategoryStatus(category.category_id!, subcategory.subcategory_id!, !subcategory.is_active);\n\n      const action = subcategory.is_active ? 'deactivated' : 'activated';\n      setSuccess(`Subcategory \"${subcategory.name}\" has been ${action} successfully`);\n      loadCategories();\n\n    } catch (err: any) {\n      setError(err.message || 'Failed to update subcategory status');\n    }\n  };\n\n  const handleSave = async (data: Category | Subcategory, parentCat?: Category) => {\n    try {\n      setModalLoading(true);\n      setError(null);\n      setSuccess(null);\n\n      if (modalMode === 'add_category') {\n        // TODO: Implement actual API call\n        // await categoryService.createCategory(data as Category);\n        setSuccess(`Category \"${data.name}\" has been created successfully`);\n      } else if (modalMode === 'edit_category') {\n        // TODO: Implement actual API call\n        // await categoryService.updateCategory(editingCategory!.category_id!, data as Category);\n        setSuccess(`Category \"${data.name}\" has been updated successfully`);\n      } else if (modalMode === 'add_subcategory') {\n        // TODO: Implement actual API call\n        // await categoryService.createSubcategory(parentCat!.category_id!, data as Subcategory);\n        setSuccess(`Subcategory \"${data.name}\" has been created successfully`);\n      } else if (modalMode === 'edit_subcategory') {\n        // TODO: Implement actual API call\n        // await categoryService.updateSubcategory(parentCat!.category_id!, editingSubcategory!.subcategory_id!, data as Subcategory);\n        setSuccess(`Subcategory \"${data.name}\" has been updated successfully`);\n      }\n\n      loadCategories();\n\n    } catch (err: any) {\n      setError(err.message || 'Failed to save changes');\n      throw err; // Re-throw to prevent modal from closing\n    } finally {\n      setModalLoading(false);\n    }\n  };\n\n  const clearMessages = () => {\n    setError(null);\n    setSuccess(null);\n  };\n\n  if (!permissions.canManageCategories) {\n    return (\n      <div style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      }}>\n        <FolderTree size={64} style={{ marginBottom: '1rem', opacity: 0.5 }} />\n        <h2 style={{ margin: '0 0 0.5rem', fontSize: '1.5rem', fontWeight: '600' }}>\n          Access Denied\n        </h2>\n        <p style={{ margin: 0, fontSize: '1rem' }}>\n          You do not have permission to manage categories and subcategories.\n        </p>\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        }}>\n          Current Role: {permissions.getPositionDisplayName()}\n        </div>\n      </div>\n    );\n  }\n\n  if (loading) {\n    return (\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px'\n      }}>\n        <div style={{\n          width: '40px',\n          height: '40px',\n          border: '4px solid #f3f4f6',\n          borderTop: '4px solid #3b82f6',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }} />\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div style={{\n        padding: '2rem',\n        textAlign: 'center',\n        color: '#ef4444'\n      }}>\n        <h2>Error</h2>\n        <p>{error}</p>\n        <button\n          onClick={loadCategories}\n          style={{\n            padding: '0.5rem 1rem',\n            background: '#3b82f6',\n            color: 'white',\n            border: 'none',\n            borderRadius: '6px',\n            cursor: 'pointer'\n          }}\n        >\n          Retry\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ maxWidth: '1400px', margin: '0 auto' }}>\n      {/* Header */}\n      <div style={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      }}>\n        <div>\n          <h1 style={{\n            margin: '0 0 0.5rem',\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          }}>\n            Category Management\n          </h1>\n          <p style={{\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          }}>\n            Manage categories and subcategories for announcements and events\n          </p>\n        </div>\n\n        <button\n          onClick={handleAddCategory}\n          style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            padding: '0.75rem 1.5rem',\n            background: '#3b82f6',\n            color: 'white',\n            border: 'none',\n            borderRadius: '8px',\n            fontSize: '0.875rem',\n            fontWeight: '600',\n            cursor: 'pointer',\n            transition: 'background-color 0.2s'\n          }}\n          onMouseEnter={(e) => e.currentTarget.style.background = '#2563eb'}\n          onMouseLeave={(e) => e.currentTarget.style.background = '#3b82f6'}\n        >\n          <Plus size={16} />\n          Add Category\n        </button>\n      </div>\n\n      {/* Success/Error Messages */}\n      {(error || success) && (\n        <div style={{ marginBottom: '1.5rem' }}>\n          {error && (\n            <div style={{\n              padding: '1rem',\n              background: '#fef2f2',\n              border: '1px solid #fecaca',\n              borderRadius: '8px',\n              color: '#dc2626',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            }}>\n              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                <AlertTriangle size={16} />\n                {error}\n              </div>\n              <button\n                onClick={clearMessages}\n                style={{\n                  background: 'transparent',\n                  border: 'none',\n                  color: '#dc2626',\n                  cursor: 'pointer',\n                  padding: '0.25rem'\n                }}\n              >\n                ×\n              </button>\n            </div>\n          )}\n\n          {success && (\n            <div style={{\n              padding: '1rem',\n              background: '#f0fdf4',\n              border: '1px solid #bbf7d0',\n              borderRadius: '8px',\n              color: '#166534',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            }}>\n              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                <CheckCircle size={16} />\n                {success}\n              </div>\n              <button\n                onClick={clearMessages}\n                style={{\n                  background: 'transparent',\n                  border: 'none',\n                  color: '#166534',\n                  cursor: 'pointer',\n                  padding: '0.25rem'\n                }}\n              >\n                ×\n              </button>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Category List */}\n      <CategoryList\n        categories={categories}\n        loading={loading}\n        onEditCategory={handleEditCategory}\n        onDeleteCategory={handleDeleteCategory}\n        onToggleCategoryStatus={handleToggleCategoryStatus}\n        onAddSubcategory={handleAddSubcategory}\n        onEditSubcategory={handleEditSubcategory}\n        onDeleteSubcategory={handleDeleteSubcategory}\n        onToggleSubcategoryStatus={handleToggleSubcategoryStatus}\n      />\n\n      {/* Category Modal */}\n      <CategoryModal\n        isOpen={showModal}\n        onClose={() => setShowModal(false)}\n        onSave={handleSave}\n        category={editingCategory}\n        subcategory={editingSubcategory}\n        parentCategory={parentCategory}\n        mode={modalMode}\n        loading={modalLoading}\n      />\n    </div>\n  );\n};\n\nexport default CategoryManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,UAAU,EAAEC,IAAI,EAAEC,aAAa,EAAEC,WAAW,QAAQ,cAAc;AAC3E,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AAExD,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,OAAOC,aAAa,MAAM,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAoBjE,MAAMC,kBAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM;IAAEC;EAAK,CAAC,GAAGR,YAAY,CAAC,CAAC;EAC/B,MAAMS,WAAW,GAAGR,cAAc,CAACO,IAAI,CAAC;;EAExC;EACA,MAAM,CAACE,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAa,EAAE,CAAC;EAC5D,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAgB,IAAI,CAAC;;EAE3D;EACA,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAA4E,cAAc,CAAC;EACrI,MAAM,CAAC4B,eAAe,EAAEC,kBAAkB,CAAC,GAAG7B,QAAQ,CAAkB,IAAI,CAAC;EAC7E,MAAM,CAAC8B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/B,QAAQ,CAAqB,IAAI,CAAC;EACtF,MAAM,CAACgC,cAAc,EAAEC,iBAAiB,CAAC,GAAGjC,QAAQ,CAAkB,IAAI,CAAC;EAC3E,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACc,WAAW,CAACqB,mBAAmB,EAAE;MACpCf,QAAQ,CAAC,iDAAiD,CAAC;MAC3DF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEAkB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACtB,WAAW,CAACqB,mBAAmB,CAAC,CAAC;EAErC,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFlB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA;MACA;;MAEA;MACA,MAAMiB,cAA0B,GAAG,CACjC;QACEC,WAAW,EAAE,CAAC;QACdC,IAAI,EAAE,UAAU;QAChBC,WAAW,EAAE,2CAA2C;QACxDC,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,IAAI;QACfC,aAAa,EAAE,CACb;UACEC,cAAc,EAAE,CAAC;UACjBL,IAAI,EAAE,OAAO;UACbC,WAAW,EAAE,mCAAmC;UAChDC,UAAU,EAAE,SAAS;UACrBI,aAAa,EAAE,CAAC;UAChBH,SAAS,EAAE;QACb,CAAC,EACD;UACEE,cAAc,EAAE,CAAC;UACjBL,IAAI,EAAE,aAAa;UACnBC,WAAW,EAAE,sCAAsC;UACnDC,UAAU,EAAE,SAAS;UACrBI,aAAa,EAAE,CAAC;UAChBH,SAAS,EAAE;QACb,CAAC,EACD;UACEE,cAAc,EAAE,CAAC;UACjBL,IAAI,EAAE,iBAAiB;UACvBC,WAAW,EAAE,mCAAmC;UAChDC,UAAU,EAAE,SAAS;UACrBI,aAAa,EAAE,CAAC;UAChBH,SAAS,EAAE;QACb,CAAC;MAEL,CAAC,EACD;QACEJ,WAAW,EAAE,CAAC;QACdC,IAAI,EAAE,QAAQ;QACdC,WAAW,EAAE,8BAA8B;QAC3CC,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,IAAI;QACfC,aAAa,EAAE,CACb;UACEC,cAAc,EAAE,CAAC;UACjBL,IAAI,EAAE,QAAQ;UACdC,WAAW,EAAE,gCAAgC;UAC7CC,UAAU,EAAE,SAAS;UACrBI,aAAa,EAAE,CAAC;UAChBH,SAAS,EAAE;QACb,CAAC,EACD;UACEE,cAAc,EAAE,CAAC;UACjBL,IAAI,EAAE,UAAU;UAChBC,WAAW,EAAE,kCAAkC;UAC/CC,UAAU,EAAE,SAAS;UACrBI,aAAa,EAAE,CAAC;UAChBH,SAAS,EAAE;QACb,CAAC;MAEL,CAAC,EACD;QACEJ,WAAW,EAAE,CAAC;QACdC,IAAI,EAAE,gBAAgB;QACtBC,WAAW,EAAE,oCAAoC;QACjDC,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,IAAI;QACfC,aAAa,EAAE,CACb;UACEC,cAAc,EAAE,CAAC;UACjBL,IAAI,EAAE,UAAU;UAChBC,WAAW,EAAE,iCAAiC;UAC9CC,UAAU,EAAE,SAAS;UACrBI,aAAa,EAAE,CAAC;UAChBH,SAAS,EAAE;QACb,CAAC;MAEL,CAAC,EACD;QACEJ,WAAW,EAAE,CAAC;QACdC,IAAI,EAAE,WAAW;QACjBC,WAAW,EAAE,oCAAoC;QACjDC,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,IAAI;QACfC,aAAa,EAAE;MACjB,CAAC,CACF;MAED3B,aAAa,CAACqB,cAAc,CAAC;IAC/B,CAAC,CAAC,OAAOS,GAAQ,EAAE;MACjB1B,QAAQ,CAAC0B,GAAG,CAACC,OAAO,IAAI,2BAA2B,CAAC;IACtD,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM8B,iBAAiB,GAAGA,CAAA,KAAM;IAC9BtB,YAAY,CAAC,cAAc,CAAC;IAC5BE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,qBAAqB,CAAC,IAAI,CAAC;IAC3BE,iBAAiB,CAAC,IAAI,CAAC;IACvBR,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMyB,kBAAkB,GAAIC,QAAkB,IAAK;IACjDxB,YAAY,CAAC,eAAe,CAAC;IAC7BE,kBAAkB,CAACsB,QAAQ,CAAC;IAC5BpB,qBAAqB,CAAC,IAAI,CAAC;IAC3BE,iBAAiB,CAAC,IAAI,CAAC;IACvBR,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAM2B,oBAAoB,GAAG,MAAOD,QAAkB,IAAK;IACzD,IAAI;MACF9B,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA;;MAEAA,UAAU,CAAC,aAAa4B,QAAQ,CAACX,IAAI,iCAAiC,CAAC;MACvEH,cAAc,CAAC,CAAC;IAElB,CAAC,CAAC,OAAOU,GAAQ,EAAE;MACjB1B,QAAQ,CAAC0B,GAAG,CAACC,OAAO,IAAI,2BAA2B,CAAC;IACtD;EACF,CAAC;EAED,MAAMK,0BAA0B,GAAG,MAAOF,QAAkB,IAAK;IAC/D,IAAI;MACF9B,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA;;MAEA,MAAM+B,MAAM,GAAGH,QAAQ,CAACR,SAAS,GAAG,aAAa,GAAG,WAAW;MAC/DpB,UAAU,CAAC,aAAa4B,QAAQ,CAACX,IAAI,cAAcc,MAAM,eAAe,CAAC;MACzEjB,cAAc,CAAC,CAAC;IAElB,CAAC,CAAC,OAAOU,GAAQ,EAAE;MACjB1B,QAAQ,CAAC0B,GAAG,CAACC,OAAO,IAAI,kCAAkC,CAAC;IAC7D;EACF,CAAC;EAED,MAAMO,oBAAoB,GAAIJ,QAAkB,IAAK;IACnDxB,YAAY,CAAC,iBAAiB,CAAC;IAC/BE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,qBAAqB,CAAC,IAAI,CAAC;IAC3BE,iBAAiB,CAACkB,QAAQ,CAAC;IAC3B1B,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAM+B,qBAAqB,GAAGA,CAACL,QAAkB,EAAEM,WAAwB,KAAK;IAC9E9B,YAAY,CAAC,kBAAkB,CAAC;IAChCE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,qBAAqB,CAAC0B,WAAW,CAAC;IAClCxB,iBAAiB,CAACkB,QAAQ,CAAC;IAC3B1B,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMiC,uBAAuB,GAAG,MAAAA,CAAOP,QAAkB,EAAEM,WAAwB,KAAK;IACtF,IAAI;MACFpC,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA;;MAEAA,UAAU,CAAC,gBAAgBkC,WAAW,CAACjB,IAAI,iCAAiC,CAAC;MAC7EH,cAAc,CAAC,CAAC;IAElB,CAAC,CAAC,OAAOU,GAAQ,EAAE;MACjB1B,QAAQ,CAAC0B,GAAG,CAACC,OAAO,IAAI,8BAA8B,CAAC;IACzD;EACF,CAAC;EAED,MAAMW,6BAA6B,GAAG,MAAAA,CAAOR,QAAkB,EAAEM,WAAwB,KAAK;IAC5F,IAAI;MACFpC,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA;;MAEA,MAAM+B,MAAM,GAAGG,WAAW,CAACd,SAAS,GAAG,aAAa,GAAG,WAAW;MAClEpB,UAAU,CAAC,gBAAgBkC,WAAW,CAACjB,IAAI,cAAcc,MAAM,eAAe,CAAC;MAC/EjB,cAAc,CAAC,CAAC;IAElB,CAAC,CAAC,OAAOU,GAAQ,EAAE;MACjB1B,QAAQ,CAAC0B,GAAG,CAACC,OAAO,IAAI,qCAAqC,CAAC;IAChE;EACF,CAAC;EAED,MAAMY,UAAU,GAAG,MAAAA,CAAOC,IAA4B,EAAEC,SAAoB,KAAK;IAC/E,IAAI;MACF3B,eAAe,CAAC,IAAI,CAAC;MACrBd,QAAQ,CAAC,IAAI,CAAC;MACdE,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIG,SAAS,KAAK,cAAc,EAAE;QAChC;QACA;QACAH,UAAU,CAAC,aAAasC,IAAI,CAACrB,IAAI,iCAAiC,CAAC;MACrE,CAAC,MAAM,IAAId,SAAS,KAAK,eAAe,EAAE;QACxC;QACA;QACAH,UAAU,CAAC,aAAasC,IAAI,CAACrB,IAAI,iCAAiC,CAAC;MACrE,CAAC,MAAM,IAAId,SAAS,KAAK,iBAAiB,EAAE;QAC1C;QACA;QACAH,UAAU,CAAC,gBAAgBsC,IAAI,CAACrB,IAAI,iCAAiC,CAAC;MACxE,CAAC,MAAM,IAAId,SAAS,KAAK,kBAAkB,EAAE;QAC3C;QACA;QACAH,UAAU,CAAC,gBAAgBsC,IAAI,CAACrB,IAAI,iCAAiC,CAAC;MACxE;MAEAH,cAAc,CAAC,CAAC;IAElB,CAAC,CAAC,OAAOU,GAAQ,EAAE;MACjB1B,QAAQ,CAAC0B,GAAG,CAACC,OAAO,IAAI,wBAAwB,CAAC;MACjD,MAAMD,GAAG,CAAC,CAAC;IACb,CAAC,SAAS;MACRZ,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAM4B,aAAa,GAAGA,CAAA,KAAM;IAC1B1C,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;EAClB,CAAC;EAED,IAAI,CAACR,WAAW,CAACqB,mBAAmB,EAAE;IACpC,oBACEzB,OAAA;MAAKqD,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE,OAAO;QAClBC,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACA7D,OAAA,CAACT,UAAU;QAACuE,IAAI,EAAE,EAAG;QAACT,KAAK,EAAE;UAAEU,YAAY,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAI;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvEpE,OAAA;QAAIqD,KAAK,EAAE;UAAEgB,MAAM,EAAE,YAAY;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAM,CAAE;QAAAV,QAAA,EAAC;MAE5E;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLpE,OAAA;QAAGqD,KAAK,EAAE;UAAEgB,MAAM,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAT,QAAA,EAAC;MAE3C;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJpE,OAAA;QAAKqD,KAAK,EAAE;UACVmB,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAEtE,WAAW,CAACuE,qBAAqB,CAAC,CAAC;UAC/CC,YAAY,EAAE,KAAK;UACnBhB,KAAK,EAAE,OAAO;UACdU,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE;QACd,CAAE;QAAAV,QAAA,GAAC,gBACa,EAACzD,WAAW,CAACyE,sBAAsB,CAAC,CAAC;MAAA;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI7D,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKqD,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE;MACb,CAAE;MAAAG,QAAA,eACA7D,OAAA;QAAKqD,KAAK,EAAE;UACVyB,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,mBAAmB;UAC3BC,SAAS,EAAE,mBAAmB;UAC9BL,YAAY,EAAE,KAAK;UACnBM,SAAS,EAAE;QACb;MAAE;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEV;EAEA,IAAI3D,KAAK,EAAE;IACT,oBACET,OAAA;MAAKqD,KAAK,EAAE;QACVoB,OAAO,EAAE,MAAM;QACfd,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACA7D,OAAA;QAAA6D,QAAA,EAAI;MAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdpE,OAAA;QAAA6D,QAAA,EAAIpD;MAAK;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdpE,OAAA;QACEmF,OAAO,EAAEzD,cAAe;QACxB2B,KAAK,EAAE;UACLoB,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAE,SAAS;UACrBd,KAAK,EAAE,OAAO;UACdoB,MAAM,EAAE,MAAM;UACdJ,YAAY,EAAE,KAAK;UACnBQ,MAAM,EAAE;QACV,CAAE;QAAAvB,QAAA,EACH;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACEpE,OAAA;IAAKqD,KAAK,EAAE;MAAEgC,QAAQ,EAAE,QAAQ;MAAEhB,MAAM,EAAE;IAAS,CAAE;IAAAR,QAAA,gBAEnD7D,OAAA;MAAKqD,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfG,cAAc,EAAE,eAAe;QAC/BD,UAAU,EAAE,QAAQ;QACpBO,YAAY,EAAE;MAChB,CAAE;MAAAF,QAAA,gBACA7D,OAAA;QAAA6D,QAAA,gBACE7D,OAAA;UAAIqD,KAAK,EAAE;YACTgB,MAAM,EAAE,YAAY;YACpBC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjBX,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,EAAC;QAEH;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLpE,OAAA;UAAGqD,KAAK,EAAE;YACRgB,MAAM,EAAE,CAAC;YACTT,KAAK,EAAE,SAAS;YAChBU,QAAQ,EAAE;UACZ,CAAE;UAAAT,QAAA,EAAC;QAEH;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENpE,OAAA;QACEmF,OAAO,EAAE7C,iBAAkB;QAC3Be,KAAK,EAAE;UACLC,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpB8B,GAAG,EAAE,QAAQ;UACbb,OAAO,EAAE,gBAAgB;UACzBC,UAAU,EAAE,SAAS;UACrBd,KAAK,EAAE,OAAO;UACdoB,MAAM,EAAE,MAAM;UACdJ,YAAY,EAAE,KAAK;UACnBN,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE,KAAK;UACjBa,MAAM,EAAE,SAAS;UACjBG,UAAU,EAAE;QACd,CAAE;QACFC,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACrC,KAAK,CAACqB,UAAU,GAAG,SAAU;QAClEiB,YAAY,EAAGF,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACrC,KAAK,CAACqB,UAAU,GAAG,SAAU;QAAAb,QAAA,gBAElE7D,OAAA,CAACR,IAAI;UAACsE,IAAI,EAAE;QAAG;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEpB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGL,CAAC3D,KAAK,IAAIE,OAAO,kBAChBX,OAAA;MAAKqD,KAAK,EAAE;QAAEU,YAAY,EAAE;MAAS,CAAE;MAAAF,QAAA,GACpCpD,KAAK,iBACJT,OAAA;QAAKqD,KAAK,EAAE;UACVoB,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,SAAS;UACrBM,MAAM,EAAE,mBAAmB;UAC3BJ,YAAY,EAAE,KAAK;UACnBhB,KAAK,EAAE,SAAS;UAChBN,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE;QAClB,CAAE;QAAAI,QAAA,gBACA7D,OAAA;UAAKqD,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAE8B,GAAG,EAAE;UAAS,CAAE;UAAAzB,QAAA,gBACnE7D,OAAA,CAACP,aAAa;YAACqE,IAAI,EAAE;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAC1B3D,KAAK;QAAA;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpE,OAAA;UACEmF,OAAO,EAAE/B,aAAc;UACvBC,KAAK,EAAE;YACLqB,UAAU,EAAE,aAAa;YACzBM,MAAM,EAAE,MAAM;YACdpB,KAAK,EAAE,SAAS;YAChBwB,MAAM,EAAE,SAAS;YACjBX,OAAO,EAAE;UACX,CAAE;UAAAZ,QAAA,EACH;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAEAzD,OAAO,iBACNX,OAAA;QAAKqD,KAAK,EAAE;UACVoB,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,SAAS;UACrBM,MAAM,EAAE,mBAAmB;UAC3BJ,YAAY,EAAE,KAAK;UACnBhB,KAAK,EAAE,SAAS;UAChBN,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE;QAClB,CAAE;QAAAI,QAAA,gBACA7D,OAAA;UAAKqD,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAE8B,GAAG,EAAE;UAAS,CAAE;UAAAzB,QAAA,gBACnE7D,OAAA,CAACN,WAAW;YAACoE,IAAI,EAAE;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACxBzD,OAAO;QAAA;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNpE,OAAA;UACEmF,OAAO,EAAE/B,aAAc;UACvBC,KAAK,EAAE;YACLqB,UAAU,EAAE,aAAa;YACzBM,MAAM,EAAE,MAAM;YACdpB,KAAK,EAAE,SAAS;YAChBwB,MAAM,EAAE,SAAS;YACjBX,OAAO,EAAE;UACX,CAAE;UAAAZ,QAAA,EACH;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAGDpE,OAAA,CAACH,YAAY;MACXQ,UAAU,EAAEA,UAAW;MACvBE,OAAO,EAAEA,OAAQ;MACjBqF,cAAc,EAAErD,kBAAmB;MACnCsD,gBAAgB,EAAEpD,oBAAqB;MACvCqD,sBAAsB,EAAEpD,0BAA2B;MACnDqD,gBAAgB,EAAEnD,oBAAqB;MACvCoD,iBAAiB,EAAEnD,qBAAsB;MACzCoD,mBAAmB,EAAElD,uBAAwB;MAC7CmD,yBAAyB,EAAElD;IAA8B;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC,eAGFpE,OAAA,CAACF,aAAa;MACZqG,MAAM,EAAEtF,SAAU;MAClBuF,OAAO,EAAEA,CAAA,KAAMtF,YAAY,CAAC,KAAK,CAAE;MACnCuF,MAAM,EAAEpD,UAAW;MACnBT,QAAQ,EAAEvB,eAAgB;MAC1B6B,WAAW,EAAE3B,kBAAmB;MAChCE,cAAc,EAAEA,cAAe;MAC/BiF,IAAI,EAAEvF,SAAU;MAChBR,OAAO,EAAEgB;IAAa;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAClE,EAAA,CA5eID,kBAA4B;EAAA,QACfN,YAAY,EACTC,cAAc;AAAA;AAAA2G,EAAA,GAF9BtG,kBAA4B;AA8elC,eAAeA,kBAAkB;AAAC,IAAAsG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}