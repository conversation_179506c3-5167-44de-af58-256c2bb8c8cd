{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\admin\\\\CategoryModal.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { X, FolderTree, Tag, Palette, FileText, Hash } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CategoryModal = ({\n  isOpen,\n  onClose,\n  onSave,\n  category,\n  subcategory,\n  parentCategory,\n  mode,\n  loading = false\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    color_code: '#3b82f6',\n    is_active: true,\n    ...(mode.includes('subcategory') ? {\n      display_order: 1\n    } : {})\n  });\n  const [errors, setErrors] = useState({});\n  const isSubcategoryMode = mode.includes('subcategory');\n  const isEditMode = mode.includes('edit');\n\n  // Predefined color options\n  const colorOptions = ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#ec4899', '#06b6d4', '#84cc16', '#f97316', '#6366f1', '#14b8a6', '#f43f5e', '#8b5a2b', '#64748b', '#1f2937'];\n  useEffect(() => {\n    if (isEditMode) {\n      if (isSubcategoryMode && subcategory) {\n        setFormData(subcategory);\n      } else if (!isSubcategoryMode && category) {\n        setFormData(category);\n      }\n    } else {\n      // Reset form for new items\n      setFormData({\n        name: '',\n        description: '',\n        color_code: '#3b82f6',\n        is_active: true,\n        ...(isSubcategoryMode ? {\n          display_order: 1\n        } : {})\n      });\n    }\n    setErrors({});\n  }, [category, subcategory, mode, isOpen]);\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Name validation\n    if (!formData.name.trim()) {\n      newErrors.name = `${isSubcategoryMode ? 'Subcategory' : 'Category'} name is required`;\n    } else if (formData.name.trim().length < 2) {\n      newErrors.name = 'Name must be at least 2 characters long';\n    } else if (formData.name.trim().length > 50) {\n      newErrors.name = 'Name must be less than 50 characters';\n    }\n\n    // Description validation\n    if (formData.description && formData.description.length > 200) {\n      newErrors.description = 'Description must be less than 200 characters';\n    }\n\n    // Color validation\n    if (!formData.color_code || !/^#[0-9A-F]{6}$/i.test(formData.color_code)) {\n      newErrors.color_code = 'Please select a valid color';\n    }\n\n    // Display order validation (for subcategories)\n    if (isSubcategoryMode) {\n      const displayOrder = formData.display_order;\n      if (!displayOrder || displayOrder < 1 || displayOrder > 999) {\n        newErrors.display_order = 'Display order must be between 1 and 999';\n      }\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    try {\n      await onSave(formData, parentCategory || undefined);\n      onClose();\n    } catch (error) {\n      console.error('Error saving:', error);\n    }\n  };\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: ''\n      }));\n    }\n  };\n  const getModalTitle = () => {\n    switch (mode) {\n      case 'add_category':\n        return 'Add New Category';\n      case 'edit_category':\n        return 'Edit Category';\n      case 'add_subcategory':\n        return `Add Subcategory to \"${parentCategory === null || parentCategory === void 0 ? void 0 : parentCategory.name}\"`;\n      case 'edit_subcategory':\n        return 'Edit Subcategory';\n      default:\n        return 'Category Management';\n    }\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      background: 'rgba(0, 0, 0, 0.5)',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      zIndex: 1000,\n      padding: '1rem'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderRadius: '12px',\n        width: '100%',\n        maxWidth: '500px',\n        maxHeight: '90vh',\n        overflow: 'hidden',\n        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          padding: '1.5rem',\n          borderBottom: '1px solid #e5e7eb'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            margin: 0,\n            fontSize: '1.5rem',\n            fontWeight: '600',\n            color: '#1f2937',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [isSubcategoryMode ? /*#__PURE__*/_jsxDEV(Tag, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 34\n          }, this) : /*#__PURE__*/_jsxDEV(FolderTree, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 54\n          }, this), getModalTitle()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          style: {\n            padding: '0.5rem',\n            background: 'transparent',\n            border: 'none',\n            borderRadius: '6px',\n            cursor: 'pointer',\n            color: '#6b7280'\n          },\n          children: /*#__PURE__*/_jsxDEV(X, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        style: {\n          padding: '1.5rem',\n          overflowY: 'auto',\n          maxHeight: 'calc(90vh - 140px)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(FileText, {\n                size: 16,\n                style: {\n                  display: 'inline',\n                  marginRight: '0.5rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this), isSubcategoryMode ? 'Subcategory' : 'Category', \" Name *\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: formData.name,\n              onChange: e => handleInputChange('name', e.target.value),\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: `1px solid ${errors.name ? '#ef4444' : '#d1d5db'}`,\n                borderRadius: '6px',\n                fontSize: '0.875rem'\n              },\n              placeholder: `Enter ${isSubcategoryMode ? 'subcategory' : 'category'} name`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this), errors.name && /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0.25rem 0 0',\n                fontSize: '0.75rem',\n                color: '#ef4444'\n              },\n              children: errors.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              },\n              children: \"Description (Optional)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: formData.description || '',\n              onChange: e => handleInputChange('description', e.target.value),\n              rows: 3,\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: `1px solid ${errors.description ? '#ef4444' : '#d1d5db'}`,\n                borderRadius: '6px',\n                fontSize: '0.875rem',\n                resize: 'vertical'\n              },\n              placeholder: `Brief description of the ${isSubcategoryMode ? 'subcategory' : 'category'}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this), errors.description && /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0.25rem 0 0',\n                fontSize: '0.75rem',\n                color: '#ef4444'\n              },\n              children: errors.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Palette, {\n                size: 16,\n                style: {\n                  display: 'inline',\n                  marginRight: '0.5rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this), \"Color *\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1rem',\n                marginBottom: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '48px',\n                  height: '48px',\n                  background: formData.color_code,\n                  borderRadius: '8px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'\n                },\n                children: isSubcategoryMode ? /*#__PURE__*/_jsxDEV(Tag, {\n                  size: 20,\n                  color: \"white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 40\n                }, this) : /*#__PURE__*/_jsxDEV(FolderTree, {\n                  size: 20,\n                  color: \"white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 74\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: formData.color_code,\n                onChange: e => handleInputChange('color_code', e.target.value),\n                style: {\n                  flex: 1,\n                  padding: '0.5rem',\n                  border: `1px solid ${errors.color_code ? '#ef4444' : '#d1d5db'}`,\n                  borderRadius: '6px',\n                  fontSize: '0.875rem',\n                  fontFamily: 'monospace'\n                },\n                placeholder: \"#3b82f6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(40px, 1fr))',\n                gap: '0.5rem'\n              },\n              children: colorOptions.map(color => /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => handleInputChange('color_code', color),\n                style: {\n                  width: '40px',\n                  height: '40px',\n                  background: color,\n                  border: formData.color_code === color ? '3px solid #1f2937' : '1px solid #e5e7eb',\n                  borderRadius: '6px',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                },\n                children: formData.color_code === color && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '8px',\n                    height: '8px',\n                    background: 'white',\n                    borderRadius: '50%'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 23\n                }, this)\n              }, color, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this), errors.color_code && /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0.25rem 0 0',\n                fontSize: '0.75rem',\n                color: '#ef4444'\n              },\n              children: errors.color_code\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this), isSubcategoryMode && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Hash, {\n                size: 16,\n                style: {\n                  display: 'inline',\n                  marginRight: '0.5rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 19\n              }, this), \"Display Order *\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              min: \"1\",\n              max: \"999\",\n              value: formData.display_order || 1,\n              onChange: e => handleInputChange('display_order', parseInt(e.target.value) || 1),\n              style: {\n                width: '100%',\n                padding: '0.75rem',\n                border: `1px solid ${errors.display_order ? '#ef4444' : '#d1d5db'}`,\n                borderRadius: '6px',\n                fontSize: '0.875rem'\n              },\n              placeholder: \"1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0.25rem 0 0',\n                fontSize: '0.75rem',\n                color: '#6b7280'\n              },\n              children: \"Lower numbers appear first in the list\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 17\n            }, this), errors.display_order && /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0.25rem 0 0',\n                fontSize: '0.75rem',\n                color: '#ef4444'\n              },\n              children: errors.display_order\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                cursor: 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: formData.is_active,\n                onChange: e => handleInputChange('is_active', e.target.checked),\n                style: {\n                  width: '16px',\n                  height: '16px',\n                  cursor: 'pointer'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  color: '#374151'\n                },\n                children: \"Active\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0.25rem 0 0',\n                fontSize: '0.75rem',\n                color: '#6b7280'\n              },\n              children: [\"Inactive \", isSubcategoryMode ? 'subcategories' : 'categories', \" won't be available for selection\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem',\n            justifyContent: 'flex-end',\n            marginTop: '2rem',\n            paddingTop: '1.5rem',\n            borderTop: '1px solid #e5e7eb'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: onClose,\n            disabled: loading,\n            style: {\n              padding: '0.75rem 1.5rem',\n              background: '#f3f4f6',\n              color: '#6b7280',\n              border: 'none',\n              borderRadius: '6px',\n              cursor: loading ? 'not-allowed' : 'pointer',\n              fontSize: '0.875rem',\n              fontWeight: '500'\n            },\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: loading,\n            style: {\n              padding: '0.75rem 1.5rem',\n              background: loading ? '#9ca3af' : '#3b82f6',\n              color: 'white',\n              border: 'none',\n              borderRadius: '6px',\n              cursor: loading ? 'not-allowed' : 'pointer',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [loading && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '16px',\n                height: '16px',\n                border: '2px solid transparent',\n                borderTop: '2px solid white',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 17\n            }, this), loading ? 'Saving...' : isEditMode ? `Update ${isSubcategoryMode ? 'Subcategory' : 'Category'}` : `Create ${isSubcategoryMode ? 'Subcategory' : 'Category'}`]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 162,\n    columnNumber: 5\n  }, this);\n};\n_s(CategoryModal, \"+2HYt1Q1gYbzIV47N0qwVeUVrCc=\");\n_c = CategoryModal;\nexport default CategoryModal;\nvar _c;\n$RefreshReg$(_c, \"CategoryModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "X", "FolderTree", "Tag", "Palette", "FileText", "Hash", "jsxDEV", "_jsxDEV", "CategoryModal", "isOpen", "onClose", "onSave", "category", "subcategory", "parentCategory", "mode", "loading", "_s", "formData", "setFormData", "name", "description", "color_code", "is_active", "includes", "display_order", "errors", "setErrors", "isSubcategoryMode", "isEditMode", "colorOptions", "validateForm", "newErrors", "trim", "length", "test", "displayOrder", "Object", "keys", "handleSubmit", "e", "preventDefault", "undefined", "error", "console", "handleInputChange", "field", "value", "prev", "getModalTitle", "style", "position", "top", "left", "right", "bottom", "background", "display", "alignItems", "justifyContent", "zIndex", "padding", "children", "borderRadius", "width", "max<PERSON><PERSON><PERSON>", "maxHeight", "overflow", "boxShadow", "borderBottom", "margin", "fontSize", "fontWeight", "color", "gap", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "border", "cursor", "onSubmit", "overflowY", "flexDirection", "marginBottom", "marginRight", "type", "onChange", "target", "placeholder", "rows", "resize", "height", "flex", "fontFamily", "gridTemplateColumns", "map", "min", "max", "parseInt", "checked", "marginTop", "paddingTop", "borderTop", "disabled", "animation", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/admin/CategoryModal.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { X, FolderTree, Tag, Palette, FileText, Hash } from 'lucide-react';\n\ninterface Subcategory {\n  subcategory_id?: number;\n  category_id?: number;\n  name: string;\n  description?: string;\n  color_code: string;\n  is_active: boolean;\n  display_order: number;\n  created_at?: string;\n  updated_at?: string;\n}\n\ninterface Category {\n  category_id?: number;\n  name: string;\n  description?: string;\n  color_code: string;\n  is_active: boolean;\n  created_at?: string;\n  updated_at?: string;\n  subcategories?: Subcategory[];\n}\n\ninterface CategoryModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onSave: (categoryData: Category | Subcategory, parentCategory?: Category) => Promise<void>;\n  category?: Category | null;\n  subcategory?: Subcategory | null;\n  parentCategory?: Category | null;\n  mode: 'add_category' | 'edit_category' | 'add_subcategory' | 'edit_subcategory';\n  loading?: boolean;\n}\n\nconst CategoryModal: React.FC<CategoryModalProps> = ({\n  isOpen,\n  onClose,\n  onSave,\n  category,\n  subcategory,\n  parentCategory,\n  mode,\n  loading = false\n}) => {\n  const [formData, setFormData] = useState<Category | Subcategory>({\n    name: '',\n    description: '',\n    color_code: '#3b82f6',\n    is_active: true,\n    ...(mode.includes('subcategory') ? { display_order: 1 } : {})\n  });\n\n  const [errors, setErrors] = useState<Record<string, string>>({});\n\n  const isSubcategoryMode = mode.includes('subcategory');\n  const isEditMode = mode.includes('edit');\n\n  // Predefined color options\n  const colorOptions = [\n    '#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6',\n    '#ec4899', '#06b6d4', '#84cc16', '#f97316', '#6366f1',\n    '#14b8a6', '#f43f5e', '#8b5a2b', '#64748b', '#1f2937'\n  ];\n\n  useEffect(() => {\n    if (isEditMode) {\n      if (isSubcategoryMode && subcategory) {\n        setFormData(subcategory);\n      } else if (!isSubcategoryMode && category) {\n        setFormData(category);\n      }\n    } else {\n      // Reset form for new items\n      setFormData({\n        name: '',\n        description: '',\n        color_code: '#3b82f6',\n        is_active: true,\n        ...(isSubcategoryMode ? { display_order: 1 } : {})\n      });\n    }\n    setErrors({});\n  }, [category, subcategory, mode, isOpen]);\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {};\n\n    // Name validation\n    if (!formData.name.trim()) {\n      newErrors.name = `${isSubcategoryMode ? 'Subcategory' : 'Category'} name is required`;\n    } else if (formData.name.trim().length < 2) {\n      newErrors.name = 'Name must be at least 2 characters long';\n    } else if (formData.name.trim().length > 50) {\n      newErrors.name = 'Name must be less than 50 characters';\n    }\n\n    // Description validation\n    if (formData.description && formData.description.length > 200) {\n      newErrors.description = 'Description must be less than 200 characters';\n    }\n\n    // Color validation\n    if (!formData.color_code || !/^#[0-9A-F]{6}$/i.test(formData.color_code)) {\n      newErrors.color_code = 'Please select a valid color';\n    }\n\n    // Display order validation (for subcategories)\n    if (isSubcategoryMode) {\n      const displayOrder = (formData as Subcategory).display_order;\n      if (!displayOrder || displayOrder < 1 || displayOrder > 999) {\n        newErrors.display_order = 'Display order must be between 1 and 999';\n      }\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    try {\n      await onSave(formData, parentCategory || undefined);\n      onClose();\n    } catch (error) {\n      console.error('Error saving:', error);\n    }\n  };\n\n  const handleInputChange = (field: string, value: any) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: '' }));\n    }\n  };\n\n  const getModalTitle = () => {\n    switch (mode) {\n      case 'add_category': return 'Add New Category';\n      case 'edit_category': return 'Edit Category';\n      case 'add_subcategory': return `Add Subcategory to \"${parentCategory?.name}\"`;\n      case 'edit_subcategory': return 'Edit Subcategory';\n      default: return 'Category Management';\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div style={{\n      position: 'fixed',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      background: 'rgba(0, 0, 0, 0.5)',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      zIndex: 1000,\n      padding: '1rem'\n    }}>\n      <div style={{\n        background: 'white',\n        borderRadius: '12px',\n        width: '100%',\n        maxWidth: '500px',\n        maxHeight: '90vh',\n        overflow: 'hidden',\n        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)'\n      }}>\n        {/* Header */}\n        <div style={{\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          padding: '1.5rem',\n          borderBottom: '1px solid #e5e7eb'\n        }}>\n          <h2 style={{\n            margin: 0,\n            fontSize: '1.5rem',\n            fontWeight: '600',\n            color: '#1f2937',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          }}>\n            {isSubcategoryMode ? <Tag size={20} /> : <FolderTree size={20} />}\n            {getModalTitle()}\n          </h2>\n          \n          <button\n            onClick={onClose}\n            style={{\n              padding: '0.5rem',\n              background: 'transparent',\n              border: 'none',\n              borderRadius: '6px',\n              cursor: 'pointer',\n              color: '#6b7280'\n            }}\n          >\n            <X size={20} />\n          </button>\n        </div>\n\n        {/* Form */}\n        <form onSubmit={handleSubmit} style={{ padding: '1.5rem', overflowY: 'auto', maxHeight: 'calc(90vh - 140px)' }}>\n          <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>\n            {/* Name */}\n            <div>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                <FileText size={16} style={{ display: 'inline', marginRight: '0.5rem' }} />\n                {isSubcategoryMode ? 'Subcategory' : 'Category'} Name *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.name}\n                onChange={(e) => handleInputChange('name', e.target.value)}\n                style={{\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: `1px solid ${errors.name ? '#ef4444' : '#d1d5db'}`,\n                  borderRadius: '6px',\n                  fontSize: '0.875rem'\n                }}\n                placeholder={`Enter ${isSubcategoryMode ? 'subcategory' : 'category'} name`}\n              />\n              {errors.name && (\n                <p style={{ margin: '0.25rem 0 0', fontSize: '0.75rem', color: '#ef4444' }}>\n                  {errors.name}\n                </p>\n              )}\n            </div>\n\n            {/* Description */}\n            <div>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                Description (Optional)\n              </label>\n              <textarea\n                value={formData.description || ''}\n                onChange={(e) => handleInputChange('description', e.target.value)}\n                rows={3}\n                style={{\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: `1px solid ${errors.description ? '#ef4444' : '#d1d5db'}`,\n                  borderRadius: '6px',\n                  fontSize: '0.875rem',\n                  resize: 'vertical'\n                }}\n                placeholder={`Brief description of the ${isSubcategoryMode ? 'subcategory' : 'category'}`}\n              />\n              {errors.description && (\n                <p style={{ margin: '0.25rem 0 0', fontSize: '0.75rem', color: '#ef4444' }}>\n                  {errors.description}\n                </p>\n              )}\n            </div>\n\n            {/* Color Selection */}\n            <div>\n              <label style={{\n                display: 'block',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                color: '#374151',\n                marginBottom: '0.5rem'\n              }}>\n                <Palette size={16} style={{ display: 'inline', marginRight: '0.5rem' }} />\n                Color *\n              </label>\n              \n              {/* Color Preview */}\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1rem',\n                marginBottom: '1rem'\n              }}>\n                <div style={{\n                  width: '48px',\n                  height: '48px',\n                  background: formData.color_code,\n                  borderRadius: '8px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'\n                }}>\n                  {isSubcategoryMode ? <Tag size={20} color=\"white\" /> : <FolderTree size={20} color=\"white\" />}\n                </div>\n                \n                <input\n                  type=\"text\"\n                  value={formData.color_code}\n                  onChange={(e) => handleInputChange('color_code', e.target.value)}\n                  style={{\n                    flex: 1,\n                    padding: '0.5rem',\n                    border: `1px solid ${errors.color_code ? '#ef4444' : '#d1d5db'}`,\n                    borderRadius: '6px',\n                    fontSize: '0.875rem',\n                    fontFamily: 'monospace'\n                  }}\n                  placeholder=\"#3b82f6\"\n                />\n              </div>\n\n              {/* Color Options */}\n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(40px, 1fr))',\n                gap: '0.5rem'\n              }}>\n                {colorOptions.map((color) => (\n                  <button\n                    key={color}\n                    type=\"button\"\n                    onClick={() => handleInputChange('color_code', color)}\n                    style={{\n                      width: '40px',\n                      height: '40px',\n                      background: color,\n                      border: formData.color_code === color ? '3px solid #1f2937' : '1px solid #e5e7eb',\n                      borderRadius: '6px',\n                      cursor: 'pointer',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center'\n                    }}\n                  >\n                    {formData.color_code === color && (\n                      <div style={{\n                        width: '8px',\n                        height: '8px',\n                        background: 'white',\n                        borderRadius: '50%'\n                      }} />\n                    )}\n                  </button>\n                ))}\n              </div>\n              \n              {errors.color_code && (\n                <p style={{ margin: '0.25rem 0 0', fontSize: '0.75rem', color: '#ef4444' }}>\n                  {errors.color_code}\n                </p>\n              )}\n            </div>\n\n            {/* Display Order (for subcategories) */}\n            {isSubcategoryMode && (\n              <div>\n                <label style={{\n                  display: 'block',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  color: '#374151',\n                  marginBottom: '0.5rem'\n                }}>\n                  <Hash size={16} style={{ display: 'inline', marginRight: '0.5rem' }} />\n                  Display Order *\n                </label>\n                <input\n                  type=\"number\"\n                  min=\"1\"\n                  max=\"999\"\n                  value={(formData as Subcategory).display_order || 1}\n                  onChange={(e) => handleInputChange('display_order', parseInt(e.target.value) || 1)}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: `1px solid ${errors.display_order ? '#ef4444' : '#d1d5db'}`,\n                    borderRadius: '6px',\n                    fontSize: '0.875rem'\n                  }}\n                  placeholder=\"1\"\n                />\n                <p style={{ margin: '0.25rem 0 0', fontSize: '0.75rem', color: '#6b7280' }}>\n                  Lower numbers appear first in the list\n                </p>\n                {errors.display_order && (\n                  <p style={{ margin: '0.25rem 0 0', fontSize: '0.75rem', color: '#ef4444' }}>\n                    {errors.display_order}\n                  </p>\n                )}\n              </div>\n            )}\n\n            {/* Status */}\n            <div>\n              <label style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                cursor: 'pointer'\n              }}>\n                <input\n                  type=\"checkbox\"\n                  checked={formData.is_active}\n                  onChange={(e) => handleInputChange('is_active', e.target.checked)}\n                  style={{\n                    width: '16px',\n                    height: '16px',\n                    cursor: 'pointer'\n                  }}\n                />\n                <span style={{\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  color: '#374151'\n                }}>\n                  Active\n                </span>\n              </label>\n              <p style={{ margin: '0.25rem 0 0', fontSize: '0.75rem', color: '#6b7280' }}>\n                Inactive {isSubcategoryMode ? 'subcategories' : 'categories'} won't be available for selection\n              </p>\n            </div>\n          </div>\n\n          {/* Form Actions */}\n          <div style={{\n            display: 'flex',\n            gap: '1rem',\n            justifyContent: 'flex-end',\n            marginTop: '2rem',\n            paddingTop: '1.5rem',\n            borderTop: '1px solid #e5e7eb'\n          }}>\n            <button\n              type=\"button\"\n              onClick={onClose}\n              disabled={loading}\n              style={{\n                padding: '0.75rem 1.5rem',\n                background: '#f3f4f6',\n                color: '#6b7280',\n                border: 'none',\n                borderRadius: '6px',\n                cursor: loading ? 'not-allowed' : 'pointer',\n                fontSize: '0.875rem',\n                fontWeight: '500'\n              }}\n            >\n              Cancel\n            </button>\n            \n            <button\n              type=\"submit\"\n              disabled={loading}\n              style={{\n                padding: '0.75rem 1.5rem',\n                background: loading ? '#9ca3af' : '#3b82f6',\n                color: 'white',\n                border: 'none',\n                borderRadius: '6px',\n                cursor: loading ? 'not-allowed' : 'pointer',\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              }}\n            >\n              {loading && (\n                <div style={{\n                  width: '16px',\n                  height: '16px',\n                  border: '2px solid transparent',\n                  borderTop: '2px solid white',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }} />\n              )}\n              {loading ? 'Saving...' : (isEditMode ? `Update ${isSubcategoryMode ? 'Subcategory' : 'Category'}` : `Create ${isSubcategoryMode ? 'Subcategory' : 'Category'}`)}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default CategoryModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,CAAC,EAAEC,UAAU,EAAEC,GAAG,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,IAAI,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAoC3E,MAAMC,aAA2C,GAAGA,CAAC;EACnDC,MAAM;EACNC,OAAO;EACPC,MAAM;EACNC,QAAQ;EACRC,WAAW;EACXC,cAAc;EACdC,IAAI;EACJC,OAAO,GAAG;AACZ,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAyB;IAC/DsB,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,SAAS;IACrBC,SAAS,EAAE,IAAI;IACf,IAAIR,IAAI,CAACS,QAAQ,CAAC,aAAa,CAAC,GAAG;MAAEC,aAAa,EAAE;IAAE,CAAC,GAAG,CAAC,CAAC;EAC9D,CAAC,CAAC;EAEF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG7B,QAAQ,CAAyB,CAAC,CAAC,CAAC;EAEhE,MAAM8B,iBAAiB,GAAGb,IAAI,CAACS,QAAQ,CAAC,aAAa,CAAC;EACtD,MAAMK,UAAU,GAAGd,IAAI,CAACS,QAAQ,CAAC,MAAM,CAAC;;EAExC;EACA,MAAMM,YAAY,GAAG,CACnB,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EACrD,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EACrD,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CACtD;EAED/B,SAAS,CAAC,MAAM;IACd,IAAI8B,UAAU,EAAE;MACd,IAAID,iBAAiB,IAAIf,WAAW,EAAE;QACpCM,WAAW,CAACN,WAAW,CAAC;MAC1B,CAAC,MAAM,IAAI,CAACe,iBAAiB,IAAIhB,QAAQ,EAAE;QACzCO,WAAW,CAACP,QAAQ,CAAC;MACvB;IACF,CAAC,MAAM;MACL;MACAO,WAAW,CAAC;QACVC,IAAI,EAAE,EAAE;QACRC,WAAW,EAAE,EAAE;QACfC,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,IAAI;QACf,IAAIK,iBAAiB,GAAG;UAAEH,aAAa,EAAE;QAAE,CAAC,GAAG,CAAC,CAAC;MACnD,CAAC,CAAC;IACJ;IACAE,SAAS,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,EAAE,CAACf,QAAQ,EAAEC,WAAW,EAAEE,IAAI,EAAEN,MAAM,CAAC,CAAC;EAEzC,MAAMsB,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAiC,GAAG,CAAC,CAAC;;IAE5C;IACA,IAAI,CAACd,QAAQ,CAACE,IAAI,CAACa,IAAI,CAAC,CAAC,EAAE;MACzBD,SAAS,CAACZ,IAAI,GAAG,GAAGQ,iBAAiB,GAAG,aAAa,GAAG,UAAU,mBAAmB;IACvF,CAAC,MAAM,IAAIV,QAAQ,CAACE,IAAI,CAACa,IAAI,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC,EAAE;MAC1CF,SAAS,CAACZ,IAAI,GAAG,yCAAyC;IAC5D,CAAC,MAAM,IAAIF,QAAQ,CAACE,IAAI,CAACa,IAAI,CAAC,CAAC,CAACC,MAAM,GAAG,EAAE,EAAE;MAC3CF,SAAS,CAACZ,IAAI,GAAG,sCAAsC;IACzD;;IAEA;IACA,IAAIF,QAAQ,CAACG,WAAW,IAAIH,QAAQ,CAACG,WAAW,CAACa,MAAM,GAAG,GAAG,EAAE;MAC7DF,SAAS,CAACX,WAAW,GAAG,8CAA8C;IACxE;;IAEA;IACA,IAAI,CAACH,QAAQ,CAACI,UAAU,IAAI,CAAC,iBAAiB,CAACa,IAAI,CAACjB,QAAQ,CAACI,UAAU,CAAC,EAAE;MACxEU,SAAS,CAACV,UAAU,GAAG,6BAA6B;IACtD;;IAEA;IACA,IAAIM,iBAAiB,EAAE;MACrB,MAAMQ,YAAY,GAAIlB,QAAQ,CAAiBO,aAAa;MAC5D,IAAI,CAACW,YAAY,IAAIA,YAAY,GAAG,CAAC,IAAIA,YAAY,GAAG,GAAG,EAAE;QAC3DJ,SAAS,CAACP,aAAa,GAAG,yCAAyC;MACrE;IACF;IAEAE,SAAS,CAACK,SAAS,CAAC;IACpB,OAAOK,MAAM,CAACC,IAAI,CAACN,SAAS,CAAC,CAACE,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMK,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACV,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEA,IAAI;MACF,MAAMpB,MAAM,CAACO,QAAQ,EAAEJ,cAAc,IAAI4B,SAAS,CAAC;MACnDhC,OAAO,CAAC,CAAC;IACX,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC;EACF,CAAC;EAED,MAAME,iBAAiB,GAAGA,CAACC,KAAa,EAAEC,KAAU,KAAK;IACvD5B,WAAW,CAAC6B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACF,KAAK,GAAGC;IACX,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIrB,MAAM,CAACoB,KAAK,CAAC,EAAE;MACjBnB,SAAS,CAACqB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACF,KAAK,GAAG;MAAG,CAAC,CAAC,CAAC;IAC/C;EACF,CAAC;EAED,MAAMG,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQlC,IAAI;MACV,KAAK,cAAc;QAAE,OAAO,kBAAkB;MAC9C,KAAK,eAAe;QAAE,OAAO,eAAe;MAC5C,KAAK,iBAAiB;QAAE,OAAO,uBAAuBD,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEM,IAAI,GAAG;MAC7E,KAAK,kBAAkB;QAAE,OAAO,kBAAkB;MAClD;QAAS,OAAO,qBAAqB;IACvC;EACF,CAAC;EAED,IAAI,CAACX,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEF,OAAA;IAAK2C,KAAK,EAAE;MACVC,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,UAAU,EAAE,oBAAoB;MAChCC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE;IACX,CAAE;IAAAC,QAAA,eACAvD,OAAA;MAAK2C,KAAK,EAAE;QACVM,UAAU,EAAE,OAAO;QACnBO,YAAY,EAAE,MAAM;QACpBC,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE,OAAO;QACjBC,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE,QAAQ;QAClBC,SAAS,EAAE;MACb,CAAE;MAAAN,QAAA,gBAEAvD,OAAA;QAAK2C,KAAK,EAAE;UACVO,OAAO,EAAE,MAAM;UACfE,cAAc,EAAE,eAAe;UAC/BD,UAAU,EAAE,QAAQ;UACpBG,OAAO,EAAE,QAAQ;UACjBQ,YAAY,EAAE;QAChB,CAAE;QAAAP,QAAA,gBACAvD,OAAA;UAAI2C,KAAK,EAAE;YACToB,MAAM,EAAE,CAAC;YACTC,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE,KAAK;YACjBC,KAAK,EAAE,SAAS;YAChBhB,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBgB,GAAG,EAAE;UACP,CAAE;UAAAZ,QAAA,GACClC,iBAAiB,gBAAGrB,OAAA,CAACL,GAAG;YAACyE,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGxE,OAAA,CAACN,UAAU;YAAC0E,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAChE9B,aAAa,CAAC,CAAC;QAAA;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eAELxE,OAAA;UACEyE,OAAO,EAAEtE,OAAQ;UACjBwC,KAAK,EAAE;YACLW,OAAO,EAAE,QAAQ;YACjBL,UAAU,EAAE,aAAa;YACzByB,MAAM,EAAE,MAAM;YACdlB,YAAY,EAAE,KAAK;YACnBmB,MAAM,EAAE,SAAS;YACjBT,KAAK,EAAE;UACT,CAAE;UAAAX,QAAA,eAEFvD,OAAA,CAACP,CAAC;YAAC2E,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNxE,OAAA;QAAM4E,QAAQ,EAAE5C,YAAa;QAACW,KAAK,EAAE;UAAEW,OAAO,EAAE,QAAQ;UAAEuB,SAAS,EAAE,MAAM;UAAElB,SAAS,EAAE;QAAqB,CAAE;QAAAJ,QAAA,gBAC7GvD,OAAA;UAAK2C,KAAK,EAAE;YAAEO,OAAO,EAAE,MAAM;YAAE4B,aAAa,EAAE,QAAQ;YAAEX,GAAG,EAAE;UAAS,CAAE;UAAAZ,QAAA,gBAEtEvD,OAAA;YAAAuD,QAAA,gBACEvD,OAAA;cAAO2C,KAAK,EAAE;gBACZO,OAAO,EAAE,OAAO;gBAChBc,QAAQ,EAAE,UAAU;gBACpBC,UAAU,EAAE,KAAK;gBACjBC,KAAK,EAAE,SAAS;gBAChBa,YAAY,EAAE;cAChB,CAAE;cAAAxB,QAAA,gBACAvD,OAAA,CAACH,QAAQ;gBAACuE,IAAI,EAAE,EAAG;gBAACzB,KAAK,EAAE;kBAAEO,OAAO,EAAE,QAAQ;kBAAE8B,WAAW,EAAE;gBAAS;cAAE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAC1EnD,iBAAiB,GAAG,aAAa,GAAG,UAAU,EAAC,SAClD;YAAA;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxE,OAAA;cACEiF,IAAI,EAAC,MAAM;cACXzC,KAAK,EAAE7B,QAAQ,CAACE,IAAK;cACrBqE,QAAQ,EAAGjD,CAAC,IAAKK,iBAAiB,CAAC,MAAM,EAAEL,CAAC,CAACkD,MAAM,CAAC3C,KAAK,CAAE;cAC3DG,KAAK,EAAE;gBACLc,KAAK,EAAE,MAAM;gBACbH,OAAO,EAAE,SAAS;gBAClBoB,MAAM,EAAE,aAAavD,MAAM,CAACN,IAAI,GAAG,SAAS,GAAG,SAAS,EAAE;gBAC1D2C,YAAY,EAAE,KAAK;gBACnBQ,QAAQ,EAAE;cACZ,CAAE;cACFoB,WAAW,EAAE,SAAS/D,iBAAiB,GAAG,aAAa,GAAG,UAAU;YAAQ;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC,EACDrD,MAAM,CAACN,IAAI,iBACVb,OAAA;cAAG2C,KAAK,EAAE;gBAAEoB,MAAM,EAAE,aAAa;gBAAEC,QAAQ,EAAE,SAAS;gBAAEE,KAAK,EAAE;cAAU,CAAE;cAAAX,QAAA,EACxEpC,MAAM,CAACN;YAAI;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CACJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNxE,OAAA;YAAAuD,QAAA,gBACEvD,OAAA;cAAO2C,KAAK,EAAE;gBACZO,OAAO,EAAE,OAAO;gBAChBc,QAAQ,EAAE,UAAU;gBACpBC,UAAU,EAAE,KAAK;gBACjBC,KAAK,EAAE,SAAS;gBAChBa,YAAY,EAAE;cAChB,CAAE;cAAAxB,QAAA,EAAC;YAEH;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxE,OAAA;cACEwC,KAAK,EAAE7B,QAAQ,CAACG,WAAW,IAAI,EAAG;cAClCoE,QAAQ,EAAGjD,CAAC,IAAKK,iBAAiB,CAAC,aAAa,EAAEL,CAAC,CAACkD,MAAM,CAAC3C,KAAK,CAAE;cAClE6C,IAAI,EAAE,CAAE;cACR1C,KAAK,EAAE;gBACLc,KAAK,EAAE,MAAM;gBACbH,OAAO,EAAE,SAAS;gBAClBoB,MAAM,EAAE,aAAavD,MAAM,CAACL,WAAW,GAAG,SAAS,GAAG,SAAS,EAAE;gBACjE0C,YAAY,EAAE,KAAK;gBACnBQ,QAAQ,EAAE,UAAU;gBACpBsB,MAAM,EAAE;cACV,CAAE;cACFF,WAAW,EAAE,4BAA4B/D,iBAAiB,GAAG,aAAa,GAAG,UAAU;YAAG;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F,CAAC,EACDrD,MAAM,CAACL,WAAW,iBACjBd,OAAA;cAAG2C,KAAK,EAAE;gBAAEoB,MAAM,EAAE,aAAa;gBAAEC,QAAQ,EAAE,SAAS;gBAAEE,KAAK,EAAE;cAAU,CAAE;cAAAX,QAAA,EACxEpC,MAAM,CAACL;YAAW;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CACJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNxE,OAAA;YAAAuD,QAAA,gBACEvD,OAAA;cAAO2C,KAAK,EAAE;gBACZO,OAAO,EAAE,OAAO;gBAChBc,QAAQ,EAAE,UAAU;gBACpBC,UAAU,EAAE,KAAK;gBACjBC,KAAK,EAAE,SAAS;gBAChBa,YAAY,EAAE;cAChB,CAAE;cAAAxB,QAAA,gBACAvD,OAAA,CAACJ,OAAO;gBAACwE,IAAI,EAAE,EAAG;gBAACzB,KAAK,EAAE;kBAAEO,OAAO,EAAE,QAAQ;kBAAE8B,WAAW,EAAE;gBAAS;cAAE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,WAE5E;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAGRxE,OAAA;cAAK2C,KAAK,EAAE;gBACVO,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBgB,GAAG,EAAE,MAAM;gBACXY,YAAY,EAAE;cAChB,CAAE;cAAAxB,QAAA,gBACAvD,OAAA;gBAAK2C,KAAK,EAAE;kBACVc,KAAK,EAAE,MAAM;kBACb8B,MAAM,EAAE,MAAM;kBACdtC,UAAU,EAAEtC,QAAQ,CAACI,UAAU;kBAC/ByC,YAAY,EAAE,KAAK;kBACnBN,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBC,cAAc,EAAE,QAAQ;kBACxBS,SAAS,EAAE;gBACb,CAAE;gBAAAN,QAAA,EACClC,iBAAiB,gBAAGrB,OAAA,CAACL,GAAG;kBAACyE,IAAI,EAAE,EAAG;kBAACF,KAAK,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGxE,OAAA,CAACN,UAAU;kBAAC0E,IAAI,EAAE,EAAG;kBAACF,KAAK,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1F,CAAC,eAENxE,OAAA;gBACEiF,IAAI,EAAC,MAAM;gBACXzC,KAAK,EAAE7B,QAAQ,CAACI,UAAW;gBAC3BmE,QAAQ,EAAGjD,CAAC,IAAKK,iBAAiB,CAAC,YAAY,EAAEL,CAAC,CAACkD,MAAM,CAAC3C,KAAK,CAAE;gBACjEG,KAAK,EAAE;kBACL6C,IAAI,EAAE,CAAC;kBACPlC,OAAO,EAAE,QAAQ;kBACjBoB,MAAM,EAAE,aAAavD,MAAM,CAACJ,UAAU,GAAG,SAAS,GAAG,SAAS,EAAE;kBAChEyC,YAAY,EAAE,KAAK;kBACnBQ,QAAQ,EAAE,UAAU;kBACpByB,UAAU,EAAE;gBACd,CAAE;gBACFL,WAAW,EAAC;cAAS;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNxE,OAAA;cAAK2C,KAAK,EAAE;gBACVO,OAAO,EAAE,MAAM;gBACfwC,mBAAmB,EAAE,qCAAqC;gBAC1DvB,GAAG,EAAE;cACP,CAAE;cAAAZ,QAAA,EACChC,YAAY,CAACoE,GAAG,CAAEzB,KAAK,iBACtBlE,OAAA;gBAEEiF,IAAI,EAAC,QAAQ;gBACbR,OAAO,EAAEA,CAAA,KAAMnC,iBAAiB,CAAC,YAAY,EAAE4B,KAAK,CAAE;gBACtDvB,KAAK,EAAE;kBACLc,KAAK,EAAE,MAAM;kBACb8B,MAAM,EAAE,MAAM;kBACdtC,UAAU,EAAEiB,KAAK;kBACjBQ,MAAM,EAAE/D,QAAQ,CAACI,UAAU,KAAKmD,KAAK,GAAG,mBAAmB,GAAG,mBAAmB;kBACjFV,YAAY,EAAE,KAAK;kBACnBmB,MAAM,EAAE,SAAS;kBACjBzB,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBC,cAAc,EAAE;gBAClB,CAAE;gBAAAG,QAAA,EAED5C,QAAQ,CAACI,UAAU,KAAKmD,KAAK,iBAC5BlE,OAAA;kBAAK2C,KAAK,EAAE;oBACVc,KAAK,EAAE,KAAK;oBACZ8B,MAAM,EAAE,KAAK;oBACbtC,UAAU,EAAE,OAAO;oBACnBO,YAAY,EAAE;kBAChB;gBAAE;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cACL,GAtBIN,KAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuBJ,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAELrD,MAAM,CAACJ,UAAU,iBAChBf,OAAA;cAAG2C,KAAK,EAAE;gBAAEoB,MAAM,EAAE,aAAa;gBAAEC,QAAQ,EAAE,SAAS;gBAAEE,KAAK,EAAE;cAAU,CAAE;cAAAX,QAAA,EACxEpC,MAAM,CAACJ;YAAU;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CACJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGLnD,iBAAiB,iBAChBrB,OAAA;YAAAuD,QAAA,gBACEvD,OAAA;cAAO2C,KAAK,EAAE;gBACZO,OAAO,EAAE,OAAO;gBAChBc,QAAQ,EAAE,UAAU;gBACpBC,UAAU,EAAE,KAAK;gBACjBC,KAAK,EAAE,SAAS;gBAChBa,YAAY,EAAE;cAChB,CAAE;cAAAxB,QAAA,gBACAvD,OAAA,CAACF,IAAI;gBAACsE,IAAI,EAAE,EAAG;gBAACzB,KAAK,EAAE;kBAAEO,OAAO,EAAE,QAAQ;kBAAE8B,WAAW,EAAE;gBAAS;cAAE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAEzE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxE,OAAA;cACEiF,IAAI,EAAC,QAAQ;cACbW,GAAG,EAAC,GAAG;cACPC,GAAG,EAAC,KAAK;cACTrD,KAAK,EAAG7B,QAAQ,CAAiBO,aAAa,IAAI,CAAE;cACpDgE,QAAQ,EAAGjD,CAAC,IAAKK,iBAAiB,CAAC,eAAe,EAAEwD,QAAQ,CAAC7D,CAAC,CAACkD,MAAM,CAAC3C,KAAK,CAAC,IAAI,CAAC,CAAE;cACnFG,KAAK,EAAE;gBACLc,KAAK,EAAE,MAAM;gBACbH,OAAO,EAAE,SAAS;gBAClBoB,MAAM,EAAE,aAAavD,MAAM,CAACD,aAAa,GAAG,SAAS,GAAG,SAAS,EAAE;gBACnEsC,YAAY,EAAE,KAAK;gBACnBQ,QAAQ,EAAE;cACZ,CAAE;cACFoB,WAAW,EAAC;YAAG;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACFxE,OAAA;cAAG2C,KAAK,EAAE;gBAAEoB,MAAM,EAAE,aAAa;gBAAEC,QAAQ,EAAE,SAAS;gBAAEE,KAAK,EAAE;cAAU,CAAE;cAAAX,QAAA,EAAC;YAE5E;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EACHrD,MAAM,CAACD,aAAa,iBACnBlB,OAAA;cAAG2C,KAAK,EAAE;gBAAEoB,MAAM,EAAE,aAAa;gBAAEC,QAAQ,EAAE,SAAS;gBAAEE,KAAK,EAAE;cAAU,CAAE;cAAAX,QAAA,EACxEpC,MAAM,CAACD;YAAa;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CACJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,eAGDxE,OAAA;YAAAuD,QAAA,gBACEvD,OAAA;cAAO2C,KAAK,EAAE;gBACZO,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBgB,GAAG,EAAE,QAAQ;gBACbQ,MAAM,EAAE;cACV,CAAE;cAAApB,QAAA,gBACAvD,OAAA;gBACEiF,IAAI,EAAC,UAAU;gBACfc,OAAO,EAAEpF,QAAQ,CAACK,SAAU;gBAC5BkE,QAAQ,EAAGjD,CAAC,IAAKK,iBAAiB,CAAC,WAAW,EAAEL,CAAC,CAACkD,MAAM,CAACY,OAAO,CAAE;gBAClEpD,KAAK,EAAE;kBACLc,KAAK,EAAE,MAAM;kBACb8B,MAAM,EAAE,MAAM;kBACdZ,MAAM,EAAE;gBACV;cAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFxE,OAAA;gBAAM2C,KAAK,EAAE;kBACXqB,QAAQ,EAAE,UAAU;kBACpBC,UAAU,EAAE,KAAK;kBACjBC,KAAK,EAAE;gBACT,CAAE;gBAAAX,QAAA,EAAC;cAEH;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACRxE,OAAA;cAAG2C,KAAK,EAAE;gBAAEoB,MAAM,EAAE,aAAa;gBAAEC,QAAQ,EAAE,SAAS;gBAAEE,KAAK,EAAE;cAAU,CAAE;cAAAX,QAAA,GAAC,WACjE,EAAClC,iBAAiB,GAAG,eAAe,GAAG,YAAY,EAAC,mCAC/D;YAAA;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNxE,OAAA;UAAK2C,KAAK,EAAE;YACVO,OAAO,EAAE,MAAM;YACfiB,GAAG,EAAE,MAAM;YACXf,cAAc,EAAE,UAAU;YAC1B4C,SAAS,EAAE,MAAM;YACjBC,UAAU,EAAE,QAAQ;YACpBC,SAAS,EAAE;UACb,CAAE;UAAA3C,QAAA,gBACAvD,OAAA;YACEiF,IAAI,EAAC,QAAQ;YACbR,OAAO,EAAEtE,OAAQ;YACjBgG,QAAQ,EAAE1F,OAAQ;YAClBkC,KAAK,EAAE;cACLW,OAAO,EAAE,gBAAgB;cACzBL,UAAU,EAAE,SAAS;cACrBiB,KAAK,EAAE,SAAS;cAChBQ,MAAM,EAAE,MAAM;cACdlB,YAAY,EAAE,KAAK;cACnBmB,MAAM,EAAElE,OAAO,GAAG,aAAa,GAAG,SAAS;cAC3CuD,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE;YACd,CAAE;YAAAV,QAAA,EACH;UAED;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETxE,OAAA;YACEiF,IAAI,EAAC,QAAQ;YACbkB,QAAQ,EAAE1F,OAAQ;YAClBkC,KAAK,EAAE;cACLW,OAAO,EAAE,gBAAgB;cACzBL,UAAU,EAAExC,OAAO,GAAG,SAAS,GAAG,SAAS;cAC3CyD,KAAK,EAAE,OAAO;cACdQ,MAAM,EAAE,MAAM;cACdlB,YAAY,EAAE,KAAK;cACnBmB,MAAM,EAAElE,OAAO,GAAG,aAAa,GAAG,SAAS;cAC3CuD,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBf,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBgB,GAAG,EAAE;YACP,CAAE;YAAAZ,QAAA,GAED9C,OAAO,iBACNT,OAAA;cAAK2C,KAAK,EAAE;gBACVc,KAAK,EAAE,MAAM;gBACb8B,MAAM,EAAE,MAAM;gBACdb,MAAM,EAAE,uBAAuB;gBAC/BwB,SAAS,EAAE,iBAAiB;gBAC5B1C,YAAY,EAAE,KAAK;gBACnB4C,SAAS,EAAE;cACb;YAAE;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CACL,EACA/D,OAAO,GAAG,WAAW,GAAIa,UAAU,GAAG,UAAUD,iBAAiB,GAAG,aAAa,GAAG,UAAU,EAAE,GAAG,UAAUA,iBAAiB,GAAG,aAAa,GAAG,UAAU,EAAG;UAAA;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9D,EAAA,CAxdIT,aAA2C;AAAAoG,EAAA,GAA3CpG,aAA2C;AA0djD,eAAeA,aAAa;AAAC,IAAAoG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}