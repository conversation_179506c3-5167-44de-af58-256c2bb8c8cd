ONLINE E-BULL<PERSON>IN BOARD SYSTEM - TECHNOLOGY STACK
================================================================

FRONTEND TECHNOLOGIES (React.js Application)
============================================

Core Framework & Language:
- React.js (v19.1.0) - Main frontend framework
- TypeScript (v4.9.5) - Type-safe JavaScript superset
- React DOM (v19.1.0) - DOM rendering for React
- React Router DOM (v6.0.0) - Client-side routing

Build Tools & Development:
- React Scripts (v5.0.1) - Create React App build toolchain
- Web Vitals (v5.0.3) - Performance monitoring

UI & Icons:
- Lucide React (v0.525.0) - Modern icon library

HTTP Client:
- Axios (v1.10.0) - HTTP client for API calls
- Form Data (v4.0.4) - Form data handling

Testing:
- Jest - Testing framework (via React Scripts)
- React Testing Library (v16.3.0) - React component testing
- Jest DOM (v6.6.3) - DOM testing utilities

BACKEND TECHNOLOGIES (Node.js Application)
==========================================

Core Runtime & Framework:
- Node.js (≥18.0.0) - JavaScript runtime
- Express.js (v4.18.2) - Web application framework
- Express Async Errors (v3.1.1) - Error handling middleware

Database:
- MySQL (v8.0) - Primary database

Authentication & Security:
- JWT (jsonwebtoken) (v9.0.2) - JSON Web Token authentication
- bcrypt (v6.0.0) & bcryptjs (v2.4.3) - Password hashing
- Helmet (v7.1.0) - Security headers
- CORS (v2.8.5) - Cross-origin resource sharing
- Express Rate Limit (v7.1.5) - Rate limiting
- Express Validator (v7.0.1) - Input validation
- Joi (v17.11.0) - Schema validation

Real-time Communication:
- Socket.io (v4.7.4) - WebSocket implementation for real-time features

File Handling & Email:
- Multer (v1.4.5-lts.1) - File upload middleware
- Nodemailer (v6.9.7) - Email sending

Logging & Monitoring:
- Winston (v3.11.0) - Logging library
- Winston Daily Rotate File (v4.7.1) - Log rotation
- Morgan (v1.10.0) - HTTP request logging

Utilities & Scheduling:
- Cron (v3.1.6) - Task scheduling
- Date Holidays (v3.24.4) - Holiday calculations
- Compression (v1.7.4) - Response compression
- Axios (v1.10.0) - HTTP client
- dotenv (v16.3.1) - Environment variable management

DEVELOPMENT & DEVOPS TECHNOLOGIES
=================================

Containerization:
- Docker - Application containerization
- Docker Compose (v3.8) - Multi-container orchestration
- Nginx - Reverse proxy and web server

Development Utilities:
- HTTP Proxy Middleware (v3.0.5) - Development proxy

DATABASE & INFRASTRUCTURE
=========================
- MySQL 8.0 - Relational database
- Redis 7 - In-memory data store for caching

ARCHITECTURE PATTERNS
=====================
- Full-stack separation - Clear backend/frontend division
- RESTful API - Backend serves API endpoints
- JWT-based authentication - Stateless authentication
- Role-based access control - User permission system
- Docker containerization - Production deployment ready
- Professional logging system - Comprehensive error tracking