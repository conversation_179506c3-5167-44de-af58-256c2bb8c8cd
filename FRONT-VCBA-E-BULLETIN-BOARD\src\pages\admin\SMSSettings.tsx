import React, { useState, useEffect } from 'react';
import { MessageSquare, Send, Settings, AlertTriangle, CheckCircle, Phone, Globe, Key } from 'lucide-react';
import { useAdminAuth } from '../../contexts/AdminAuthContext';
import { usePermissions } from '../../utils/permissions';

interface SMSConfig {
  apiKey: string;
  apiUrl: string;
  isEnabled: boolean;
  defaultSender: string;
  maxMessageLength: number;
  rateLimitPerMinute: number;
}

interface SMSTemplate {
  id: string;
  name: string;
  content: string;
  category: 'announcement' | 'emergency' | 'reminder' | 'general';
}

const SMSSettings: React.FC = () => {
  const { user } = useAdminAuth();
  const permissions = usePermissions(user);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'config' | 'templates' | 'test'>('config');

  // SMS Configuration State
  const [smsConfig, setSmsConfig] = useState<SMSConfig>({
    apiKey: '',
    apiUrl: '',
    isEnabled: false,
    defaultSender: 'VCBA',
    maxMessageLength: 160,
    rateLimitPerMinute: 10
  });

  // SMS Templates State
  const [templates, setTemplates] = useState<SMSTemplate[]>([
    {
      id: '1',
      name: 'Class Cancellation',
      content: 'NOTICE: Your {subject} class scheduled for {time} on {date} has been cancelled. Please check the bulletin board for updates.',
      category: 'announcement'
    },
    {
      id: '2',
      name: 'Emergency Alert',
      content: 'EMERGENCY: {message}. Please follow safety protocols and await further instructions.',
      category: 'emergency'
    },
    {
      id: '3',
      name: 'Event Reminder',
      content: 'REMINDER: {event_name} is scheduled for {date} at {time}. Location: {venue}. Don\'t miss it!',
      category: 'reminder'
    }
  ]);

  // Test SMS State
  const [testPhone, setTestPhone] = useState('');
  const [testMessage, setTestMessage] = useState('This is a test message from VCBA E-Bulletin Board system.');
  const [testSending, setTestSending] = useState(false);

  useEffect(() => {
    // Check if user has permission to manage SMS settings
    if (!permissions.canManageSMSSettings) {
      setError('You do not have permission to manage SMS settings');
      setLoading(false);
      return;
    }

    loadSMSSettings();
  }, [permissions.canManageSMSSettings]);

  const loadSMSSettings = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // TODO: Implement API call to fetch SMS settings
      // const response = await smsService.getSettings();
      // setSmsConfig(response.data.config);
      
      // Mock data for now - in production, load from backend
      setTimeout(() => {
        setSmsConfig({
          apiKey: '••••••••••••••••',
          apiUrl: 'https://api.semaphore.co',
          isEnabled: true,
          defaultSender: 'VCBA',
          maxMessageLength: 160,
          rateLimitPerMinute: 10
        });
        setLoading(false);
      }, 1000);
      
    } catch (err) {
      setError('Failed to load SMS settings');
      console.error('Error loading SMS settings:', err);
      setLoading(false);
    }
  };

  const handleSaveConfig = async () => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(null);
      
      // TODO: Implement API call to save SMS settings
      // await smsService.updateSettings(smsConfig);
      
      // Mock save for now
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSuccess('SMS settings saved successfully!');
      setTimeout(() => setSuccess(null), 3000);
      
    } catch (err) {
      setError('Failed to save SMS settings');
      console.error('Error saving SMS settings:', err);
    } finally {
      setSaving(false);
    }
  };

  const handleTestSMS = async () => {
    if (!testPhone.trim() || !testMessage.trim()) {
      setError('Please enter both phone number and message');
      return;
    }

    try {
      setTestSending(true);
      setError(null);
      setSuccess(null);
      
      // TODO: Implement API call to send test SMS
      // await smsService.sendTestMessage(testPhone, testMessage);
      
      // Mock send for now
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setSuccess(`Test SMS sent successfully to ${testPhone}!`);
      setTimeout(() => setSuccess(null), 3000);
      
    } catch (err) {
      setError('Failed to send test SMS');
      console.error('Error sending test SMS:', err);
    } finally {
      setTestSending(false);
    }
  };

  // Permission check
  if (!permissions.canManageSMSSettings) {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '400px',
        textAlign: 'center',
        color: '#6b7280'
      }}>
        <MessageSquare size={64} style={{ marginBottom: '1rem', opacity: 0.5 }} />
        <h2 style={{ margin: '0 0 0.5rem', fontSize: '1.5rem', fontWeight: '600' }}>
          Access Denied
        </h2>
        <p style={{ margin: 0, fontSize: '1rem' }}>
          You do not have permission to manage SMS settings.
        </p>
        <div style={{
          marginTop: '1rem',
          padding: '0.5rem 1rem',
          background: permissions.getPositionBadgeColor(),
          borderRadius: '6px',
          color: 'white',
          fontSize: '0.875rem',
          fontWeight: '600'
        }}>
          Current Role: {permissions.getPositionDisplayName()}
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '400px'
      }}>
        <div style={{
          width: '40px',
          height: '40px',
          border: '4px solid #f3f4f6',
          borderTop: '4px solid #3b82f6',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }} />
      </div>
    );
  }

  const tabs = [
    { key: 'config', label: 'Configuration', icon: Settings },
    { key: 'templates', label: 'Templates', icon: MessageSquare },
    { key: 'test', label: 'Test SMS', icon: Send }
  ];

  return (
    <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '2rem'
      }}>
        <div>
          <h1 style={{
            margin: '0 0 0.5rem',
            fontSize: '2rem',
            fontWeight: '700',
            color: '#1f2937'
          }}>
            SMS Settings
          </h1>
          <p style={{
            margin: 0,
            color: '#6b7280',
            fontSize: '1rem'
          }}>
            Configure SMS notifications and messaging settings
          </p>
        </div>
        
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem',
          padding: '0.75rem 1rem',
          background: smsConfig.isEnabled ? '#dcfce7' : '#fef2f2',
          color: smsConfig.isEnabled ? '#166534' : '#dc2626',
          borderRadius: '8px',
          fontSize: '0.875rem',
          fontWeight: '600'
        }}>
          <Phone size={16} />
          SMS {smsConfig.isEnabled ? 'Enabled' : 'Disabled'}
        </div>
      </div>

      {/* Error/Success Messages */}
      {error && (
        <div style={{
          padding: '1rem',
          background: '#fef2f2',
          border: '1px solid #fecaca',
          borderRadius: '8px',
          color: '#dc2626',
          marginBottom: '1rem',
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem'
        }}>
          <AlertTriangle size={16} />
          {error}
        </div>
      )}

      {success && (
        <div style={{
          padding: '1rem',
          background: '#f0fdf4',
          border: '1px solid #bbf7d0',
          borderRadius: '8px',
          color: '#166534',
          marginBottom: '1rem',
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem'
        }}>
          <CheckCircle size={16} />
          {success}
        </div>
      )}

      {/* Tabs */}
      <div style={{
        display: 'flex',
        borderBottom: '1px solid #e5e7eb',
        marginBottom: '2rem'
      }}>
        {tabs.map((tab) => {
          const Icon = tab.icon;
          return (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                padding: '1rem 1.5rem',
                border: 'none',
                background: 'transparent',
                color: activeTab === tab.key ? '#3b82f6' : '#6b7280',
                borderBottom: activeTab === tab.key ? '2px solid #3b82f6' : '2px solid transparent',
                cursor: 'pointer',
                fontSize: '0.875rem',
                fontWeight: '600',
                transition: 'all 0.2s'
              }}
            >
              <Icon size={16} />
              {tab.label}
            </button>
          );
        })}
      </div>

      {/* Tab Content */}
      {activeTab === 'config' && (
        <div style={{
          background: 'white',
          borderRadius: '12px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          padding: '2rem'
        }}>
          <h3 style={{
            margin: '0 0 1.5rem',
            fontSize: '1.25rem',
            fontWeight: '600',
            color: '#1f2937'
          }}>
            SMS Configuration
          </h3>

          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '1.5rem',
            marginBottom: '2rem'
          }}>
            <div>
              <label style={{
                display: 'block',
                fontSize: '0.875rem',
                fontWeight: '600',
                color: '#374151',
                marginBottom: '0.5rem'
              }}>
                <Key size={16} style={{ display: 'inline', marginRight: '0.5rem' }} />
                API Key
              </label>
              <input
                type="password"
                value={smsConfig.apiKey}
                onChange={(e) => setSmsConfig({ ...smsConfig, apiKey: e.target.value })}
                placeholder="Enter your SMS API key"
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '0.875rem'
                }}
              />
            </div>

            <div>
              <label style={{
                display: 'block',
                fontSize: '0.875rem',
                fontWeight: '600',
                color: '#374151',
                marginBottom: '0.5rem'
              }}>
                <Globe size={16} style={{ display: 'inline', marginRight: '0.5rem' }} />
                API URL
              </label>
              <input
                type="url"
                value={smsConfig.apiUrl}
                onChange={(e) => setSmsConfig({ ...smsConfig, apiUrl: e.target.value })}
                placeholder="https://api.sms-provider.com"
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '0.875rem'
                }}
              />
            </div>

            <div>
              <label style={{
                display: 'block',
                fontSize: '0.875rem',
                fontWeight: '600',
                color: '#374151',
                marginBottom: '0.5rem'
              }}>
                Default Sender ID
              </label>
              <input
                type="text"
                value={smsConfig.defaultSender}
                onChange={(e) => setSmsConfig({ ...smsConfig, defaultSender: e.target.value })}
                placeholder="VCBA"
                maxLength={11}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '0.875rem'
                }}
              />
            </div>

            <div>
              <label style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                fontSize: '0.875rem',
                fontWeight: '600',
                color: '#374151',
                marginBottom: '0.5rem'
              }}>
                <input
                  type="checkbox"
                  checked={smsConfig.isEnabled}
                  onChange={(e) => setSmsConfig({ ...smsConfig, isEnabled: e.target.checked })}
                  style={{ marginRight: '0.5rem' }}
                />
                Enable SMS Notifications
              </label>
              <p style={{
                fontSize: '0.75rem',
                color: '#6b7280',
                margin: 0
              }}>
                Allow the system to send SMS notifications to students and staff
              </p>
            </div>
          </div>

          <div style={{
            display: 'flex',
            justifyContent: 'flex-end',
            gap: '1rem',
            paddingTop: '1rem',
            borderTop: '1px solid #e5e7eb'
          }}>
            <button
              onClick={handleSaveConfig}
              disabled={saving}
              style={{
                padding: '0.75rem 1.5rem',
                background: saving ? '#9ca3af' : '#3b82f6',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                fontSize: '0.875rem',
                fontWeight: '600',
                cursor: saving ? 'not-allowed' : 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}
            >
              <Settings size={16} />
              {saving ? 'Saving...' : 'Save Configuration'}
            </button>
          </div>
        </div>
      )}

      {activeTab === 'test' && (
        <div style={{
          background: 'white',
          borderRadius: '12px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          padding: '2rem'
        }}>
          <h3 style={{
            margin: '0 0 1.5rem',
            fontSize: '1.25rem',
            fontWeight: '600',
            color: '#1f2937'
          }}>
            Test SMS Functionality
          </h3>

          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '1.5rem',
            marginBottom: '2rem'
          }}>
            <div>
              <label style={{
                display: 'block',
                fontSize: '0.875rem',
                fontWeight: '600',
                color: '#374151',
                marginBottom: '0.5rem'
              }}>
                Phone Number
              </label>
              <input
                type="tel"
                value={testPhone}
                onChange={(e) => setTestPhone(e.target.value)}
                placeholder="+639123456789"
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '0.875rem'
                }}
              />
            </div>

            <div style={{ gridColumn: '1 / -1' }}>
              <label style={{
                display: 'block',
                fontSize: '0.875rem',
                fontWeight: '600',
                color: '#374151',
                marginBottom: '0.5rem'
              }}>
                Test Message
              </label>
              <textarea
                value={testMessage}
                onChange={(e) => setTestMessage(e.target.value)}
                placeholder="Enter your test message here..."
                rows={4}
                maxLength={160}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '0.875rem',
                  resize: 'vertical'
                }}
              />
              <div style={{
                fontSize: '0.75rem',
                color: '#6b7280',
                textAlign: 'right',
                marginTop: '0.25rem'
              }}>
                {testMessage.length}/160 characters
              </div>
            </div>
          </div>

          <div style={{
            display: 'flex',
            justifyContent: 'flex-end',
            gap: '1rem',
            paddingTop: '1rem',
            borderTop: '1px solid #e5e7eb'
          }}>
            <button
              onClick={handleTestSMS}
              disabled={testSending || !testPhone.trim() || !testMessage.trim()}
              style={{
                padding: '0.75rem 1.5rem',
                background: testSending ? '#9ca3af' : '#10b981',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                fontSize: '0.875rem',
                fontWeight: '600',
                cursor: testSending ? 'not-allowed' : 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}
            >
              <Send size={16} />
              {testSending ? 'Sending...' : 'Send Test SMS'}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default SMSSettings;
