const BaseModel = require('./BaseModel');
const { ValidationError, ConflictError, NotFoundError } = require('../middleware/errorHandler');

class CategoryModel extends BaseModel {
  constructor() {
    super('categories', 'category_id');
  }

  // Get all active categories
  async getCategories() {
    const sql = `
      SELECT 
        category_id,
        name,
        description,
        color_code,
        is_active,
        created_at,
        updated_at
      FROM categories 
      WHERE is_active = 1 
      ORDER BY name
    `;
    return await this.db.query(sql);
  }

  // Get categories with their subcategories
  async getCategoriesWithSubcategories() {
    const sql = `
      SELECT
        c.category_id,
        c.name as category_name,
        c.description as category_description,
        c.color_code as category_color,
        c.is_active as category_active,
        s.subcategory_id,
        s.name as subcategory_name,
        s.description as subcategory_description,
        s.color_code as subcategory_color,
        s.is_active as subcategory_active,
        s.display_order
      FROM categories c
      LEFT JOIN subcategories s ON c.category_id = s.category_id AND s.is_active = 1
      WHERE c.is_active = 1
      ORDER BY c.name, s.display_order, s.name
    `;

    const rows = await this.db.query(sql);
    
    // Group subcategories under their categories
    const categoriesMap = new Map();
    
    rows.forEach(row => {
      if (!categoriesMap.has(row.category_id)) {
        categoriesMap.set(row.category_id, {
          category_id: row.category_id,
          name: row.category_name,
          description: row.category_description,
          color_code: row.category_color,
          is_active: row.category_active,
          subcategories: []
        });
      }
      
      if (row.subcategory_id) {
        categoriesMap.get(row.category_id).subcategories.push({
          subcategory_id: row.subcategory_id,
          name: row.subcategory_name,
          description: row.subcategory_description,
          color_code: row.subcategory_color,
          is_active: row.subcategory_active,
          display_order: row.display_order
        });
      }
    });
    
    return Array.from(categoriesMap.values());
  }

  // Get subcategories by category ID
  async getSubcategoriesByCategory(categoryId) {
    const sql = `
      SELECT
        s.*,
        c.name as category_name,
        c.color_code as category_color
      FROM subcategories s
      LEFT JOIN categories c ON s.category_id = c.category_id
      WHERE s.category_id = ? AND s.is_active = 1 AND c.is_active = 1
      ORDER BY s.display_order, s.name
    `;
    return await this.db.query(sql, [categoryId]);
  }

  // Get all subcategories
  async getSubcategories() {
    const sql = `
      SELECT
        s.*,
        c.name as category_name,
        c.color_code as category_color
      FROM subcategories s
      LEFT JOIN categories c ON s.category_id = c.category_id
      WHERE s.is_active = 1 AND c.is_active = 1
      ORDER BY c.name, s.display_order, s.name
    `;
    return await this.db.query(sql);
  }

  // Create category
  async createCategory(categoryData) {
    // Validate required fields
    this.validateRequired(categoryData, ['name']);

    // Check if category name already exists
    const existingCategory = await this.db.findOne(
      'SELECT category_id FROM categories WHERE name = ?',
      [categoryData.name]
    );

    if (existingCategory) {
      throw new ConflictError('Category name already exists');
    }

    // Prepare category data
    const category = {
      name: categoryData.name,
      description: categoryData.description || null,
      color_code: categoryData.color_code || '#007bff',
      is_active: categoryData.is_active !== undefined ? categoryData.is_active : true,
      created_by: categoryData.created_by || null,
      created_at: new Date(),
      updated_at: new Date(),
    };

    const result = await this.db.insert('categories', category);
    
    return await this.getCategoryById(result.insertId);
  }

  // Update category
  async updateCategory(categoryId, updateData) {
    // Check if category exists
    const existingCategory = await this.getCategoryById(categoryId);
    if (!existingCategory) {
      throw new NotFoundError('Category not found');
    }

    // Check if new name conflicts with existing categories (excluding current)
    if (updateData.name) {
      const nameConflict = await this.db.findOne(
        'SELECT category_id FROM categories WHERE name = ? AND category_id != ?',
        [updateData.name, categoryId]
      );

      if (nameConflict) {
        throw new ConflictError('Category name already exists');
      }
    }

    // Prepare update data
    const allowedFields = ['name', 'description', 'color_code', 'is_active', 'updated_by'];
    const filteredData = {};
    
    allowedFields.forEach(field => {
      if (updateData[field] !== undefined) {
        filteredData[field] = updateData[field];
      }
    });

    if (Object.keys(filteredData).length === 0) {
      throw new ValidationError('No valid fields to update');
    }

    filteredData.updated_at = new Date();

    const result = await this.db.update(
      'categories',
      filteredData,
      'category_id = ?',
      [categoryId]
    );

    if (result.affectedRows === 0) {
      throw new NotFoundError('Category not found');
    }

    return await this.getCategoryById(categoryId);
  }

  // Delete category (soft delete by setting is_active = false)
  async deleteCategory(categoryId) {
    // Check if category exists
    const existingCategory = await this.getCategoryById(categoryId);
    if (!existingCategory) {
      throw new NotFoundError('Category not found');
    }

    // Check if category has active subcategories
    const activeSubcategories = await this.db.findOne(
      'SELECT COUNT(*) as count FROM subcategories WHERE category_id = ? AND is_active = 1',
      [categoryId]
    );

    if (activeSubcategories.count > 0) {
      throw new ValidationError('Cannot delete category with active subcategories');
    }

    // Soft delete by setting is_active = false
    const result = await this.db.update(
      'categories',
      { is_active: false, updated_at: new Date() },
      'category_id = ?',
      [categoryId]
    );

    if (result.affectedRows === 0) {
      throw new NotFoundError('Category not found');
    }

    return true;
  }

  // Get category by ID
  async getCategoryById(categoryId) {
    const sql = `
      SELECT 
        category_id,
        name,
        description,
        color_code,
        is_active,
        created_by,
        created_at,
        updated_at
      FROM categories 
      WHERE category_id = ?
    `;
    return await this.db.findOne(sql, [categoryId]);
  }

  // Update category status
  async updateCategoryStatus(categoryId, isActive) {
    const result = await this.db.update(
      'categories',
      { is_active: isActive, updated_at: new Date() },
      'category_id = ?',
      [categoryId]
    );

    if (result.affectedRows === 0) {
      throw new NotFoundError('Category not found');
    }

    return true;
  }

  // Create subcategory
  async createSubcategory(subcategoryData) {
    // Validate required fields
    this.validateRequired(subcategoryData, ['name', 'category_id']);

    // Check if parent category exists and is active
    const parentCategory = await this.getCategoryById(subcategoryData.category_id);
    if (!parentCategory || !parentCategory.is_active) {
      throw new ValidationError('Parent category not found or inactive');
    }

    // Check if subcategory name already exists within the same category
    const existingSubcategory = await this.db.findOne(
      'SELECT subcategory_id FROM subcategories WHERE name = ? AND category_id = ?',
      [subcategoryData.name, subcategoryData.category_id]
    );

    if (existingSubcategory) {
      throw new ConflictError('Subcategory name already exists in this category');
    }

    // Get next display order if not provided
    let displayOrder = subcategoryData.display_order;
    if (displayOrder === undefined || displayOrder === null) {
      const maxOrder = await this.db.findOne(
        'SELECT COALESCE(MAX(display_order), 0) as max_order FROM subcategories WHERE category_id = ?',
        [subcategoryData.category_id]
      );
      displayOrder = maxOrder.max_order + 1;
    }

    // Prepare subcategory data
    const subcategory = {
      name: subcategoryData.name,
      category_id: subcategoryData.category_id,
      description: subcategoryData.description || null,
      color_code: subcategoryData.color_code || parentCategory.color_code,
      display_order: displayOrder,
      is_active: subcategoryData.is_active !== undefined ? subcategoryData.is_active : true,
      created_by: subcategoryData.created_by || null,
      created_at: new Date(),
      updated_at: new Date(),
    };

    const result = await this.db.insert('subcategories', subcategory);
    
    return await this.getSubcategoryById(result.insertId);
  }

  // Update subcategory
  async updateSubcategory(subcategoryId, updateData) {
    // Check if subcategory exists
    const existingSubcategory = await this.getSubcategoryById(subcategoryId);
    if (!existingSubcategory) {
      throw new NotFoundError('Subcategory not found');
    }

    // If category_id is being updated, check if new parent category exists
    if (updateData.category_id) {
      const parentCategory = await this.getCategoryById(updateData.category_id);
      if (!parentCategory || !parentCategory.is_active) {
        throw new ValidationError('Parent category not found or inactive');
      }
    }

    // Check if new name conflicts within the same category
    if (updateData.name) {
      const categoryId = updateData.category_id || existingSubcategory.category_id;
      const nameConflict = await this.db.findOne(
        'SELECT subcategory_id FROM subcategories WHERE name = ? AND category_id = ? AND subcategory_id != ?',
        [updateData.name, categoryId, subcategoryId]
      );

      if (nameConflict) {
        throw new ConflictError('Subcategory name already exists in this category');
      }
    }

    // Prepare update data
    const allowedFields = ['name', 'category_id', 'description', 'color_code', 'display_order', 'is_active', 'updated_by'];
    const filteredData = {};
    
    allowedFields.forEach(field => {
      if (updateData[field] !== undefined) {
        filteredData[field] = updateData[field];
      }
    });

    if (Object.keys(filteredData).length === 0) {
      throw new ValidationError('No valid fields to update');
    }

    filteredData.updated_at = new Date();

    const result = await this.db.update(
      'subcategories',
      filteredData,
      'subcategory_id = ?',
      [subcategoryId]
    );

    if (result.affectedRows === 0) {
      throw new NotFoundError('Subcategory not found');
    }

    return await this.getSubcategoryById(subcategoryId);
  }

  // Delete subcategory (soft delete)
  async deleteSubcategory(subcategoryId) {
    // Check if subcategory exists
    const existingSubcategory = await this.getSubcategoryById(subcategoryId);
    if (!existingSubcategory) {
      throw new NotFoundError('Subcategory not found');
    }

    // Soft delete by setting is_active = false
    const result = await this.db.update(
      'subcategories',
      { is_active: false, updated_at: new Date() },
      'subcategory_id = ?',
      [subcategoryId]
    );

    if (result.affectedRows === 0) {
      throw new NotFoundError('Subcategory not found');
    }

    return true;
  }

  // Get subcategory by ID
  async getSubcategoryById(subcategoryId) {
    const sql = `
      SELECT 
        s.*,
        c.name as category_name,
        c.color_code as category_color
      FROM subcategories s
      LEFT JOIN categories c ON s.category_id = c.category_id
      WHERE s.subcategory_id = ?
    `;
    return await this.db.findOne(sql, [subcategoryId]);
  }

  // Update subcategory status
  async updateSubcategoryStatus(subcategoryId, isActive) {
    const result = await this.db.update(
      'subcategories',
      { is_active: isActive, updated_at: new Date() },
      'subcategory_id = ?',
      [subcategoryId]
    );

    if (result.affectedRows === 0) {
      throw new NotFoundError('Subcategory not found');
    }

    return true;
  }

  // Update subcategory order
  async updateSubcategoryOrder(subcategoryId, displayOrder) {
    const result = await this.db.update(
      'subcategories',
      { display_order: displayOrder, updated_at: new Date() },
      'subcategory_id = ?',
      [subcategoryId]
    );

    if (result.affectedRows === 0) {
      throw new NotFoundError('Subcategory not found');
    }

    return true;
  }
}

module.exports = new CategoryModel();
