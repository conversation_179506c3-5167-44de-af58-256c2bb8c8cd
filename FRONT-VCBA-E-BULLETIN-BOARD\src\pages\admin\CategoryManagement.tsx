import React, { useState, useEffect } from 'react';
import { FolderTree, Plus, AlertTriangle, CheckCircle } from 'lucide-react';
import { useAdminAuth } from '../../contexts/AdminAuthContext';
import { usePermissions } from '../../utils/permissions';
import { categoryService } from '../../services/categoryService';
import CategoryList from '../../components/admin/CategoryList';
import CategoryModal from '../../components/admin/CategoryModal';

interface Category {
  category_id?: number;
  name: string;
  description?: string;
  color_code: string;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
  subcategories?: Subcategory[];
}

interface Subcategory {
  subcategory_id?: number;
  category_id?: number;
  name: string;
  description?: string;
  color_code: string;
  is_active: boolean;
  display_order: number;
  created_at?: string;
  updated_at?: string;
}

const CategoryManagement: React.FC = () => {
  const { user } = useAdminAuth();
  const permissions = usePermissions(user);

  // State management
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Modal state
  const [showModal, setShowModal] = useState(false);
  const [modalMode, setModalMode] = useState<'add_category' | 'edit_category' | 'add_subcategory' | 'edit_subcategory'>('add_category');
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [editingSubcategory, setEditingSubcategory] = useState<Subcategory | null>(null);
  const [parentCategory, setParentCategory] = useState<Category | null>(null);
  const [modalLoading, setModalLoading] = useState(false);

  useEffect(() => {
    // Check if user has permission to manage categories
    if (!permissions.canManageCategories) {
      setError('You do not have permission to manage categories');
      setLoading(false);
      return;
    }

    loadCategories();
  }, [permissions.canManageCategories]);

  const loadCategories = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔍 CategoryManagement - Loading categories from API...');
      const response = await categoryService.getCategoriesWithSubcategories();
      console.log('🔍 CategoryManagement - API response:', response);

      if (response.success && response.data?.categories) {
        setCategories(response.data.categories);
        console.log('✅ CategoryManagement - Categories loaded successfully:', response.data.categories.length);
      } else {
        throw new Error(response.message || 'Failed to load categories');
      }

    } catch (err: any) {
      console.error('❌ CategoryManagement - Error loading categories:', err);

      // Check if it's a 404 or network error (API not available)
      const isApiUnavailable = err.response?.status === 404 ||
                              err.code === 'ECONNREFUSED' ||
                              err.message.includes('Network Error') ||
                              err.message.includes('Failed to fetch');

      if (isApiUnavailable) {
        console.log('⚠️ CategoryManagement - API not available, using mock data');

        // Use mock data as fallback
        const mockCategories: Category[] = [
          {
            category_id: 1,
            name: 'Academic',
            description: 'Academic-related announcements and events',
            color_code: '#3b82f6',
            is_active: true,
            created_at: '2025-01-01T00:00:00Z',
            updated_at: '2025-01-01T00:00:00Z',
            subcategories: [
              {
                subcategory_id: 1,
                category_id: 1,
                name: 'Exams',
                description: 'Examination schedules and updates',
                color_code: '#ef4444',
                display_order: 1,
                is_active: true,
                created_at: '2025-01-01T00:00:00Z',
                updated_at: '2025-01-01T00:00:00Z'
              },
              {
                subcategory_id: 2,
                category_id: 1,
                name: 'Assignments',
                description: 'Assignment deadlines and submissions',
                color_code: '#f59e0b',
                display_order: 2,
                is_active: true,
                created_at: '2025-01-01T00:00:00Z',
                updated_at: '2025-01-01T00:00:00Z'
              },
              {
                subcategory_id: 3,
                category_id: 1,
                name: 'Class Schedules',
                description: 'Class timing and schedule changes',
                color_code: '#06b6d4',
                display_order: 3,
                is_active: true,
                created_at: '2025-01-01T00:00:00Z',
                updated_at: '2025-01-01T00:00:00Z'
              }
            ]
          },
          {
            category_id: 2,
            name: 'Events',
            description: 'School events and activities',
            color_code: '#10b981',
            is_active: true,
            created_at: '2025-01-01T00:00:00Z',
            updated_at: '2025-01-01T00:00:00Z',
            subcategories: [
              {
                subcategory_id: 4,
                category_id: 2,
                name: 'Sports',
                description: 'Sports events and competitions',
                color_code: '#8b5cf6',
                display_order: 1,
                is_active: true,
                created_at: '2025-01-01T00:00:00Z',
                updated_at: '2025-01-01T00:00:00Z'
              },
              {
                subcategory_id: 5,
                category_id: 2,
                name: 'Cultural',
                description: 'Cultural events and celebrations',
                color_code: '#ec4899',
                display_order: 2,
                is_active: true,
                created_at: '2025-01-01T00:00:00Z',
                updated_at: '2025-01-01T00:00:00Z'
              }
            ]
          },
          {
            category_id: 3,
            name: 'Administrative',
            description: 'Administrative notices and updates',
            color_code: '#f97316',
            is_active: true,
            created_at: '2025-01-01T00:00:00Z',
            updated_at: '2025-01-01T00:00:00Z',
            subcategories: [
              {
                subcategory_id: 6,
                category_id: 3,
                name: 'Policies',
                description: 'School policies and regulations',
                color_code: '#6366f1',
                display_order: 1,
                is_active: true,
                created_at: '2025-01-01T00:00:00Z',
                updated_at: '2025-01-01T00:00:00Z'
              }
            ]
          },
          {
            category_id: 4,
            name: 'Emergency',
            description: 'Emergency announcements and alerts',
            color_code: '#dc2626',
            is_active: true,
            created_at: '2025-01-01T00:00:00Z',
            updated_at: '2025-01-01T00:00:00Z',
            subcategories: []
          }
        ];

        setCategories(mockCategories);
        setError('⚠️ Using demo data - Backend API not connected. Categories will work in demo mode.');
      } else {
        setError(err.message || 'Failed to load categories');
        setCategories([]);
      }
    } finally {
      setLoading(false);
    }
  };

  // Handler functions
  const handleAddCategory = () => {
    setModalMode('add_category');
    setEditingCategory(null);
    setEditingSubcategory(null);
    setParentCategory(null);
    setShowModal(true);
  };

  const handleEditCategory = (category: Category) => {
    setModalMode('edit_category');
    setEditingCategory(category);
    setEditingSubcategory(null);
    setParentCategory(null);
    setShowModal(true);
  };

  const handleDeleteCategory = async (category: Category) => {
    try {
      setError(null);
      setSuccess(null);

      if (!category.category_id) {
        throw new Error('Category ID is required');
      }

      console.log('🔍 CategoryManagement - Deleting category:', category.category_id);
      const response = await categoryService.deleteCategory(category.category_id);

      if (response.success) {
        setSuccess(`Category "${category.name}" has been deleted successfully`);
        loadCategories();
      } else {
        throw new Error(response.message || 'Failed to delete category');
      }

    } catch (err: any) {
      console.error('❌ CategoryManagement - Error deleting category:', err);

      // Check if API is unavailable
      const isApiUnavailable = err.response?.status === 404 ||
                              err.code === 'ECONNREFUSED' ||
                              err.message.includes('Network Error') ||
                              err.message.includes('Failed to fetch');

      if (isApiUnavailable) {
        setError('⚠️ Demo mode: Cannot delete categories without backend API connection');
      } else {
        setError(err.message || 'Failed to delete category');
      }
    }
  };

  const handleToggleCategoryStatus = async (category: Category) => {
    try {
      setError(null);
      setSuccess(null);

      if (!category.category_id) {
        throw new Error('Category ID is required');
      }

      console.log('🔍 CategoryManagement - Toggling category status:', category.category_id, !category.is_active);
      const response = await categoryService.toggleCategoryStatus(category.category_id, !category.is_active);

      if (response.success) {
        const action = category.is_active ? 'deactivated' : 'activated';
        setSuccess(`Category "${category.name}" has been ${action} successfully`);
        loadCategories();
      } else {
        throw new Error(response.message || 'Failed to update category status');
      }

    } catch (err: any) {
      console.error('❌ CategoryManagement - Error toggling category status:', err);
      setError(err.message || 'Failed to update category status');
    }
  };

  const handleAddSubcategory = (category: Category) => {
    setModalMode('add_subcategory');
    setEditingCategory(null);
    setEditingSubcategory(null);
    setParentCategory(category);
    setShowModal(true);
  };

  const handleEditSubcategory = (category: Category, subcategory: Subcategory) => {
    setModalMode('edit_subcategory');
    setEditingCategory(null);
    setEditingSubcategory(subcategory);
    setParentCategory(category);
    setShowModal(true);
  };

  const handleDeleteSubcategory = async (category: Category, subcategory: Subcategory) => {
    try {
      setError(null);
      setSuccess(null);

      if (!category.category_id || !subcategory.subcategory_id) {
        throw new Error('Category ID and Subcategory ID are required');
      }

      console.log('🔍 CategoryManagement - Deleting subcategory:', category.category_id, subcategory.subcategory_id);
      const response = await categoryService.deleteSubcategory(category.category_id, subcategory.subcategory_id);

      if (response.success) {
        setSuccess(`Subcategory "${subcategory.name}" has been deleted successfully`);
        loadCategories();
      } else {
        throw new Error(response.message || 'Failed to delete subcategory');
      }

    } catch (err: any) {
      console.error('❌ CategoryManagement - Error deleting subcategory:', err);
      setError(err.message || 'Failed to delete subcategory');
    }
  };

  const handleToggleSubcategoryStatus = async (category: Category, subcategory: Subcategory) => {
    try {
      setError(null);
      setSuccess(null);

      if (!category.category_id || !subcategory.subcategory_id) {
        throw new Error('Category ID and Subcategory ID are required');
      }

      console.log('🔍 CategoryManagement - Toggling subcategory status:', category.category_id, subcategory.subcategory_id, !subcategory.is_active);
      const response = await categoryService.toggleSubcategoryStatus(category.category_id, subcategory.subcategory_id, !subcategory.is_active);

      if (response.success) {
        const action = subcategory.is_active ? 'deactivated' : 'activated';
        setSuccess(`Subcategory "${subcategory.name}" has been ${action} successfully`);
        loadCategories();
      } else {
        throw new Error(response.message || 'Failed to update subcategory status');
      }

    } catch (err: any) {
      console.error('❌ CategoryManagement - Error toggling subcategory status:', err);
      setError(err.message || 'Failed to update subcategory status');
    }
  };

  const handleSave = async (data: Category | Subcategory, parentCat?: Category) => {
    try {
      setModalLoading(true);
      setError(null);
      setSuccess(null);

      let response;

      if (modalMode === 'add_category') {
        console.log('🔍 CategoryManagement - Creating category:', data);
        response = await categoryService.createCategory(data as Category);
        if (response.success) {
          setSuccess(`Category "${data.name}" has been created successfully`);
        }
      } else if (modalMode === 'edit_category') {
        if (!editingCategory?.category_id) {
          throw new Error('Category ID is required for editing');
        }
        console.log('🔍 CategoryManagement - Updating category:', editingCategory.category_id, data);
        response = await categoryService.updateCategory(editingCategory.category_id, data as Category);
        if (response.success) {
          setSuccess(`Category "${data.name}" has been updated successfully`);
        }
      } else if (modalMode === 'add_subcategory') {
        if (!parentCat?.category_id) {
          throw new Error('Parent category ID is required for creating subcategory');
        }
        console.log('🔍 CategoryManagement - Creating subcategory:', parentCat.category_id, data);
        response = await categoryService.createSubcategory(parentCat.category_id, data as Subcategory);
        if (response.success) {
          setSuccess(`Subcategory "${data.name}" has been created successfully`);
        }
      } else if (modalMode === 'edit_subcategory') {
        if (!parentCat?.category_id || !editingSubcategory?.subcategory_id) {
          throw new Error('Category ID and Subcategory ID are required for editing');
        }
        console.log('🔍 CategoryManagement - Updating subcategory:', parentCat.category_id, editingSubcategory.subcategory_id, data);
        response = await categoryService.updateSubcategory(parentCat.category_id, editingSubcategory.subcategory_id, data as Subcategory);
        if (response.success) {
          setSuccess(`Subcategory "${data.name}" has been updated successfully`);
        }
      }

      if (!response?.success) {
        throw new Error(response?.message || 'Failed to save changes');
      }

      loadCategories();

    } catch (err: any) {
      console.error('❌ CategoryManagement - Error saving:', err);

      // Check if API is unavailable
      const isApiUnavailable = err.response?.status === 404 ||
                              err.code === 'ECONNREFUSED' ||
                              err.message.includes('Network Error') ||
                              err.message.includes('Failed to fetch');

      if (isApiUnavailable) {
        setError('⚠️ Demo mode: Cannot save changes without backend API connection');
      } else {
        setError(err.message || 'Failed to save changes');
      }

      throw err; // Re-throw to prevent modal from closing
    } finally {
      setModalLoading(false);
    }
  };

  const clearMessages = () => {
    setError(null);
    setSuccess(null);
  };

  if (!permissions.canManageCategories) {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '400px',
        textAlign: 'center',
        color: '#6b7280'
      }}>
        <FolderTree size={64} style={{ marginBottom: '1rem', opacity: 0.5 }} />
        <h2 style={{ margin: '0 0 0.5rem', fontSize: '1.5rem', fontWeight: '600' }}>
          Access Denied
        </h2>
        <p style={{ margin: 0, fontSize: '1rem' }}>
          You do not have permission to manage categories and subcategories.
        </p>
        <div style={{
          marginTop: '1rem',
          padding: '0.5rem 1rem',
          background: permissions.getPositionBadgeColor(),
          borderRadius: '6px',
          color: 'white',
          fontSize: '0.875rem',
          fontWeight: '600'
        }}>
          Current Role: {permissions.getPositionDisplayName()}
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '400px'
      }}>
        <div style={{
          width: '40px',
          height: '40px',
          border: '4px solid #f3f4f6',
          borderTop: '4px solid #3b82f6',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }} />
      </div>
    );
  }

  if (error) {
    return (
      <div style={{
        padding: '2rem',
        textAlign: 'center',
        color: '#ef4444'
      }}>
        <h2>Error</h2>
        <p>{error}</p>
        <button
          onClick={loadCategories}
          style={{
            padding: '0.5rem 1rem',
            background: '#3b82f6',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: 'pointer'
          }}
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div style={{ maxWidth: '1400px', margin: '0 auto' }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '2rem'
      }}>
        <div>
          <h1 style={{
            margin: '0 0 0.5rem',
            fontSize: '2rem',
            fontWeight: '700',
            color: '#1f2937'
          }}>
            Category Management
          </h1>
          <p style={{
            margin: 0,
            color: '#6b7280',
            fontSize: '1rem'
          }}>
            Manage categories and subcategories for announcements and events
          </p>
        </div>

        <button
          onClick={handleAddCategory}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            padding: '0.75rem 1.5rem',
            background: '#3b82f6',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            fontSize: '0.875rem',
            fontWeight: '600',
            cursor: 'pointer',
            transition: 'background-color 0.2s'
          }}
          onMouseEnter={(e) => e.currentTarget.style.background = '#2563eb'}
          onMouseLeave={(e) => e.currentTarget.style.background = '#3b82f6'}
        >
          <Plus size={16} />
          Add Category
        </button>
      </div>

      {/* Success/Error Messages */}
      {(error || success) && (
        <div style={{ marginBottom: '1.5rem' }}>
          {error && (
            <div style={{
              padding: '1rem',
              background: '#fef2f2',
              border: '1px solid #fecaca',
              borderRadius: '8px',
              color: '#dc2626',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <AlertTriangle size={16} />
                {error}
              </div>
              <button
                onClick={clearMessages}
                style={{
                  background: 'transparent',
                  border: 'none',
                  color: '#dc2626',
                  cursor: 'pointer',
                  padding: '0.25rem'
                }}
              >
                ×
              </button>
            </div>
          )}

          {success && (
            <div style={{
              padding: '1rem',
              background: '#f0fdf4',
              border: '1px solid #bbf7d0',
              borderRadius: '8px',
              color: '#166534',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <CheckCircle size={16} />
                {success}
              </div>
              <button
                onClick={clearMessages}
                style={{
                  background: 'transparent',
                  border: 'none',
                  color: '#166534',
                  cursor: 'pointer',
                  padding: '0.25rem'
                }}
              >
                ×
              </button>
            </div>
          )}
        </div>
      )}

      {/* Category List */}
      <CategoryList
        categories={categories}
        loading={loading}
        onEditCategory={handleEditCategory}
        onDeleteCategory={handleDeleteCategory}
        onToggleCategoryStatus={handleToggleCategoryStatus}
        onAddSubcategory={handleAddSubcategory}
        onEditSubcategory={handleEditSubcategory}
        onDeleteSubcategory={handleDeleteSubcategory}
        onToggleSubcategoryStatus={handleToggleSubcategoryStatus}
      />

      {/* Category Modal */}
      <CategoryModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onSave={handleSave}
        category={editingCategory}
        subcategory={editingSubcategory}
        parentCategory={parentCategory}
        mode={modalMode}
        loading={modalLoading}
      />
    </div>
  );
};

export default CategoryManagement;
