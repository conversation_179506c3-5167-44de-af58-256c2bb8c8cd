{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\admin\\\\AdminManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminManagement = () => {\n  _s();\n  const {\n    user\n  } = useAdminAuth();\n  const permissions = usePermissions(user);\n\n  // State management\n  const [admins, setAdmins] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n\n  // Modal state\n  const [showModal, setShowModal] = useState(false);\n  const [editingAdmin, setEditingAdmin] = useState(null);\n  const [modalLoading, setModalLoading] = useState(false);\n\n  // Search and filter state\n  const [searchTerm, setSearchTerm] = useState('');\n  const [positionFilter, setPositionFilter] = useState('');\n  const [statusFilter, setStatusFilter] = useState('');\n\n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(10);\n  const [totalItems, setTotalItems] = useState(0);\n  useEffect(() => {\n    // Check if user has permission to manage admins\n    if (!permissions.canManageAdmins) {\n      setError('You do not have permission to manage admin accounts');\n      setLoading(false);\n      return;\n    }\n    loadAdmins();\n  }, [permissions.canManageAdmins]);\n  const loadAdmins = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // TODO: Implement actual API call\n      // const response = await adminManagementService.getAdmins({\n      //   page: currentPage,\n      //   limit: itemsPerPage,\n      //   search: searchTerm,\n      //   position: positionFilter,\n      //   status: statusFilter\n      // });\n\n      // Mock data with proper structure\n      const mockAdmins = [{\n        admin_id: 1,\n        email: '<EMAIL>',\n        is_active: true,\n        last_login: '2025-08-05T10:30:00Z',\n        created_at: '2025-01-01T00:00:00Z',\n        profile: {\n          first_name: 'Josh',\n          middle_name: 'Christian',\n          last_name: 'Mojica',\n          full_name: 'Josh Christian Mojica',\n          phone_number: '+************',\n          department: 'Administration',\n          position: 'super_admin',\n          grade_level: 12,\n          bio: 'System Administrator with expertise in educational technology.'\n        }\n      }, {\n        admin_id: 2,\n        email: '<EMAIL>',\n        is_active: true,\n        last_login: '2025-08-05T09:15:00Z',\n        created_at: '2025-01-15T00:00:00Z',\n        profile: {\n          first_name: 'Zaira',\n          last_name: 'Plarisan',\n          full_name: 'Zaira Plarisan',\n          phone_number: '+***********1',\n          department: 'Mathematics',\n          position: 'professor',\n          grade_level: 11,\n          bio: 'Mathematics professor specializing in Grade 11 curriculum.'\n        }\n      }];\n\n      // Apply filters\n      let filteredAdmins = mockAdmins;\n      if (searchTerm) {\n        filteredAdmins = filteredAdmins.filter(admin => admin.profile.full_name.toLowerCase().includes(searchTerm.toLowerCase()) || admin.email.toLowerCase().includes(searchTerm.toLowerCase()));\n      }\n      if (positionFilter) {\n        filteredAdmins = filteredAdmins.filter(admin => admin.profile.position === positionFilter);\n      }\n      if (statusFilter) {\n        const isActive = statusFilter === 'active';\n        filteredAdmins = filteredAdmins.filter(admin => admin.is_active === isActive);\n      }\n      setTotalItems(filteredAdmins.length);\n\n      // Apply pagination\n      const startIndex = (currentPage - 1) * itemsPerPage;\n      const paginatedAdmins = filteredAdmins.slice(startIndex, startIndex + itemsPerPage);\n      setAdmins(paginatedAdmins);\n    } catch (err) {\n      setError(err.message || 'Failed to load admin accounts');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getPositionIcon = position => {\n    return position === 'super_admin' ? Shield : User;\n  };\n  const getPositionColor = position => {\n    return position === 'super_admin' ? '#dc2626' : '#2563eb';\n  };\n  const getPositionLabel = position => {\n    return position === 'super_admin' ? 'Super Administrator' : 'Professor';\n  };\n  if (!permissions.canManageAdmins) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      },\n      children: [/*#__PURE__*/_jsxDEV(UserCog, {\n        size: 64,\n        style: {\n          marginBottom: '1rem',\n          opacity: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          margin: '0 0 0.5rem',\n          fontSize: '1.5rem',\n          fontWeight: '600'\n        },\n        children: \"Access Denied\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: 0,\n          fontSize: '1rem'\n        },\n        children: \"You do not have permission to manage admin accounts.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        },\n        children: [\"Current Role: \", permissions.getPositionDisplayName()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '40px',\n          height: '40px',\n          border: '4px solid #f3f4f6',\n          borderTop: '4px solid #3b82f6',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '2rem',\n        textAlign: 'center',\n        color: '#ef4444'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Error\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: loadAdmins,\n        style: {\n          padding: '0.5rem 1rem',\n          background: '#3b82f6',\n          color: 'white',\n          border: 'none',\n          borderRadius: '6px',\n          cursor: 'pointer'\n        },\n        children: \"Retry\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      maxWidth: '1200px',\n      margin: '0 auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            margin: '0 0 0.5rem',\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          },\n          children: \"Admin Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          },\n          children: \"Manage administrator accounts and permissions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          padding: '0.75rem 1.5rem',\n          background: '#3b82f6',\n          color: 'white',\n          border: 'none',\n          borderRadius: '8px',\n          fontSize: '0.875rem',\n          fontWeight: '600',\n          cursor: 'pointer',\n          transition: 'background-color 0.2s'\n        },\n        onMouseEnter: e => e.currentTarget.style.background = '#2563eb',\n        onMouseLeave: e => e.currentTarget.style.background = '#3b82f6',\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this), \"Add Admin\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fill, minmax(400px, 1fr))',\n        gap: '1.5rem'\n      },\n      children: admins.map(admin => {\n        const PositionIcon = getPositionIcon(admin.position);\n        const positionColor = getPositionColor(admin.position);\n        const positionLabel = getPositionLabel(admin.position);\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'white',\n            borderRadius: '12px',\n            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n            padding: '1.5rem',\n            border: admin.admin_id === (user === null || user === void 0 ? void 0 : user.id) ? '2px solid #facc15' : '1px solid #e5e7eb'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'flex-start',\n              marginBottom: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '48px',\n                  height: '48px',\n                  background: `linear-gradient(135deg, ${positionColor}20, ${positionColor}10)`,\n                  borderRadius: '12px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                },\n                children: /*#__PURE__*/_jsxDEV(PositionIcon, {\n                  size: 24,\n                  color: positionColor\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  style: {\n                    margin: '0 0 0.25rem',\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: '#1f2937'\n                  },\n                  children: [admin.first_name, \" \", admin.last_name, admin.admin_id === (user === null || user === void 0 ? void 0 : user.id) && /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      marginLeft: '0.5rem',\n                      padding: '0.125rem 0.375rem',\n                      background: '#facc15',\n                      color: '#1f2937',\n                      borderRadius: '4px',\n                      fontSize: '0.625rem',\n                      fontWeight: '700'\n                    },\n                    children: \"YOU\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 344,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    padding: '0.25rem 0.5rem',\n                    background: positionColor,\n                    color: 'white',\n                    borderRadius: '4px',\n                    fontSize: '0.75rem',\n                    fontWeight: '600',\n                    display: 'inline-block'\n                  },\n                  children: positionLabel\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '0.25rem 0.5rem',\n                background: admin.is_active ? '#dcfce7' : '#fef2f2',\n                color: admin.is_active ? '#166534' : '#dc2626',\n                borderRadius: '4px',\n                fontSize: '0.75rem',\n                fontWeight: '600'\n              },\n              children: admin.is_active ? 'Active' : 'Inactive'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                marginBottom: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Mail, {\n                size: 16,\n                color: \"#6b7280\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                },\n                children: admin.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 17\n            }, this), admin.phone_number && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                marginBottom: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Phone, {\n                size: 16,\n                color: \"#6b7280\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                },\n                children: admin.phone_number\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 19\n            }, this), admin.department && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                color: '#6b7280',\n                marginTop: '0.5rem'\n              },\n              children: [\"Department: \", admin.department]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 19\n            }, this), admin.grade_level && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                color: '#6b7280',\n                marginTop: '0.25rem'\n              },\n              children: [\"Grade Level: \", admin.grade_level]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 15\n          }, this), admin.last_login && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.75rem',\n              color: '#9ca3af',\n              marginBottom: '1rem'\n            },\n            children: [\"Last login: \", new Date(admin.last_login).toLocaleDateString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '0.5rem',\n              paddingTop: '1rem',\n              borderTop: '1px solid #f3f4f6'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                flex: 1,\n                padding: '0.5rem',\n                background: 'transparent',\n                border: '1px solid #d1d5db',\n                borderRadius: '6px',\n                cursor: 'pointer',\n                color: '#6b7280',\n                fontSize: '0.875rem',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                gap: '0.25rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Edit, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 19\n              }, this), \"Edit\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 17\n            }, this), admin.admin_id !== (user === null || user === void 0 ? void 0 : user.id) && /*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                flex: 1,\n                padding: '0.5rem',\n                background: 'transparent',\n                border: '1px solid #fca5a5',\n                borderRadius: '6px',\n                cursor: 'pointer',\n                color: '#dc2626',\n                fontSize: '0.875rem',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                gap: '0.25rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Trash2, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 21\n              }, this), \"Delete\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 15\n          }, this)]\n        }, admin.admin_id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 245,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminManagement, \"1ta6jFr/aZK9eOUImOuE16b5P8E=\", false, function () {\n  return [useAdminAuth, usePermissions];\n});\n_c = AdminManagement;\nexport default AdminManagement;\nvar _c;\n$RefreshReg$(_c, \"AdminManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAdminAuth", "usePermissions", "jsxDEV", "_jsxDEV", "AdminManagement", "_s", "user", "permissions", "admins", "setAdmins", "loading", "setLoading", "error", "setError", "success", "setSuccess", "showModal", "setShowModal", "editingAdmin", "setEditingAdmin", "modalLoading", "setModalLoading", "searchTerm", "setSearchTerm", "positionFilter", "setPositionFilter", "statusFilter", "setStatus<PERSON>ilter", "currentPage", "setCurrentPage", "itemsPerPage", "setItemsPerPage", "totalItems", "setTotalItems", "canManageAdmins", "loadAdmins", "mockAdmins", "admin_id", "email", "is_active", "last_login", "created_at", "profile", "first_name", "middle_name", "last_name", "full_name", "phone_number", "department", "position", "grade_level", "bio", "filteredAdmins", "filter", "admin", "toLowerCase", "includes", "isActive", "length", "startIndex", "paginatedAdmins", "slice", "err", "message", "getPositionIcon", "Shield", "User", "getPositionColor", "getPositionLabel", "style", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "textAlign", "color", "children", "UserCog", "size", "marginBottom", "opacity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "margin", "fontSize", "fontWeight", "marginTop", "padding", "background", "getPositionBadgeColor", "borderRadius", "getPositionDisplayName", "width", "height", "border", "borderTop", "animation", "onClick", "cursor", "max<PERSON><PERSON><PERSON>", "gap", "transition", "onMouseEnter", "e", "currentTarget", "onMouseLeave", "Plus", "gridTemplateColumns", "map", "PositionIcon", "positionColor", "position<PERSON><PERSON><PERSON>", "boxShadow", "id", "marginLeft", "Mail", "Phone", "Date", "toLocaleDateString", "paddingTop", "flex", "Edit", "Trash2", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/admin/AdminManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { UserPlus, Search, Filter, Users, AlertTriangle, CheckCircle } from 'lucide-react';\nimport { useAdminAuth } from '../../contexts/AdminAuthContext';\nimport { usePermissions } from '../../utils/permissions';\nimport { adminManagementService } from '../../services/adminManagementService';\nimport AdminAccountList from '../../components/admin/AdminAccountList';\nimport AdminAccountModal from '../../components/admin/AdminAccountModal';\n\ninterface AdminAccount {\n  admin_id: number;\n  email: string;\n  is_active: boolean;\n  last_login: string | null;\n  created_at: string;\n  profile: {\n    first_name: string;\n    middle_name?: string;\n    last_name: string;\n    suffix?: string;\n    full_name: string;\n    phone_number?: string;\n    department?: string;\n    position: 'super_admin' | 'professor';\n    grade_level?: number;\n    bio?: string;\n    profile_picture?: string;\n  };\n}\n\nconst AdminManagement: React.FC = () => {\n  const { user } = useAdminAuth();\n  const permissions = usePermissions(user);\n\n  // State management\n  const [admins, setAdmins] = useState<AdminAccount[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  // Modal state\n  const [showModal, setShowModal] = useState(false);\n  const [editingAdmin, setEditingAdmin] = useState<AdminAccount | null>(null);\n  const [modalLoading, setModalLoading] = useState(false);\n\n  // Search and filter state\n  const [searchTerm, setSearchTerm] = useState('');\n  const [positionFilter, setPositionFilter] = useState<string>('');\n  const [statusFilter, setStatusFilter] = useState<string>('');\n\n  // Pagination state\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(10);\n  const [totalItems, setTotalItems] = useState(0);\n\n  useEffect(() => {\n    // Check if user has permission to manage admins\n    if (!permissions.canManageAdmins) {\n      setError('You do not have permission to manage admin accounts');\n      setLoading(false);\n      return;\n    }\n\n    loadAdmins();\n  }, [permissions.canManageAdmins]);\n\n  const loadAdmins = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // TODO: Implement actual API call\n      // const response = await adminManagementService.getAdmins({\n      //   page: currentPage,\n      //   limit: itemsPerPage,\n      //   search: searchTerm,\n      //   position: positionFilter,\n      //   status: statusFilter\n      // });\n\n      // Mock data with proper structure\n      const mockAdmins: AdminAccount[] = [\n        {\n          admin_id: 1,\n          email: '<EMAIL>',\n          is_active: true,\n          last_login: '2025-08-05T10:30:00Z',\n          created_at: '2025-01-01T00:00:00Z',\n          profile: {\n            first_name: 'Josh',\n            middle_name: 'Christian',\n            last_name: 'Mojica',\n            full_name: 'Josh Christian Mojica',\n            phone_number: '+************',\n            department: 'Administration',\n            position: 'super_admin',\n            grade_level: 12,\n            bio: 'System Administrator with expertise in educational technology.'\n          }\n        },\n        {\n          admin_id: 2,\n          email: '<EMAIL>',\n          is_active: true,\n          last_login: '2025-08-05T09:15:00Z',\n          created_at: '2025-01-15T00:00:00Z',\n          profile: {\n            first_name: 'Zaira',\n            last_name: 'Plarisan',\n            full_name: 'Zaira Plarisan',\n            phone_number: '+***********1',\n            department: 'Mathematics',\n            position: 'professor',\n            grade_level: 11,\n            bio: 'Mathematics professor specializing in Grade 11 curriculum.'\n          }\n        }\n      ];\n\n      // Apply filters\n      let filteredAdmins = mockAdmins;\n\n      if (searchTerm) {\n        filteredAdmins = filteredAdmins.filter(admin =>\n          admin.profile.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n          admin.email.toLowerCase().includes(searchTerm.toLowerCase())\n        );\n      }\n\n      if (positionFilter) {\n        filteredAdmins = filteredAdmins.filter(admin => admin.profile.position === positionFilter);\n      }\n\n      if (statusFilter) {\n        const isActive = statusFilter === 'active';\n        filteredAdmins = filteredAdmins.filter(admin => admin.is_active === isActive);\n      }\n\n      setTotalItems(filteredAdmins.length);\n\n      // Apply pagination\n      const startIndex = (currentPage - 1) * itemsPerPage;\n      const paginatedAdmins = filteredAdmins.slice(startIndex, startIndex + itemsPerPage);\n\n      setAdmins(paginatedAdmins);\n\n    } catch (err: any) {\n      setError(err.message || 'Failed to load admin accounts');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getPositionIcon = (position: string) => {\n    return position === 'super_admin' ? Shield : User;\n  };\n\n  const getPositionColor = (position: string) => {\n    return position === 'super_admin' ? '#dc2626' : '#2563eb';\n  };\n\n  const getPositionLabel = (position: string) => {\n    return position === 'super_admin' ? 'Super Administrator' : 'Professor';\n  };\n\n  if (!permissions.canManageAdmins) {\n    return (\n      <div style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px',\n        textAlign: 'center',\n        color: '#6b7280'\n      }}>\n        <UserCog size={64} style={{ marginBottom: '1rem', opacity: 0.5 }} />\n        <h2 style={{ margin: '0 0 0.5rem', fontSize: '1.5rem', fontWeight: '600' }}>\n          Access Denied\n        </h2>\n        <p style={{ margin: 0, fontSize: '1rem' }}>\n          You do not have permission to manage admin accounts.\n        </p>\n        <div style={{\n          marginTop: '1rem',\n          padding: '0.5rem 1rem',\n          background: permissions.getPositionBadgeColor(),\n          borderRadius: '6px',\n          color: 'white',\n          fontSize: '0.875rem',\n          fontWeight: '600'\n        }}>\n          Current Role: {permissions.getPositionDisplayName()}\n        </div>\n      </div>\n    );\n  }\n\n  if (loading) {\n    return (\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '400px'\n      }}>\n        <div style={{\n          width: '40px',\n          height: '40px',\n          border: '4px solid #f3f4f6',\n          borderTop: '4px solid #3b82f6',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }} />\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div style={{\n        padding: '2rem',\n        textAlign: 'center',\n        color: '#ef4444'\n      }}>\n        <h2>Error</h2>\n        <p>{error}</p>\n        <button\n          onClick={loadAdmins}\n          style={{\n            padding: '0.5rem 1rem',\n            background: '#3b82f6',\n            color: 'white',\n            border: 'none',\n            borderRadius: '6px',\n            cursor: 'pointer'\n          }}\n        >\n          Retry\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ maxWidth: '1200px', margin: '0 auto' }}>\n      {/* Header */}\n      <div style={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      }}>\n        <div>\n          <h1 style={{\n            margin: '0 0 0.5rem',\n            fontSize: '2rem',\n            fontWeight: '700',\n            color: '#1f2937'\n          }}>\n            Admin Management\n          </h1>\n          <p style={{\n            margin: 0,\n            color: '#6b7280',\n            fontSize: '1rem'\n          }}>\n            Manage administrator accounts and permissions\n          </p>\n        </div>\n        \n        <button\n          style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            padding: '0.75rem 1.5rem',\n            background: '#3b82f6',\n            color: 'white',\n            border: 'none',\n            borderRadius: '8px',\n            fontSize: '0.875rem',\n            fontWeight: '600',\n            cursor: 'pointer',\n            transition: 'background-color 0.2s'\n          }}\n          onMouseEnter={(e) => e.currentTarget.style.background = '#2563eb'}\n          onMouseLeave={(e) => e.currentTarget.style.background = '#3b82f6'}\n        >\n          <Plus size={16} />\n          Add Admin\n        </button>\n      </div>\n\n      {/* Admin Cards */}\n      <div style={{\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fill, minmax(400px, 1fr))',\n        gap: '1.5rem'\n      }}>\n        {admins.map((admin) => {\n          const PositionIcon = getPositionIcon(admin.position);\n          const positionColor = getPositionColor(admin.position);\n          const positionLabel = getPositionLabel(admin.position);\n          \n          return (\n            <div\n              key={admin.admin_id}\n              style={{\n                background: 'white',\n                borderRadius: '12px',\n                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n                padding: '1.5rem',\n                border: admin.admin_id === user?.id ? '2px solid #facc15' : '1px solid #e5e7eb'\n              }}\n            >\n              {/* Header */}\n              <div style={{\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'flex-start',\n                marginBottom: '1rem'\n              }}>\n                <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>\n                  <div style={{\n                    width: '48px',\n                    height: '48px',\n                    background: `linear-gradient(135deg, ${positionColor}20, ${positionColor}10)`,\n                    borderRadius: '12px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  }}>\n                    <PositionIcon size={24} color={positionColor} />\n                  </div>\n                  <div>\n                    <h3 style={{\n                      margin: '0 0 0.25rem',\n                      fontSize: '1.125rem',\n                      fontWeight: '600',\n                      color: '#1f2937'\n                    }}>\n                      {admin.first_name} {admin.last_name}\n                      {admin.admin_id === user?.id && (\n                        <span style={{\n                          marginLeft: '0.5rem',\n                          padding: '0.125rem 0.375rem',\n                          background: '#facc15',\n                          color: '#1f2937',\n                          borderRadius: '4px',\n                          fontSize: '0.625rem',\n                          fontWeight: '700'\n                        }}>\n                          YOU\n                        </span>\n                      )}\n                    </h3>\n                    <div style={{\n                      padding: '0.25rem 0.5rem',\n                      background: positionColor,\n                      color: 'white',\n                      borderRadius: '4px',\n                      fontSize: '0.75rem',\n                      fontWeight: '600',\n                      display: 'inline-block'\n                    }}>\n                      {positionLabel}\n                    </div>\n                  </div>\n                </div>\n                \n                <div style={{\n                  padding: '0.25rem 0.5rem',\n                  background: admin.is_active ? '#dcfce7' : '#fef2f2',\n                  color: admin.is_active ? '#166534' : '#dc2626',\n                  borderRadius: '4px',\n                  fontSize: '0.75rem',\n                  fontWeight: '600'\n                }}>\n                  {admin.is_active ? 'Active' : 'Inactive'}\n                </div>\n              </div>\n\n              {/* Contact Info */}\n              <div style={{ marginBottom: '1rem' }}>\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  marginBottom: '0.5rem'\n                }}>\n                  <Mail size={16} color=\"#6b7280\" />\n                  <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>\n                    {admin.email}\n                  </span>\n                </div>\n                \n                {admin.phone_number && (\n                  <div style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem',\n                    marginBottom: '0.5rem'\n                  }}>\n                    <Phone size={16} color=\"#6b7280\" />\n                    <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>\n                      {admin.phone_number}\n                    </span>\n                  </div>\n                )}\n                \n                {admin.department && (\n                  <div style={{\n                    fontSize: '0.875rem',\n                    color: '#6b7280',\n                    marginTop: '0.5rem'\n                  }}>\n                    Department: {admin.department}\n                  </div>\n                )}\n                \n                {admin.grade_level && (\n                  <div style={{\n                    fontSize: '0.875rem',\n                    color: '#6b7280',\n                    marginTop: '0.25rem'\n                  }}>\n                    Grade Level: {admin.grade_level}\n                  </div>\n                )}\n              </div>\n\n              {/* Last Login */}\n              {admin.last_login && (\n                <div style={{\n                  fontSize: '0.75rem',\n                  color: '#9ca3af',\n                  marginBottom: '1rem'\n                }}>\n                  Last login: {new Date(admin.last_login).toLocaleDateString()}\n                </div>\n              )}\n\n              {/* Actions */}\n              <div style={{\n                display: 'flex',\n                gap: '0.5rem',\n                paddingTop: '1rem',\n                borderTop: '1px solid #f3f4f6'\n              }}>\n                <button\n                  style={{\n                    flex: 1,\n                    padding: '0.5rem',\n                    background: 'transparent',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '6px',\n                    cursor: 'pointer',\n                    color: '#6b7280',\n                    fontSize: '0.875rem',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    gap: '0.25rem'\n                  }}\n                >\n                  <Edit size={14} />\n                  Edit\n                </button>\n                \n                {admin.admin_id !== user?.id && (\n                  <button\n                    style={{\n                      flex: 1,\n                      padding: '0.5rem',\n                      background: 'transparent',\n                      border: '1px solid #fca5a5',\n                      borderRadius: '6px',\n                      cursor: 'pointer',\n                      color: '#dc2626',\n                      fontSize: '0.875rem',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      gap: '0.25rem'\n                    }}\n                  >\n                    <Trash2 size={14} />\n                    Delete\n                  </button>\n                )}\n              </div>\n            </div>\n          );\n        })}\n      </div>\n    </div>\n  );\n};\n\nexport default AdminManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAElD,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA0BzD,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IAAEC;EAAK,CAAC,GAAGN,YAAY,CAAC,CAAC;EAC/B,MAAMO,WAAW,GAAGN,cAAc,CAACK,IAAI,CAAC;;EAExC;EACA,MAAM,CAACE,MAAM,EAAEC,SAAS,CAAC,GAAGX,QAAQ,CAAiB,EAAE,CAAC;EACxD,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAgB,IAAI,CAAC;;EAE3D;EACA,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAsB,IAAI,CAAC;EAC3E,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0B,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAAS,EAAE,CAAC;EAChE,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAS,EAAE,CAAC;;EAE5D;EACA,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACkC,UAAU,EAAEC,aAAa,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;EAE/CC,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACQ,WAAW,CAAC2B,eAAe,EAAE;MAChCrB,QAAQ,CAAC,qDAAqD,CAAC;MAC/DF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEAwB,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAC5B,WAAW,CAAC2B,eAAe,CAAC,CAAC;EAEjC,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFxB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA,MAAMuB,UAA0B,GAAG,CACjC;QACEC,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE,0BAA0B;QACjCC,SAAS,EAAE,IAAI;QACfC,UAAU,EAAE,sBAAsB;QAClCC,UAAU,EAAE,sBAAsB;QAClCC,OAAO,EAAE;UACPC,UAAU,EAAE,MAAM;UAClBC,WAAW,EAAE,WAAW;UACxBC,SAAS,EAAE,QAAQ;UACnBC,SAAS,EAAE,uBAAuB;UAClCC,YAAY,EAAE,eAAe;UAC7BC,UAAU,EAAE,gBAAgB;UAC5BC,QAAQ,EAAE,aAAa;UACvBC,WAAW,EAAE,EAAE;UACfC,GAAG,EAAE;QACP;MACF,CAAC,EACD;QACEd,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE,oBAAoB;QAC3BC,SAAS,EAAE,IAAI;QACfC,UAAU,EAAE,sBAAsB;QAClCC,UAAU,EAAE,sBAAsB;QAClCC,OAAO,EAAE;UACPC,UAAU,EAAE,OAAO;UACnBE,SAAS,EAAE,UAAU;UACrBC,SAAS,EAAE,gBAAgB;UAC3BC,YAAY,EAAE,eAAe;UAC7BC,UAAU,EAAE,aAAa;UACzBC,QAAQ,EAAE,WAAW;UACrBC,WAAW,EAAE,EAAE;UACfC,GAAG,EAAE;QACP;MACF,CAAC,CACF;;MAED;MACA,IAAIC,cAAc,GAAGhB,UAAU;MAE/B,IAAId,UAAU,EAAE;QACd8B,cAAc,GAAGA,cAAc,CAACC,MAAM,CAACC,KAAK,IAC1CA,KAAK,CAACZ,OAAO,CAACI,SAAS,CAACS,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClC,UAAU,CAACiC,WAAW,CAAC,CAAC,CAAC,IACxED,KAAK,CAAChB,KAAK,CAACiB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClC,UAAU,CAACiC,WAAW,CAAC,CAAC,CAC7D,CAAC;MACH;MAEA,IAAI/B,cAAc,EAAE;QAClB4B,cAAc,GAAGA,cAAc,CAACC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACZ,OAAO,CAACO,QAAQ,KAAKzB,cAAc,CAAC;MAC5F;MAEA,IAAIE,YAAY,EAAE;QAChB,MAAM+B,QAAQ,GAAG/B,YAAY,KAAK,QAAQ;QAC1C0B,cAAc,GAAGA,cAAc,CAACC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACf,SAAS,KAAKkB,QAAQ,CAAC;MAC/E;MAEAxB,aAAa,CAACmB,cAAc,CAACM,MAAM,CAAC;;MAEpC;MACA,MAAMC,UAAU,GAAG,CAAC/B,WAAW,GAAG,CAAC,IAAIE,YAAY;MACnD,MAAM8B,eAAe,GAAGR,cAAc,CAACS,KAAK,CAACF,UAAU,EAAEA,UAAU,GAAG7B,YAAY,CAAC;MAEnFrB,SAAS,CAACmD,eAAe,CAAC;IAE5B,CAAC,CAAC,OAAOE,GAAQ,EAAE;MACjBjD,QAAQ,CAACiD,GAAG,CAACC,OAAO,IAAI,+BAA+B,CAAC;IAC1D,CAAC,SAAS;MACRpD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqD,eAAe,GAAIf,QAAgB,IAAK;IAC5C,OAAOA,QAAQ,KAAK,aAAa,GAAGgB,MAAM,GAAGC,IAAI;EACnD,CAAC;EAED,MAAMC,gBAAgB,GAAIlB,QAAgB,IAAK;IAC7C,OAAOA,QAAQ,KAAK,aAAa,GAAG,SAAS,GAAG,SAAS;EAC3D,CAAC;EAED,MAAMmB,gBAAgB,GAAInB,QAAgB,IAAK;IAC7C,OAAOA,QAAQ,KAAK,aAAa,GAAG,qBAAqB,GAAG,WAAW;EACzE,CAAC;EAED,IAAI,CAAC1C,WAAW,CAAC2B,eAAe,EAAE;IAChC,oBACE/B,OAAA;MAAKkE,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE,OAAO;QAClBC,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACA1E,OAAA,CAAC2E,OAAO;QAACC,IAAI,EAAE,EAAG;QAACV,KAAK,EAAE;UAAEW,YAAY,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAI;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpElF,OAAA;QAAIkE,KAAK,EAAE;UAAEiB,MAAM,EAAE,YAAY;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAM,CAAE;QAAAX,QAAA,EAAC;MAE5E;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLlF,OAAA;QAAGkE,KAAK,EAAE;UAAEiB,MAAM,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAV,QAAA,EAAC;MAE3C;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJlF,OAAA;QAAKkE,KAAK,EAAE;UACVoB,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAEpF,WAAW,CAACqF,qBAAqB,CAAC,CAAC;UAC/CC,YAAY,EAAE,KAAK;UACnBjB,KAAK,EAAE,OAAO;UACdW,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE;QACd,CAAE;QAAAX,QAAA,GAAC,gBACa,EAACtE,WAAW,CAACuF,sBAAsB,CAAC,CAAC;MAAA;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI3E,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKkE,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE;MACb,CAAE;MAAAG,QAAA,eACA1E,OAAA;QAAKkE,KAAK,EAAE;UACV0B,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,mBAAmB;UAC3BC,SAAS,EAAE,mBAAmB;UAC9BL,YAAY,EAAE,KAAK;UACnBM,SAAS,EAAE;QACb;MAAE;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEV;EAEA,IAAIzE,KAAK,EAAE;IACT,oBACET,OAAA;MAAKkE,KAAK,EAAE;QACVqB,OAAO,EAAE,MAAM;QACff,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,gBACA1E,OAAA;QAAA0E,QAAA,EAAI;MAAK;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdlF,OAAA;QAAA0E,QAAA,EAAIjE;MAAK;QAAAsE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdlF,OAAA;QACEiG,OAAO,EAAEjE,UAAW;QACpBkC,KAAK,EAAE;UACLqB,OAAO,EAAE,aAAa;UACtBC,UAAU,EAAE,SAAS;UACrBf,KAAK,EAAE,OAAO;UACdqB,MAAM,EAAE,MAAM;UACdJ,YAAY,EAAE,KAAK;UACnBQ,MAAM,EAAE;QACV,CAAE;QAAAxB,QAAA,EACH;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACElF,OAAA;IAAKkE,KAAK,EAAE;MAAEiC,QAAQ,EAAE,QAAQ;MAAEhB,MAAM,EAAE;IAAS,CAAE;IAAAT,QAAA,gBAEnD1E,OAAA;MAAKkE,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfG,cAAc,EAAE,eAAe;QAC/BD,UAAU,EAAE,QAAQ;QACpBQ,YAAY,EAAE;MAChB,CAAE;MAAAH,QAAA,gBACA1E,OAAA;QAAA0E,QAAA,gBACE1E,OAAA;UAAIkE,KAAK,EAAE;YACTiB,MAAM,EAAE,YAAY;YACpBC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjBZ,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,EAAC;QAEH;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlF,OAAA;UAAGkE,KAAK,EAAE;YACRiB,MAAM,EAAE,CAAC;YACTV,KAAK,EAAE,SAAS;YAChBW,QAAQ,EAAE;UACZ,CAAE;UAAAV,QAAA,EAAC;QAEH;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENlF,OAAA;QACEkE,KAAK,EAAE;UACLC,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpB+B,GAAG,EAAE,QAAQ;UACbb,OAAO,EAAE,gBAAgB;UACzBC,UAAU,EAAE,SAAS;UACrBf,KAAK,EAAE,OAAO;UACdqB,MAAM,EAAE,MAAM;UACdJ,YAAY,EAAE,KAAK;UACnBN,QAAQ,EAAE,UAAU;UACpBC,UAAU,EAAE,KAAK;UACjBa,MAAM,EAAE,SAAS;UACjBG,UAAU,EAAE;QACd,CAAE;QACFC,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACtC,KAAK,CAACsB,UAAU,GAAG,SAAU;QAClEiB,YAAY,EAAGF,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACtC,KAAK,CAACsB,UAAU,GAAG,SAAU;QAAAd,QAAA,gBAElE1E,OAAA,CAAC0G,IAAI;UAAC9B,IAAI,EAAE;QAAG;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,aAEpB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNlF,OAAA;MAAKkE,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfwC,mBAAmB,EAAE,uCAAuC;QAC5DP,GAAG,EAAE;MACP,CAAE;MAAA1B,QAAA,EACCrE,MAAM,CAACuG,GAAG,CAAEzD,KAAK,IAAK;QACrB,MAAM0D,YAAY,GAAGhD,eAAe,CAACV,KAAK,CAACL,QAAQ,CAAC;QACpD,MAAMgE,aAAa,GAAG9C,gBAAgB,CAACb,KAAK,CAACL,QAAQ,CAAC;QACtD,MAAMiE,aAAa,GAAG9C,gBAAgB,CAACd,KAAK,CAACL,QAAQ,CAAC;QAEtD,oBACE9C,OAAA;UAEEkE,KAAK,EAAE;YACLsB,UAAU,EAAE,OAAO;YACnBE,YAAY,EAAE,MAAM;YACpBsB,SAAS,EAAE,8BAA8B;YACzCzB,OAAO,EAAE,QAAQ;YACjBO,MAAM,EAAE3C,KAAK,CAACjB,QAAQ,MAAK/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8G,EAAE,IAAG,mBAAmB,GAAG;UAC9D,CAAE;UAAAvC,QAAA,gBAGF1E,OAAA;YAAKkE,KAAK,EAAE;cACVC,OAAO,EAAE,MAAM;cACfG,cAAc,EAAE,eAAe;cAC/BD,UAAU,EAAE,YAAY;cACxBQ,YAAY,EAAE;YAChB,CAAE;YAAAH,QAAA,gBACA1E,OAAA;cAAKkE,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAE+B,GAAG,EAAE;cAAU,CAAE;cAAA1B,QAAA,gBACpE1E,OAAA;gBAAKkE,KAAK,EAAE;kBACV0B,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdL,UAAU,EAAE,2BAA2BsB,aAAa,OAAOA,aAAa,KAAK;kBAC7EpB,YAAY,EAAE,MAAM;kBACpBvB,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBC,cAAc,EAAE;gBAClB,CAAE;gBAAAI,QAAA,eACA1E,OAAA,CAAC6G,YAAY;kBAACjC,IAAI,EAAE,EAAG;kBAACH,KAAK,EAAEqC;gBAAc;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACNlF,OAAA;gBAAA0E,QAAA,gBACE1E,OAAA;kBAAIkE,KAAK,EAAE;oBACTiB,MAAM,EAAE,aAAa;oBACrBC,QAAQ,EAAE,UAAU;oBACpBC,UAAU,EAAE,KAAK;oBACjBZ,KAAK,EAAE;kBACT,CAAE;kBAAAC,QAAA,GACCvB,KAAK,CAACX,UAAU,EAAC,GAAC,EAACW,KAAK,CAACT,SAAS,EAClCS,KAAK,CAACjB,QAAQ,MAAK/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8G,EAAE,kBAC1BjH,OAAA;oBAAMkE,KAAK,EAAE;sBACXgD,UAAU,EAAE,QAAQ;sBACpB3B,OAAO,EAAE,mBAAmB;sBAC5BC,UAAU,EAAE,SAAS;sBACrBf,KAAK,EAAE,SAAS;sBAChBiB,YAAY,EAAE,KAAK;sBACnBN,QAAQ,EAAE,UAAU;sBACpBC,UAAU,EAAE;oBACd,CAAE;oBAAAX,QAAA,EAAC;kBAEH;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACLlF,OAAA;kBAAKkE,KAAK,EAAE;oBACVqB,OAAO,EAAE,gBAAgB;oBACzBC,UAAU,EAAEsB,aAAa;oBACzBrC,KAAK,EAAE,OAAO;oBACdiB,YAAY,EAAE,KAAK;oBACnBN,QAAQ,EAAE,SAAS;oBACnBC,UAAU,EAAE,KAAK;oBACjBlB,OAAO,EAAE;kBACX,CAAE;kBAAAO,QAAA,EACCqC;gBAAa;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENlF,OAAA;cAAKkE,KAAK,EAAE;gBACVqB,OAAO,EAAE,gBAAgB;gBACzBC,UAAU,EAAErC,KAAK,CAACf,SAAS,GAAG,SAAS,GAAG,SAAS;gBACnDqC,KAAK,EAAEtB,KAAK,CAACf,SAAS,GAAG,SAAS,GAAG,SAAS;gBAC9CsD,YAAY,EAAE,KAAK;gBACnBN,QAAQ,EAAE,SAAS;gBACnBC,UAAU,EAAE;cACd,CAAE;cAAAX,QAAA,EACCvB,KAAK,CAACf,SAAS,GAAG,QAAQ,GAAG;YAAU;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNlF,OAAA;YAAKkE,KAAK,EAAE;cAAEW,YAAY,EAAE;YAAO,CAAE;YAAAH,QAAA,gBACnC1E,OAAA;cAAKkE,KAAK,EAAE;gBACVC,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE,QAAQ;gBACpB+B,GAAG,EAAE,QAAQ;gBACbvB,YAAY,EAAE;cAChB,CAAE;cAAAH,QAAA,gBACA1E,OAAA,CAACmH,IAAI;gBAACvC,IAAI,EAAE,EAAG;gBAACH,KAAK,EAAC;cAAS;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClClF,OAAA;gBAAMkE,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,UAAU;kBAAEX,KAAK,EAAE;gBAAU,CAAE;gBAAAC,QAAA,EACrDvB,KAAK,CAAChB;cAAK;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EAEL/B,KAAK,CAACP,YAAY,iBACjB5C,OAAA;cAAKkE,KAAK,EAAE;gBACVC,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE,QAAQ;gBACpB+B,GAAG,EAAE,QAAQ;gBACbvB,YAAY,EAAE;cAChB,CAAE;cAAAH,QAAA,gBACA1E,OAAA,CAACoH,KAAK;gBAACxC,IAAI,EAAE,EAAG;gBAACH,KAAK,EAAC;cAAS;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnClF,OAAA;gBAAMkE,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,UAAU;kBAAEX,KAAK,EAAE;gBAAU,CAAE;gBAAAC,QAAA,EACrDvB,KAAK,CAACP;cAAY;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN,EAEA/B,KAAK,CAACN,UAAU,iBACf7C,OAAA;cAAKkE,KAAK,EAAE;gBACVkB,QAAQ,EAAE,UAAU;gBACpBX,KAAK,EAAE,SAAS;gBAChBa,SAAS,EAAE;cACb,CAAE;cAAAZ,QAAA,GAAC,cACW,EAACvB,KAAK,CAACN,UAAU;YAAA;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CACN,EAEA/B,KAAK,CAACJ,WAAW,iBAChB/C,OAAA;cAAKkE,KAAK,EAAE;gBACVkB,QAAQ,EAAE,UAAU;gBACpBX,KAAK,EAAE,SAAS;gBAChBa,SAAS,EAAE;cACb,CAAE;cAAAZ,QAAA,GAAC,eACY,EAACvB,KAAK,CAACJ,WAAW;YAAA;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGL/B,KAAK,CAACd,UAAU,iBACfrC,OAAA;YAAKkE,KAAK,EAAE;cACVkB,QAAQ,EAAE,SAAS;cACnBX,KAAK,EAAE,SAAS;cAChBI,YAAY,EAAE;YAChB,CAAE;YAAAH,QAAA,GAAC,cACW,EAAC,IAAI2C,IAAI,CAAClE,KAAK,CAACd,UAAU,CAAC,CAACiF,kBAAkB,CAAC,CAAC;UAAA;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CACN,eAGDlF,OAAA;YAAKkE,KAAK,EAAE;cACVC,OAAO,EAAE,MAAM;cACfiC,GAAG,EAAE,QAAQ;cACbmB,UAAU,EAAE,MAAM;cAClBxB,SAAS,EAAE;YACb,CAAE;YAAArB,QAAA,gBACA1E,OAAA;cACEkE,KAAK,EAAE;gBACLsD,IAAI,EAAE,CAAC;gBACPjC,OAAO,EAAE,QAAQ;gBACjBC,UAAU,EAAE,aAAa;gBACzBM,MAAM,EAAE,mBAAmB;gBAC3BJ,YAAY,EAAE,KAAK;gBACnBQ,MAAM,EAAE,SAAS;gBACjBzB,KAAK,EAAE,SAAS;gBAChBW,QAAQ,EAAE,UAAU;gBACpBjB,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB8B,GAAG,EAAE;cACP,CAAE;cAAA1B,QAAA,gBAEF1E,OAAA,CAACyH,IAAI;gBAAC7C,IAAI,EAAE;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,QAEpB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAER/B,KAAK,CAACjB,QAAQ,MAAK/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8G,EAAE,kBAC1BjH,OAAA;cACEkE,KAAK,EAAE;gBACLsD,IAAI,EAAE,CAAC;gBACPjC,OAAO,EAAE,QAAQ;gBACjBC,UAAU,EAAE,aAAa;gBACzBM,MAAM,EAAE,mBAAmB;gBAC3BJ,YAAY,EAAE,KAAK;gBACnBQ,MAAM,EAAE,SAAS;gBACjBzB,KAAK,EAAE,SAAS;gBAChBW,QAAQ,EAAE,UAAU;gBACpBjB,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB8B,GAAG,EAAE;cACP,CAAE;cAAA1B,QAAA,gBAEF1E,OAAA,CAAC0H,MAAM;gBAAC9C,IAAI,EAAE;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAEtB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,GAxLD/B,KAAK,CAACjB,QAAQ;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAyLhB,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChF,EAAA,CApdID,eAAyB;EAAA,QACZJ,YAAY,EACTC,cAAc;AAAA;AAAA6H,EAAA,GAF9B1H,eAAyB;AAsd/B,eAAeA,eAAe;AAAC,IAAA0H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}